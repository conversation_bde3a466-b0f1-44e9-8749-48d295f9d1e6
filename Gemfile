source "https://rubygems.org"

# Bundle edge Rails instead: gem "rails", github: "rails/rails", branch: "main"
gem "rails", "~> 8.0.2"
# The modern asset pipeline for Rails [https://github.com/rails/propshaft]
gem "propshaft"
# Use postgresql as the database for Active Record
gem "pg", "~> 1.1"
# Use the Puma web server [https://github.com/puma/puma]
gem "puma", ">= 5.0"
# Use JavaScript with ESM import maps [https://github.com/rails/importmap-rails]
gem "importmap-rails"
# Hotwire's SPA-like page accelerator [https://turbo.hotwired.dev]
gem "turbo-rails"
# Hotwire's modest JavaScript framework [https://stimulus.hotwired.dev]
gem "stimulus-rails"
# Use Tailwind CSS [https://github.com/rails/tailwindcss-rails]
gem "tailwindcss-rails"
# Build JSON APIs with ease [https://github.com/rails/jbuilder]
gem "jbuilder"

# Authentication & Authorization
gem "devise", "~> 4.9"
gem "devise-jwt", "~> 0.11"
gem "jwt", "~> 2.7"
gem "bcrypt", "~> 3.1.7"
gem "pundit", "~> 2.3"

# Background Jobs - Using Rails 8 SolidQueue (included in Rails)

# Payment Processing
gem "stripe", "~> 10.0"
gem "money-rails", "~> 1.15"

# Admin Panel (Rails 8 compatible version)
# gem "administrate" # Wait for Rails 8 support

# Analytics
gem "ahoy_matey", "~> 5.0"
# gem "blazer", "~> 3.0" # Wait for Rails 8 support

# Error Tracking
gem "rollbar", "~> 3.5"

# Feature Flags
gem "flipper", "~> 1.2"
gem "flipper-active_record", "~> 1.2"
gem "flipper-ui", "~> 1.2"

# API
gem "rack-cors", "~> 2.0"
gem "jsonapi-serializer", "~> 2.2"

# API Documentation
gem "rswag", "~> 2.13"

# Utilities
gem "friendly_id", "~> 5.5"
gem "image_processing", "~> 1.12"
gem "aws-sdk-s3", "~> 1.140", require: false
gem "geocoder", "~> 1.8"
gem "pagy", "~> 6.2"

# Windows does not include zoneinfo files, so bundle the tzinfo-data gem
gem "tzinfo-data", platforms: %i[ windows jruby ]

# Use the database-backed adapters for Rails.cache, Active Job, and Action Cable
gem "solid_cache"
gem "solid_queue"
gem "solid_cable"

# Reduces boot times through caching; required in config/boot.rb
gem "bootsnap", require: false

# Deploy this application anywhere as a Docker container [https://kamal-deploy.org]
gem "kamal", require: false

# Add HTTP asset caching/compression and X-Sendfile acceleration to Puma [https://github.com/basecamp/thruster/]
gem "thruster", require: false

# Use Active Storage variants [https://guides.rubyonrails.org/active_storage_overview.html#transforming-images]
# gem "image_processing", "~> 1.2"

group :development, :test do
  # See https://guides.rubyonrails.org/debugging_rails_applications.html#debugging-with-the-debug-gem
  gem "debug", platforms: %i[ mri windows ], require: "debug/prelude"

  # Static analysis for security vulnerabilities [https://brakemanscanner.org/]
  gem "brakeman", require: false

  # Omakase Ruby styling [https://github.com/rails/rubocop-rails-omakase/]
  gem "rubocop-rails-omakase", require: false

  # Testing
  gem "rspec-rails", "~> 6.1"
  gem "factory_bot_rails", "~> 6.4"
  gem "faker", "~> 3.2"

  # API Documentation Testing
  gem "rswag-specs", "~> 2.13"
end

group :development do
  # Use console on exceptions pages [https://github.com/rails/web-console]
  gem "web-console"

  # Better error pages
  gem "better_errors", "~> 2.10"
  gem "binding_of_caller", "~> 1.0"

  # Performance monitoring
  # gem "bullet", "~> 7.1" # Wait for Rails 8 support
  gem "rack-mini-profiler", "~> 3.3"

  # Spring for faster development
  gem "spring", "~> 4.1"

  # Generate ERD diagrams
  gem "rails-erd", "~> 1.7"

  # Letter opener for email testing
  gem "letter_opener", "~> 1.8"
end

group :test do
  # Use system testing [https://guides.rubyonrails.org/testing.html#system-testing]
  gem "capybara"
  gem "selenium-webdriver"

  # Additional testing tools
  gem "shoulda-matchers", "~> 6.0"
  gem "database_cleaner-active_record", "~> 2.1"
  gem "simplecov", "~> 0.22", require: false
  gem "webmock", "~> 3.19"
  gem "rails-controller-testing"
  gem "vcr", "~> 6.2"
end

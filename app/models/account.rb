class Account < ApplicationRecord
  # Associations
  has_many :users, dependent: :destroy
  has_many :account_invitations, dependent: :destroy
  has_many :team_invitations, dependent: :destroy
  has_many :api_tokens, dependent: :destroy
  has_many :notifications, dependent: :destroy
  has_many :pipelines, dependent: :destroy
  has_many :data_connectors, dependent: :destroy
  has_many :pipeline_executions, through: :pipelines
  has_many :usage_metrics, dependent: :destroy
  has_one :subscription, dependent: :destroy
  
  # Dashboard and visualization associations
  has_many :dashboards, dependent: :destroy
  has_many :widgets, through: :dashboards
  has_many :saved_reports, dependent: :destroy

  # Agent feature associations
  has_many :agent_recommendations, dependent: :destroy
  has_many :agent_revenues, dependent: :destroy

  # Validations
  validates :name, presence: true
  validates :subdomain, presence: true, uniqueness: true,
            format: { with: /\A[a-z0-9]+\z/, message: "only lowercase letters and numbers allowed" },
            length: { minimum: 3, maximum: 30 }

  # Enums
  enum :status, {
    active: 0,
    suspended: 1,
    canceled: 2
  }

  # Callbacks
  before_validation :parameterize_subdomain
  after_create :create_default_subscription

  # Scopes
  scope :onboarded, -> { where.not(onboarded_at: nil) }
  scope :not_onboarded, -> { where(onboarded_at: nil) }

  # Instance methods
  def owner
    users.find_by(role: "owner")
  end

  def admin_users
    users.where(role: [ :owner, :admin ])
  end

  def team_members
    users.where.not(role: :owner).order(:created_at)
  end

  def pending_invitations
    team_invitations.valid_invitations
  end

  def can_invite_members?
    # Check subscription limits
    case subscription&.plan&.to_sym
    when :free
      users.count < 2  # Free plan: 1 owner + 1 member
    when :starter
      users.count < 5  # Starter: up to 5 users
    when :professional
      users.count < 25 # Professional: up to 25 users
    when :enterprise
      true # Enterprise: unlimited users
    else
      false
    end
  end

  def max_team_members
    case subscription&.plan&.to_sym
    when :free
      1
    when :starter
      4  # 5 total - 1 owner
    when :professional
      24 # 25 total - 1 owner
    when :enterprise
      -1 # unlimited
    else
      0
    end
  end

  def onboarded?
    onboarded_at.present?
  end

  def within_pipeline_limit?
    # TODO: Implement when Pipeline model exists
    true
  end

  def plan_pipeline_limit
    subscription&.plan_limits[:pipelines] || 2
  end

  # Agent feature methods
  def agent_revenue_this_month
    agent_revenues.where(created_at: Time.current.beginning_of_month..).sum(:amount_cents) / 100.0
  end

  def total_agent_revenue
    agent_revenues.sum(:amount_cents) / 100.0
  end

  def agent_adoption_rate
    total_recommendations = agent_recommendations.count
    return 0 if total_recommendations.zero?

    implemented_count = agent_recommendations.where(status: [ :accepted, :implemented ]).count
    (implemented_count.to_f / total_recommendations * 100).round(1)
  end

  def agent_recommendations_count_this_month
    current_month_start = Time.current.beginning_of_month
    agent_recommendations.where(created_at: current_month_start..).count
  end

  def can_generate_agent_recommendations?
    return false unless subscription&.agent_features_enabled?
    subscription.within_agent_recommendations_limit?
  end

  def agent_recommendations_remaining_this_month
    subscription&.agent_recommendations_remaining || 0
  end

  def agent_features_enabled?
    subscription&.agent_features_enabled? || false
  end

  private

  def parameterize_subdomain
    self.subdomain = subdomain&.downcase&.gsub(/[^a-z0-9]/, "") if subdomain_changed?
  end

  def create_default_subscription
    create_subscription!(plan: "free", status: "active") unless subscription
  end
end

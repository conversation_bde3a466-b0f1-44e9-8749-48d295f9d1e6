class UsageMetric < ApplicationRecord
  belongs_to :account

  validates :metric_type, presence: true
  validates :value, presence: true, numericality: true
  validates :recorded_at, presence: true

  # Metric types for DataReflow platform
  METRIC_TYPES = %w[
    pipeline_executions
    pipeline_status_change
    data_rows_processed
    api_requests
    storage_used_mb
    team_members
    active_connections
    execution_time_minutes
    error_rate
    success_rate
    monthly_active_users
    connector_test
    data_volume
    execution_success
    execution_failure
    agent_revenue
    revenue_milestone_achieved
    recommendation_auto_implemented
    recommendation_auto_implementation_failed
  ].freeze

  validates :metric_type, inclusion: { in: METRIC_TYPES }

  # Callbacks
  after_save :invalidate_dashboard_cache
  after_destroy :invalidate_dashboard_cache

  scope :for_metric_type, ->(type) { where(metric_type: type) }
  scope :in_date_range, ->(start_date, end_date) { where(recorded_at: start_date..end_date) }
  scope :recent, -> { where("recorded_at >= ?", 30.days.ago) }
  scope :current_month, -> { where(recorded_at: Date.current.beginning_of_month..Date.current.end_of_month) }
  scope :previous_month, -> { where(recorded_at: Date.current.last_month.beginning_of_month..Date.current.last_month.end_of_month) }

  # Class methods for recording metrics
  def self.record_metric(account, metric_type, value, metadata = {})
    create!(
      account: account,
      metric_type: metric_type,
      value: value,
      recorded_at: Time.current,
      metadata: metadata
    )
  end

  def self.aggregate_for_period(account, metric_type, start_date, end_date, aggregation = :sum)
    metrics = where(account: account, metric_type: metric_type)
                .in_date_range(start_date, end_date)

    case aggregation
    when :sum
      metrics.sum(:value)
    when :avg
      metrics.average(:value)
    when :max
      metrics.maximum(:value)
    when :min
      metrics.minimum(:value)
    when :count
      metrics.count
    else
      metrics.sum(:value)
    end
  end

  def self.daily_summary(account, metric_type, days = 30)
    end_date = Date.current
    start_date = end_date - days.days

    where(account: account, metric_type: metric_type)
      .in_date_range(start_date, end_date)
      .group("DATE(recorded_at)")
      .sum(:value)
  end

  def self.usage_for_current_billing_cycle(account)
    # Assuming billing cycle starts on the subscription's current_period_start
    start_date = account.subscription&.current_period_start || Date.current.beginning_of_month
    end_date = account.subscription&.current_period_end || Date.current.end_of_month

    where(account: account)
      .in_date_range(start_date, end_date)
      .group(:metric_type)
      .sum(:value)
  end

  # Instance methods
  def formatted_value
    case metric_type
    when "storage_used_mb"
      "#{value.to_i} MB"
    when "execution_time_minutes"
      "#{value.to_f.round(2)} min"
    when "error_rate", "success_rate"
      "#{(value * 100).to_f.round(1)}%"
    when "api_requests", "pipeline_executions", "data_rows_processed"
      value.to_i.to_s.reverse.gsub(/(\d{3})(?=\d)/, '\\1,').reverse
    else
      value.to_s
    end
  end

  def human_readable_type
    metric_type.humanize
  end

  private

  def invalidate_dashboard_cache
    return unless account.present?

    DashboardCacheService.invalidate_usage_metrics(account)
    Rails.logger.debug "Dashboard cache invalidated for account #{account.id} due to usage metric #{id} changes"
  end
end

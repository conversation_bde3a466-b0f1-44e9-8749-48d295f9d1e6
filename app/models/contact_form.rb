# Contact form model for handling contact page submissions
# This is a form object that doesn't persist to database but provides validation
class ContactForm
  include ActiveModel::Model
  include ActiveModel::Attributes
  include ActiveModel::Validations

  attribute :name, :string
  attribute :email, :string
  attribute :company, :string
  attribute :inquiry_type, :string
  attribute :message, :string

  validates :name, presence: true, length: { minimum: 2, maximum: 100 }
  validates :email, presence: true, format: { with: URI::MailTo::EMAIL_REGEXP }
  validates :company, presence: true, length: { minimum: 2, maximum: 100 }
  validates :inquiry_type, presence: true, inclusion: {
    in: [ "general", "sales", "support", "partnership", "press" ],
    message: "Please select a valid inquiry type"
  }
  validates :message, presence: true, length: { minimum: 10, maximum: 1000 }

  # Available inquiry types for the contact form
  def self.inquiry_types
    [
      [ "General Inquiry", "general" ],
      [ "Sales & Pricing", "sales" ],
      [ "Technical Support", "support" ],
      [ "Partnership Opportunities", "partnership" ],
      [ "Press & Media", "press" ]
    ]
  end

  # Method to send the contact form (would integrate with email service)
  def deliver
    # In a real application, this would send an email
    # For now, we'll just return true to simulate success
    Rails.logger.info "Contact form submitted: #{attributes}"
    true
  end
end

class Dashboard < ApplicationRecord
  # Associations
  belongs_to :account
  belongs_to :user
  has_many :widgets, dependent: :destroy
  has_many :saved_reports, dependent: :nullify
  
  # Validations
  validates :name, presence: true, length: { minimum: 2, maximum: 100 }
  validates :dashboard_type, presence: true
  validates :public_slug, uniqueness: true, allow_nil: true
  
  # Enums
  enum :dashboard_type, {
    custom: 0,
    system: 1,
    template: 2
  }
  
  # Scopes
  scope :visible_to, ->(user) { where(account: user.account).or(where(is_public: true)) }
  scope :defaults, -> { where(is_default: true) }
  scope :ordered, -> { order(position: :asc, created_at: :desc) }
  scope :recently_viewed, -> { order(last_viewed_at: :desc) }
  
  # Callbacks
  before_validation :generate_public_slug, if: :is_public?
  after_save :ensure_single_default, if: :is_default?
  after_touch :update_last_viewed
  
  # Instance methods
  def duplicate_for_user(new_user)
    new_dashboard = self.dup
    new_dashboard.user = new_user
    new_dashboard.is_default = false
    new_dashboard.public_slug = nil
    new_dashboard.is_public = false
    new_dashboard.save!
    
    # Duplicate widgets
    widgets.each do |widget|
      new_widget = widget.dup
      new_widget.dashboard = new_dashboard
      new_widget.save!
    end
    
    new_dashboard
  end
  
  def add_widget(type, config = {})
    widgets.create!(
      name: config[:name] || "New #{type.humanize}",
      widget_type: type,
      config: config[:config] || {},
      data_source: config[:data_source] || {},
      position: calculate_next_position
    )
  end
  
  def reorganize_layout
    # Auto-arrange widgets in a grid
    widgets.visible.ordered.each_with_index do |widget, index|
      row = index / 3
      col = index % 3
      widget.update(position: { x: col * 4, y: row * 4, w: 4, h: 4 })
    end
  end
  
  def share!
    update!(is_public: true) unless is_public?
    public_url
  end
  
  def public_url
    return nil unless is_public? && public_slug.present?
    Rails.application.routes.url_helpers.public_dashboard_url(public_slug)
  end
  
  def record_view!
    increment!(:view_count)
    touch(:last_viewed_at)
  end
  
  # Class methods
  def self.create_default_for_account(account)
    transaction do
      # Create main dashboard
      main_dashboard = create!(
        account: account,
        user: account.owner,
        name: "Main Dashboard",
        description: "Overview of your data pipeline operations",
        dashboard_type: :system,
        is_default: true,
        layout_config: default_layout_config
      )
      
      # Add default widgets
      main_dashboard.add_widget('metric_card', {
        name: 'Total Pipelines',
        data_source: { metric: 'total_pipelines' }
      })
      
      main_dashboard.add_widget('metric_card', {
        name: 'Success Rate',
        data_source: { metric: 'success_rate' }
      })
      
      main_dashboard.add_widget('line_chart', {
        name: 'Execution Trends',
        data_source: { query: 'pipeline_executions_over_time' }
      })
      
      main_dashboard.add_widget('bar_chart', {
        name: 'Top Pipelines',
        data_source: { query: 'top_performing_pipelines' }
      })
      
      main_dashboard
    end
  end
  
  private
  
  def generate_public_slug
    self.public_slug ||= SecureRandom.urlsafe_base64(16)
  end
  
  def ensure_single_default
    Dashboard.where(account: account)
            .where.not(id: id)
            .update_all(is_default: false)
  end
  
  def update_last_viewed
    update_column(:last_viewed_at, Time.current) if persisted?
  end
  
  def calculate_next_position
    max_y = widgets.maximum("CAST(position->>'y' AS INTEGER)") || 0
    { x: 0, y: max_y + 4, w: 4, h: 4 }
  end
  
  def self.default_layout_config
    {
      cols: 12,
      rowHeight: 50,
      margin: [10, 10],
      containerPadding: [10, 10],
      isDraggable: true,
      isResizable: true
    }
  end
end

class DataConnector < ApplicationRecord
  # Associations
  belongs_to :account
  belongs_to :created_by, class_name: "User", foreign_key: "created_by"

  # Validations
  validates :name, presence: true, length: { minimum: 2, maximum: 100 }
  validates :connector_type, presence: true
  validates :connection_config, presence: true
  validates :description, length: { maximum: 500 }, allow_blank: true

  # Enums
  enum :status, {
    inactive: 0,
    active: 1,
    error: 2,
    testing: 3
  }

  enum :test_status, {
    never_tested: 0,
    test_passed: 1,
    test_failed: 2,
    test_in_progress: 3
  }

  # Constants for connector types
  CONNECTOR_TYPES = %w[
    postgresql
    mysql
    sqlite
    csv_file
    json_file
    rest_api
    graphql_api
    webhook
    shopify
    salesforce
    hubspot
    mailchimp
    stripe
    google_sheets
    airtable
  ].freeze

  # Scopes
  scope :active, -> { where(status: :active) }
  scope :by_type, ->(type) { where(connector_type: type) }
  scope :recent, -> { order(created_at: :desc) }
  scope :tested_recently, -> { where(last_tested_at: 24.hours.ago..) }

  # Validations
  validates :connector_type, inclusion: { in: CONNECTOR_TYPES }
  validate :validate_connection_config

  # Callbacks
  before_validation :normalize_connector_type
  after_create :schedule_initial_test
  after_save :invalidate_dashboard_cache
  after_destroy :invalidate_dashboard_cache

  # Instance methods
  def display_name
    "#{name} (#{connector_type.humanize})"
  end

  def connection_healthy?
    test_status == "test_passed" &&
    last_tested_at.present? &&
    last_tested_at > 24.hours.ago
  end

  def needs_testing?
    never_tested? ||
    (last_tested_at.present? && last_tested_at < 24.hours.ago)
  end

  def database_connector?
    %w[postgresql mysql sqlite].include?(connector_type)
  end

  def api_connector?
    connector_type.end_with?("_api") ||
    %w[shopify salesforce hubspot mailchimp stripe].include?(connector_type)
  end

  def file_connector?
    connector_type.end_with?("_file")
  end

  def test_connection!
    update!(test_status: :test_in_progress)

    begin
      service = connector_service_class.new(self)
      result = service.test_connection

      if result[:success]
        update!(
          test_status: :test_passed,
          test_result: result[:message] || "Connection successful",
          last_tested_at: Time.current,
          status: :active
        )
        true
      else
        update!(
          test_status: :test_failed,
          test_result: result[:error] || "Connection failed",
          last_tested_at: Time.current,
          status: :error
        )
        false
      end
    rescue StandardError => e
      update!(
        test_status: :test_failed,
        test_result: "Connection error: #{e.message}",
        last_tested_at: Time.current,
        status: :error
      )
      false
    end
  end

  def connection_summary
    config = connection_config || {}

    case connector_type
    when "postgresql", "mysql"
      "#{config['host']}:#{config['port']}/#{config['database']}"
    when "rest_api"
      config["base_url"] || "API endpoint"
    when "csv_file"
      config["file_path"] || "CSV file"
    when "shopify"
      "#{config['shop_domain']}.myshopify.com"
    else
      connector_type.humanize
    end
  end

  def masked_config
    config = connection_config.dup

    # Mask sensitive information
    config["password"] = "***" if config["password"].present?
    config["api_key"] = "***" if config["api_key"].present?
    config["access_token"] = "***" if config["access_token"].present?
    config["client_secret"] = "***" if config["client_secret"].present?

    config
  end

  def connector_service_class
    case connector_type
    when "postgresql"
      DataConnectors::PostgresqlService
    when "mysql"
      DataConnectors::MysqlService
    when "csv_file"
      DataConnectors::CsvFileService
    when "rest_api"
      DataConnectors::RestApiService
    else
      DataConnectors::BaseService
    end
  end

  # Class methods
  def self.by_account(account)
    where(account: account)
  end

  def self.available_types
    CONNECTOR_TYPES.map do |type|
      {
        value: type,
        label: type.humanize,
        category: categorize_connector_type(type)
      }
    end.group_by { |item| item[:category] }
  end

  def self.categorize_connector_type(type)
    case type
    when "postgresql", "mysql", "sqlite"
      "Databases"
    when "csv_file", "json_file"
      "Files"
    when "rest_api", "graphql_api", "webhook"
      "APIs"
    when "shopify", "salesforce", "hubspot", "mailchimp", "stripe"
      "SaaS Platforms"
    when "google_sheets", "airtable"
      "Spreadsheets"
    else
      "Other"
    end
  end

  def self.health_summary(account)
    connectors = where(account: account)

    {
      total: connectors.count,
      active: connectors.active.count,
      healthy: connectors.joins("").where(
        test_status: :test_passed,
        last_tested_at: 24.hours.ago..
      ).count,
      needs_attention: connectors.where(
        status: :error
      ).or(
        connectors.where(
          "last_tested_at IS NULL OR last_tested_at < ?", 24.hours.ago
        )
      ).count
    }
  end

  private

  def normalize_connector_type
    self.connector_type = connector_type&.downcase&.strip
  end

  def validate_connection_config
    return unless connection_config.present?

    case connector_type
    when "postgresql", "mysql"
      validate_database_config
    when "rest_api"
      validate_api_config
    when "csv_file"
      validate_file_config
    end
  end

  def validate_database_config
    required_fields = %w[host port database username]
    missing_fields = required_fields - connection_config.keys

    if missing_fields.any?
      errors.add(:connection_config, "Missing required fields: #{missing_fields.join(', ')}")
    end

    # Validate port is numeric
    if connection_config["port"].present? && !connection_config["port"].to_s.match?(/^\d+$/)
      errors.add(:connection_config, "Port must be a number")
    end
  end

  def validate_api_config
    if connection_config["base_url"].blank?
      errors.add(:connection_config, "Base URL is required for API connectors")
    end
  end

  def validate_file_config
    if connection_config["file_path"].blank?
      errors.add(:connection_config, "File path is required for file connectors")
    end
  end

  def schedule_initial_test
    # Test connection in background after creation
    DataConnectorTestJob.perform_later(id)
  end

  def invalidate_dashboard_cache
    return unless account.present?

    DashboardCacheService.invalidate_connector_metrics(account)
    Rails.logger.debug "Dashboard cache invalidated for account #{account.id} due to connector #{id} changes"
  end
end

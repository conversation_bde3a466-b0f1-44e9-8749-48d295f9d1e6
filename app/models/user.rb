class User < ApplicationRecord
  # Include default devise modules. Others available are:
  # :confirmable, :lockable, :timeoutable, :trackable and :omniauthable
  devise :database_authenticatable, :registerable,
         :recoverable, :rememberable, :validatable,
         :confirmable, :trackable, :lockable,
         :jwt_authenticatable, jwt_revocation_strategy: JwtDenylist

  # Associations
  belongs_to :account
  has_many :api_tokens, foreign_key: :created_by_id, dependent: :destroy
  has_many :account_invitations, foreign_key: :invited_by_id, dependent: :destroy
  has_many :notifications, dependent: :destroy

  # Enums
  enum :role, {
    owner: 0,
    admin: 1,
    member: 2,
    viewer: 3
  }

  # Validations
  validates :email, uniqueness: { scope: :account_id }
  validates :role, presence: true

  # Callbacks
  before_validation :set_default_role, on: :create

  # Instance methods
  def full_name
    "#{first_name} #{last_name}".strip.presence || email
  end

  def can_manage_account?
    owner? || admin?
  end

  def can_manage_team?
    owner? || admin?
  end

  def can_execute_pipelines?
    !viewer?
  end

  private

  def set_default_role
    self.role ||= "member"
  end
end

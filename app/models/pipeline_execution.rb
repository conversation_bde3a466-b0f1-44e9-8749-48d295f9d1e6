class PipelineExecution < ApplicationRecord
  # Associations
  belongs_to :pipeline

  # Validations
  validates :status, presence: true
  validates :started_at, presence: true
  validates :records_processed, :records_success, :records_failed,
            numericality: { greater_than_or_equal_to: 0 }

  # Enums
  enum :status, {
    pending: 0,
    running: 1,
    success: 2,
    failed: 3,
    cancelled: 4
  }

  # Scopes
  scope :recent, -> { order(started_at: :desc) }
  scope :completed, -> { where(status: [ :success, :failed, :cancelled ]) }
  scope :successful, -> { where(status: :success) }
  scope :failed, -> { where(status: :failed) }
  scope :for_period, ->(period) { where(started_at: period) }

  # Callbacks
  before_validation :set_defaults
  after_update :update_pipeline_metrics, if: :saved_change_to_status?
  after_save :invalidate_dashboard_cache
  after_destroy :invalidate_dashboard_cache

  # Instance methods
  def duration
    return nil unless started_at && completed_at
    completed_at - started_at
  end

  def success_rate
    return 0 if records_processed.zero?
    (records_success.to_f / records_processed * 100).round(2)
  end

  def failure_rate
    return 0 if records_processed.zero?
    (records_failed.to_f / records_processed * 100).round(2)
  end

  def completed?
    success? || failed? || cancelled?
  end

  def in_progress?
    running?
  end

  def can_be_cancelled?
    pending? || running?
  end

  def display_status
    case status
    when "pending"
      "Waiting to start"
    when "running"
      "In progress"
    when "success"
      "Completed successfully"
    when "failed"
      "Failed"
    when "cancelled"
      "Cancelled"
    else
      status.humanize
    end
  end

  def execution_summary
    if completed?
      summary = "#{records_processed} records processed"
      summary += " (#{records_success} successful" if records_success > 0
      summary += ", #{records_failed} failed" if records_failed > 0
      summary += ")"
      summary += " in #{formatted_duration}" if duration
      summary
    else
      display_status
    end
  end

  def formatted_duration
    return nil unless duration

    if duration < 1
      "#{(duration * 1000).round}ms"
    elsif duration < 60
      "#{duration.round(2)}s"
    elsif duration < 3600
      minutes = (duration / 60).floor
      seconds = (duration % 60).round
      "#{minutes}m #{seconds}s"
    else
      hours = (duration / 3600).floor
      minutes = ((duration % 3600) / 60).floor
      "#{hours}h #{minutes}m"
    end
  end

  def log_entries
    return [] unless execution_log.present?

    execution_log.split("\n").map do |line|
      # Parse timestamp and message if formatted properly
      match = line.match(/\[(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2})\] (.+)/)
      if match
        {
          timestamp: Time.parse(match[1]),
          message: match[2]
        }
      else
        {
          timestamp: created_at,
          message: line
        }
      end
    rescue
      {
        timestamp: created_at,
        message: line
      }
    end
  end

  def add_log_entry(message, level: "INFO")
    timestamp = Time.current.strftime("%Y-%m-%d %H:%M:%S")
    log_line = "[#{timestamp}] #{level.upcase}: #{message}"

    current_log = execution_log || ""
    self.execution_log = current_log.empty? ? log_line : "#{current_log}\n#{log_line}"

    # Keep log size reasonable (max 10KB)
    if execution_log.length > 10_240
      lines = execution_log.split("\n")
      self.execution_log = lines.last(50).join("\n")
    end
  end

  # Start execution
  def start!
    update!(
      status: :running,
      started_at: Time.current,
      execution_log: "[#{Time.current.strftime('%Y-%m-%d %H:%M:%S')}] INFO: Execution started"
    )
  end

  # Complete with success
  def complete_success!(processed: 0, successful: 0, failed: 0)
    duration_seconds = Time.current - started_at

    update!(
      status: :success,
      completed_at: Time.current,
      execution_time: duration_seconds,
      records_processed: processed,
      records_success: successful,
      records_failed: failed
    )

    add_log_entry("Execution completed successfully - #{processed} records processed")
    save!
  end

  # Complete with failure
  def complete_failure!(error_message = nil)
    duration_seconds = Time.current - started_at

    update!(
      status: :failed,
      completed_at: Time.current,
      execution_time: duration_seconds,
      error_message: error_message
    )

    add_log_entry("Execution failed: #{error_message}", level: "ERROR")
    save!
  end

  # Cancel execution
  def cancel!(reason = nil)
    duration_seconds = started_at ? Time.current - started_at : 0

    update!(
      status: :cancelled,
      completed_at: Time.current,
      execution_time: duration_seconds,
      error_message: reason
    )

    add_log_entry("Execution cancelled: #{reason || 'Manual cancellation'}", level: "WARN")
    save!
  end

  # Class methods
  def self.recent_performance_stats(pipeline, period = 30.days)
    executions = where(pipeline: pipeline, started_at: period.ago..)

    {
      total_executions: executions.count,
      successful_executions: executions.successful.count,
      failed_executions: executions.failed.count,
      success_rate: executions.count > 0 ? (executions.successful.count.to_f / executions.count * 100).round(2) : 0,
      avg_duration: executions.completed.where.not(execution_time: nil).average(:execution_time)&.round(2) || 0,
      total_records_processed: executions.sum(:records_processed)
    }
  end

  private

  def set_defaults
    self.started_at ||= Time.current if status == "running"
    self.records_processed ||= 0
    self.records_success ||= 0
    self.records_failed ||= 0
    self.metadata ||= {}
  end

  def update_pipeline_metrics
    return unless pipeline

    # Update pipeline performance metrics after execution completes
    if completed?
      pipeline.update_performance_metrics!
    end
  end

  def invalidate_dashboard_cache
    return unless pipeline&.account

    DashboardCacheService.invalidate_pipeline_metrics(pipeline.account)
    Rails.logger.debug "Dashboard cache invalidated for account #{pipeline.account.id} due to execution #{id} changes"
  end
end

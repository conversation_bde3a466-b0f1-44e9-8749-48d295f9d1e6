class SavedReport < ApplicationRecord
  # Associations
  belongs_to :account
  belongs_to :user
  belongs_to :dashboard, optional: true
  
  # Validations
  validates :name, presence: true, length: { minimum: 2, maximum: 100 }
  validates :report_type, presence: true
  validates :schedule_type, presence: true
  validates :format, presence: true
  validates :format, inclusion: { in: %w[pdf excel csv html json] }
  
  # Enums
  enum :report_type, {
    standard: 0,
    executive: 1,
    detailed: 2,
    custom: 3
  }
  
  enum :schedule_type, {
    once: 0,
    daily: 1,
    weekly: 2,
    monthly: 3,
    quarterly: 4,
    custom_schedule: 5
  }
  
  # Scopes
  scope :active, -> { where(is_active: true) }
  scope :scheduled, -> { where.not(schedule_type: :once) }
  scope :due_for_generation, -> { active.where('next_scheduled_at <= ?', Time.current) }
  scope :recent, -> { order(created_at: :desc) }
  
  # Callbacks
  before_create :set_initial_schedule
  after_save :schedule_next_generation, if: :saved_change_to_schedule_type?
  
  # Instance methods
  def generate!
    ReportGenerationService.new(self).generate
    increment!(:generation_count)
    update!(last_generated_at: Time.current)
    schedule_next_generation if scheduled?
  end
  
  def send_to_recipients!
    return unless recipients.present?
    
    recipients.each do |recipient|
      if recipient.is_a?(String) && recipient.include?('@')
        # Email address
        ReportMailer.deliver_report(self, recipient).deliver_later
      elsif recipient.is_a?(Integer)
        # User ID
        user = account.users.find_by(id: recipient)
        ReportMailer.deliver_report(self, user.email).deliver_later if user
      end
    end
  end
  
  def preview_data
    ReportGenerationService.new(self).preview_data
  end
  
  def scheduled?
    !once? && is_active?
  end
  
  def calculate_next_scheduled_at
    return nil unless scheduled?
    
    base_time = last_generated_at || Time.current
    
    case schedule_type
    when 'daily'
      base_time + 1.day
    when 'weekly'
      base_time + 1.week
    when 'monthly'
      base_time + 1.month
    when 'quarterly'
      base_time + 3.months
    when 'custom_schedule'
      calculate_custom_schedule(base_time)
    else
      nil
    end
  end
  
  def duplicate
    new_report = self.dup
    new_report.last_generated_at = nil
    new_report.next_scheduled_at = nil
    new_report.generation_count = 0
    new_report
  end
  
  def export_template
    {
      name: "#{name} Template",
      description: description,
      report_type: report_type,
      report_config: report_config,
      data_sources: sanitized_data_sources,
      sections: sections,
      format: format
    }
  end
  
  # Class methods
  def self.create_from_dashboard(dashboard, options = {})
    report = new(
      account: dashboard.account,
      user: dashboard.user,
      dashboard: dashboard,
      name: options[:name] || "#{dashboard.name} Report",
      description: options[:description] || "Automated report based on #{dashboard.name}",
      report_type: options[:report_type] || :standard,
      format: options[:format] || 'pdf'
    )
    
    # Build report sections from dashboard widgets
    report.sections = dashboard.widgets.visible.map do |widget|
      {
        title: widget.name,
        type: widget.widget_type,
        data_source: widget.data_source,
        visualization: widget.visualization_options
      }
    end
    
    report.save!
    report
  end
  
  def self.standard_templates
    {
      executive_summary: {
        name: 'Executive Summary',
        report_type: :executive,
        sections: [
          { type: 'kpi_summary', title: 'Key Performance Indicators' },
          { type: 'trend_analysis', title: 'Performance Trends' },
          { type: 'highlights', title: 'Notable Events' },
          { type: 'recommendations', title: 'Recommendations' }
        ]
      },
      operational_report: {
        name: 'Operational Report',
        report_type: :detailed,
        sections: [
          { type: 'pipeline_status', title: 'Pipeline Status' },
          { type: 'execution_details', title: 'Execution Details' },
          { type: 'error_analysis', title: 'Error Analysis' },
          { type: 'resource_usage', title: 'Resource Utilization' }
        ]
      },
      compliance_report: {
        name: 'Compliance Report',
        report_type: :standard,
        sections: [
          { type: 'data_quality', title: 'Data Quality Metrics' },
          { type: 'compliance_status', title: 'Compliance Status' },
          { type: 'audit_trail', title: 'Audit Trail' },
          { type: 'violations', title: 'Violations and Issues' }
        ]
      }
    }
  end
  
  private
  
  def set_initial_schedule
    self.next_scheduled_at = calculate_next_scheduled_at if scheduled?
  end
  
  def schedule_next_generation
    update_column(:next_scheduled_at, calculate_next_scheduled_at)
  end
  
  def calculate_custom_schedule(base_time)
    return nil unless schedule_config['cron'].present?
    
    # Parse cron expression and calculate next run time
    # This is a simplified version - you might want to use a gem like 'whenever' or 'rufus-scheduler'
    cron_parser = CronParser.new(schedule_config['cron'])
    cron_parser.next(base_time)
  rescue => e
    Rails.logger.error "Failed to parse cron expression: #{e.message}"
    nil
  end
  
  def sanitized_data_sources
    # Remove sensitive information from data sources
    data_sources.map do |source|
      source = source.dup
      source.delete('api_key')
      source.delete('credentials')
      source.delete('password')
      source
    end
  end
end

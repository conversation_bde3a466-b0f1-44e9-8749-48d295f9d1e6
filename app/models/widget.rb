class Widget < ApplicationRecord
  # Associations
  belongs_to :dashboard
  
  # Validations
  validates :name, presence: true
  validates :widget_type, presence: true
  validates :widget_type, inclusion: { in: %w[
    line_chart bar_chart pie_chart donut_chart area_chart scatter_plot
    metric_card kpi_card gauge_chart funnel_chart heatmap
    data_table pivot_table tree_map radar_chart
    timeline gantt_chart waterfall_chart candlestick_chart
  ] }
  
  # Scopes
  scope :visible, -> { where(is_visible: true) }
  scope :ordered, -> { order(order_position: :asc, id: :asc) }
  scope :charts, -> { where(widget_type: %w[line_chart bar_chart pie_chart area_chart]) }
  scope :metrics, -> { where(widget_type: %w[metric_card kpi_card gauge_chart]) }
  
  # Callbacks
  before_create :set_order_position
  after_save :invalidate_cache, if: :saved_change_to_data_source?
  
  # Constants
  REFRESH_INTERVALS = {
    realtime: 5,
    fast: 30,
    normal: 60,
    slow: 300,
    hourly: 3600,
    daily: 86400
  }.freeze
  
  # Instance methods
  def fetch_data(options = {})
    return cached_data if use_cache? && cached_data_valid?
    
    data = case data_source['type']
    when 'query'
      execute_query(data_source['query'], options)
    when 'metric'
      fetch_metric(data_source['metric'], options)
    when 'pipeline'
      fetch_pipeline_data(data_source['pipeline_id'], options)
    when 'custom'
      fetch_custom_data(data_source['endpoint'], options)
    else
      { error: 'Unknown data source type' }
    end
    
    cache_data!(data) if should_cache?
    data
  end
  
  def chart_config
    base_config = {
      type: chart_type_for_widget,
      data: prepare_chart_data,
      options: merge_visualization_options
    }
    
    # Add widget-specific configuration
    case widget_type
    when 'line_chart'
      base_config[:options][:scales] = { y: { beginAtZero: true } }
    when 'pie_chart', 'donut_chart'
      base_config[:options][:plugins] = { legend: { position: 'right' } }
    when 'gauge_chart'
      base_config[:options][:min] = config['min'] || 0
      base_config[:options][:max] = config['max'] || 100
    end
    
    base_config
  end
  
  def duplicate
    new_widget = self.dup
    new_widget.cached_data = nil
    new_widget.cached_at = nil
    new_widget
  end
  
  def refresh!
    invalidate_cache
    fetch_data(force_refresh: true)
  end
  
  def export_config
    {
      name: name,
      type: widget_type,
      config: config,
      data_source: sanitized_data_source,
      visualization_options: visualization_options,
      filters: filters
    }
  end
  
  def self.create_from_template(template, dashboard)
    create!(
      dashboard: dashboard,
      name: template[:name],
      widget_type: template[:type],
      config: template[:config] || {},
      data_source: template[:data_source] || {},
      visualization_options: template[:visualization_options] || {},
      position: template[:position] || { x: 0, y: 0, w: 4, h: 4 }
    )
  end
  
  # Widget Templates
  def self.templates
    {
      pipeline_performance: {
        name: 'Pipeline Performance',
        type: 'line_chart',
        data_source: { type: 'query', query: 'pipeline_performance_over_time' },
        visualization_options: {
          colors: ['#10B981', '#EF4444'],
          title: 'Pipeline Performance Trends'
        }
      },
      data_quality_score: {
        name: 'Data Quality Score',
        type: 'gauge_chart',
        config: { min: 0, max: 100 },
        data_source: { type: 'metric', metric: 'data_quality_score' },
        visualization_options: {
          colors: ['#EF4444', '#F59E0B', '#10B981'],
          thresholds: [60, 80]
        }
      },
      execution_heatmap: {
        name: 'Execution Heatmap',
        type: 'heatmap',
        data_source: { type: 'query', query: 'hourly_execution_heatmap' },
        visualization_options: {
          colorScale: 'Blues',
          showValues: true
        }
      },
      top_errors: {
        name: 'Top Errors',
        type: 'data_table',
        data_source: { type: 'query', query: 'top_pipeline_errors' },
        config: {
          columns: ['Pipeline', 'Error', 'Count', 'Last Occurrence'],
          sortable: true,
          paginate: true
        }
      }
    }
  end
  
  private
  
  def set_order_position
    self.order_position ||= dashboard.widgets.maximum(:order_position).to_i + 1
  end
  
  def use_cache?
    refresh_interval.present? && refresh_interval > 30
  end
  
  def cached_data_valid?
    cached_at.present? && 
    cached_data.present? && 
    cached_at > (refresh_interval || 60).seconds.ago
  end
  
  def should_cache?
    refresh_interval.present? && refresh_interval >= 60
  end
  
  def cache_data!(data)
    update_columns(
      cached_data: data,
      cached_at: Time.current
    )
  end
  
  def invalidate_cache
    update_columns(cached_data: nil, cached_at: nil) if cached_data.present?
  end
  
  def execute_query(query_name, options = {})
    WidgetDataService.new(dashboard.account).execute_query(query_name, options)
  end
  
  def fetch_metric(metric_name, options = {})
    WidgetDataService.new(dashboard.account).fetch_metric(metric_name, options)
  end
  
  def fetch_pipeline_data(pipeline_id, options = {})
    pipeline = dashboard.account.pipelines.find(pipeline_id)
    WidgetDataService.new(dashboard.account).pipeline_data(pipeline, options)
  end
  
  def fetch_custom_data(endpoint, options = {})
    WidgetDataService.new(dashboard.account).custom_data(endpoint, options)
  end
  
  def chart_type_for_widget
    case widget_type
    when 'line_chart' then 'line'
    when 'bar_chart' then 'bar'
    when 'pie_chart' then 'pie'
    when 'donut_chart' then 'doughnut'
    when 'area_chart' then 'line'
    when 'scatter_plot' then 'scatter'
    when 'radar_chart' then 'radar'
    else 'line'
    end
  end
  
  def prepare_chart_data
    data = fetch_data
    return {} if data[:error]
    
    {
      labels: data[:labels] || [],
      datasets: data[:datasets] || []
    }
  end
  
  def merge_visualization_options
    default_options = {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: { display: true },
        tooltip: { enabled: true }
      }
    }
    
    default_options.deep_merge(visualization_options || {})
  end
  
  def sanitized_data_source
    # Remove sensitive information from data source for export
    source = data_source.dup
    source.delete('api_key')
    source.delete('credentials')
    source
  end
end

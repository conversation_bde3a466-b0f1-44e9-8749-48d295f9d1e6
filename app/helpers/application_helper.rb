module ApplicationHelper
  def nav_link_classes(active = false)
    base_classes = "group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors duration-150"
    if active
      "#{base_classes} bg-indigo-50 border-r-2 border-indigo-500 text-indigo-700"
    else
      "#{base_classes} text-gray-700 hover:bg-gray-50 hover:text-gray-900"
    end
  end

  def nav_link_styles(active = false)
    base_styles = "padding: var(--space-3) var(--space-4); border-radius: var(--radius-md); font-size: var(--text-sm); font-weight: var(--font-weight-medium); text-decoration: none; display: flex; align-items: center; margin-bottom: var(--space-1);"

    if active
      "#{base_styles} background-color: var(--color-primary-50); color: var(--color-primary-700); border-left: 3px solid var(--color-primary-500);"
    else
      "#{base_styles} color: var(--color-gray-700); transition: all 0.2s ease;"
    end
  end

  # Role-based UI helpers
  def can_manage_account?(user = current_user)
    user&.can_manage_account?
  end

  # Simple authorization helper for views
  # This is a basic implementation - can be replaced with CanCan or Pundit later
  def can?(action, resource)
    return false unless current_user
    
    # For now, allow all authenticated users to edit their own resources
    case resource
    when Dashboard
      # Users can edit dashboards belonging to their account
      resource.account_id == current_user.account_id
    else
      # Default to allowing authenticated users
      true
    end
  end

  def can_manage_team?(user = current_user)
    user&.can_manage_team?
  end

  def can_execute_pipelines?(user = current_user)
    user&.can_execute_pipelines?
  end

  def show_for_roles(*roles, user: current_user)
    return false unless user
    roles.map(&:to_s).include?(user.role)
  end

  # Plan-based feature helpers
  def feature_available?(feature, account = current_account)
    return false unless account&.subscription

    case feature.to_sym
    when :advanced_analytics
      %w[professional enterprise].include?(account.subscription.plan)
    when :team_management
      %w[starter professional enterprise].include?(account.subscription.plan)
    when :api_access
      %w[professional enterprise].include?(account.subscription.plan)
    when :custom_integrations
      account.subscription.plan == "enterprise"
    when :priority_support
      %w[professional enterprise].include?(account.subscription.plan)
    else
      true
    end
  end

  # Tenant customization helpers
  def account_branding_color(account = current_account)
    account&.settings&.dig("branding", "primary_color") || "#4F46E5"
  end

  def account_logo_url(account = current_account)
    account&.settings&.dig("branding", "logo_url") || nil
  end

  def account_custom_domain(account = current_account)
    account&.settings&.dig("domain", "custom_domain") || nil
  end

  # Usage limit helpers
  def usage_percentage(current, limit)
    return 0 if limit.zero? || limit == -1
    [ (current.to_f / limit * 100).round(1), 100 ].min
  end

  def usage_status_class(percentage)
    case percentage
    when 0..70
      "text-green-600 bg-green-100"
    when 71..90
      "text-yellow-600 bg-yellow-100"
    else
      "text-red-600 bg-red-100"
    end
  end

  # Business metrics formatting
  def format_metric_value(value, type = :number)
    case type
    when :currency
      number_to_currency(value)
    when :percentage
      number_to_percentage(value, precision: 1)
    when :bytes
      number_to_human_size(value)
    when :duration
      format_duration(value)
    when :compact
      number_to_human(value, precision: 1)
    else
      number_with_delimiter(value)
    end
  end

  def format_duration(seconds)
    return "0s" if seconds.zero?

    if seconds < 60
      "#{seconds.round}s"
    elsif seconds < 3600
      "#{(seconds / 60).round}m"
    else
      hours = seconds / 3600
      minutes = (seconds % 3600) / 60
      "#{hours.round}h #{minutes.round}m"
    end
  end

  # Status indicators
  def status_badge(status, text = nil)
    text ||= status.to_s.humanize

    case status.to_s.downcase
    when "active", "healthy", "success", "completed"
      content_tag :span, text, class: "inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800"
    when "warning", "pending", "in_progress"
      content_tag :span, text, class: "inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800"
    when "error", "failed", "inactive", "suspended"
      content_tag :span, text, class: "inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800"
    else
      content_tag :span, text, class: "inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800"
    end
  end

  # SVG Icon helper for consistent, scalable icons throughout the application
  def svg_icon(name, options = {})
    size = options[:size] || 24
    css_class = options[:class] || "w-6 h-6"
    stroke_width = options[:stroke_width] || 2

    icons = {
      "lightning" => {
        viewBox: "0 0 24 24",
        path: "M13 10V3L4 14h7v7l9-11h-7z"
      },
      "shield-check" => {
        viewBox: "0 0 24 24",
        path: "M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"
      },
      "puzzle" => {
        viewBox: "0 0 24 24",
        path: "M11 4a2 2 0 114 0v1a1 1 0 001 1h3a1 1 0 011 1v3a1 1 0 01-1 1h-1a2 2 0 100 4h1a1 1 0 011 1v3a1 1 0 01-1 1h-3a1 1 0 01-1-1v-1a2 2 0 10-4 0v1a1 1 0 01-1 1H7a1 1 0 01-1-1v-3a1 1 0 00-1-1H4a2 2 0 110-4h1a1 1 0 001-1V7a1 1 0 011-1h3a1 1 0 001-1V4z"
      },
      "chart-bar" => {
        viewBox: "0 0 24 24",
        path: "M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
      },
      "code" => {
        viewBox: "0 0 24 24",
        path: "M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4"
      },
      "clock" => {
        viewBox: "0 0 24 24",
        path: "M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
      },
      "check-circle" => {
        viewBox: "0 0 24 24",
        path: "M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
      },
      "star" => {
        viewBox: "0 0 24 24",
        path: "M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"
      },
      "arrow-right" => {
        viewBox: "0 0 24 24",
        path: "M17 8l4 4m0 0l-4 4m4-4H3"
      },
      "play" => {
        viewBox: "0 0 24 24",
        path: "M14.828 14.828a4 4 0 01-5.656 0M9 10h1.586a1 1 0 01.707.293l2.414 2.414a1 1 0 00.707.293H15M9 10v4a2 2 0 002 2h2a2 2 0 002-2v-4M9 10V9a2 2 0 012-2h2a2 2 0 012 2v1"
      },
      "users" => {
        viewBox: "0 0 24 24",
        path: "M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"
      },
      "globe" => {
        viewBox: "0 0 24 24",
        path: "M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9a9 9 0 01-9-9m9 9c1.657 0 3-4.03 3-9s-1.343-9-3-9m0 18c-1.657 0-3-4.03-3-9s1.343-9 3-9m-9 9a9 9 0 019-9"
      },
      "x-circle" => {
        viewBox: "0 0 24 24",
        path: "M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z"
      },
      "search" => {
        viewBox: "0 0 24 24",
        path: "M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
      },
      "chevron-down" => {
        viewBox: "0 0 24 24",
        path: "M19 9l-7 7-7-7"
      },
      "chevron-up" => {
        viewBox: "0 0 24 24",
        path: "M5 15l7-7 7 7"
      },
      "menu" => {
        viewBox: "0 0 24 24",
        path: "M4 6h16M4 12h16M4 18h16"
      }
    }

    icon_data = icons[name]
    return "" unless icon_data

    content_tag :svg,
      class: css_class,
      fill: "none",
      stroke: "currentColor",
      viewBox: icon_data[:viewBox],
      "stroke-width": stroke_width,
      "stroke-linecap": "round",
      "stroke-linejoin": "round" do
        content_tag :path, "", d: icon_data[:path]
      end
  end

  # Color classes for consistent theming
  def color_classes(color, type = "bg")
    colors = {
      "blue" => {
        "bg" => "bg-blue-100",
        "text" => "text-blue-600",
        "border" => "border-blue-200"
      },
      "green" => {
        "bg" => "bg-green-100",
        "text" => "text-green-600",
        "border" => "border-green-200"
      },
      "purple" => {
        "bg" => "bg-purple-100",
        "text" => "text-purple-600",
        "border" => "border-purple-200"
      },
      "indigo" => {
        "bg" => "bg-indigo-100",
        "text" => "text-indigo-600",
        "border" => "border-indigo-200"
      },
      "orange" => {
        "bg" => "bg-orange-100",
        "text" => "text-orange-600",
        "border" => "border-orange-200"
      },
      "teal" => {
        "bg" => "bg-teal-100",
        "text" => "text-teal-600",
        "border" => "border-teal-200"
      }
    }

    colors.dig(color, type) || "bg-gray-100"
  end
end

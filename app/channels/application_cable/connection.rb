# frozen_string_literal: true

module ApplicationCable
  class Connection < ActionCable::Connection::Base
    identified_by :current_user, :current_account

    def connect
      self.current_user = find_verified_user
      self.current_account = current_user&.account
      reject_unauthorized_connection unless current_user && current_account
    end

    private

    def find_verified_user
      # In a real application, you might verify the user through cookies or a token
      # For development, we'll extract from the connection request
      if request.params[:user_id].present?
        User.find_by(id: request.params[:user_id])
      elsif cookies.signed[:user_id].present?
        User.find_by(id: cookies.signed[:user_id])
      end
    end
  end
end

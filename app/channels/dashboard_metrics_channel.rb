# frozen_string_literal: true

class DashboardMetricsChannel < ApplicationCable::Channel
  def subscribed
    # Only allow authenticated users to subscribe
    reject unless current_user && current_account

    # Stream account-specific dashboard metrics
    stream_from account_stream_name("dashboard_metrics")

    # Send initial metrics data
    transmit(
      type: "metrics_update",
      data: fetch_dashboard_metrics
    )
  end

  def unsubscribed
    # Any cleanup needed when channel is unsubscribed
    stop_all_streams
  end

  def request_metrics(data)
    # Allow client to request specific metrics updates
    metric_type = data["metric_type"]

    case metric_type
    when "pipeline_metrics"
      transmit(
        type: "pipeline_metrics_update",
        data: fetch_pipeline_metrics
      )
    when "connector_metrics"
      transmit(
        type: "connector_metrics_update",
        data: fetch_connector_metrics
      )
    when "usage_metrics"
      transmit(
        type: "usage_metrics_update",
        data: fetch_usage_metrics
      )
    when "system_health"
      transmit(
        type: "system_health_update",
        data: fetch_system_health
      )
    else
      # Send all metrics
      transmit(
        type: "metrics_update",
        data: fetch_dashboard_metrics
      )
    end
  end

  private

  def fetch_dashboard_metrics
    {
      pipeline_metrics: fetch_pipeline_metrics,
      connector_metrics: fetch_connector_metrics,
      usage_metrics: fetch_usage_metrics,
      system_health: fetch_system_health,
      recent_activity: fetch_recent_activity,
      timestamp: Time.current.iso8601
    }
  end

  def fetch_pipeline_metrics
    Rails.cache.fetch("dashboard/pipeline_metrics/#{current_account.id}", expires_in: 5.minutes) do
      begin
        if defined?(Pipeline)
          pipelines = current_account.pipelines
          total_executions = pipelines.joins(:pipeline_executions).count rescue 0
          successful_executions = pipelines.joins(:pipeline_executions)
                                          .where(pipeline_executions: { status: "completed" }).count rescue 0
          success_rate = total_executions > 0 ? (successful_executions.to_f / total_executions * 100).round(2) : 0

          {
            total_pipelines: pipelines.count,
            active_pipelines: pipelines.where(active: true).count,
            avg_success_rate: success_rate / 100.0,
            total_executions: total_executions,
            successful_executions: successful_executions,
            failed_executions: total_executions - successful_executions
          }
        else
          default_pipeline_metrics
        end
      rescue => e
        Rails.logger.error "Error fetching pipeline metrics: #{e.message}"
        default_pipeline_metrics
      end
    end
  end

  def fetch_connector_metrics
    Rails.cache.fetch("dashboard/connector_metrics/#{current_account.id}", expires_in: 10.minutes) do
      begin
        if defined?(DataConnector)
          connectors = current_account.data_connectors
          {
            total_connectors: connectors.count,
            active_connectors: connectors.where(active: true).count,
            healthy_connectors: connectors.where(health_status: "healthy").count,
            connection_errors: connectors.where(health_status: [ "error", "warning" ]).count
          }
        else
          default_connector_metrics
        end
      rescue => e
        Rails.logger.error "Error fetching connector metrics: #{e.message}"
        default_connector_metrics
      end
    end
  end

  def fetch_usage_metrics
    Rails.cache.fetch("dashboard/usage_metrics/#{current_account.id}", expires_in: 15.minutes) do
      begin
        if defined?(Pipeline) && defined?(DataConnector)
          # Calculate usage metrics
          thirty_days_ago = 30.days.ago

          {
            data_processed_mb: rand(100..5000), # Placeholder - replace with actual calculation
            api_requests: rand(50..1000),
            storage_used_mb: rand(100..8000),
            monthly_executions: (current_account.pipelines.joins(:pipeline_executions)
                                             .where("pipeline_executions.created_at > ?", thirty_days_ago).count rescue 0)
          }
        else
          default_usage_metrics
        end
      rescue => e
        Rails.logger.error "Error fetching usage metrics: #{e.message}"
        default_usage_metrics
      end
    end
  end

  def fetch_system_health
    Rails.cache.fetch("dashboard/system_health/#{current_account.id}", expires_in: 2.minutes) do
      begin
        # System health checks
        database_healthy = ActiveRecord::Base.connection.execute("SELECT 1").present? rescue false
        cache_healthy = Rails.cache.write("health_check", "ok") && Rails.cache.read("health_check") == "ok" rescue false

        overall_status = database_healthy && cache_healthy ? "healthy" : "warning"

        {
          overall_status: overall_status,
          database_status: database_healthy ? "healthy" : "error",
          cache_status: cache_healthy ? "healthy" : "error",
          last_check: Time.current.iso8601
        }
      rescue => e
        Rails.logger.error "Error fetching system health: #{e.message}"
        {
          overall_status: "error",
          database_status: "error",
          cache_status: "error",
          last_check: Time.current.iso8601
        }
      end
    end
  end

  def fetch_recent_activity
    begin
      if defined?(Pipeline) && defined?(DataConnector)
        activities = []

        # Recent pipeline activities
        recent_executions = current_account.pipelines
                                         .joins(:pipeline_executions)
                                         .includes(:pipeline_executions)
                                         .where("pipeline_executions.created_at > ?", 24.hours.ago)
                                         .order("pipeline_executions.created_at DESC")
                                         .limit(5) rescue []

        recent_executions.each do |pipeline|
          execution = pipeline.pipeline_executions.first
          activities << {
            type: "pipeline_execution",
            title: "Pipeline '#{pipeline.name}' executed",
            status: execution&.status || "unknown",
            timestamp: execution&.created_at&.iso8601 || Time.current.iso8601
          }
        end

        # Recent connector activities
        recent_connectors = current_account.data_connectors
                                         .where("updated_at > ?", 24.hours.ago)
                                         .order(updated_at: :desc)
                                         .limit(3) rescue []

        recent_connectors.each do |connector|
          activities << {
            type: "connector_update",
            title: "Data connector '#{connector.name}' updated",
            status: connector.health_status || "unknown",
            timestamp: connector.updated_at.iso8601
          }
        end

        activities.sort_by { |a| a[:timestamp] }.reverse.first(10)
      else
        default_recent_activity
      end
    rescue => e
      Rails.logger.error "Error fetching recent activity: #{e.message}"
      default_recent_activity
    end
  end

  def default_pipeline_metrics
    {
      total_pipelines: 0,
      active_pipelines: 0,
      avg_success_rate: 0,
      total_executions: 0,
      successful_executions: 0,
      failed_executions: 0
    }
  end

  def default_connector_metrics
    {
      total_connectors: 0,
      active_connectors: 0,
      healthy_connectors: 0,
      connection_errors: 0
    }
  end

  def default_usage_metrics
    {
      data_processed_mb: 0,
      api_requests: 0,
      storage_used_mb: 0,
      monthly_executions: 0
    }
  end

  def default_recent_activity
    []
  end
end

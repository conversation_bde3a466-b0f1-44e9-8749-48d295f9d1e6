class AnalyticsChannel < ApplicationCable::Channel
  def subscribed
    # Subscribe to account-specific analytics updates
    stream_from "analytics_#{current_account.id}" if current_account
    
    Rails.logger.info "Analytics channel subscribed for account: #{current_account&.id}"
  end

  def unsubscribed
    Rails.logger.info "Analytics channel unsubscribed for account: #{current_account&.id}"
  end

  # Handle chart refresh requests from client
  def refresh_chart(data)
    chart_type = data['chart_type']
    date_range = data['date_range'] || '30_days'
    
    Rails.logger.info "Chart refresh requested: #{chart_type}, #{date_range}"
    
    # Broadcast updated chart data
    broadcast_chart_update(chart_type, date_range)
  end

  # Handle auto-refresh toggle
  def toggle_auto_refresh(data)
    chart_type = data['chart_type']
    enabled = data['enabled']
    
    Rails.logger.info "Auto-refresh toggled: #{chart_type}, enabled: #{enabled}"
    
    # Could store user preferences here
    # current_user.update_analytics_preferences(chart_type, auto_refresh: enabled)
  end

  private

  def current_account
    @current_account ||= current_user&.accounts&.first
  end

  def broadcast_chart_update(chart_type, date_range)
    return unless current_account

    # Generate fresh chart data
    chart_data = case chart_type
    when 'execution_trends'
      build_execution_trends_data(date_range)
    when 'data_volume'
      build_data_volume_data(date_range)
    else
      return
    end

    # Broadcast the update via Turbo Stream
    ActionCable.server.broadcast(
      "analytics_#{current_account.id}",
      {
        type: 'chart_update',
        chart_type: chart_type,
        html: ApplicationController.render(
          partial: 'analytics/chart',
          locals: { 
            chart_type: chart_type, 
            chart_data: chart_data, 
            date_range: date_range 
          }
        )
      }
    )
  end

  # Helper methods for chart data generation (duplicated from controller for now)
  def build_execution_trends_data(date_range)
    start_date, end_date = parse_date_range(date_range)
    dates = generate_date_labels(start_date, end_date)

    successful_data = dates.map { rand(5..50) }
    failed_data = dates.map { rand(0..5) }

    {
      labels: dates.map { |date| date.strftime("%m/%d") },
      datasets: [
        {
          label: "Successful Executions",
          data: successful_data,
          borderColor: "#10B981",
          backgroundColor: "rgba(16, 185, 129, 0.1)"
        },
        {
          label: "Failed Executions",
          data: failed_data,
          borderColor: "#EF4444",
          backgroundColor: "rgba(239, 68, 68, 0.1)"
        }
      ]
    }
  end

  def build_data_volume_data(date_range)
    start_date, end_date = parse_date_range(date_range)
    dates = generate_date_labels(start_date, end_date)
    volume_data = dates.map { rand(500..5000) }

    {
      labels: dates.map { |date| date.strftime("%m/%d") },
      datasets: [
        {
          label: "Records Processed",
          data: volume_data,
          borderColor: "#3B82F6",
          backgroundColor: "rgba(59, 130, 246, 0.1)"
        }
      ]
    }
  end

  def parse_date_range(date_range)
    case date_range
    when "7_days"
      [7.days.ago.to_date, Date.current]
    when "30_days"
      [30.days.ago.to_date, Date.current]
    when "90_days"
      [90.days.ago.to_date, Date.current]
    when "1_year"
      [1.year.ago.to_date, Date.current]
    else
      [30.days.ago.to_date, Date.current]
    end
  end

  def generate_date_labels(start_date, end_date)
    (start_date..end_date).to_a
  end
end
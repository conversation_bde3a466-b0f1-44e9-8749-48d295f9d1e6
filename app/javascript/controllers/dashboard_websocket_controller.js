import { Controller } from "@hotwired/stimulus"
import consumer from "../channels/consumer"

// Connects to data-controller="dashboard-websocket"
export default class extends Controller {
  static targets = [
    "pipelineCount", "activeCount", "successRate", "totalExecutions",
    "connectorCount", "healthyConnectors", "connectionErrors",
    "dataProcessed", "apiRequests", "storageUsed", "monthlyExecutions",
    "systemHealth", "databaseStatus", "cacheStatus",
    "recentActivity", "lastUpdated"
  ]

  static values = {
    userId: Number,
    accountId: Number
  }

  connect() {
    console.log("Dashboard WebSocket controller connected")
    this.connectToChannel()
    this.startHeartbeat()
  }

  disconnect() {
    console.log("Dashboard WebSocket controller disconnected")
    if (this.channel) {
      this.channel.unsubscribe()
    }
    this.stopHeartbeat()
  }

  connectToChannel() {
    this.channel = consumer.subscriptions.create(
      { 
        channel: "DashboardMetricsChannel",
        user_id: this.userIdValue,
        account_id: this.accountIdValue
      },
      {
        connected: () => {
          console.log("Connected to DashboardMetricsChannel")
          this.showConnectionStatus("connected")
        },

        disconnected: () => {
          console.log("Disconnected from DashboardMetricsChannel")
          this.showConnectionStatus("disconnected")
        },

        received: (data) => {
          console.log("Received dashboard metrics:", data)
          this.handleMetricsUpdate(data)
        },

        rejected: () => {
          console.log("Rejected from DashboardMetricsChannel")
          this.showConnectionStatus("rejected")
        }
      }
    )
  }

  startHeartbeat() {
    // Request metrics updates every 30 seconds
    this.heartbeatInterval = setInterval(() => {
      if (this.channel) {
        this.channel.perform("request_metrics", {})
      }
    }, 30000)
  }

  stopHeartbeat() {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval)
    }
  }

  handleMetricsUpdate(data) {
    switch (data.type) {
      case 'metrics_update':
        this.updateAllMetrics(data.data)
        break
      case 'pipeline_metrics_update':
        this.updatePipelineMetrics(data.data)
        break
      case 'connector_metrics_update':
        this.updateConnectorMetrics(data.data)
        break
      case 'usage_metrics_update':
        this.updateUsageMetrics(data.data)
        break
      case 'system_health_update':
        this.updateSystemHealth(data.data)
        break
      case 'recent_activity_update':
        this.updateRecentActivity(data.data)
        break
      default:
        console.log("Unknown metrics update type:", data.type)
    }

    // Update last updated timestamp
    this.updateLastUpdated(data.timestamp)
  }

  updateAllMetrics(metrics) {
    if (metrics.pipeline_metrics) {
      this.updatePipelineMetrics(metrics.pipeline_metrics)
    }
    if (metrics.connector_metrics) {
      this.updateConnectorMetrics(metrics.connector_metrics)
    }
    if (metrics.usage_metrics) {
      this.updateUsageMetrics(metrics.usage_metrics)
    }
    if (metrics.system_health) {
      this.updateSystemHealth(metrics.system_health)
    }
    if (metrics.recent_activity) {
      this.updateRecentActivity(metrics.recent_activity)
    }
  }

  updatePipelineMetrics(metrics) {
    this.updateElementText("pipelineCountTarget", metrics.total_pipelines)
    this.updateElementText("activeCountTarget", metrics.active_pipelines)
    this.updateElementText("successRateTarget", `${(metrics.avg_success_rate * 100).toFixed(1)}%`)
    this.updateElementText("totalExecutionsTarget", metrics.total_executions)

    // Add visual feedback for success rate
    const successRateElement = this.getTargetElement("successRateTarget")
    if (successRateElement) {
      const rate = metrics.avg_success_rate * 100
      successRateElement.className = successRateElement.className.replace(/text-(green|yellow|red)-\d+/g, '')
      if (rate >= 95) {
        successRateElement.classList.add('text-green-600')
      } else if (rate >= 80) {
        successRateElement.classList.add('text-yellow-600')
      } else {
        successRateElement.classList.add('text-red-600')
      }
    }
  }

  updateConnectorMetrics(metrics) {
    this.updateElementText("connectorCountTarget", metrics.total_connectors)
    this.updateElementText("healthyConnectorsTarget", metrics.healthy_connectors)
    this.updateElementText("connectionErrorsTarget", metrics.connection_errors)

    // Add visual feedback for connection errors
    const errorsElement = this.getTargetElement("connectionErrorsTarget")
    if (errorsElement) {
      errorsElement.className = errorsElement.className.replace(/text-(green|red)-\d+/g, '')
      if (metrics.connection_errors === 0) {
        errorsElement.classList.add('text-green-600')
      } else {
        errorsElement.classList.add('text-red-600')
      }
    }
  }

  updateUsageMetrics(metrics) {
    this.updateElementText("dataProcessedTarget", `${metrics.data_processed_mb} MB`)
    this.updateElementText("apiRequestsTarget", metrics.api_requests.toLocaleString())
    this.updateElementText("storageUsedTarget", `${(metrics.storage_used_mb / 1024).toFixed(2)} GB`)
    this.updateElementText("monthlyExecutionsTarget", metrics.monthly_executions)

    // Add visual feedback for storage usage (assuming 10GB limit)
    const storageElement = this.getTargetElement("storageUsedTarget")
    if (storageElement) {
      const usagePercentage = (metrics.storage_used_mb / 10240) * 100 // 10GB = 10240MB
      storageElement.className = storageElement.className.replace(/text-(green|yellow|red)-\d+/g, '')
      if (usagePercentage < 70) {
        storageElement.classList.add('text-green-600')
      } else if (usagePercentage < 90) {
        storageElement.classList.add('text-yellow-600')
      } else {
        storageElement.classList.add('text-red-600')
      }
    }
  }

  updateSystemHealth(health) {
    this.updateElementText("systemHealthTarget", health.overall_status)
    this.updateElementText("databaseStatusTarget", health.database_status)
    this.updateElementText("cacheStatusTarget", health.cache_status)

    // Add visual feedback for system health
    const systemHealthElement = this.getTargetElement("systemHealthTarget")
    if (systemHealthElement) {
      systemHealthElement.className = systemHealthElement.className.replace(/text-(green|yellow|red)-\d+/g, '')
      switch (health.overall_status) {
        case 'healthy':
          systemHealthElement.classList.add('text-green-600')
          break
        case 'warning':
          systemHealthElement.classList.add('text-yellow-600')
          break
        case 'error':
          systemHealthElement.classList.add('text-red-600')
          break
      }
    }
  }

  updateRecentActivity(activities) {
    const activityElement = this.getTargetElement("recentActivityTarget")
    if (activityElement && Array.isArray(activities)) {
      const activityHTML = activities.map(activity => {
        const statusClass = this.getStatusClass(activity.status)
        const timeAgo = this.timeAgo(activity.timestamp)
        
        return `
          <div class="flex items-center justify-between py-2 border-b border-gray-100 last:border-b-0">
            <div class="flex-1">
              <p class="text-sm text-gray-900">${activity.title}</p>
              <p class="text-xs text-gray-500">${timeAgo}</p>
            </div>
            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${statusClass}">
              ${activity.status}
            </span>
          </div>
        `
      }).join('')

      activityElement.innerHTML = activityHTML || '<p class="text-sm text-gray-500">No recent activity</p>'
    }
  }

  updateLastUpdated(timestamp) {
    if (timestamp) {
      const lastUpdatedElement = this.getTargetElement("lastUpdatedTarget")
      if (lastUpdatedElement) {
        const date = new Date(timestamp)
        lastUpdatedElement.textContent = `Last updated: ${date.toLocaleTimeString()}`
      }
    }
  }

  updateElementText(targetName, value) {
    const element = this.getTargetElement(targetName)
    if (element) {
      // Add animation class
      element.classList.add('transition-all', 'duration-300')
      
      // Update the text content
      element.textContent = value
      
      // Add flash effect
      element.classList.add('bg-blue-50')
      setTimeout(() => {
        element.classList.remove('bg-blue-50')
      }, 1000)
    }
  }

  getTargetElement(targetName) {
    try {
      return this[targetName]
    } catch (error) {
      // Target doesn't exist, which is fine
      return null
    }
  }

  showConnectionStatus(status) {
    // You can add a connection status indicator to the dashboard
    const statusElement = document.querySelector('[data-websocket-status]')
    if (statusElement) {
      statusElement.className = statusElement.className.replace(/text-(green|yellow|red)-\d+/g, '')
      switch (status) {
        case 'connected':
          statusElement.classList.add('text-green-600')
          statusElement.textContent = '● Connected'
          break
        case 'disconnected':
          statusElement.classList.add('text-yellow-600')
          statusElement.textContent = '● Reconnecting...'
          break
        case 'rejected':
          statusElement.classList.add('text-red-600')
          statusElement.textContent = '● Connection Error'
          break
      }
    }
  }

  getStatusClass(status) {
    switch (status) {
      case 'completed':
      case 'healthy':
      case 'success':
        return 'bg-green-100 text-green-800'
      case 'running':
      case 'warning':
        return 'bg-yellow-100 text-yellow-800'
      case 'failed':
      case 'error':
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  timeAgo(timestamp) {
    const now = new Date()
    const time = new Date(timestamp)
    const diffInMinutes = Math.floor((now - time) / (1000 * 60))

    if (diffInMinutes < 1) return 'Just now'
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`
    
    const diffInHours = Math.floor(diffInMinutes / 60)
    if (diffInHours < 24) return `${diffInHours}h ago`
    
    const diffInDays = Math.floor(diffInHours / 24)
    return `${diffInDays}d ago`
  }

  // Public methods that can be called from other controllers or views
  requestMetricsUpdate(metricType = null) {
    if (this.channel) {
      this.channel.perform("request_metrics", { metric_type: metricType })
    }
  }

  refreshPipelineMetrics() {
    this.requestMetricsUpdate('pipeline_metrics')
  }

  refreshConnectorMetrics() {
    this.requestMetricsUpdate('connector_metrics')
  }

  refreshUsageMetrics() {
    this.requestMetricsUpdate('usage_metrics')
  }

  refreshSystemHealth() {
    this.requestMetricsUpdate('system_health')
  }
}
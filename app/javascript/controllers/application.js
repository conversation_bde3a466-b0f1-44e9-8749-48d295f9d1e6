import { Application } from "@hotwired/stimulus"

const application = Application.start()

// Configure Stimulus development experience
application.debug = true
window.Stimulus = application

// Enterprise-grade initialization logging
console.log("🚀 Stimulus application started")
console.log("📊 Stimulus debug mode:", application.debug)
console.log("🌐 Stimulus globally available:", typeof window.Stimulus !== 'undefined')

// Ensure proper initialization
document.addEventListener('DOMContentLoaded', () => {
  setTimeout(() => {
    console.log("✅ DOM loaded, Stimulus controllers:", Object.keys(application.router.modulesByIdentifier))
    console.log("🔧 Stimulus application ready:", application.router.modulesByIdentifier)
  }, 100) // Small delay to ensure controllers are registered
})

export { application }

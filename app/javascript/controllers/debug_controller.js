import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  connect() {
    console.log("🎯 DEBUG CONTROLLER CONNECTED! Stimulus is working!")
    this.element.style.border = "2px solid green"
    this.element.innerHTML += "<p style='color: green; font-weight: bold;'>✅ Stimulus Debug Controller Connected!</p>"
  }

  test(event) {
    console.log("🔥 TEST METHOD CALLED!")
    alert("Stimulus is working! Button click detected.")
    event.target.style.backgroundColor = "lightgreen"
  }
}

import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  static targets = ["password", "passwordConfirmation", "strengthIndicator", "strengthText", "toggleIcon", "form", "submitButton", "subdomainInput", "subdomainPreview", "subdomainError", "subdomainSuccess"]
  static values = {
    showPassword: <PERSON><PERSON>an,
    loading: <PERSON><PERSON><PERSON>
  }

  connect() {
    this.showPasswordValue = false
    this.loadingValue = false
    this.setupPasswordStrengthChecker()
    this.setupSubdomainValidation()
  }

  setupPasswordStrengthChecker() {
    if (this.hasPasswordTarget) {
      this.passwordTarget.addEventListener('input', this.checkPasswordStrength.bind(this))
    }
  }

  togglePasswordVisibility() {
    this.showPasswordValue = !this.showPasswordValue
    
    if (this.hasPasswordTarget) {
      this.passwordTarget.type = this.showPasswordValue ? 'text' : 'password'
    }
    
    if (this.hasPasswordConfirmationTarget) {
      this.passwordConfirmationTarget.type = this.showPasswordValue ? 'text' : 'password'
    }
    
    this.updateToggleIcon()
  }

  updateToggleIcon() {
    if (this.hasToggleIconTarget) {
      const eyeIcon = `
        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
        </svg>
      `
      
      const eyeOffIcon = `
        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21"></path>
        </svg>
      `
      
      this.toggleIconTarget.innerHTML = this.showPasswordValue ? eyeOffIcon : eyeIcon
    }
  }

  checkPasswordStrength() {
    const password = this.passwordTarget.value
    const strength = this.calculatePasswordStrength(password)
    this.updateStrengthIndicator(strength)
  }

  calculatePasswordStrength(password) {
    let score = 0
    
    if (password.length >= 8) score += 1
    if (password.length >= 12) score += 1
    if (/[a-z]/.test(password)) score += 1
    if (/[A-Z]/.test(password)) score += 1
    if (/[0-9]/.test(password)) score += 1
    if (/[^A-Za-z0-9]/.test(password)) score += 1
    
    if (score < 3) return 'weak'
    if (score < 5) return 'medium'
    return 'strong'
  }

  updateStrengthIndicator(strength) {
    if (!this.hasStrengthIndicatorTarget || !this.hasStrengthTextTarget) return
    
    const indicators = {
      weak: {
        width: '33%',
        color: 'bg-red-500',
        text: 'Weak password',
        textColor: 'password-strength-weak'
      },
      medium: {
        width: '66%',
        color: 'bg-yellow-500',
        text: 'Medium strength',
        textColor: 'password-strength-medium'
      },
      strong: {
        width: '100%',
        color: 'bg-green-500',
        text: 'Strong password',
        textColor: 'password-strength-strong'
      }
    }
    
    const indicator = indicators[strength]
    
    // Update progress bar
    this.strengthIndicatorTarget.style.width = indicator.width
    this.strengthIndicatorTarget.className = `h-2 rounded-full transition-all duration-300 ${indicator.color}`
    
    // Update text
    this.strengthTextTarget.textContent = indicator.text
    this.strengthTextTarget.className = `text-sm mt-1 ${indicator.textColor}`
  }

  submitForm(event) {
    if (this.loadingValue) {
      event.preventDefault()
      return
    }
    
    this.loadingValue = true
    this.updateSubmitButton()
    
    // Add a small delay to show loading state
    setTimeout(() => {
      if (this.hasFormTarget) {
        this.formTarget.submit()
      }
    }, 100)
  }

  updateSubmitButton() {
    if (this.hasSubmitButtonTarget) {
      if (this.loadingValue) {
        this.submitButtonTarget.classList.add('btn-loading')
        this.submitButtonTarget.disabled = true
        this.submitButtonTarget.textContent = 'Please wait...'
      } else {
        this.submitButtonTarget.classList.remove('btn-loading')
        this.submitButtonTarget.disabled = false
      }
    }
  }

  // Form field focus animations
  focusField(event) {
    const field = event.target
    const container = field.closest('.form-group')
    if (container) {
      container.classList.add('focused')
    }
  }

  blurField(event) {
    const field = event.target
    const container = field.closest('.form-group')
    if (container && !field.value) {
      container.classList.remove('focused')
    }
  }

  // Real-time validation feedback
  validateField(event) {
    const field = event.target
    const isValid = field.checkValidity()
    const container = field.closest('.form-group')

    if (container) {
      if (isValid) {
        container.classList.remove('error')
        container.classList.add('valid')
      } else if (field.value) {
        container.classList.remove('valid')
        container.classList.add('error')
      }
    }

    // Special validation for password confirmation
    if (field.dataset.authTarget === 'passwordConfirmation') {
      this.validatePasswordConfirmation()
    }
  }

  validatePasswordConfirmation() {
    if (!this.hasPasswordTarget || !this.hasPasswordConfirmationTarget) return

    const password = this.passwordTarget.value
    const confirmation = this.passwordConfirmationTarget.value
    const container = this.passwordConfirmationTarget.closest('.form-group')

    if (confirmation && password !== confirmation) {
      container.classList.add('error')
      container.classList.remove('valid')
      this.showPasswordMismatchError()
    } else if (confirmation && password === confirmation) {
      container.classList.remove('error')
      container.classList.add('valid')
      this.hidePasswordMismatchError()
    }
  }

  showPasswordMismatchError() {
    const container = this.passwordConfirmationTarget.closest('.form-group')
    let errorMsg = container.querySelector('.password-mismatch-error')

    if (!errorMsg) {
      errorMsg = document.createElement('p')
      errorMsg.className = 'password-mismatch-error text-sm text-red-600 mt-1'
      errorMsg.textContent = 'Passwords do not match'
      container.appendChild(errorMsg)
    }
  }

  hidePasswordMismatchError() {
    const container = this.passwordConfirmationTarget.closest('.form-group')
    const errorMsg = container.querySelector('.password-mismatch-error')
    if (errorMsg) {
      errorMsg.remove()
    }
  }

  // Form submission with enhanced validation
  validateForm() {
    let isValid = true
    const form = this.formTarget
    const inputs = form.querySelectorAll('input[required]')

    inputs.forEach(input => {
      if (!input.checkValidity()) {
        isValid = false
        const container = input.closest('.form-group')
        if (container) {
          container.classList.add('error')
        }
      }
    })

    // Check password confirmation if present
    if (this.hasPasswordConfirmationTarget) {
      const password = this.passwordTarget.value
      const confirmation = this.passwordConfirmationTarget.value
      if (password !== confirmation) {
        isValid = false
        this.showPasswordMismatchError()
      }
    }

    return isValid
  }

  // Enhanced form submission
  submitForm(event) {
    if (this.loadingValue) {
      event.preventDefault()
      return
    }

    // For login forms, skip complex validation and just check required fields
    if (this.isLoginForm()) {
      const email = this.formTarget.querySelector('input[type="email"]')
      const password = this.formTarget.querySelector('input[type="password"]')

      if (!email?.value || !password?.value) {
        event.preventDefault()
        this.showSimpleError('Please fill in all required fields')
        return
      }
    } else if (!this.validateForm()) {
      event.preventDefault()
      this.showValidationErrors()
      return
    }

    this.loadingValue = true
    this.updateSubmitButton()
  }

  // Handle submit button click
  handleSubmit(event) {
    // Allow form to submit normally, just update UI
    this.loadingValue = true
    this.updateSubmitButton()
  }

  // Check if this is a login form
  isLoginForm() {
    return this.formTarget.action.includes('/sign_in') ||
           this.formTarget.querySelector('input[name="commit"][value*="Sign In"]')
  }

  // Show simple error message
  showSimpleError(message) {
    // Remove existing error
    const existingError = this.formTarget.querySelector('.simple-error')
    if (existingError) {
      existingError.remove()
    }

    // Add new error
    const errorDiv = document.createElement('div')
    errorDiv.className = 'simple-error bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-4'
    errorDiv.textContent = message
    this.formTarget.insertBefore(errorDiv, this.formTarget.firstChild)
  }

  showValidationErrors() {
    const errorContainers = this.formTarget.querySelectorAll('.form-group.error')
    if (errorContainers.length > 0) {
      errorContainers[0].scrollIntoView({ behavior: 'smooth', block: 'center' })
    }
  }

  // Keyboard navigation support
  handleKeyDown(event) {
    if (event.key === 'Enter' && event.target.type !== 'submit') {
      event.preventDefault()
      const form = event.target.form
      const inputs = Array.from(form.querySelectorAll('input, select, textarea'))
      const currentIndex = inputs.indexOf(event.target)
      const nextInput = inputs[currentIndex + 1]

      if (nextInput) {
        nextInput.focus()
      } else {
        const submitButton = form.querySelector('input[type="submit"], button[type="submit"]')
        if (submitButton) {
          submitButton.click()
        }
      }
    }
  }

  // Subdomain validation setup
  setupSubdomainValidation() {
    if (this.hasSubdomainInputTarget) {
      this.subdomainInputTarget.addEventListener('input', this.updateSubdomainPreview.bind(this))
    }
  }

  // Update subdomain preview as user types
  updateSubdomainPreview() {
    if (!this.hasSubdomainPreviewTarget) return

    const subdomain = this.subdomainInputTarget.value.toLowerCase()
    this.subdomainPreviewTarget.textContent = subdomain ? `${subdomain}.datareflow.io` : 'yourcompany.datareflow.io'
  }

  // Validate subdomain availability and format
  async validateSubdomain(event) {
    const subdomain = event.target.value.toLowerCase().trim()

    // Clear previous states
    this.hideSubdomainError()
    this.hideSubdomainSuccess()

    if (!subdomain) return

    // Validate format
    if (!this.isValidSubdomainFormat(subdomain)) {
      this.showSubdomainError('Subdomain must be 3-30 characters, lowercase letters and numbers only')
      return
    }

    // Check availability
    try {
      const isAvailable = await this.checkSubdomainAvailability(subdomain)
      if (isAvailable) {
        this.showSubdomainSuccess()
      } else {
        this.showSubdomainError('This subdomain is already taken')
      }
    } catch (error) {
      console.error('Error checking subdomain availability:', error)
    }
  }

  // Validate subdomain format
  isValidSubdomainFormat(subdomain) {
    const pattern = /^[a-z0-9]{3,30}$/
    return pattern.test(subdomain)
  }

  // Check subdomain availability via API
  async checkSubdomainAvailability(subdomain) {
    try {
      const response = await fetch(`/api/check_subdomain?subdomain=${encodeURIComponent(subdomain)}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content')
        }
      })

      if (response.ok) {
        const data = await response.json()
        return data.available
      }
      return false
    } catch (error) {
      console.error('Error checking subdomain:', error)
      return false
    }
  }

  // Show subdomain error
  showSubdomainError(message) {
    if (this.hasSubdomainErrorTarget) {
      this.subdomainErrorTarget.querySelector('p').textContent = message
      this.subdomainErrorTarget.classList.remove('hidden')
    }
  }

  // Hide subdomain error
  hideSubdomainError() {
    if (this.hasSubdomainErrorTarget) {
      this.subdomainErrorTarget.classList.add('hidden')
    }
  }

  // Show subdomain success
  showSubdomainSuccess() {
    if (this.hasSubdomainSuccessTarget) {
      this.subdomainSuccessTarget.classList.remove('hidden')
    }
  }

  // Hide subdomain success
  hideSubdomainSuccess() {
    if (this.hasSubdomainSuccessTarget) {
      this.subdomainSuccessTarget.classList.add('hidden')
    }
  }
}

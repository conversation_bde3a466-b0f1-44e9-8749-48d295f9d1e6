import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  static targets = ["output"]

  connect() {
    console.log("🎉 Hello controller connected!")
    this.element.textContent = "Hello World!"
  }

  greet() {
    console.log("🎉 Hello controller greet method called!")
    if (this.hasOutputTarget) {
      this.outputTarget.textContent = "✅ Stimulus is working!"
    }
  }
}

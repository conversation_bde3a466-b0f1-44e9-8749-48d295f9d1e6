// Hotwire-optimized Chart Controller
// Replaces legacy chart controller with modern Hotwire patterns
import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  static targets = ["container", "loading", "error", "metrics", "loadTime", "renderTime"]
  static values = { 
    type: String,
    data: Object, 
    height: Number,
    frameId: String,
    refreshInterval: Number
  }

  // Performance tracking
  loadStartTime = null
  renderStartTime = null
  chart = null
  refreshTimer = null

  connect() {
    this.loadStartTime = performance.now()
    this.logHotwireConnection()
    console.log('🎯 Chart data received:', this.dataValue)
    this.initializeChart()
    this.setupAutoRefresh()
  }

  disconnect() {
    this.cleanup()
    this.logHotwireDisconnection()
  }

  // Hotwire-specific lifecycle methods
  dataValueChanged() {
    if (this.chart) {
      this.updateChartData()
    }
  }

  typeValueChanged() {
    if (this.chart) {
      this.reinitializeChart()
    }
  }

  // Chart initialization with Hotwire optimization
  async initializeChart() {
    try {
      console.log('🚀 Starting chart initialization...')
      console.log('📊 Controller state:', {
        hasContainerTarget: this.hasContainerTarget,
        containerElement: this.hasContainerTarget ? this.containerTarget : null,
        typeValue: this.typeValue,
        dataValue: this.dataValue,
        heightValue: this.heightValue
      })
      
      this.showLoading()
      
      // Validate container target exists
      if (!this.hasContainerTarget) {
        throw new Error("Chart container target not found")
      }
      
      // Check ApexCharts availability with multiple sources
      console.log('🔍 Checking ApexCharts availability...')
      const ApexChartsInstance = await this.getApexCharts()
      if (!ApexChartsInstance) {
        throw new Error("ApexCharts library not available")
      }

      console.log('✅ ApexCharts instance obtained, creating chart...')
      await this.createChart(ApexChartsInstance)
      this.trackLoadTime()
      this.hideLoading()
      console.log('✅ Chart initialization completed successfully')
      
    } catch (error) {
      console.error('❌ Chart initialization failed:', error)
      console.error('💥 Error stack:', error.stack)
      this.handleChartError(error)
    }
  }

  // Modern ApexCharts detection with fallbacks and retry mechanism
  async getApexCharts() {
    const maxRetries = 10
    const retryDelay = 100 // ms
    
    for (let i = 0; i < maxRetries; i++) {
      console.log(`🔍 Checking ApexCharts availability (attempt ${i + 1}/${maxRetries})...`)
      
      // Try global window object first (CDN)
      if (typeof window.ApexCharts !== 'undefined') {
        console.log('✅ ApexCharts found on window object')
        return window.ApexCharts
      }

      // Check if it's available as ApexCharts (without window)
      if (typeof ApexCharts !== 'undefined') {
        console.log('✅ ApexCharts found as global')
        return ApexCharts
      }

      // If not found and we have retries left, wait and try again
      if (i < maxRetries - 1) {
        console.log(`⏳ ApexCharts not ready yet, waiting ${retryDelay}ms...`)
        await new Promise(resolve => setTimeout(resolve, retryDelay))
      }
    }

    // Try module import as last resort (if using importmaps)
    try {
      console.log('🔄 Attempting module import as fallback...')
      const ApexCharts = await import('apexcharts')
      console.log('✅ ApexCharts loaded via module import')
      return ApexCharts.default || ApexCharts
    } catch (e) {
      console.warn('❌ ApexCharts module import failed:', e)
    }

    console.error('❌ ApexCharts not found anywhere after all attempts')
    return null
  }

  // Create chart with Hotwire-optimized configuration
  async createChart(ApexChartsInstance) {
    console.log('🏗️ Creating chart with ApexCharts instance:', ApexChartsInstance)
    this.renderStartTime = performance.now()

    console.log('⚙️ Building chart options...')
    const options = this.buildChartOptions()
    console.log('📋 Chart options built:', options)
    
    console.log('🎨 Creating ApexCharts instance...')
    console.log('📍 Container target:', this.containerTarget)
    this.chart = new ApexChartsInstance(this.containerTarget, options)
    console.log('✅ ApexCharts instance created:', this.chart)
    
    console.log('🖼️ Rendering chart...')
    await this.chart.render()
    console.log('✅ Chart rendered successfully')
    
    this.trackRenderTime()
  }

  // Build chart configuration based on type and data
  buildChartOptions() {
    const baseOptions = {
      chart: {
        type: this.getChartType(),
        height: this.heightValue || 320,
        toolbar: { show: false },
        zoom: { enabled: false },
        animations: {
          enabled: true,
          easing: 'easeinout',
          speed: 400
        }
      },
      responsive: [{
        breakpoint: 768,
        options: {
          chart: { height: 280 },
          legend: { position: 'bottom' }
        }
      }],
      theme: {
        mode: 'light'
      }
    }

    // Apply type-specific configurations
    return this.applyTypeSpecificOptions(baseOptions)
  }

  // Get chart type with intelligent defaults
  getChartType() {
    switch (this.typeValue) {
      case 'line': return 'line'
      case 'area': return 'area'
      case 'bar': return 'bar'
      case 'pie': return 'pie'
      case 'donut': return 'donut'
      default: return 'line'
    }
  }

  // Apply configuration based on chart type
  applyTypeSpecificOptions(baseOptions) {
    const data = this.dataValue || {}
    
    console.log('🔧 Building chart options with data:', data)
    console.log('🔍 Data type:', typeof data)
    console.log('🔍 Data keys:', Object.keys(data))
    
    // Ensure we have valid datasets
    if (!data.datasets || !Array.isArray(data.datasets)) {
      console.warn('⚠️ Invalid or missing datasets in chart data')
      return { ...baseOptions, series: [], xaxis: { categories: [] } }
    }
    
    // Convert datasets to ApexCharts series format
    const series = data.datasets.map(dataset => {
      if (!dataset) {
        console.warn('⚠️ Null dataset found')
        return { name: 'Unknown', data: [] }
      }
      
      return {
        name: dataset.label || 'Unnamed Series',
        data: Array.isArray(dataset.data) ? dataset.data : []
      }
    })
    
    console.log('📊 Series data for ApexCharts:', series)
    console.log('🏷️ Labels available:', data.labels)
    
    // Ensure we have valid labels
    const labels = Array.isArray(data.labels) ? data.labels : []
    console.log('🏷️ Processing labels:', labels)
    
    const typeConfigs = {
      line: {
        stroke: { curve: 'smooth', width: 3 },
        markers: { size: 4 },
        xaxis: { categories: labels },
        series: series
      },
      area: {
        fill: { type: 'gradient' },
        stroke: { curve: 'smooth', width: 2 },
        xaxis: { categories: labels },
        series: series
      },
      bar: {
        plotOptions: {
          bar: { horizontal: false, columnWidth: '60%' }
        },
        xaxis: { categories: labels },
        series: series
      }
    }

    const finalOptions = { ...baseOptions, ...(typeConfigs[this.typeValue] || typeConfigs.line) }
    console.log('⚙️ Final chart options:', finalOptions)
    
    return finalOptions
  }

  // Update chart data (for real-time updates via Turbo Streams)
  updateChartData() {
    if (!this.chart) return

    try {
      const newData = this.dataValue
      if (newData && newData.datasets) {
        this.chart.updateSeries(newData.datasets)
        
        if (newData.labels) {
          this.chart.updateOptions({
            xaxis: { categories: newData.labels }
          })
        }
      }
    } catch (error) {
      console.error('Error updating chart data:', error)
    }
  }

  // Reinitialize chart (for type changes)
  async reinitializeChart() {
    this.cleanup()
    await this.initializeChart()
  }

  // Retry mechanism for failed chart loads
  async retryLoad() {
    this.hideError()
    await this.initializeChart()
  }

  // Auto-refresh functionality
  setupAutoRefresh() {
    if (this.refreshIntervalValue && this.refreshIntervalValue > 0) {
      this.refreshTimer = setInterval(() => {
        this.refreshChart()
      }, this.refreshIntervalValue * 1000)
    }
  }

  // Refresh chart via Turbo Frame
  refreshChart() {
    const frameId = this.frameIdValue
    if (frameId) {
      const frame = document.getElementById(frameId)
      if (frame && frame.reload) {
        frame.reload()
      }
    }
  }

  // UI state management
  showLoading() {
    if (this.hasLoadingTarget) {
      this.loadingTarget.classList.remove('hidden')
    }
    this.hideError()
  }

  hideLoading() {
    if (this.hasLoadingTarget) {
      this.loadingTarget.classList.add('hidden')
    }
  }

  showError() {
    if (this.hasErrorTarget) {
      this.errorTarget.classList.remove('hidden')
    }
    this.hideLoading()
  }

  hideError() {
    if (this.hasErrorTarget) {
      this.errorTarget.classList.add('hidden')
    }
  }

  // Performance tracking
  trackLoadTime() {
    if (this.loadStartTime && this.hasLoadTimeTarget) {
      const loadTime = performance.now() - this.loadStartTime
      this.loadTimeTarget.textContent = `Load: ${loadTime.toFixed(0)}ms`
    }
  }

  trackRenderTime() {
    if (this.renderStartTime && this.hasRenderTimeTarget) {
      const renderTime = performance.now() - this.renderStartTime
      this.renderTimeTarget.textContent = `Render: ${renderTime.toFixed(0)}ms`
    }
  }

  // Error handling
  handleChartError(error) {
    console.error('Hotwire Chart Error:', error)
    console.error('💥 Full error details:', {
      message: error.message,
      stack: error.stack,
      name: error.name,
      chartType: this.typeValue,
      dataValue: this.dataValue,
      hasContainer: this.hasContainerTarget
    })
    
    this.showError()
    
    // Show fallback chart or message in the container
    if (this.hasContainerTarget) {
      this.containerTarget.innerHTML = `
        <div class="flex items-center justify-center h-full">
          <div class="text-center p-4">
            <div class="text-red-500 mb-2">
              <svg class="w-12 h-12 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
            </div>
            <p class="text-sm text-gray-600">Chart Failed to Load</p>
            <p class="text-xs text-gray-500 mt-1">${error.message}</p>
            <button onclick="this.parentElement.parentElement.parentElement.closest('[data-controller]').querySelector('[data-action*=\"retryLoad\"]')?.click()" 
                    class="mt-2 px-3 py-1 text-xs bg-blue-500 text-white rounded hover:bg-blue-600">
              Retry
            </button>
          </div>
        </div>
      `
    }
    
    // Dispatch custom event for error handling
    this.dispatch('error', { 
      detail: { 
        error: error.message,
        chartType: this.typeValue,
        frameId: this.frameIdValue
      }
    })
  }

  // Cleanup resources
  cleanup() {
    if (this.chart) {
      try {
        this.chart.destroy()
      } catch (e) {
        console.warn('Error destroying chart:', e)
      }
      this.chart = null
    }

    if (this.refreshTimer) {
      clearInterval(this.refreshTimer)
      this.refreshTimer = null
    }
  }

  // Enhanced logging for Hotwire context
  logHotwireConnection() {
    console.log(`📊 Hotwire Chart Controller Connected`, {
      type: this.typeValue,
      frameId: this.frameIdValue,
      hasData: !!this.dataValue,
      autoRefresh: this.refreshIntervalValue,
      element: this.element
    })
  }

  logHotwireDisconnection() {
    console.log(`📊 Hotwire Chart Controller Disconnected`, {
      type: this.typeValue,
      frameId: this.frameIdValue
    })
  }

  // Public API for external control
  updateData(newData) {
    this.dataValue = newData
  }

  changeType(newType) {
    this.typeValue = newType
  }

  // Accessibility support
  announceChartUpdate() {
    const announcement = `Chart updated with new ${this.typeValue} data`
    // Could dispatch to screen reader announcement system
    console.log(`♿ ${announcement}`)
  }
}
// Hotwire-optimized Chart Controller
// Replaces legacy chart controller with modern Hotwire patterns
import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  static targets = ["container", "loading", "error", "metrics", "loadTime", "renderTime"]
  static values = { 
    type: String,
    data: Object, 
    height: Number,
    frameId: String,
    refreshInterval: Number
  }

  // Performance tracking
  loadStartTime = null
  renderStartTime = null
  chart = null
  refreshTimer = null

  connect() {
    this.loadStartTime = performance.now()
    this.logHotwireConnection()
    this.initializeChart()
    this.setupAutoRefresh()
  }

  disconnect() {
    this.cleanup()
    this.logHotwireDisconnection()
  }

  // Hotwire-specific lifecycle methods
  dataValueChanged() {
    if (this.chart) {
      this.updateChartData()
    }
  }

  typeValueChanged() {
    if (this.chart) {
      this.reinitializeChart()
    }
  }

  // Chart initialization with Hotwire optimization
  async initializeChart() {
    try {
      this.showLoading()
      
      // Check ApexCharts availability with multiple sources
      const ApexChartsInstance = await this.getApexCharts()
      if (!ApexChartsInstance) {
        throw new Error("ApexCharts library not available")
      }

      await this.createChart(ApexChartsInstance)
      this.trackLoadTime()
      this.hideLoading()
      
    } catch (error) {
      this.handleChartError(error)
    }
  }

  // Modern ApexCharts detection with fallbacks
  async getApexCharts() {
    // Try global window object first (CDN)
    if (typeof window.ApexCharts !== 'undefined') {
      return window.ApexCharts
    }

    // Try module import (if using importmaps)
    try {
      const ApexCharts = await import('apexcharts')
      return ApexCharts.default || ApexCharts
    } catch (e) {
      console.warn('ApexCharts module import failed:', e)
    }

    return null
  }

  // Create chart with Hotwire-optimized configuration
  async createChart(ApexChartsInstance) {
    this.renderStartTime = performance.now()

    const options = this.buildChartOptions()
    this.chart = new ApexChartsInstance(this.containerTarget, options)
    
    await this.chart.render()
    this.trackRenderTime()
  }

  // Build chart configuration based on type and data
  buildChartOptions() {
    const baseOptions = {
      chart: {
        type: this.getChartType(),
        height: this.heightValue || 320,
        toolbar: { show: false },
        zoom: { enabled: false },
        animations: {
          enabled: true,
          easing: 'easeinout',
          speed: 400
        }
      },
      responsive: [{
        breakpoint: 768,
        options: {
          chart: { height: 280 },
          legend: { position: 'bottom' }
        }
      }],
      theme: {
        mode: 'light'
      }
    }

    // Apply type-specific configurations
    return this.applyTypeSpecificOptions(baseOptions)
  }

  // Get chart type with intelligent defaults
  getChartType() {
    switch (this.typeValue) {
      case 'line': return 'line'
      case 'area': return 'area'
      case 'bar': return 'bar'
      case 'pie': return 'pie'
      case 'donut': return 'donut'
      default: return 'line'
    }
  }

  // Apply configuration based on chart type
  applyTypeSpecificOptions(baseOptions) {
    const data = this.dataValue || {}
    
    const typeConfigs = {
      line: {
        stroke: { curve: 'smooth', width: 3 },
        markers: { size: 6 },
        xaxis: { categories: data.labels || [] },
        series: data.datasets || []
      },
      area: {
        fill: { type: 'gradient' },
        stroke: { curve: 'smooth', width: 2 },
        xaxis: { categories: data.labels || [] },
        series: data.datasets || []
      },
      bar: {
        plotOptions: {
          bar: { horizontal: false, columnWidth: '60%' }
        },
        xaxis: { categories: data.labels || [] },
        series: data.datasets || []
      }
    }

    return { ...baseOptions, ...(typeConfigs[this.typeValue] || typeConfigs.line) }
  }

  // Update chart data (for real-time updates via Turbo Streams)
  updateChartData() {
    if (!this.chart) return

    try {
      const newData = this.dataValue
      if (newData && newData.datasets) {
        this.chart.updateSeries(newData.datasets)
        
        if (newData.labels) {
          this.chart.updateOptions({
            xaxis: { categories: newData.labels }
          })
        }
      }
    } catch (error) {
      console.error('Error updating chart data:', error)
    }
  }

  // Reinitialize chart (for type changes)
  async reinitializeChart() {
    this.cleanup()
    await this.initializeChart()
  }

  // Retry mechanism for failed chart loads
  async retryLoad() {
    this.hideError()
    await this.initializeChart()
  }

  // Auto-refresh functionality
  setupAutoRefresh() {
    if (this.refreshIntervalValue && this.refreshIntervalValue > 0) {
      this.refreshTimer = setInterval(() => {
        this.refreshChart()
      }, this.refreshIntervalValue * 1000)
    }
  }

  // Refresh chart via Turbo Frame
  refreshChart() {
    const frameId = this.frameIdValue
    if (frameId) {
      const frame = document.getElementById(frameId)
      if (frame && frame.reload) {
        frame.reload()
      }
    }
  }

  // UI state management
  showLoading() {
    if (this.hasLoadingTarget) {
      this.loadingTarget.classList.remove('hidden')
    }
    this.hideError()
  }

  hideLoading() {
    if (this.hasLoadingTarget) {
      this.loadingTarget.classList.add('hidden')
    }
  }

  showError() {
    if (this.hasErrorTarget) {
      this.errorTarget.classList.remove('hidden')
    }
    this.hideLoading()
  }

  hideError() {
    if (this.hasErrorTarget) {
      this.errorTarget.classList.add('hidden')
    }
  }

  // Performance tracking
  trackLoadTime() {
    if (this.loadStartTime && this.hasLoadTimeTarget) {
      const loadTime = performance.now() - this.loadStartTime
      this.loadTimeTarget.textContent = `Load: ${loadTime.toFixed(0)}ms`
    }
  }

  trackRenderTime() {
    if (this.renderStartTime && this.hasRenderTimeTarget) {
      const renderTime = performance.now() - this.renderStartTime
      this.renderTimeTarget.textContent = `Render: ${renderTime.toFixed(0)}ms`
    }
  }

  // Error handling
  handleChartError(error) {
    console.error('Hotwire Chart Error:', error)
    this.showError()
    
    // Dispatch custom event for error handling
    this.dispatch('error', { 
      detail: { 
        error: error.message,
        chartType: this.typeValue,
        frameId: this.frameIdValue
      }
    })
  }

  // Cleanup resources
  cleanup() {
    if (this.chart) {
      try {
        this.chart.destroy()
      } catch (e) {
        console.warn('Error destroying chart:', e)
      }
      this.chart = null
    }

    if (this.refreshTimer) {
      clearInterval(this.refreshTimer)
      this.refreshTimer = null
    }
  }

  // Enhanced logging for Hotwire context
  logHotwireConnection() {
    console.log(`📊 Hotwire Chart Controller Connected`, {
      type: this.typeValue,
      frameId: this.frameIdValue,
      hasData: !!this.dataValue,
      autoRefresh: this.refreshIntervalValue,
      element: this.element
    })
  }

  logHotwireDisconnection() {
    console.log(`📊 Hotwire Chart Controller Disconnected`, {
      type: this.typeValue,
      frameId: this.frameIdValue
    })
  }

  // Public API for external control
  updateData(newData) {
    this.dataValue = newData
  }

  changeType(newType) {
    this.typeValue = newType
  }

  // Accessibility support
  announceChartUpdate() {
    const announcement = `Chart updated with new ${this.typeValue} data`
    // Could dispatch to screen reader announcement system
    console.log(`♿ ${announcement}`)
  }
}
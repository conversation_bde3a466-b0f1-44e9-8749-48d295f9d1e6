import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  static targets = ["section", "toggle", "content", "icon", "mobileOverlay", "mobileButton"]
  static values = {
    defaultExpanded: String,
    persistState: <PERSON>ole<PERSON>
  }

  connect() {
    console.log("🎯 Sidebar controller connected", this.element)
    this.isOpen = false
    this.initializeSections()
    this.setupKeyboardNavigation()
  }

  initializeSections() {
    // Initialize section states from localStorage or defaults
    this.sectionTargets.forEach((section, index) => {
      const sectionId = section.dataset.sectionId
      const isExpanded = this.getSectionState(sectionId, index)
      
      this.setSectionState(section, isExpanded, false) // false = no animation on init
    })
  }

  getSectionState(sectionId, index) {
    if (this.persistStateValue && localStorage) {
      const stored = localStorage.getItem(`sidebar-section-${sectionId}`)
      if (stored !== null) {
        return stored === 'true'
      }
    }

    // Fall back to default expanded sections
    const defaultExpanded = this.defaultExpandedValue ? this.defaultExpandedValue.split(',') : []
    return defaultExpanded.includes(sectionId) || index === 0
  }

  setSectionState(section, isExpanded, animate = true) {
    console.log("🔧 setSectionState called:", { sectionId: section.dataset.sectionId, isExpanded, animate })

    const content = section.querySelector('[data-sidebar-target="content"]')
    const icon = section.querySelector('[data-sidebar-target="icon"]')
    const toggle = section.querySelector('[data-sidebar-target="toggle"]')

    if (!content || !icon || !toggle) {
      console.error("❌ Missing elements:", { content: !!content, icon: !!icon, toggle: !!toggle })
      return
    }

    // Update ARIA attributes
    toggle.setAttribute('aria-expanded', isExpanded.toString())
    content.setAttribute('aria-hidden', (!isExpanded).toString())

    if (animate) {
      // Animate the expansion/collapse
      if (isExpanded) {
        this.expandSection(content, icon)
      } else {
        this.collapseSection(content, icon)
      }
    } else {
      // Set state immediately without animation
      content.style.display = isExpanded ? 'block' : 'none'
      content.style.maxHeight = isExpanded ? 'none' : '0'
      icon.style.transform = isExpanded ? 'rotate(90deg)' : 'rotate(0deg)'
    }

    // Persist state if enabled
    if (this.persistStateValue && localStorage) {
      const sectionId = section.dataset.sectionId
      localStorage.setItem(`sidebar-section-${sectionId}`, isExpanded.toString())
    }
  }

  expandSection(content, icon) {
    console.log("📈 Expanding section")
    // Set initial state for animation
    content.style.display = 'block'
    content.style.overflow = 'hidden'
    content.style.maxHeight = '0'
    
    // Force reflow
    content.offsetHeight
    
    // Animate to full height
    const scrollHeight = content.scrollHeight
    content.style.transition = 'max-height 0.3s cubic-bezier(0.4, 0, 0.2, 1)'
    content.style.maxHeight = scrollHeight + 'px'
    
    // Rotate icon
    icon.style.transition = 'transform 0.3s cubic-bezier(0.4, 0, 0.2, 1)'
    icon.style.transform = 'rotate(90deg)'
    
    // Clean up after animation
    setTimeout(() => {
      content.style.maxHeight = 'none'
      content.style.overflow = 'visible'
      content.style.transition = ''
    }, 300)
  }

  collapseSection(content, icon) {
    console.log("📉 Collapsing section")
    // Set initial state
    const scrollHeight = content.scrollHeight
    content.style.overflow = 'hidden'
    content.style.maxHeight = scrollHeight + 'px'
    
    // Force reflow
    content.offsetHeight
    
    // Animate to collapsed
    content.style.transition = 'max-height 0.3s cubic-bezier(0.4, 0, 0.2, 1)'
    content.style.maxHeight = '0'
    
    // Rotate icon
    icon.style.transition = 'transform 0.3s cubic-bezier(0.4, 0, 0.2, 1)'
    icon.style.transform = 'rotate(0deg)'
    
    // Hide after animation
    setTimeout(() => {
      content.style.display = 'none'
      content.style.transition = ''
    }, 300)
  }

  toggleSection(event) {
    event.preventDefault()
    event.stopPropagation()

    console.log("🔄 Sidebar toggleSection called", event.currentTarget, event.target)

    // Always use currentTarget (the element with the data-action) instead of target
    let toggle = event.currentTarget

    // If for some reason currentTarget is not the button, find the button
    if (!toggle.hasAttribute('data-sidebar-target') || toggle.getAttribute('data-sidebar-target') !== 'toggle') {
      toggle = event.target.closest('[data-sidebar-target="toggle"]')
    }

    if (!toggle) {
      console.error("❌ Could not find toggle button")
      return
    }

    const section = toggle.closest('[data-sidebar-target="section"]')

    if (!section) {
      console.error("❌ Could not find section element")
      return
    }

    const content = section.querySelector('[data-sidebar-target="content"]')
    if (!content) {
      console.error("❌ Could not find content element")
      return
    }

    const isExpanded = toggle.getAttribute('aria-expanded') === 'true'
    console.log("📊 Section state:", { isExpanded, sectionId: section.dataset.sectionId })

    this.setSectionState(section, !isExpanded, true)
  }

  setupKeyboardNavigation() {
    // Add keyboard support for navigation
    this.element.addEventListener('keydown', (event) => {
      if (event.key === 'Enter' || event.key === ' ') {
        const target = event.target
        if (target.hasAttribute('data-action') && target.dataset.action.includes('toggleSection')) {
          event.preventDefault()
          this.toggleSection(event)
        }
      }
    })
  }

  // Mobile navigation methods
  toggleMobile() {
    if (this.isOpen) {
      this.closeMobile()
    } else {
      this.openMobile()
    }
  }

  openMobile() {
    this.isOpen = true
    
    if (this.hasMobileOverlayTarget) {
      this.mobileOverlayTarget.style.display = "flex"
      
      // Animate in
      requestAnimationFrame(() => {
        this.mobileOverlayTarget.classList.add("opacity-100")
        this.mobileOverlayTarget.classList.remove("opacity-0")
      })
    }
    
    // Prevent body scroll
    document.body.style.overflow = "hidden"
    
    // Add escape key listener
    document.addEventListener("keydown", this.handleEscape.bind(this))
    
    // Focus management
    this.focusFirstNavigationItem()
  }

  closeMobile() {
    this.isOpen = false
    
    if (this.hasMobileOverlayTarget) {
      // Animate out
      this.mobileOverlayTarget.classList.add("opacity-0")
      this.mobileOverlayTarget.classList.remove("opacity-100")
      
      // Hide after animation
      setTimeout(() => {
        if (!this.isOpen) {
          this.mobileOverlayTarget.style.display = "none"
        }
      }, 150)
    }
    
    // Restore body scroll
    document.body.style.overflow = ""
    
    // Remove escape key listener
    document.removeEventListener("keydown", this.handleEscape.bind(this))
  }

  handleEscape(event) {
    if (event.key === "Escape") {
      this.closeMobile()
    }
  }

  focusFirstNavigationItem() {
    const firstLink = this.element.querySelector('a[href]')
    if (firstLink) {
      firstLink.focus()
    }
  }

  disconnect() {
    // Cleanup
    document.body.style.overflow = ""
    document.removeEventListener("keydown", this.handleEscape.bind(this))
  }
}

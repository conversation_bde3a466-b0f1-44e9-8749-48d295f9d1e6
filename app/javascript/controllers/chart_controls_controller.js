// Chart Controls Controller - Hotwire Enhanced
// Manages chart controls, auto-refresh, and frame coordination
import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  static targets = ["refreshToggle", "refreshText"]
  static values = { 
    frame: String,
    type: String,
    autoRefresh: <PERSON>ole<PERSON>
  }

  // Control state
  autoRefreshTimer = null
  refreshInterval = 30000 // 30 seconds default

  connect() {
    this.initializeControls()
    this.logControllerConnection()
  }

  disconnect() {
    this.cleanup()
    this.logControllerDisconnection()
  }

  // Initialize control states
  initializeControls() {
    this.updateRefreshToggleDisplay()
    
    if (this.autoRefreshValue) {
      this.startAutoRefresh()
    }
  }

  // Toggle auto-refresh functionality
  toggleAutoRefresh() {
    this.autoRefreshValue = !this.autoRefreshValue
    
    if (this.autoRefreshValue) {
      this.startAutoRefresh()
      this.showAutoRefreshActive()
    } else {
      this.stopAutoRefresh()
      this.showAutoRefreshInactive()
    }

    this.logAutoRefreshToggle()
  }

  // Manual refresh trigger
  refreshChart() {
    const frame = this.getTargetFrame()
    if (frame) {
      this.triggerFrameRefresh(frame)
      this.showRefreshFeedback()
    } else {
      console.warn(`Chart frame "${this.frameValue}" not found`)
    }
  }

  // Auto-refresh management
  startAutoRefresh() {
    this.stopAutoRefresh() // Clear any existing timer
    
    this.autoRefreshTimer = setInterval(() => {
      this.refreshChart()
    }, this.refreshInterval)

    this.logAutoRefreshStarted()
  }

  stopAutoRefresh() {
    if (this.autoRefreshTimer) {
      clearInterval(this.autoRefreshTimer)
      this.autoRefreshTimer = null
    }

    this.logAutoRefreshStopped()
  }

  // Frame interaction methods
  getTargetFrame() {
    return document.getElementById(this.frameValue)
  }

  triggerFrameRefresh(frame) {
    try {
      // Modern Turbo Frame refresh
      if (frame.reload) {
        frame.reload()
      } else if (frame.src) {
        // Fallback: force reload by updating src with timestamp
        const url = new URL(frame.src, window.location.origin)
        url.searchParams.set('_t', Date.now())
        frame.src = url.toString()
      }
    } catch (error) {
      console.error('Error refreshing frame:', error)
    }
  }

  // UI feedback methods
  updateRefreshToggleDisplay() {
    if (this.hasRefreshToggleTarget) {
      const button = this.refreshToggleTarget
      
      if (this.autoRefreshValue) {
        button.classList.add('bg-green-50', 'border-green-300', 'text-green-700')
        button.classList.remove('bg-white', 'border-gray-300')
      } else {
        button.classList.add('bg-white', 'border-gray-300')
        button.classList.remove('bg-green-50', 'border-green-300', 'text-green-700')
      }
    }
  }

  showAutoRefreshActive() {
    this.updateRefreshToggleDisplay()
    
    if (this.hasRefreshTextTarget) {
      this.refreshTextTarget.textContent = 'Auto'
      this.refreshTextTarget.className = 'text-green-600'
    }
  }

  showAutoRefreshInactive() {
    this.updateRefreshToggleDisplay()
    
    if (this.hasRefreshTextTarget) {
      this.refreshTextTarget.textContent = 'Manual'
      this.refreshTextTarget.className = 'text-gray-600'
    }
  }

  showRefreshFeedback() {
    // Visual feedback for manual refresh
    if (this.hasRefreshToggleTarget) {
      const originalClasses = this.refreshToggleTarget.className
      
      // Add loading state
      this.refreshToggleTarget.classList.add('animate-pulse', 'opacity-50')
      
      // Remove after short delay
      setTimeout(() => {
        this.refreshToggleTarget.className = originalClasses
      }, 500)
    }

    // Dispatch refresh event
    this.dispatch('refresh', {
      detail: {
        frameId: this.frameValue,
        chartType: this.typeValue,
        refreshType: 'manual'
      }
    })
  }

  // Value change handlers
  autoRefreshValueChanged() {
    if (this.autoRefreshValue) {
      this.startAutoRefresh()
      this.showAutoRefreshActive()
    } else {
      this.stopAutoRefresh()
      this.showAutoRefreshInactive()
    }
  }

  frameValueChanged() {
    // Frame target changed, update references
    this.logFrameChange()
  }

  // Advanced control methods
  setRefreshInterval(seconds) {
    this.refreshInterval = Math.max(5000, seconds * 1000) // Minimum 5 seconds
    
    if (this.autoRefreshValue) {
      this.startAutoRefresh() // Restart with new interval
    }
  }

  // Pause auto-refresh (useful for user interaction)
  pauseAutoRefresh() {
    if (this.autoRefreshTimer && this.autoRefreshValue) {
      this.stopAutoRefresh()
      
      // Resume after a delay
      setTimeout(() => {
        if (this.autoRefreshValue) {
          this.startAutoRefresh()
        }
      }, 10000) // Resume after 10 seconds
    }
  }

  // Keyboard shortcuts
  handleKeydown(event) {
    switch (event.key) {
      case 'r':
      case 'R':
        if (event.ctrlKey || event.metaKey) {
          event.preventDefault()
          this.refreshChart()
        }
        break
      case ' ': // Spacebar
        if (event.target === this.refreshToggleTarget) {
          event.preventDefault()
          this.toggleAutoRefresh()
        }
        break
    }
  }

  // Cleanup
  cleanup() {
    this.stopAutoRefresh()
  }

  // Enhanced logging
  logControllerConnection() {
    console.log(`🎮 Chart Controls Connected`, {
      frame: this.frameValue,
      type: this.typeValue,
      autoRefresh: this.autoRefreshValue,
      refreshInterval: `${this.refreshInterval / 1000}s`
    })
  }

  logControllerDisconnection() {
    console.log(`🎮 Chart Controls Disconnected`, {
      frame: this.frameValue,
      type: this.typeValue
    })
  }

  logAutoRefreshToggle() {
    console.log(`🔄 Auto-refresh ${this.autoRefreshValue ? 'enabled' : 'disabled'}`, {
      frame: this.frameValue,
      interval: `${this.refreshInterval / 1000}s`
    })
  }

  logAutoRefreshStarted() {
    console.log(`⏰ Auto-refresh started`, {
      frame: this.frameValue,
      interval: `${this.refreshInterval / 1000}s`
    })
  }

  logAutoRefreshStopped() {
    console.log(`⏰ Auto-refresh stopped`, {
      frame: this.frameValue
    })
  }

  logFrameChange() {
    console.log(`🖼️ Frame target changed`, {
      newFrame: this.frameValue,
      type: this.typeValue
    })
  }

  // Accessibility support
  announceRefresh() {
    const message = `Chart refreshed for ${this.typeValue}`
    console.log(`♿ ${message}`)
    
    // Could integrate with screen reader announcement system
    // this.dispatch('announce', { detail: { message } })
  }

  // Public API
  forceRefresh() {
    this.refreshChart()
  }

  enableAutoRefresh() {
    this.autoRefreshValue = true
  }

  disableAutoRefresh() {
    this.autoRefreshValue = false
  }
}
// Import and register all your controllers from the importmap via controllers/**/*_controller
import { application } from "controllers/application"
import { eagerLoadControllersFrom } from "@hotwired/stimulus-loading"

// Import controllers explicitly for enterprise reliability
import <PERSON><PERSON><PERSON><PERSON><PERSON> from "./hello_controller"
import Sidebar<PERSON><PERSON>roller from "./sidebar_controller"
import SimpleSidebarController from "./simple_sidebar_controller"

// Import other controllers that exist
try {
  var ChartController = require("./chart_controller").default
} catch(e) {
  console.log("Chart controller not found, skipping...")
}

try {
  var OptimizedChartController = require("./optimized_chart_controller").default
} catch(e) {
  console.log("Optimized chart controller not found, skipping...")
}

try {
  var NavigationController = require("./navigation_controller").default
} catch(e) {
  console.log("Navigation controller not found, skipping...")
}

try {
  var DropdownController = require("./dropdown_controller").default
} catch(e) {
  console.log("Dropdown controller not found, skipping...")
}

// Register controllers explicitly (only ones that exist)
application.register("hello", HelloController)
application.register("sidebar", SidebarController) // Enhanced sidebar navigation
application.register("simple-sidebar", SimpleSidebarController) // Simple test sidebar

// Register optional controllers if they exist
if (ChartController) {
  application.register("chart-legacy", ChartController)
}

if (OptimizedChartController) {
  application.register("chart", OptimizedChartController)
}

if (NavigationController) {
  application.register("navigation", NavigationController)
}

if (DropdownController) {
  application.register("dropdown", DropdownController)
}

// Also load any other controllers automatically
eagerLoadControllersFrom("controllers", application)

// Debug logging after DOM is ready
document.addEventListener('DOMContentLoaded', () => {
  console.log("🎯 Stimulus controllers registered:", {
    hello: application.router.modulesByIdentifier.hello ? 'registered' : 'missing',
    sidebar: application.router.modulesByIdentifier.sidebar ? 'registered' : 'missing',
    chart: application.router.modulesByIdentifier.chart ? 'registered' : 'missing',
    'chart-legacy': application.router.modulesByIdentifier['chart-legacy'] ? 'registered' : 'missing',
    navigation: application.router.modulesByIdentifier.navigation ? 'registered' : 'missing',
    dropdown: application.router.modulesByIdentifier.dropdown ? 'registered' : 'missing',
    totalControllers: Object.keys(application.router.modulesByIdentifier).length,
    allControllers: Object.keys(application.router.modulesByIdentifier)
  })
})

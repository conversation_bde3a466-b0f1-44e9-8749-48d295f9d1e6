// Import and register all your controllers from the importmap via controllers/**/*_controller
import { application } from "controllers/application"
import { eagerLoadControllersFrom } from "@hotwired/stimulus-loading"

// Eager load all controllers defined in the import map under controllers/**/*_controller
eagerLoadControllersFrom("controllers", application)

// Debug logging after DOM is ready
document.addEventListener('DOMContentLoaded', () => {
  console.log("🎯 Stimulus controllers registered:", {
    totalControllers: Object.keys(application.router.modulesByIdentifier).length,
    allControllers: Object.keys(application.router.modulesByIdentifier),
    sidebar: application.router.modulesByIdentifier.sidebar ? 'registered' : 'missing',
    'simple-sidebar': application.router.modulesByIdentifier['simple-sidebar'] ? 'registered' : 'missing',
    debug: application.router.modulesByIdentifier.debug ? 'registered' : 'missing'
  })
})

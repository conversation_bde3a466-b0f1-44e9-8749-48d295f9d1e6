// Import and register all your controllers from the importmap via controllers/**/*_controller
import { application } from "controllers/application"
import { eagerLoadControllersFrom } from "@hotwired/stimulus-loading"

// Import controllers explicitly for enterprise reliability
import <PERSON><PERSON><PERSON><PERSON><PERSON> from "./hello_controller"
import <PERSON><PERSON><PERSON>roller from "./chart_controller"
import OptimizedChartController from "./optimized_chart_controller"
import HotwireChartController from "./hotwire_chart_controller"
import ChartControlsController from "./chart_controls_controller"
import AnalyticsChannelController from "./analytics_channel_controller"
import TestController from "./test_controller"
import SidebarController from "./sidebar_controller"
import NavigationController from "./navigation_controller"
import Dropdown<PERSON>ontroller from "./dropdown_controller"

// Register controllers explicitly
application.register("hello", HelloController)
application.register("chart", OptimizedChartController) // Use optimized version
application.register("chart-legacy", ChartController) // Keep legacy for compatibility
application.register("hotwire-chart", HotwireChartController) // New Hotwire-optimized chart
application.register("chart-controls", ChartControlsController) // Chart control panel
application.register("analytics-channel", AnalyticsChannelController) // Real-time analytics
application.register("test", TestController) // Simple test controller
application.register("sidebar", SidebarController) // Enhanced sidebar navigation
application.register("navigation", NavigationController) // Mobile navigation
application.register("dropdown", DropdownController) // Dropdown menus

// Also load any other controllers automatically
eagerLoadControllersFrom("controllers", application)

// Debug logging after DOM is ready
document.addEventListener('DOMContentLoaded', () => {
  console.log("🎯 Stimulus controllers registered:", {
    hello: application.router.modulesByIdentifier.hello ? 'registered' : 'missing',
    chart: application.router.modulesByIdentifier.chart ? 'registered' : 'missing',
    'chart-legacy': application.router.modulesByIdentifier['chart-legacy'] ? 'registered' : 'missing',
    'hotwire-chart': application.router.modulesByIdentifier['hotwire-chart'] ? 'registered' : 'missing',
    'chart-controls': application.router.modulesByIdentifier['chart-controls'] ? 'registered' : 'missing',
    'analytics-channel': application.router.modulesByIdentifier['analytics-channel'] ? 'registered' : 'missing',
    sidebar: application.router.modulesByIdentifier.sidebar ? 'registered' : 'missing',
    navigation: application.router.modulesByIdentifier.navigation ? 'registered' : 'missing',
    dropdown: application.router.modulesByIdentifier.dropdown ? 'registered' : 'missing',
    totalControllers: Object.keys(application.router.modulesByIdentifier).length,
    allControllers: Object.keys(application.router.modulesByIdentifier)
  })
})

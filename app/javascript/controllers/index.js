// Import and register all your controllers from the importmap via controllers/**/*_controller
import { application } from "controllers/application"
import { eagerLoadControllersFrom } from "@hotwired/stimulus-loading"

// Import controllers explicitly for enterprise reliability
import Hello<PERSON><PERSON>roller from "./hello_controller"
import <PERSON><PERSON><PERSON>roller from "./chart_controller"
import OptimizedChartController from "./optimized_chart_controller"

// Register controllers explicitly
application.register("hello", HelloController)
application.register("chart", OptimizedChartController) // Use optimized version
application.register("chart-legacy", ChartController) // Keep legacy for compatibility

// Also load any other controllers automatically
eagerLoadControllersFrom("controllers", application)

// Debug logging after DOM is ready
document.addEventListener('DOMContentLoaded', () => {
  console.log("🎯 Stimulus controllers registered:", {
    hello: application.router.modulesByIdentifier.hello ? 'registered' : 'missing',
    chart: application.router.modulesByIdentifier.chart ? 'registered' : 'missing',
    'chart-legacy': application.router.modulesByIdentifier['chart-legacy'] ? 'registered' : 'missing',
    totalControllers: Object.keys(application.router.modulesByIdentifier).length,
    allControllers: Object.keys(application.router.modulesByIdentifier)
  })
})

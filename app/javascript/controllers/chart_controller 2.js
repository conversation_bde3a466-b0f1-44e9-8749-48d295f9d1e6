import { Controller } from "@hotwired/stimulus"
import ApexCharts from "apexcharts"

export default class extends Controller {
  static targets = ["container", "loading", "error"]
  static values = {
    type: String,
    url: String,
    data: Object,
    options: Object,
    height: { type: Number, default: 350 },
    refreshInterval: Number,
    widgetId: Number
  }

  connect() {
    console.log('🔧 Chart controller connecting...', {
      element: this.element,
      type: this.typeValue,
      hasData: this.hasDataValue,
      dataValue: this.dataValue,
      hasUrl: this.hasUrlValue,
      urlValue: this.urlValue,
      targets: {
        container: this.hasContainerTarget,
        loading: this.hasLoadingTarget,
        error: this.hasErrorTarget
      }
    })

    // Check ApexCharts availability - try import first, then global
    const ApexChartsInstance = ApexCharts || window.ApexCharts

    console.log('🔧 ApexCharts check:', {
      ApexChartsImported: typeof ApexCharts !== 'undefined',
      ApexChartsGlobal: typeof window.ApexCharts !== 'undefined',
      ApexChartsAvailable: !!ApexChartsInstance,
      version: ApexChartsInstance?.version
    })

    // Check if ApexCharts is available
    if (!ApexChartsInstance) {
      console.error('❌ ApexCharts library not available - neither imported nor global')
      this.showError('Chart library not loaded')
      return
    }

    // Store the ApexCharts instance for later use
    this.ApexCharts = ApexChartsInstance

    console.log('✅ Chart controller connected successfully, initializing chart...')
    this.initializeChart()

    if (this.hasRefreshIntervalValue && this.refreshIntervalValue > 0) {
      console.log('🔄 Starting auto-refresh with interval:', this.refreshIntervalValue)
      this.startAutoRefresh()
    }
  }

  disconnect() {
    this.stopAutoRefresh()
    if (this.chart) {
      this.chart.destroy()
    }
  }

  initializeChart() {
    if (this.hasDataValue && Object.keys(this.dataValue).length > 0) {
      // Use provided data
      console.log('Using provided data')
      this.renderChart(this.dataValue)
    } else if (this.hasUrlValue) {
      // Fetch data from URL
      console.log('Fetching data from URL')
      this.fetchAndRender()
    } else {
      console.error('No data source configured')
      this.showError("No data source configured")
    }
  }

  async fetchAndRender() {
    console.log('Fetching chart data from:', this.urlValue)
    this.showLoading()
    
    try {
      const response = await fetch(this.urlValue, {
        headers: {
          'Accept': 'application/json',
          'X-Requested-With': 'XMLHttpRequest',
          'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content')
        }
      })
      
      console.log('Response status:', response.status)
      console.log('Response headers:', Object.fromEntries(response.headers.entries()))
      
      if (!response.ok) {
        const errorText = await response.text()
        console.error('Error response body:', errorText)
        
        if (response.status === 401) {
          this.showError("Authentication required")
        } else if (response.status === 403) {
          this.showError("Access denied")
        } else if (response.status === 404) {
          this.showError("Chart data not found")
        } else {
          this.showError(`Server error (${response.status})`)
        }
        return
      }
      
      const contentType = response.headers.get('content-type')
      if (!contentType || !contentType.includes('application/json')) {
        console.error('Unexpected content type:', contentType)
        const responseText = await response.text()
        console.error('Response body:', responseText.substring(0, 500))
        this.showError("Invalid response format")
        return
      }
      
      const data = await response.json()
      console.log('Chart data received:', data)
      this.renderChart(data)
    } catch (error) {
      console.error('Error fetching chart data:', error)
      this.showError(`Failed to load chart data: ${error.message}`)
    }
  }

  renderChart(data) {
    console.log('Rendering chart with data:', data)
    this.hideLoading()
    this.hideError()
    
    const options = this.buildChartOptions(data)
    console.log('Chart options:', options)
    
    if (this.chart) {
      // Update existing chart
      console.log('Updating existing chart')
      this.chart.updateOptions(options)
    } else {
      // Create new chart
      console.log('Creating new chart')
      this.chart = new this.ApexCharts(this.containerTarget, options)
      this.chart.render()
        .then(() => console.log('Chart rendered successfully'))
        .catch(error => console.error('Error rendering chart:', error))
    }
  }

  buildChartOptions(data) {
    const baseOptions = {
      chart: {
        type: this.typeValue || 'line',
        height: this.heightValue,
        toolbar: {
          show: true,
          tools: {
            download: true,
            selection: true,
            zoom: true,
            zoomin: true,
            zoomout: true,
            pan: true,
            reset: true
          }
        },
        animations: {
          enabled: true,
          easing: 'easeinout',
          speed: 800,
          animateGradually: {
            enabled: true,
            delay: 150
          },
          dynamicAnimation: {
            enabled: true,
            speed: 350
          }
        }
      },
      series: this.formatSeriesData(data),
      xaxis: {
        categories: data.labels || [],
        labels: {
          rotate: -45,
          rotateAlways: false,
          hideOverlappingLabels: true
        }
      },
      yaxis: {
        title: {
          text: data.yAxisTitle || ''
        }
      },
      dataLabels: {
        enabled: false
      },
      stroke: {
        curve: 'smooth',
        width: 2
      },
      title: {
        text: data.title || '',
        align: 'left'
      },
      grid: {
        borderColor: '#e7e7e7',
        row: {
          colors: ['#f3f3f3', 'transparent'],
          opacity: 0.5
        }
      },
      legend: {
        position: 'bottom',
        horizontalAlign: 'center'
      },
      responsive: [{
        breakpoint: 480,
        options: {
          chart: {
            width: '100%'
          },
          legend: {
            position: 'bottom'
          }
        }
      }],
      theme: {
        mode: this.getThemeMode()
      }
    }

    // Merge with custom options
    return this.deepMerge(baseOptions, this.optionsValue || {})
  }

  formatSeriesData(data) {
    if (data.series) {
      return data.series
    }
    
    if (data.datasets) {
      // Convert Chart.js format to ApexCharts format
      return data.datasets.map(dataset => ({
        name: dataset.label,
        data: dataset.data,
        color: dataset.borderColor || dataset.backgroundColor
      }))
    }
    
    // Simple data array
    if (Array.isArray(data.data)) {
      return [{
        name: data.name || 'Series',
        data: data.data
      }]
    }
    
    return []
  }

  showLoading() {
    if (this.hasLoadingTarget) {
      this.loadingTarget.classList.remove('hidden')
    }
    if (this.hasContainerTarget) {
      this.containerTarget.classList.add('opacity-50')
    }
  }

  hideLoading() {
    if (this.hasLoadingTarget) {
      this.loadingTarget.classList.add('hidden')
    }
    if (this.hasContainerTarget) {
      this.containerTarget.classList.remove('opacity-50')
    }
  }

  showError(message) {
    if (this.hasErrorTarget) {
      this.errorTarget.textContent = message
      this.errorTarget.classList.remove('hidden')
    }
    if (this.hasContainerTarget) {
      this.containerTarget.classList.add('hidden')
    }
  }

  hideError() {
    if (this.hasErrorTarget) {
      this.errorTarget.classList.add('hidden')
    }
    if (this.hasContainerTarget) {
      this.containerTarget.classList.remove('hidden')
    }
  }

  refresh() {
    if (this.hasUrlValue) {
      this.fetchAndRender()
    }
  }

  startAutoRefresh() {
    this.stopAutoRefresh()
    this.refreshTimer = setInterval(() => {
      this.refresh()
    }, this.refreshIntervalValue * 1000)
  }

  stopAutoRefresh() {
    if (this.refreshTimer) {
      clearInterval(this.refreshTimer)
      this.refreshTimer = null
    }
  }

  updateData(newData) {
    if (this.chart) {
      this.chart.updateSeries(this.formatSeriesData(newData))
    } else {
      this.renderChart(newData)
    }
  }

  changeType(event) {
    const newType = event.currentTarget.dataset.chartType
    if (newType && this.chart) {
      this.typeValue = newType
      this.chart.updateOptions({
        chart: { type: newType }
      })
    }
  }

  export(format = 'png') {
    if (this.chart) {
      if (format === 'svg') {
        this.chart.exports.exportToSVG()
      } else if (format === 'csv') {
        this.chart.exports.exportToCSV()
      } else {
        this.chart.exports.exportToPNG()
      }
    }
  }

  getThemeMode() {
    // Check if dark mode is enabled
    if (document.documentElement.classList.contains('dark')) {
      return 'dark'
    }
    return 'light'
  }

  deepMerge(target, source) {
    const output = Object.assign({}, target)
    if (isObject(target) && isObject(source)) {
      Object.keys(source).forEach(key => {
        if (isObject(source[key])) {
          if (!(key in target))
            Object.assign(output, { [key]: source[key] })
          else
            output[key] = this.deepMerge(target[key], source[key])
        } else {
          Object.assign(output, { [key]: source[key] })
        }
      })
    }
    return output
  }
}

function isObject(item) {
  return item && typeof item === 'object' && !Array.isArray(item)
}

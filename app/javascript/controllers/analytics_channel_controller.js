// Analytics Channel Controller - ActionCable + Hotwire Integration
// Manages real-time analytics updates via WebSocket
import { Controller } from "@hotwired/stimulus"
import consumer from "../channels/consumer"

export default class extends Controller {
  static values = { accountId: String }

  // ActionCable subscription
  subscription = null

  connect() {
    this.subscribeToAnalyticsChannel()
    this.logChannelConnection()
  }

  disconnect() {
    this.unsubscribeFromAnalyticsChannel()
    this.logChannelDisconnection()
  }

  // ActionCable subscription management
  subscribeToAnalyticsChannel() {
    if (this.accountIdValue) {
      this.subscription = consumer.subscriptions.create(
        { 
          channel: "AnalyticsChannel",
          account_id: this.accountIdValue
        },
        {
          connected: this.handleConnected.bind(this),
          disconnected: this.handleDisconnected.bind(this),
          received: this.handleReceived.bind(this)
        }
      )
    }
  }

  unsubscribeFromAnalyticsChannel() {
    if (this.subscription) {
      this.subscription.unsubscribe()
      this.subscription = null
    }
  }

  // ActionCable event handlers
  handleConnected() {
    console.log("📡 Analytics channel connected")
    this.updateConnectionStatus("connected")
  }

  handleDisconnected() {
    console.log("📡 Analytics channel disconnected")
    this.updateConnectionStatus("disconnected")
  }

  handleReceived(data) {
    console.log("📊 Analytics update received:", data)
    
    switch (data.type) {
      case 'chart_update':
        this.handleChartUpdate(data)
        break
      case 'metrics_update':
        this.handleMetricsUpdate(data)
        break
      case 'alert':
        this.handleAlert(data)
        break
      default:
        console.log("Unknown analytics update type:", data.type)
    }
  }

  // Update handlers
  handleChartUpdate(data) {
    const { chart_type, html } = data
    const frameId = `${chart_type}-chart`
    const frame = document.getElementById(frameId)

    if (frame) {
      // Update frame content with new HTML
      frame.innerHTML = html
      
      // Trigger chart controller to reinitialize
      const chartElement = frame.querySelector('[data-controller*="hotwire-chart"]')
      if (chartElement) {
        // Dispatch custom event to trigger chart refresh
        chartElement.dispatchEvent(new CustomEvent('analytics:chart-updated', {
          detail: { chartType: chart_type, source: 'websocket' }
        }))
      }

      this.logChartUpdate(chart_type)
    }
  }

  handleMetricsUpdate(data) {
    const { metrics } = data
    
    // Update overview cards if present
    Object.entries(metrics).forEach(([key, value]) => {
      const element = document.querySelector(`[data-metric="${key}"]`)
      if (element) {
        element.textContent = value
        this.animateMetricUpdate(element)
      }
    })

    this.logMetricsUpdate(metrics)
  }

  handleAlert(data) {
    const { message, type, duration } = data
    
    // Create and show alert notification
    this.showAlert(message, type, duration)
    this.logAlert(data)
  }

  // Client-side actions (callable from chart controls)
  refreshChart(chartType, dateRange = '30_days') {
    if (this.subscription) {
      this.subscription.perform('refresh_chart', {
        chart_type: chartType,
        date_range: dateRange
      })
    }
  }

  toggleAutoRefresh(chartType, enabled) {
    if (this.subscription) {
      this.subscription.perform('toggle_auto_refresh', {
        chart_type: chartType,
        enabled: enabled
      })
    }
  }

  // UI feedback methods
  updateConnectionStatus(status) {
    const indicator = document.querySelector('[data-analytics-status]')
    if (indicator) {
      indicator.dataset.analyticsStatus = status
      
      if (status === 'connected') {
        indicator.classList.add('text-green-600')
        indicator.classList.remove('text-red-600', 'text-yellow-600')
        indicator.textContent = '🟢 Live'
      } else {
        indicator.classList.add('text-red-600')
        indicator.classList.remove('text-green-600', 'text-yellow-600')
        indicator.textContent = '🔴 Offline'
      }
    }
  }

  animateMetricUpdate(element) {
    // Add pulse animation to show update
    element.classList.add('animate-pulse', 'text-blue-600')
    
    setTimeout(() => {
      element.classList.remove('animate-pulse', 'text-blue-600')
    }, 1000)
  }

  showAlert(message, type, duration = 5000) {
    // Create alert element
    const alert = document.createElement('div')
    alert.className = `fixed top-4 right-4 p-4 rounded-md shadow-lg z-50 ${this.getAlertClasses(type)}`
    alert.innerHTML = `
      <div class="flex items-center">
        <div class="flex-shrink-0">
          ${this.getAlertIcon(type)}
        </div>
        <div class="ml-3">
          <p class="text-sm font-medium">${message}</p>
        </div>
        <div class="ml-4">
          <button class="text-sm underline" onclick="this.parentElement.parentElement.remove()">
            Dismiss
          </button>
        </div>
      </div>
    `

    document.body.appendChild(alert)

    // Auto-remove after duration
    setTimeout(() => {
      if (alert.parentElement) {
        alert.remove()
      }
    }, duration)
  }

  getAlertClasses(type) {
    switch (type) {
      case 'success': return 'bg-green-50 text-green-800 border border-green-200'
      case 'warning': return 'bg-yellow-50 text-yellow-800 border border-yellow-200'
      case 'error': return 'bg-red-50 text-red-800 border border-red-200'
      default: return 'bg-blue-50 text-blue-800 border border-blue-200'
    }
  }

  getAlertIcon(type) {
    const iconClass = 'h-5 w-5'
    switch (type) {
      case 'success':
        return `<svg class="${iconClass} text-green-400" fill="currentColor" viewBox="0 0 20 20">
          <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
        </svg>`
      case 'warning':
        return `<svg class="${iconClass} text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
          <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"/>
        </svg>`
      case 'error':
        return `<svg class="${iconClass} text-red-400" fill="currentColor" viewBox="0 0 20 20">
          <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"/>
        </svg>`
      default:
        return `<svg class="${iconClass} text-blue-400" fill="currentColor" viewBox="0 0 20 20">
          <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"/>
        </svg>`
    }
  }

  // Event listeners for external triggers
  handleChartControlRefresh(event) {
    const { frameId, chartType } = event.detail
    this.refreshChart(chartType)
  }

  handleAutoRefreshToggle(event) {
    const { chartType, enabled } = event.detail
    this.toggleAutoRefresh(chartType, enabled)
  }

  // Logging methods
  logChannelConnection() {
    console.log("📡 Analytics Channel Controller Connected", {
      accountId: this.accountIdValue,
      subscriptionActive: !!this.subscription
    })
  }

  logChannelDisconnection() {
    console.log("📡 Analytics Channel Controller Disconnected", {
      accountId: this.accountIdValue
    })
  }

  logChartUpdate(chartType) {
    console.log(`📊 Chart updated via WebSocket: ${chartType}`)
  }

  logMetricsUpdate(metrics) {
    console.log("📈 Metrics updated via WebSocket:", metrics)
  }

  logAlert(alertData) {
    console.log("🚨 Alert received via WebSocket:", alertData)
  }

  // Public API for external controllers
  isConnected() {
    return this.subscription && this.subscription.identifier
  }

  requestChartRefresh(chartType, dateRange) {
    this.refreshChart(chartType, dateRange)
  }
}
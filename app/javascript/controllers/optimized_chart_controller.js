import { Controller } from "@hotwired/stimulus"
import ApexCharts from "apexcharts"

/**
 * Optimized Chart Controller for Hotwired Performance
 * Implements lazy loading, efficient rendering, and minimal DOM manipulation
 */
export default class extends Controller {
  static targets = ["container", "loading", "error"]
  static values = {
    type: String,
    height: { type: Number, default: 320 },
    data: Object,
    refreshInterval: Number
  }

  // Performance optimization: Use requestIdleCallback for non-critical operations
  connect() {
    this.log('info', 'Optimized chart controller connecting')
    this.chart = null
    this.refreshTimer = null
    this.isVisible = false
    
    // Fast path: If data is already available, render immediately
    if (this.hasDataValue && Object.keys(this.dataValue).length > 0) {
      this.renderChartOptimized(this.dataValue)
      this.setupRefresh()
      return
    }
    
    // Setup intersection observer for lazy loading
    this.setupIntersectionObserver()
  }

  disconnect() {
    this.log('info', 'Optimized chart controller disconnecting')
    this.cleanup()
  }

  // Performance: Only load when chart becomes visible
  setupIntersectionObserver() {
    if ('IntersectionObserver' in window) {
      this.observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
          if (entry.isIntersecting && !this.isVisible) {
            this.isVisible = true
            this.log('debug', 'Chart became visible, initializing')
            this.initializeChart()
            this.observer.disconnect()
          }
        })
      }, {
        rootMargin: '50px' // Start loading 50px before visible
      })
      
      this.observer.observe(this.element)
    } else {
      // Fallback for browsers without IntersectionObserver
      this.initializeChart()
    }
  }

  async initializeChart() {
    try {
      // Performance: Use requestAnimationFrame for smooth rendering
      requestAnimationFrame(() => {
        this.validateDependencies()
        this.renderChartOptimized(this.dataValue)
        this.setupRefresh()
      })
    } catch (error) {
      this.log('error', 'Chart initialization failed', error)
      this.showError(error.message)
    }
  }

  validateDependencies() {
    if (typeof window.ApexCharts === 'undefined') {
      throw new Error('ApexCharts library not available')
    }
  }

  // Optimized rendering with minimal DOM manipulation
  renderChartOptimized(data) {
    this.log('debug', 'Rendering chart with optimized path')
    
    try {
      const options = this.buildOptimizedChartOptions(data)
      
      // Performance: Cleanup existing chart efficiently
      if (this.chart) {
        this.chart.destroy()
        this.chart = null
      }

      // Performance: Hide loading state immediately
      this.hideLoading()
      
      // Performance: Create chart with optimized options
      this.chart = new window.ApexCharts(this.containerTarget, options)
      
      // Performance: Use async rendering to avoid blocking
      this.chart.render().then(() => {
        this.log('info', 'Chart rendered successfully')
      }).catch(error => {
        this.log('error', 'Chart rendering failed', error)
        this.showError('Chart rendering failed')
      })
      
    } catch (error) {
      this.log('error', 'Chart creation failed', error)
      this.showError('Chart creation failed')
    }
  }

  // Performance-optimized chart options
  buildOptimizedChartOptions(data) {
    const baseOptions = {
      series: data.datasets,
      chart: {
        type: this.typeValue,
        height: this.heightValue,
        toolbar: {
          show: true,
          tools: {
            download: true,
            zoom: true,
            pan: true,
            reset: true
          }
        },
        animations: {
          enabled: true,
          easing: 'easeinout',
          speed: 600, // Faster animations
          animateGradually: {
            enabled: true,
            delay: 50 // Reduced delay
          }
        },
        // Performance optimizations
        redrawOnParentResize: true,
        redrawOnWindowResize: true
      },
      xaxis: {
        categories: data.labels,
        labels: {
          style: { fontSize: '12px' }
        }
      },
      yaxis: {
        labels: {
          style: { fontSize: '12px' }
        }
      },
      legend: {
        position: 'top',
        fontSize: '12px'
      },
      // Performance: Optimized responsive breakpoints
      responsive: [{
        breakpoint: 768,
        options: {
          chart: { height: 280 },
          legend: { position: 'bottom' }
        }
      }],
      // Performance: Efficient data handling
      dataLabels: {
        enabled: false // Disable for better performance
      }
    }

    // Type-specific optimizations
    if (this.typeValue === 'line') {
      baseOptions.stroke = {
        curve: 'smooth',
        width: 2
      }
    } else if (this.typeValue === 'area') {
      baseOptions.fill = {
        type: 'gradient',
        gradient: {
          shadeIntensity: 1,
          opacityFrom: 0.7,
          opacityTo: 0.3
        }
      }
      baseOptions.stroke = {
        curve: 'smooth',
        width: 1
      }
    }

    return baseOptions
  }

  // Performance: Efficient refresh setup
  setupRefresh() {
    if (this.hasRefreshIntervalValue && this.refreshIntervalValue > 0) {
      this.log('debug', `Setting up optimized refresh every ${this.refreshIntervalValue} seconds`)
      
      // Performance: Use setTimeout instead of setInterval for better control
      const scheduleRefresh = () => {
        this.refreshTimer = setTimeout(() => {
          if (this.isVisible && document.contains(this.element)) {
            this.log('debug', 'Auto-refreshing chart data')
            // Note: In optimized version, we use data from Turbo Frame updates
            // rather than making additional AJAX requests
          }
          scheduleRefresh()
        }, this.refreshIntervalValue * 1000)
      }
      
      scheduleRefresh()
    }
  }

  // Performance: Efficient state management
  showLoading() {
    this.containerTarget.classList.add('hidden')
    this.loadingTarget.classList.remove('hidden')
    this.errorTarget.classList.add('hidden')
  }

  hideLoading() {
    this.containerTarget.classList.remove('hidden')
    this.loadingTarget.classList.add('hidden')
    this.errorTarget.classList.add('hidden')
  }

  showError(message) {
    this.containerTarget.classList.add('hidden')
    this.loadingTarget.classList.add('hidden')
    this.errorTarget.classList.remove('hidden')
    
    const errorElement = this.errorTarget.querySelector('p')
    if (errorElement) {
      errorElement.textContent = message
    }
  }

  // Performance: Efficient cleanup
  cleanup() {
    if (this.observer) {
      this.observer.disconnect()
      this.observer = null
    }
    
    if (this.chart) {
      this.chart.destroy()
      this.chart = null
    }
    
    if (this.refreshTimer) {
      clearTimeout(this.refreshTimer)
      this.refreshTimer = null
    }
    
    this.log('debug', 'Optimized cleanup completed')
  }

  // Lightweight logging
  log(level, message, data = null) {
    if (this.application.debug) {
      const prefix = `[OptimizedChart(${this.typeValue})]`
      console[level](`${prefix} ${message}`, data || '')
    }
  }

  // Public method for manual refresh
  refresh() {
    this.log('info', 'Manual refresh triggered')
    // In Turbo Frame context, refresh is handled by frame reload
    const frame = this.element.closest('turbo-frame')
    if (frame) {
      frame.reload()
    }
  }
}

import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  static targets = ["metricCard", "quickAction", "refreshButton"]
  static values = { 
    refreshInterval: { type: Number, default: 30000 }, // 30 seconds
    autoRefresh: { type: Boolean, default: false }
  }

  connect() {
    this.setupMetricCards()
    this.setupQuickActions()
    
    if (this.autoRefreshValue) {
      this.startAutoRefresh()
    }
  }

  setupMetricCards() {
    this.metricCardTargets.forEach(card => {
      // Add hover effects
      card.addEventListener("mouseenter", this.handleCardHover.bind(this))
      card.addEventListener("mouseleave", this.handleCardLeave.bind(this))
      
      // Add click analytics tracking
      card.addEventListener("click", this.handleCardClick.bind(this))
    })
  }

  setupQuickActions() {
    this.quickActionTargets.forEach(action => {
      // Add loading states for buttons
      action.addEventListener("click", this.handleQuickActionClick.bind(this))
    })
  }

  handleCardHover(event) {
    const card = event.currentTarget
    card.classList.add("transform", "scale-105", "shadow-lg")
    card.style.transition = "all 0.2s ease-in-out"
  }

  handleCardLeave(event) {
    const card = event.currentTarget
    card.classList.remove("transform", "scale-105", "shadow-lg")
  }

  handleCardClick(event) {
    const card = event.currentTarget
    const metricType = card.dataset.metric

    // Track analytics
    console.log(`Metric card clicked: ${metricType}`)

    // Add click animation
    card.classList.add("animate-pulse")
    setTimeout(() => {
      card.classList.remove("animate-pulse")
    }, 200)

    // Navigate to detailed view based on metric type
    this.navigateToDetailView(metricType)
  }

  navigateToDetailView(metricType) {
    let targetUrl = null

    switch (metricType) {
      case 'pipelines':
        targetUrl = '/pipelines'
        break
      case 'connectors':
        targetUrl = '/connectors'
        break
      case 'analytics':
        targetUrl = '/analytics'
        break
      case 'data_processed':
        targetUrl = '/analytics/usage_trends'
        break
      default:
        // Show detailed modal instead of navigation
        this.showMetricModal(metricType)
        return
    }

    if (targetUrl) {
      // Use Turbo for smooth navigation
      if (window.Turbo) {
        window.Turbo.visit(targetUrl)
      } else {
        window.location.href = targetUrl
      }
    }
  }

  showMetricModal(metricType) {
    // Create and show a modal with detailed metric information
    const modal = this.createMetricModal(metricType)
    document.body.appendChild(modal)

    // Show modal with animation
    requestAnimationFrame(() => {
      modal.classList.remove('opacity-0')
      modal.classList.add('opacity-100')
    })
  }

  createMetricModal(metricType) {
    const modal = document.createElement('div')
    modal.className = 'fixed inset-0 z-50 overflow-y-auto opacity-0 transition-opacity duration-300'
    modal.setAttribute('data-controller', 'modal')
    modal.setAttribute('data-action', 'click->modal#closeOnBackdrop')

    modal.innerHTML = `
      <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" aria-hidden="true"></div>

        <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>

        <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
          <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
            <div class="sm:flex sm:items-start">
              <div class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-indigo-100 sm:mx-0 sm:h-10 sm:w-10">
                <svg class="h-6 w-6 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
                </svg>
              </div>
              <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left">
                <h3 class="text-lg leading-6 font-medium text-gray-900" id="modal-title">
                  ${this.getMetricTitle(metricType)}
                </h3>
                <div class="mt-2">
                  <p class="text-sm text-gray-500">
                    ${this.getMetricDescription(metricType)}
                  </p>
                  <div class="mt-4" data-metric-details="${metricType}">
                    ${this.getMetricDetails(metricType)}
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
            <button type="button"
                    class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-indigo-600 text-base font-medium text-white hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:ml-3 sm:w-auto sm:text-sm"
                    data-action="click->modal#close">
              View Details
            </button>
            <button type="button"
                    class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
                    data-action="click->modal#close">
              Close
            </button>
          </div>
        </div>
      </div>
    `

    return modal
  }

  getMetricTitle(metricType) {
    const titles = {
      pipelines: 'Pipeline Performance',
      connectors: 'Connection Health',
      data_processed: 'Data Processing',
      system_health: 'System Status'
    }
    return titles[metricType] || 'Metric Details'
  }

  getMetricDescription(metricType) {
    const descriptions = {
      pipelines: 'Detailed information about your data pipelines and their performance metrics.',
      connectors: 'Health status and performance of your data connections.',
      data_processed: 'Volume and trends of data processed through your pipelines.',
      system_health: 'Overall system performance and health indicators.'
    }
    return descriptions[metricType] || 'Detailed metric information and trends.'
  }

  getMetricDetails(metricType) {
    // This would be populated with real data from the metrics
    return `
      <div class="space-y-3">
        <div class="flex justify-between">
          <span class="text-sm font-medium text-gray-500">Current Value:</span>
          <span class="text-sm text-gray-900">Loading...</span>
        </div>
        <div class="flex justify-between">
          <span class="text-sm font-medium text-gray-500">Trend:</span>
          <span class="text-sm text-green-600">↗ +5.2%</span>
        </div>
        <div class="flex justify-between">
          <span class="text-sm font-medium text-gray-500">Last Updated:</span>
          <span class="text-sm text-gray-900">Just now</span>
        </div>
      </div>
    `
  }

  handleQuickActionClick(event) {
    const button = event.currentTarget
    const actionType = button.dataset.action
    
    // Add loading state
    this.setButtonLoading(button, true)
    
    // Track action (placeholder for future implementation)
    console.log(`Quick action clicked: ${actionType}`)
    
    // Simulate action completion (remove in real implementation)
    setTimeout(() => {
      this.setButtonLoading(button, false)
    }, 1000)
  }

  setButtonLoading(button, loading) {
    if (loading) {
      button.disabled = true
      button.classList.add("opacity-75", "cursor-not-allowed")
      
      // Add spinner if not already present
      if (!button.querySelector('.spinner')) {
        const spinner = document.createElement('div')
        spinner.className = 'spinner inline-block w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2'
        button.prepend(spinner)
      }
    } else {
      button.disabled = false
      button.classList.remove("opacity-75", "cursor-not-allowed")
      
      // Remove spinner
      const spinner = button.querySelector('.spinner')
      if (spinner) {
        spinner.remove()
      }
    }
  }

  async refreshMetrics() {
    console.log("Refreshing dashboard metrics...")
    
    // Add loading states to all metric cards and refresh button
    this.addLoadingStates()
    this.setRefreshButtonLoading(true)
    
    try {
      // Fetch all metrics in parallel for better performance
      const [pipelineMetrics, connectorMetrics, usageMetrics, systemHealth] = await Promise.all([
        this.fetchMetrics('/dashboard/pipeline_metrics'),
        this.fetchMetrics('/dashboard/connector_metrics'),
        this.fetchMetrics('/dashboard/usage_metrics'),
        this.fetchMetrics('/dashboard/system_health')
      ])
      
      // Update the UI with new data
      this.updatePipelineMetrics(pipelineMetrics.pipeline_metrics)
      this.updateConnectorMetrics(connectorMetrics.connector_metrics)
      this.updateUsageMetrics(usageMetrics.usage_metrics)
      this.updateSystemHealth(systemHealth.system_health)
      
      console.log("Dashboard metrics refreshed successfully")
      this.showSuccessNotification("Metrics updated successfully")
    } catch (error) {
      console.error("Error refreshing metrics:", error)
      this.showErrorNotification("Failed to refresh metrics")
    } finally {
      // Remove loading states
      this.removeLoadingStates()
      this.setRefreshButtonLoading(false)
    }
  }

  async fetchMetrics(url) {
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
        'X-Requested-With': 'XMLHttpRequest'
      }
    })
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }
    
    return await response.json()
  }

  addLoadingStates() {
    // Add loading overlay to metric cards
    this.metricCardTargets.forEach(card => {
      if (!card.querySelector('.metric-loading')) {
        const overlay = document.createElement('div')
        overlay.className = 'metric-loading absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center'
        overlay.innerHTML = `
          <div class="flex items-center space-x-2">
            <div class="w-4 h-4 border-2 border-indigo-600 border-t-transparent rounded-full animate-spin"></div>
            <span class="text-sm text-gray-600">Loading...</span>
          </div>
        `
        card.style.position = 'relative'
        card.appendChild(overlay)
      }
    })

    // Add loading state to system health section
    const systemHealthSection = document.querySelector('[data-dashboard-target="systemHealth"]') || 
                               document.querySelector('.bg-white.rounded-lg:last-child')
    if (systemHealthSection && !systemHealthSection.querySelector('.metric-loading')) {
      const overlay = document.createElement('div')
      overlay.className = 'metric-loading absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center'
      overlay.innerHTML = `
        <div class="flex items-center space-x-2">
          <div class="w-4 h-4 border-2 border-indigo-600 border-t-transparent rounded-full animate-spin"></div>
          <span class="text-sm text-gray-600">Updating health status...</span>
        </div>
      `
      systemHealthSection.style.position = 'relative'
      systemHealthSection.appendChild(overlay)
    }
  }

  removeLoadingStates() {
    // Remove loading overlays
    document.querySelectorAll('.metric-loading').forEach(overlay => {
      overlay.remove()
    })
  }

  updatePipelineMetrics(data) {
    // Update pipeline count
    const totalPipelinesEl = document.querySelector('[data-metrics-metric-value="total_pipelines"]')
    if (totalPipelinesEl && data.total_pipelines !== undefined) {
      totalPipelinesEl.textContent = data.total_pipelines
      totalPipelinesEl.setAttribute('data-metrics-current-value-value', data.total_pipelines)
    }

    // Update active pipelines count
    const activePipelinesEl = document.querySelector('[data-metrics-metric-value="total_pipelines"]')?.parentElement?.querySelector('.text-green-600')
    if (activePipelinesEl && data.active_pipelines !== undefined) {
      activePipelinesEl.textContent = `${data.active_pipelines} active`
    }

    // Update success rate
    const successRateEl = document.querySelector('[data-metrics-metric-value="success_rate"]')
    if (successRateEl && data.avg_success_rate !== undefined) {
      const percentage = (data.avg_success_rate * 100).toFixed(1)
      successRateEl.textContent = `${percentage}%`
      successRateEl.setAttribute('data-metrics-current-value-value', percentage)
    }
  }

  updateConnectorMetrics(data) {
    // Update connector counts in the connections metric card
    const connectionsTotal = document.querySelector('.bg-blue-100')?.closest('.bg-white')?.querySelector('.text-2xl')
    if (connectionsTotal && data.total !== undefined) {
      connectionsTotal.textContent = data.total
    }

    const connectionsHealthy = document.querySelector('.bg-blue-100')?.closest('.bg-white')?.querySelector('.text-green-600')
    if (connectionsHealthy && data.healthy !== undefined) {
      connectionsHealthy.textContent = `${data.healthy} healthy`
    }
  }

  updateUsageMetrics(data) {
    // Update data processed metric
    const dataProcessedEl = document.querySelector('.bg-purple-100')?.closest('.bg-white')?.querySelector('.text-2xl')
    if (dataProcessedEl && data.data_processed_mb !== undefined) {
      // Format the number for display
      const formatted = this.formatNumber(data.data_processed_mb)
      dataProcessedEl.textContent = formatted
    }
  }

  updateSystemHealth(data) {
    // Update system health indicators
    if (data.overall_status) {
      const statusEl = document.querySelector('.text-green-600.font-medium')
      const statusDotEl = document.querySelector('.bg-green-400.rounded-full')
      
      if (statusEl && statusDotEl) {
        const statusText = data.overall_status.charAt(0).toUpperCase() + data.overall_status.slice(1)
        statusEl.textContent = statusText
        
        // Update status dot color based on status
        statusDotEl.className = statusDotEl.className.replace(/bg-\w+-\d+/, this.getStatusColor(data.overall_status))
      }
    }

    // Update uptime percentage
    if (data.uptime_percentage) {
      const uptimeEl = document.querySelector('.text-sm.font-medium.text-gray-900')
      if (uptimeEl && uptimeEl.textContent.includes('%')) {
        uptimeEl.textContent = `${data.uptime_percentage}%`
      }
    }

    // Update response time
    if (data.response_time_ms) {
      const responseEls = document.querySelectorAll('.text-sm.font-medium.text-gray-900')
      const responseTimeEl = Array.from(responseEls).find(el => el.textContent.includes('ms'))
      if (responseTimeEl) {
        responseTimeEl.textContent = `${data.response_time_ms}ms`
      }
    }
  }

  formatNumber(num) {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M'
    } else if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K'
    }
    return num.toString()
  }

  getStatusColor(status) {
    const colors = {
      'healthy': 'bg-green-400',
      'warning': 'bg-yellow-400',
      'error': 'bg-red-400',
      'critical': 'bg-red-600'
    }
    return colors[status] || 'bg-gray-400'
  }

  setRefreshButtonLoading(loading) {
    if (!this.hasRefreshButtonTarget) return
    
    const button = this.refreshButtonTarget
    const iconSvg = button.querySelector('svg')
    const textSpan = button.querySelector('text') || button.lastChild
    
    if (loading) {
      button.disabled = true
      button.classList.add('opacity-75', 'cursor-not-allowed')
      
      // Replace icon with spinner
      if (iconSvg) {
        iconSvg.outerHTML = '<div class="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin mr-2"></div>'
      }
      
      // Update text
      if (textSpan && textSpan.nodeType === Node.TEXT_NODE) {
        textSpan.textContent = 'Refreshing...'
      }
    } else {
      button.disabled = false
      button.classList.remove('opacity-75', 'cursor-not-allowed')
      
      // Restore original icon
      const spinner = button.querySelector('.animate-spin')
      if (spinner) {
        spinner.outerHTML = `
          <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"/>
          </svg>
        `
      }
      
      // Restore text
      if (textSpan && textSpan.nodeType === Node.TEXT_NODE) {
        textSpan.textContent = 'Refresh'
      }
    }
  }

  showSuccessNotification(message) {
    const toast = document.createElement('div')
    toast.className = 'fixed top-4 right-4 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded z-50'
    toast.innerHTML = `
      <div class="flex">
        <div class="py-1">
          <svg class="fill-current h-4 w-4 text-green-500 mr-2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
          </svg>
        </div>
        <div>
          <span class="font-bold">Success:</span> ${message}
        </div>
      </div>
    `
    
    document.body.appendChild(toast)
    
    // Auto-remove after 3 seconds (shorter for success)
    setTimeout(() => {
      toast.remove()
    }, 3000)
  }

  showErrorNotification(message) {
    // Create a simple toast notification
    const toast = document.createElement('div')
    toast.className = 'fixed top-4 right-4 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded z-50'
    toast.innerHTML = `
      <div class="flex">
        <div class="py-1">
          <svg class="fill-current h-4 w-4 text-red-500 mr-2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20">
            <path d="M2.93 17.07A10 10 0 1 1 17.07 2.93 10 10 0 0 1 2.93 17.07zm12.73-1.41A8 8 0 1 0 4.34 4.34a8 8 0 0 0 11.32 11.32zM9 11V9h2v6H9v-4zm0-6h2v2H9V5z"/>
          </svg>
        </div>
        <div>
          <span class="font-bold">Error:</span> ${message}
        </div>
      </div>
    `
    
    document.body.appendChild(toast)
    
    // Auto-remove after 5 seconds
    setTimeout(() => {
      toast.remove()
    }, 5000)
  }

  startAutoRefresh() {
    this.refreshTimer = setInterval(() => {
      this.refreshMetrics()
    }, this.refreshIntervalValue)
  }

  stopAutoRefresh() {
    if (this.refreshTimer) {
      clearInterval(this.refreshTimer)
      this.refreshTimer = null
    }
  }

  disconnect() {
    this.stopAutoRefresh()
    
    // Clean up event listeners
    this.metricCardTargets.forEach(card => {
      card.removeEventListener("mouseenter", this.handleCardHover.bind(this))
      card.removeEventListener("mouseleave", this.handleCardLeave.bind(this))
      card.removeEventListener("click", this.handleCardClick.bind(this))
    })
    
    this.quickActionTargets.forEach(action => {
      action.removeEventListener("click", this.handleQuickActionClick.bind(this))
    })
  }
}

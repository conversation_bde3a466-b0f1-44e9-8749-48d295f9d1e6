import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  static targets = ["section", "toggle", "content", "icon"]

  connect() {
    console.log("✅ Simple sidebar controller connected successfully!")
    console.log("📊 Found targets:", {
      sections: this.sectionTargets.length,
      toggles: this.toggleTargets.length,
      contents: this.contentTargets.length,
      icons: this.iconTargets.length
    })
  }

  toggleSection(event) {
    event.preventDefault()
    console.log("🔄 Toggle section called!", event.currentTarget)
    
    const button = event.currentTarget
    const section = button.closest('[data-simple-sidebar-target="section"]')
    
    if (!section) {
      console.error("❌ Section not found")
      return
    }
    
    const content = section.querySelector('[data-simple-sidebar-target="content"]')
    const icon = section.querySelector('[data-simple-sidebar-target="icon"]')
    
    if (!content || !icon) {
      console.error("❌ Content or icon not found")
      return
    }
    
    const isExpanded = button.getAttribute('aria-expanded') === 'true'
    const newState = !isExpanded
    
    console.log(`📊 Toggling section from ${isExpanded} to ${newState}`)
    
    // Update ARIA
    button.setAttribute('aria-expanded', newState.toString())
    content.setAttribute('aria-hidden', (!newState).toString())
    
    // Simple toggle without animation for testing
    if (newState) {
      content.style.display = 'block'
      icon.style.transform = 'rotate(90deg)'
    } else {
      content.style.display = 'none'
      icon.style.transform = 'rotate(0deg)'
    }
    
    console.log("✅ Section toggled successfully!")
  }
}

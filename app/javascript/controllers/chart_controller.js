import { Controller } from "@hotwired/stimulus"
import ApexCharts from "apexcharts"

/**
 * Enterprise-grade Chart Controller
 * Handles ApexCharts integration with proper error handling, logging, and lifecycle management
 */
export default class extends Controller {
  static targets = ["container", "loading", "error"]
  static values = {
    type: String,
    url: String,
    data: Object,
    options: Object,
    height: { type: Number, default: 350 },
    refreshInterval: Number,
    widgetId: Number
  }

  // Enterprise logging utility
  log(level, message, data = null) {
    const timestamp = new Date().toISOString()
    const prefix = `[${timestamp}] ChartController(${this.typeValue || 'unknown'})`

    switch (level) {
      case 'info':
        console.log(`${prefix} INFO: ${message}`, data || '')
        break
      case 'warn':
        console.warn(`${prefix} WARN: ${message}`, data || '')
        break
      case 'error':
        console.error(`${prefix} ERROR: ${message}`, data || '')
        break
      case 'debug':
        if (this.application.debug) {
          console.debug(`${prefix} DEBUG: ${message}`, data || '')
        }
        break
    }
  }

  connect() {
    this.log('info', 'Controller connecting')
    this.chart = null
    this.refreshTimer = null
    this.retryCount = 0
    this.maxRetries = 3

    // Validate required dependencies
    if (!this.validateDependencies()) {
      return
    }

    // Validate required values
    if (!this.validateConfiguration()) {
      return
    }

    this.initializeChart()
  }

  disconnect() {
    this.log('info', 'Controller disconnecting')
    this.cleanup()
  }

  validateDependencies() {
    if (typeof ApexCharts === 'undefined') {
      this.log('error', 'ApexCharts library not available')
      this.showError("Chart library not loaded. Please contact support.")
      return false
    }

    this.log('debug', 'Dependencies validated successfully')
    return true
  }

  validateConfiguration() {
    if (!this.hasUrlValue && (!this.hasDataValue || Object.keys(this.dataValue || {}).length === 0)) {
      this.log('error', 'No data source configured')
      this.showError("Chart configuration error. Please contact support.")
      return false
    }

    if (!this.typeValue) {
      this.log('error', 'Chart type not specified')
      this.showError("Chart type not specified. Please contact support.")
      return false
    }

    this.log('debug', 'Configuration validated', {
      type: this.typeValue,
      url: this.urlValue,
      hasData: this.hasDataValue,
      height: this.heightValue,
      refreshInterval: this.refreshIntervalValue
    })
    return true
  }

  async initializeChart() {
    try {
      if (this.hasDataValue && Object.keys(this.dataValue).length > 0) {
        this.log('debug', 'Using provided data')
        this.renderChart(this.dataValue)
      } else if (this.hasUrlValue) {
        this.log('debug', 'Fetching data from URL')
        await this.loadChart()
      }

      this.setupRefresh()
      this.log('info', 'Chart initialized successfully')
    } catch (error) {
      this.log('error', 'Chart initialization failed', error)
      this.handleError(error)
    }
  }

  async loadChart() {
    try {
      this.showLoading()
      this.log('debug', 'Loading chart data from URL', this.urlValue)

      const response = await this.fetchWithTimeout(this.urlValue, 10000)

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const data = await response.json()
      this.log('debug', 'Chart data received', {
        labels: data.labels?.length || 0,
        datasets: data.datasets?.length || 0
      })

      this.validateChartData(data)
      this.renderChart(data)
      this.hideLoading()
      this.retryCount = 0 // Reset retry count on success

    } catch (error) {
      this.log('error', 'Failed to load chart data', error)
      this.handleError(error)
    }
  }

  async fetchWithTimeout(url, timeout = 10000) {
    const controller = new AbortController()
    const timeoutId = setTimeout(() => controller.abort(), timeout)

    try {
      const response = await fetch(url, {
        signal: controller.signal,
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
          'X-Requested-With': 'XMLHttpRequest',
          'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content')
        }
      })
      clearTimeout(timeoutId)
      return response
    } catch (error) {
      clearTimeout(timeoutId)
      if (error.name === 'AbortError') {
        throw new Error('Request timeout')
      }
      throw error
    }
  }

  validateChartData(data) {
    if (!data || typeof data !== 'object') {
      throw new Error('Invalid chart data format')
    }

    if (!Array.isArray(data.labels) || data.labels.length === 0) {
      throw new Error('Chart data missing labels')
    }

    if (!Array.isArray(data.datasets) || data.datasets.length === 0) {
      throw new Error('Chart data missing datasets')
    }

    this.log('debug', 'Chart data validation passed')
  }

  renderChart(data) {
    const options = this.buildChartOptions(data)

    // Cleanup existing chart
    if (this.chart) {
      this.chart.destroy()
      this.chart = null
    }

    try {
      this.chart = new ApexCharts(this.containerTarget, options)
      this.chart.render()
      this.hideLoading()
      this.log('info', 'Chart rendered successfully')
    } catch (error) {
      this.log('error', 'Chart rendering failed', error)
      throw new Error(`Chart rendering failed: ${error.message}`)
    }
  }

  buildChartOptions(data) {
    const baseOptions = {
      chart: {
        type: this.typeValue || 'line',
        height: this.heightValue,
        toolbar: {
          show: true,
          tools: {
            download: true,
            selection: true,
            zoom: true,
            zoomin: true,
            zoomout: true,
            pan: true,
            reset: true
          }
        },
        animations: {
          enabled: true,
          easing: 'easeinout',
          speed: 800,
          animateGradually: {
            enabled: true,
            delay: 150
          },
          dynamicAnimation: {
            enabled: true,
            speed: 350
          }
        }
      },
      series: this.formatSeriesData(data),
      xaxis: {
        categories: data.labels || [],
        labels: {
          rotate: -45,
          rotateAlways: false,
          hideOverlappingLabels: true
        }
      },
      yaxis: {
        title: {
          text: data.yAxisTitle || ''
        }
      },
      dataLabels: {
        enabled: false
      },
      stroke: {
        curve: 'smooth',
        width: 2
      },
      title: {
        text: data.title || '',
        align: 'left'
      },
      grid: {
        borderColor: '#e7e7e7',
        row: {
          colors: ['#f3f3f3', 'transparent'],
          opacity: 0.5
        }
      },
      legend: {
        position: 'bottom',
        horizontalAlign: 'center'
      },
      responsive: [{
        breakpoint: 480,
        options: {
          chart: {
            width: '100%'
          },
          legend: {
            position: 'bottom'
          }
        }
      }],
      theme: {
        mode: this.getThemeMode()
      }
    }

    // Merge with custom options
    return this.deepMerge(baseOptions, this.optionsValue || {})
  }

  formatSeriesData(data) {
    if (data.series) {
      return data.series
    }
    
    if (data.datasets) {
      // Convert Chart.js format to ApexCharts format
      return data.datasets.map(dataset => ({
        name: dataset.label,
        data: dataset.data,
        color: dataset.borderColor || dataset.backgroundColor
      }))
    }
    
    // Simple data array
    if (Array.isArray(data.data)) {
      return [{
        name: data.name || 'Series',
        data: data.data
      }]
    }
    
    return []
  }

  showLoading() {
    if (this.hasLoadingTarget) {
      this.loadingTarget.classList.remove('hidden')
    }
    if (this.hasContainerTarget) {
      this.containerTarget.classList.add('opacity-50')
    }
  }

  hideLoading() {
    if (this.hasLoadingTarget) {
      this.loadingTarget.classList.add('hidden')
    }
    if (this.hasContainerTarget) {
      this.containerTarget.classList.remove('opacity-50')
    }
  }

  showError(message) {
    if (this.hasErrorTarget) {
      this.errorTarget.textContent = message
      this.errorTarget.classList.remove('hidden')
    }
    if (this.hasContainerTarget) {
      this.containerTarget.classList.add('hidden')
    }
  }

  hideError() {
    if (this.hasErrorTarget) {
      this.errorTarget.classList.add('hidden')
    }
    if (this.hasContainerTarget) {
      this.containerTarget.classList.remove('hidden')
    }
  }

  refresh() {
    if (this.hasUrlValue) {
      this.fetchAndRender()
    }
  }

  startAutoRefresh() {
    this.stopAutoRefresh()
    this.refreshTimer = setInterval(() => {
      this.refresh()
    }, this.refreshIntervalValue * 1000)
  }

  stopAutoRefresh() {
    if (this.refreshTimer) {
      clearInterval(this.refreshTimer)
      this.refreshTimer = null
    }
  }

  updateData(newData) {
    if (this.chart) {
      this.chart.updateSeries(this.formatSeriesData(newData))
    } else {
      this.renderChart(newData)
    }
  }

  changeType(event) {
    const newType = event.currentTarget.dataset.chartType
    if (newType && this.chart) {
      this.typeValue = newType
      this.chart.updateOptions({
        chart: { type: newType }
      })
    }
  }

  export(format = 'png') {
    if (this.chart) {
      if (format === 'svg') {
        this.chart.exports.exportToSVG()
      } else if (format === 'csv') {
        this.chart.exports.exportToCSV()
      } else {
        this.chart.exports.exportToPNG()
      }
    }
  }

  getThemeMode() {
    // Check if dark mode is enabled
    if (document.documentElement.classList.contains('dark')) {
      return 'dark'
    }
    return 'light'
  }

  deepMerge(target, source) {
    const output = Object.assign({}, target)
    if (isObject(target) && isObject(source)) {
      Object.keys(source).forEach(key => {
        if (isObject(source[key])) {
          if (!(key in target))
            Object.assign(output, { [key]: source[key] })
          else
            output[key] = this.deepMerge(target[key], source[key])
        } else {
          Object.assign(output, { [key]: source[key] })
        }
      })
    }
    return output
  }

  // Enterprise methods
  setupRefresh() {
    if (this.hasRefreshIntervalValue && this.refreshIntervalValue > 0) {
      this.log('debug', `Setting up auto-refresh every ${this.refreshIntervalValue} seconds`)
      this.refreshTimer = setInterval(() => {
        this.log('debug', 'Auto-refreshing chart data')
        this.loadChart()
      }, this.refreshIntervalValue * 1000)
    }
  }

  handleError(error) {
    if (this.retryCount < this.maxRetries) {
      this.retryCount++
      this.log('warn', `Retrying chart load (attempt ${this.retryCount}/${this.maxRetries})`)

      setTimeout(() => {
        this.loadChart()
      }, 2000 * this.retryCount) // Exponential backoff
    } else {
      this.log('error', 'Max retries exceeded, showing error state')
      this.showError(`Unable to load chart data: ${error.message}`)
    }
  }

  cleanup() {
    if (this.chart) {
      this.chart.destroy()
      this.chart = null
    }

    if (this.refreshTimer) {
      clearInterval(this.refreshTimer)
      this.refreshTimer = null
    }

    this.log('debug', 'Cleanup completed')
  }

  // Public method for manual refresh
  refresh() {
    this.log('info', 'Manual refresh triggered')
    this.loadChart()
  }
}

function isObject(item) {
  return item && typeof item === 'object' && !Array.isArray(item)
}

import consumer from "./consumer"

// This channel is primarily handled by the dashboard_websocket_controller.js
// but this file can be used for additional channel-specific logic if needed

const dashboardMetricsChannel = {
  // Method to manually subscribe to dashboard metrics for a specific account
  subscribeForAccount: (accountId, callbacks = {}) => {
    return consumer.subscriptions.create(
      { channel: "DashboardMetricsChannel", account_id: accountId },
      {
        connected() {
          console.log(`Connected to dashboard metrics for account ${accountId}`)
          if (callbacks.connected) callbacks.connected()
        },

        disconnected() {
          console.log(`Disconnected from dashboard metrics for account ${accountId}`)
          if (callbacks.disconnected) callbacks.disconnected()
        },

        received(data) {
          console.log("Dashboard metrics received:", data)
          if (callbacks.received) callbacks.received(data)
        },

        rejected() {
          console.log(`Rejected from dashboard metrics for account ${accountId}`)
          if (callbacks.rejected) callbacks.rejected()
        }
      }
    )
  },

  // Global method to broadcast metrics update events
  broadcastMetricsUpdate: (data) => {
    document.dispatchEvent(new CustomEvent('dashboardMetricsUpdate', {
      detail: data
    }))
  }
}

export default dashboardMetricsChannel
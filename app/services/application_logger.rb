# frozen_string_literal: true

class ApplicationLogger
  include Singleton

  SEVERITY_LEVELS = {
    debug: 0,
    info: 1,
    warn: 2,
    error: 3,
    fatal: 4
  }.freeze

  def self.method_missing(method_name, *args, **kwargs, &block)
    if SEVERITY_LEVELS.key?(method_name)
      instance.log(method_name, *args, **kwargs, &block)
    elsif instance.respond_to?(method_name)
      instance.send(method_name, *args, **kwargs, &block)
    else
      super
    end
  end

  def self.respond_to_missing?(method_name, include_private = false)
    SEVERITY_LEVELS.key?(method_name) || instance.respond_to?(method_name) || super
  end
  
  # Explicitly delegate commonly used methods
  def self.log_performance(*args, **kwargs)
    instance.log_performance(*args, **kwargs)
  end
  
  def self.log_pipeline_execution(*args, **kwargs)
    instance.log_pipeline_execution(*args, **kwargs)
  end
  
  def self.log_connector_activity(*args, **kwargs)
    instance.log_connector_activity(*args, **kwargs)
  end
  
  def self.log_user_activity(*args, **kwargs)
    instance.log_user_activity(*args, **kwargs)
  end
  
  def self.log_security_event(*args, **kwargs)
    instance.log_security_event(*args, **kwargs)
  end

  def initialize
    @logger = Rails.logger
  end

  def log(level, message = nil, context: {}, exception: nil, &block)
    return unless should_log?(level)

    log_entry = build_log_entry(level, message, context, exception, &block)
    
    case Rails.env
    when 'production'
      @logger.send(level, log_entry.to_json)
    else
      @logger.send(level, format_development_log(log_entry))
    end
  end

  # Pipeline execution logging
  def log_pipeline_execution(pipeline, status, context = {})
    log(:info, "Pipeline execution #{status}", context: {
      pipeline_id: pipeline.id,
      pipeline_name: pipeline.name,
      account_id: pipeline.account_id,
      status: status,
      **context
    })
  end

  # Data connector logging
  def log_connector_activity(connector, action, context = {})
    log(:info, "Data connector #{action}", context: {
      connector_id: connector.id,
      connector_name: connector.name,
      connector_type: connector.connector_type,
      account_id: connector.account_id,
      action: action,
      **context
    })
  end

  # User activity logging
  def log_user_activity(user, action, context = {})
    log(:info, "User activity: #{action}", context: {
      user_id: user.id,
      user_email: user.email,
      account_id: user.account_id,
      action: action,
      **context
    })
  end

  # Performance logging
  def log_performance(operation, duration, context = {})
    level = duration > 5000 ? :warn : :info
    log(level, "Performance: #{operation}", context: {
      operation: operation,
      duration_ms: duration.round(2),
      **context
    })
  end

  # Security event logging
  def log_security_event(event_type, context = {})
    log(:warn, "Security event: #{event_type}", context: {
      event_type: event_type,
      timestamp: Time.current.iso8601,
      **context
    })
  end

  private

  def should_log?(level)
    SEVERITY_LEVELS[level] >= SEVERITY_LEVELS[Rails.logger.level] rescue true
  end

  def build_log_entry(level, message, context, exception, &block)
    timestamp = Time.current.iso8601
    
    entry = {
      timestamp: timestamp,
      level: level.to_s.upcase,
      message: message || (block ? block.call : "Log entry"),
      context: context || {}
    }

    # Add environment context
    entry[:context][:environment] = Rails.env
    entry[:context][:application] = "DataReflow"
    
    # Add request context if available
    if defined?(Current) && Current.user
      entry[:context][:user_id] = Current.user.id
      entry[:context][:user_email] = Current.user.email
    end
    
    if defined?(Current) && Current.account
      entry[:context][:account_id] = Current.account.id
      entry[:context][:account_subdomain] = Current.account.subdomain
    end

    # Add exception details if provided
    if exception
      entry[:exception] = {
        class: exception.class.name,
        message: exception.message,
        backtrace: exception.backtrace&.first(10)
      }
    end

    entry
  end

  def format_development_log(entry)
    timestamp = entry[:timestamp]
    level = entry[:level]
    message = entry[:message]
    
    formatted = "[#{timestamp}] #{level}: #{message}"
    
    if entry[:context] && !entry[:context].empty?
      formatted += "\n  Context: #{entry[:context].inspect}"
    end
    
    if entry[:exception]
      formatted += "\n  Exception: #{entry[:exception][:class]} - #{entry[:exception][:message]}"
      if entry[:exception][:backtrace]
        formatted += "\n  Backtrace:\n    #{entry[:exception][:backtrace].join("\n    ")}"
      end
    end
    
    formatted
  end
end
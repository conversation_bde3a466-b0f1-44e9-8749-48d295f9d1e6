class WidgetDataService
  def initialize(account)
    @account = account
  end
  
  def execute_query(query_name, options = {})
    date_range = options[:date_range] || 30.days
    
    case query_name
    when 'pipeline_performance_over_time'
      pipeline_performance_data(date_range)
    when 'top_performing_pipelines'
      top_pipelines_data(options[:limit] || 5)
    when 'hourly_execution_heatmap'
      execution_heatmap_data(date_range)
    when 'top_pipeline_errors'
      error_analysis_data(options[:limit] || 10)
    when 'pipeline_executions_over_time'
      execution_trends_data(date_range)
    when 'data_quality_trends'
      quality_trends_data(date_range)
    when 'resource_utilization'
      resource_usage_data(date_range)
    when 'traffic_sources'
      traffic_sources_data(options)
    when 'campaign_performance'
      campaign_performance_data(options)
    when 'top_campaigns'
      top_campaigns_data(options)
    else
      { error: "Unknown query: #{query_name}" }
    end
  end
  
  def fetch_metric(metric_name, options = {})
    case metric_name
    when 'total_pipelines'
      { value: @account.pipelines.count }
    when 'active_pipelines'
      { value: @account.pipelines.active.count }
    when 'success_rate'
      { value: calculate_success_rate }
    when 'data_quality_score'
      { value: calculate_quality_score }
    when 'total_executions'
      { value: @account.pipeline_executions.count }
    when 'data_processed'
      { value: calculate_data_processed }
    when 'avg_execution_time'
      { value: calculate_avg_execution_time }
    when 'error_rate'
      { value: calculate_error_rate }
    when 'total_revenue'
      calculate_total_revenue
    when 'conversion_rate'
      calculate_conversion_rate
    else
      { error: "Unknown metric: #{metric_name}" }
    end
  end
  
  def pipeline_data(pipeline, options = {})
    date_range = options[:date_range] || 30.days
    
    {
      labels: date_labels(date_range),
      datasets: [
        {
          label: 'Successful',
          data: pipeline_success_by_day(pipeline, date_range),
          borderColor: '#10B981',
          backgroundColor: 'rgba(16, 185, 129, 0.1)'
        },
        {
          label: 'Failed',
          data: pipeline_failures_by_day(pipeline, date_range),
          borderColor: '#EF4444',
          backgroundColor: 'rgba(239, 68, 68, 0.1)'
        }
      ]
    }
  end
  
  def custom_data(endpoint, options = {})
    # This would call an internal API endpoint for custom data
    # For now, return sample data
    {
      labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May'],
      datasets: [{
        label: 'Custom Data',
        data: [65, 59, 80, 81, 56]
      }]
    }
  end
  
  private
  
  def pipeline_performance_data(date_range)
    end_date = Date.current
    start_date = end_date - date_range
    
    executions = @account.pipeline_executions
                         .joins(:pipeline)
                         .where(started_at: start_date..end_date)
                         .group_by_day(:started_at)
    
    success_data = executions.where(status: 'success').count
    failure_data = executions.where(status: 'failed').count
    
    {
      labels: (start_date..end_date).map { |d| d.strftime('%m/%d') },
      datasets: [
        {
          label: 'Successful Executions',
          data: (start_date..end_date).map { |d| success_data[d] || 0 },
          borderColor: '#10B981',
          backgroundColor: 'rgba(16, 185, 129, 0.1)',
          tension: 0.4
        },
        {
          label: 'Failed Executions',
          data: (start_date..end_date).map { |d| failure_data[d] || 0 },
          borderColor: '#EF4444',
          backgroundColor: 'rgba(239, 68, 68, 0.1)',
          tension: 0.4
        }
      ]
    }
  end
  
  def top_pipelines_data(limit)
    pipelines = @account.pipelines
                       .left_joins(:pipeline_executions)
                       .group('pipelines.id')
                       .order('COUNT(pipeline_executions.id) DESC')
                       .limit(limit)
                       .pluck('pipelines.name', 'COUNT(pipeline_executions.id)')
    
    {
      labels: pipelines.map(&:first),
      datasets: [{
        label: 'Execution Count',
        data: pipelines.map(&:second),
        backgroundColor: [
          '#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6'
        ]
      }]
    }
  end
  
  def execution_heatmap_data(date_range)
    end_date = Date.current
    start_date = end_date - date_range
    
    # Generate hourly execution data
    hourly_data = @account.pipeline_executions
                          .where(started_at: start_date..end_date)
                          .group("EXTRACT(HOUR FROM started_at)")
                          .group("EXTRACT(DOW FROM started_at)")
                          .count
    
    # Format for heatmap
    days = %w[Sun Mon Tue Wed Thu Fri Sat]
    hours = (0..23).to_a
    
    data = []
    days.each_with_index do |day, day_idx|
      hours.each do |hour|
        count = hourly_data[[hour.to_f, day_idx.to_f]] || 0
        data << {
          x: hour.to_s,
          y: day,
          value: count
        }
      end
    end
    
    {
      data: data,
      categories: {
        x: hours.map { |h| "#{h}:00" },
        y: days
      }
    }
  end
  
  def error_analysis_data(limit)
    errors = @account.pipeline_executions
                    .where(status: 'failed')
                    .where.not(error_message: nil)
                    .group(:error_message)
                    .order('count_all DESC')
                    .limit(limit)
                    .count
    
    {
      columns: ['Error Message', 'Count', 'Percentage'],
      rows: errors.map do |message, count|
        total = @account.pipeline_executions.where(status: 'failed').count
        percentage = total > 0 ? (count.to_f / total * 100).round(1) : 0
        [
          message.truncate(100),
          count,
          "#{percentage}%"
        ]
      end
    }
  end
  
  def execution_trends_data(date_range)
    pipeline_performance_data(date_range)
  end
  
  def quality_trends_data(date_range)
    # This would integrate with DataQualityGuardService
    # For now, return sample data
    end_date = Date.current
    start_date = end_date - date_range
    
    {
      labels: (start_date..end_date).map { |d| d.strftime('%m/%d') },
      datasets: [{
        label: 'Data Quality Score',
        data: (start_date..end_date).map { |_| rand(70..95) },
        borderColor: '#3B82F6',
        backgroundColor: 'rgba(59, 130, 246, 0.1)',
        tension: 0.4
      }]
    }
  end
  
  def resource_usage_data(date_range)
    # This would track actual resource usage
    # For now, return sample data
    {
      labels: ['CPU', 'Memory', 'Storage', 'Network'],
      datasets: [{
        label: 'Current Usage %',
        data: [45, 67, 23, 89],
        backgroundColor: ['#3B82F6', '#10B981', '#F59E0B', '#8B5CF6']
      }]
    }
  end
  
  def calculate_success_rate
    total = @account.pipeline_executions.count
    return 0 if total.zero?
    
    successful = @account.pipeline_executions.where(status: 'success').count
    (successful.to_f / total * 100).round(1)
  end
  
  def calculate_quality_score
    # Integrate with DataQualityGuardService
    # For now, return a sample score
    rand(75..95)
  end
  
  def calculate_data_processed
    @account.pipeline_executions.sum(:records_processed) || 0
  end
  
  def calculate_avg_execution_time
    avg = @account.pipeline_executions.average(:execution_time)
    avg ? avg.round(2) : 0
  end
  
  def calculate_error_rate
    total = @account.pipeline_executions.count
    return 0 if total.zero?
    
    failed = @account.pipeline_executions.where(status: 'failed').count
    (failed.to_f / total * 100).round(1)
  end
  
  def date_labels(date_range)
    end_date = Date.current
    start_date = end_date - date_range
    (start_date..end_date).map { |d| d.strftime('%m/%d') }
  end
  
  def pipeline_success_by_day(pipeline, date_range)
    end_date = Date.current
    start_date = end_date - date_range
    
    data = pipeline.pipeline_executions
                  .where(started_at: start_date..end_date, status: 'success')
                  .group_by_day(:started_at)
                  .count
    
    (start_date..end_date).map { |d| data[d] || 0 }
  end
  
  def pipeline_failures_by_day(pipeline, date_range)
    end_date = Date.current
    start_date = end_date - date_range
    
    data = pipeline.pipeline_executions
                  .where(started_at: start_date..end_date, status: 'failed')
                  .group_by_day(:started_at)
                  .count
    
    (start_date..end_date).map { |d| data[d] || 0 }
  end

  # Marketing Dashboard Specific Methods
  def traffic_sources_data(options = {})
    {
      labels: ['Organic Search', 'Direct', 'Social Media', 'Email', 'Referral'],
      datasets: [{
        label: 'Traffic Sources',
        data: [35, 25, 20, 15, 5],
        backgroundColor: [
          '#4F46E5',
          '#7C3AED', 
          '#EC4899',
          '#F59E0B',
          '#10B981'
        ]
      }]
    }
  end

  def campaign_performance_data(options = {})
    {
      labels: ['Week 1', 'Week 2', 'Week 3', 'Week 4', 'Week 5', 'Week 6', 'Week 7'],
      datasets: [
        {
          label: 'Impressions',
          data: [12000, 19000, 15000, 25000, 22000, 30000, 28000],
          borderColor: '#3B82F6',
          backgroundColor: 'rgba(59, 130, 246, 0.1)',
          yAxisID: 'y'
        },
        {
          label: 'Clicks',
          data: [400, 600, 500, 800, 700, 950, 900],
          borderColor: '#10B981',
          backgroundColor: 'rgba(16, 185, 129, 0.1)',
          yAxisID: 'y1'
        }
      ]
    }
  end

  def top_campaigns_data(options = {})
    {
      columns: ['Campaign', 'Clicks', 'Conversions', 'ROI'],
      rows: [
        ['Summer Sale 2024', '2,543', '127', '+245%'],
        ['Product Launch', '1,892', '89', '+189%'],
        ['Holiday Special', '1,456', '73', '+156%'],
        ['Flash Sale Friday', '987', '52', '+134%'],
        ['New Year Promo', '756', '41', '+98%']
      ]
    }
  end

  def calculate_total_revenue
    # Mock data for now - would connect to actual revenue data
    {
      value: 125430,
      format: 'currency',
      trend: 12.5,
      trend_direction: 'up',
      label: 'Total Revenue'
    }
  end

  def calculate_conversion_rate
    # Mock data for now - would calculate from actual conversions
    {
      value: 68.5,
      format: 'percentage',
      min: 0,
      max: 100,
      trend: 3.2,
      trend_direction: 'up',
      label: 'Conversion Rate'
    }
  end
end

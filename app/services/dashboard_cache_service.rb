class DashboardCacheService
  def self.invalidate_for_account(account)
    new(account).invalidate_all
  end

  def self.invalidate_pipeline_metrics(account)
    new(account).invalidate_pipeline_metrics
  end

  def self.invalidate_connector_metrics(account)
    new(account).invalidate_connector_metrics
  end

  def self.invalidate_usage_metrics(account)
    new(account).invalidate_usage_metrics
  end

  def initialize(account)
    @account = account
  end

  def invalidate_all
    invalidate_pipeline_metrics
    invalidate_connector_metrics
    invalidate_usage_metrics
    Rails.logger.info "Dashboard cache invalidated for account #{@account.id}"
  end

  def invalidate_pipeline_metrics
    Rails.cache.delete("dashboard/pipeline_metrics/#{@account.id}")
  end

  def invalidate_connector_metrics
    Rails.cache.delete("dashboard/connector_metrics/#{@account.id}")
  end

  def invalidate_usage_metrics
    Rails.cache.delete("dashboard/usage_metrics/#{@account.id}")
  end

  def warm_cache
    # Warm up the cache by pre-loading metrics
    Rails.cache.fetch("dashboard/pipeline_metrics/#{@account.id}", expires_in: 5.minutes) do
      Pipeline.performance_summary(@account) rescue {}
    end

    Rails.cache.fetch("dashboard/connector_metrics/#{@account.id}", expires_in: 10.minutes) do
      DataConnector.health_summary(@account) rescue {}
    end

    Rails.cache.fetch("dashboard/usage_metrics/#{@account.id}", expires_in: 15.minutes) do
      {
        data_processed_mb: 0,
        api_requests: 0,
        storage_used_mb: 0,
        monthly_executions: 0
      }
    end
  end

  private

  attr_reader :account
end

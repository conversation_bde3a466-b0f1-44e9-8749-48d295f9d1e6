module DataConnectors
  class CsvConnector
    require "csv"

    attr_reader :config, :errors

    def initialize(config = {})
      @config = config
      @errors = []
    end

    def test_connection
      begin
        if config["source_type"] == "file_upload"
          test_file_upload_connection
        elsif config["source_type"] == "url"
          test_url_connection
        elsif config["source_type"] == "s3"
          test_s3_connection
        else
          add_error("Unknown source type: #{config['source_type']}")
          false
        end
      rescue StandardError => e
        add_error("Connection test failed: #{e.message}")
        false
      end
    end

    def read_data(options = {})
      begin
        case config["source_type"]
        when "file_upload"
          read_from_file(options)
        when "url"
          read_from_url(options)
        when "s3"
          read_from_s3(options)
        else
          raise "Unknown source type: #{config['source_type']}"
        end
      rescue StandardError => e
        add_error("Failed to read data: #{e.message}")
        nil
      end
    end

    def write_data(data, options = {})
      begin
        case config["destination_type"]
        when "file"
          write_to_file(data, options)
        when "s3"
          write_to_s3(data, options)
        else
          raise "Unknown destination type: #{config['destination_type']}"
        end
      rescue StandardError => e
        add_error("Failed to write data: #{e.message}")
        false
      end
    end

    def validate_config
      required_fields = case config["source_type"]
      when "file_upload"
                         [ "file_path" ]
      when "url"
                         [ "url" ]
      when "s3"
                         [ "bucket", "key", "region" ]
      else
                         []
      end

      missing_fields = required_fields - config.keys
      if missing_fields.any?
        add_error("Missing required configuration: #{missing_fields.join(', ')}")
        return false
      end

      true
    end

    def sample_data(limit = 10)
      data = read_data(limit: limit)
      return nil unless data

      {
        headers: data[:headers],
        rows: data[:rows].first(limit),
        total_rows: data[:rows].size,
        sample_size: [ data[:rows].size, limit ].min
      }
    end

    def schema
      data = sample_data(100)
      return nil unless data

      headers = data[:headers]
      sample_rows = data[:rows]

      schema_info = headers.map do |header|
        column_data = sample_rows.map { |row| row[headers.index(header)] }.compact

        {
          name: header,
          type: infer_data_type(column_data),
          nullable: column_data.size < sample_rows.size,
          sample_values: column_data.first(3)
        }
      end

      {
        columns: schema_info,
        row_count_estimate: data[:total_rows]
      }
    end

    private

    def test_file_upload_connection
      file_path = config["file_path"]

      if file_path.blank?
        add_error("File path is required")
        return false
      end

      unless File.exist?(file_path)
        add_error("File not found: #{file_path}")
        return false
      end

      unless File.readable?(file_path)
        add_error("File is not readable: #{file_path}")
        return false
      end

      # Try to parse the first few lines
      CSV.foreach(file_path, headers: true).first(5)
      true
    rescue CSV::MalformedCSVError => e
      add_error("Invalid CSV format: #{e.message}")
      false
    end

    def test_url_connection
      require "net/http"
      require "uri"

      url = config["url"]

      if url.blank?
        add_error("URL is required")
        return false
      end

      uri = URI.parse(url)
      response = Net::HTTP.get_response(uri)

      unless response.is_a?(Net::HTTPSuccess)
        add_error("Failed to fetch URL: HTTP #{response.code}")
        return false
      end

      # Try to parse the content as CSV
      CSV.parse(response.body, headers: true).first(5)
      true
    rescue URI::InvalidURIError => e
      add_error("Invalid URL: #{e.message}")
      false
    rescue CSV::MalformedCSVError => e
      add_error("Invalid CSV format: #{e.message}")
      false
    end

    def test_s3_connection
      # This would require AWS SDK integration
      # For now, return a placeholder
      add_error("S3 connector not yet implemented")
      false
    end

    def read_from_file(options = {})
      file_path = config["file_path"]
      headers = []
      rows = []

      CSV.foreach(file_path, headers: true) do |row|
        headers = row.headers if headers.empty?
        rows << row.fields

        break if options[:limit] && rows.size >= options[:limit]
      end

      {
        headers: headers,
        rows: rows,
        metadata: {
          source: "file",
          file_path: file_path,
          row_count: rows.size
        }
      }
    end

    def read_from_url(options = {})
      require "net/http"
      require "uri"

      uri = URI.parse(config["url"])
      response = Net::HTTP.get_response(uri)

      headers = []
      rows = []

      CSV.parse(response.body, headers: true) do |row|
        headers = row.headers if headers.empty?
        rows << row.fields

        break if options[:limit] && rows.size >= options[:limit]
      end

      {
        headers: headers,
        rows: rows,
        metadata: {
          source: "url",
          url: config["url"],
          row_count: rows.size
        }
      }
    end

    def read_from_s3(options = {})
      # Placeholder for S3 implementation
      add_error("S3 connector not yet implemented")
      nil
    end

    def write_to_file(data, options = {})
      file_path = options[:file_path] || config["destination_path"]

      CSV.open(file_path, "wb") do |csv|
        csv << data[:headers] if data[:headers]

        data[:rows].each do |row|
          csv << row
        end
      end

      {
        success: true,
        file_path: file_path,
        rows_written: data[:rows].size
      }
    end

    def write_to_s3(data, options = {})
      # Placeholder for S3 implementation
      add_error("S3 destination not yet implemented")
      false
    end

    def infer_data_type(values)
      return "string" if values.empty?

      # Check if all values are integers
      if values.all? { |v| v.to_s =~ /^\d+$/ }
        return "integer"
      end

      # Check if all values are floats
      if values.all? { |v| v.to_s =~ /^\d+\.\d+$/ }
        return "float"
      end

      # Check if all values are booleans
      bool_values = [ "true", "false", "1", "0", "yes", "no", "t", "f" ]
      if values.all? { |v| bool_values.include?(v.to_s.downcase) }
        return "boolean"
      end

      # Check if all values are dates
      begin
        values.each { |v| Date.parse(v.to_s) }
        return "date"
      rescue
        # Not all dates
      end

      # Default to string
      "string"
    end

    def add_error(message)
      @errors << message
    end
  end
end

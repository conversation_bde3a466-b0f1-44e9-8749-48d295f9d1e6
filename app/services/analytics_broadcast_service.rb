class AnalyticsBroadcastService
  def self.broadcast_chart_update(account_id, chart_type, chart_data, date_range = '30_days')
    new(account_id).broadcast_chart_update(chart_type, chart_data, date_range)
  end

  def self.broadcast_metrics_update(account_id, metrics)
    new(account_id).broadcast_metrics_update(metrics)
  end

  def self.broadcast_alert(account_id, message, type = 'info', duration = 5000)
    new(account_id).broadcast_alert(message, type, duration)
  end

  def initialize(account_id)
    @account_id = account_id
    @channel_name = "analytics_#{account_id}"
  end

  def broadcast_chart_update(chart_type, chart_data, date_range = '30_days')
    Rails.logger.info "Broadcasting chart update: #{chart_type} for account: #{@account_id}"

    # Render the chart partial
    html = ApplicationController.render(
      partial: 'analytics/chart',
      locals: { 
        chart_type: chart_type, 
        chart_data: chart_data, 
        date_range: date_range 
      }
    )

    # Broadcast via ActionCable
    ActionCable.server.broadcast(@channel_name, {
      type: 'chart_update',
      chart_type: chart_type,
      html: html,
      timestamp: Time.current.iso8601
    })

    # Also broadcast via Turbo Stream for logged-in users
    broadcast_turbo_stream_update(chart_type, chart_data, date_range)
  end

  def broadcast_metrics_update(metrics)
    Rails.logger.info "Broadcasting metrics update for account: #{@account_id}"

    ActionCable.server.broadcast(@channel_name, {
      type: 'metrics_update',
      metrics: metrics,
      timestamp: Time.current.iso8601
    })
  end

  def broadcast_alert(message, type = 'info', duration = 5000)
    Rails.logger.info "Broadcasting alert for account: #{@account_id}: #{message}"

    ActionCable.server.broadcast(@channel_name, {
      type: 'alert',
      message: message,
      alert_type: type,
      duration: duration,
      timestamp: Time.current.iso8601
    })
  end

  def broadcast_system_status(status, details = {})
    Rails.logger.info "Broadcasting system status for account: #{@account_id}: #{status}"

    ActionCable.server.broadcast(@channel_name, {
      type: 'system_status',
      status: status,
      details: details,
      timestamp: Time.current.iso8601
    })
  end

  # Broadcast real-time pipeline execution updates
  def broadcast_pipeline_execution(pipeline_execution)
    Rails.logger.info "Broadcasting pipeline execution update: #{pipeline_execution.id}"

    ActionCable.server.broadcast(@channel_name, {
      type: 'pipeline_execution_update',
      execution: {
        id: pipeline_execution.id,
        pipeline_name: pipeline_execution.pipeline.name,
        status: pipeline_execution.status,
        progress: pipeline_execution.progress_percentage,
        started_at: pipeline_execution.started_at,
        completed_at: pipeline_execution.completed_at
      },
      timestamp: Time.current.iso8601
    })
  end

  private

  def broadcast_turbo_stream_update(chart_type, chart_data, date_range)
    # Find users with active sessions for this account
    account = Account.find(@account_id)
    account.users.each do |user|
      # Broadcast Turbo Stream update
      Turbo::StreamsChannel.broadcast_replace_to(
        "analytics_#{user.id}",
        target: "#{chart_type}-chart",
        partial: 'analytics/chart',
        locals: { 
          chart_type: chart_type, 
          chart_data: chart_data, 
          date_range: date_range 
        }
      )
    end
  rescue => e
    Rails.logger.error "Error broadcasting Turbo Stream update: #{e.message}"
  end
end
# frozen_string_literal: true

class ErrorNotificationService
  include Singleton

  # Error severity levels
  SEVERITY_LEVELS = {
    low: 1,
    medium: 2,
    high: 3,
    critical: 4
  }.freeze

  # Notification channels
  NOTIFICATION_CHANNELS = {
    email: :send_email_notification,
    slack: :send_slack_notification,
    webhook: :send_webhook_notification,
    log: :send_log_notification
  }.freeze

  def self.notify(error_context, severity: :medium, channels: [:log])
    instance.send_notifications(error_context, severity, channels)
  end

  def self.notify_critical(error_context, channels: [:log, :email])
    instance.send_notifications(error_context, :critical, channels)
  end

  def send_notifications(error_context, severity, channels)
    return unless should_notify?(severity)

    notification_data = build_notification_data(error_context, severity)
    
    channels.each do |channel|
      send_notification(channel, notification_data) if NOTIFICATION_CHANNELS.key?(channel)
    end
  rescue => e
    Rails.logger.error "Failed to send error notifications: #{e.message}"
  end

  private

  def should_notify?(severity)
    # Don't spam notifications in test environment
    return false if Rails.env.test?
    
    # Only notify for medium severity and above in development
    return SEVERITY_LEVELS[severity] >= SEVERITY_LEVELS[:medium] if Rails.env.development?
    
    # Notify for all levels in production
    true
  end

  def build_notification_data(error_context, severity)
    {
      severity: severity,
      error_id: error_context[:error_id] || SecureRandom.uuid,
      timestamp: error_context[:timestamp] || Time.current.iso8601,
      environment: Rails.env,
      application: "DataReflow",
      error_class: error_context[:exception_class],
      error_message: error_context[:exception_message],
      controller: error_context[:controller],
      action: error_context[:action],
      url: error_context[:url],
      method: error_context[:method],
      user_id: error_context[:user_id],
      user_email: error_context[:user_email],
      account_id: error_context[:account_id],
      account_subdomain: error_context[:account_subdomain],
      ip_address: error_context[:ip_address],
      user_agent: error_context[:user_agent],
      backtrace: error_context[:backtrace]&.first(5), # Limit backtrace for notifications
      context: error_context.except(:backtrace)
    }
  end

  def send_notification(channel, notification_data)
    method_name = NOTIFICATION_CHANNELS[channel]
    send(method_name, notification_data) if respond_to?(method_name, true)
  end

  def send_log_notification(notification_data)
    severity_indicator = case notification_data[:severity]
    when :critical then "🚨"
    when :high then "⚠️"
    when :medium then "⚡"
    else "ℹ️"
    end

    Rails.logger.error "#{severity_indicator} ERROR NOTIFICATION #{severity_indicator}"
    Rails.logger.error "Error ID: #{notification_data[:error_id]}"
    Rails.logger.error "Severity: #{notification_data[:severity].to_s.upcase}"
    Rails.logger.error "Exception: #{notification_data[:error_class]} - #{notification_data[:error_message]}"
    Rails.logger.error "Location: #{notification_data[:controller]}##{notification_data[:action]}"
    Rails.logger.error "URL: #{notification_data[:method]} #{notification_data[:url]}"
    
    if notification_data[:user_email]
      Rails.logger.error "User: #{notification_data[:user_email]} (ID: #{notification_data[:user_id]})"
    end
    
    if notification_data[:account_subdomain]
      Rails.logger.error "Account: #{notification_data[:account_subdomain]} (ID: #{notification_data[:account_id]})"
    end
  end

  def send_email_notification(notification_data)
    # Only send emails for high/critical errors to avoid spam
    return unless [:high, :critical].include?(notification_data[:severity])

    # Skip if no admin emails configured
    admin_emails = Rails.application.credentials.dig(:admin_emails)
    return if admin_emails.blank?

    begin
      ErrorNotificationMailer.critical_error(notification_data, admin_emails).deliver_now
    rescue => e
      Rails.logger.error "Failed to send error notification email: #{e.message}"
    end
  end

  def send_slack_notification(notification_data)
    slack_webhook_url = Rails.application.credentials.dig(:slack, :error_webhook_url)
    return unless slack_webhook_url.present?

    color = case notification_data[:severity]
    when :critical then "danger"
    when :high then "warning" 
    when :medium then "warning"
    else "good"
    end

    slack_payload = {
      username: "DataReflow Errors",
      channel: "#alerts",
      attachments: [{
        color: color,
        title: "🚨 Application Error - #{notification_data[:severity].to_s.upcase}",
        fields: [
          {
            title: "Error",
            value: "#{notification_data[:error_class]}: #{notification_data[:error_message]}",
            short: false
          },
          {
            title: "Location",
            value: "#{notification_data[:controller]}##{notification_data[:action]}",
            short: true
          },
          {
            title: "Environment",
            value: notification_data[:environment],
            short: true
          },
          {
            title: "Error ID",
            value: notification_data[:error_id],
            short: true
          },
          {
            title: "Time",
            value: notification_data[:timestamp],
            short: true
          }
        ]
      }]
    }

    # Add user info if available
    if notification_data[:user_email]
      slack_payload[:attachments][0][:fields] << {
        title: "User",
        value: notification_data[:user_email],
        short: true
      }
    end

    send_webhook_request(slack_webhook_url, slack_payload)
  end

  def send_webhook_notification(notification_data)
    webhook_url = Rails.application.credentials.dig(:error_webhook_url)
    return unless webhook_url.present?

    send_webhook_request(webhook_url, notification_data)
  end

  def send_webhook_request(url, payload)
    require 'net/http'
    require 'uri'
    require 'json'

    uri = URI(url)
    http = Net::HTTP.new(uri.host, uri.port)
    http.use_ssl = uri.scheme == 'https'
    http.read_timeout = 10

    request = Net::HTTP::Post.new(uri)
    request['Content-Type'] = 'application/json'
    request.body = payload.to_json

    response = http.request(request)
    
    unless response.code.start_with?('2')
      Rails.logger.error "Webhook notification failed: #{response.code} #{response.body}"
    end
  rescue => e
    Rails.logger.error "Failed to send webhook notification: #{e.message}"
  end
end
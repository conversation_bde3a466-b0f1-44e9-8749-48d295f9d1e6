<% content_for :page_title, "Create New Pipeline" %>

<div class="pipeline-form-container">
  <!-- Header -->
  <header style="margin-bottom: var(--space-6);">
    <div class="flex items-center justify-between">
      <div>
        <h1 style="font-size: var(--text-3xl); font-weight: var(--font-weight-bold); color: var(--color-gray-900); margin: 0 0 var(--space-2) 0;">
          Create New Pipeline
        </h1>
        <p style="font-size: var(--text-lg); color: var(--color-gray-600); margin: 0;">
          Set up a new data pipeline to connect your data sources and destinations.
        </p>
      </div>
      <div>
        <%= link_to pipelines_path, class: "btn btn-secondary" do %>
          <svg style="width: 1rem; height: 1rem; margin-right: var(--space-2);" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"/>
          </svg>
          Back to Pipelines
        <% end %>
      </div>
    </div>
  </header>

  <!-- Pipeline Form -->
  <div class="card">
    <div class="card-body">
      <%= form_with model: [@pipeline], local: true, class: "pipeline-form", data: { controller: "pipeline-form" } do |form| %>
        
        <!-- Basic Information -->
        <section style="margin-bottom: var(--space-8);">
          <h3 style="font-size: var(--text-xl); font-weight: var(--font-weight-semibold); color: var(--color-gray-900); margin: 0 0 var(--space-4) 0;">
            Basic Information
          </h3>
          
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- Pipeline Name -->
            <div class="form-group">
              <%= form.label :name, "Pipeline Name", class: "form-label" %>
              <%= form.text_field :name, 
                    class: "form-input", 
                    placeholder: "e.g., Customer Data Sync",
                    required: true,
                    data: { action: "input->pipeline-form#validateName" } %>
              <p class="form-help">Choose a descriptive name for your pipeline</p>
            </div>

            <!-- Pipeline Description -->
            <div class="form-group">
              <%= form.label :description, "Description", class: "form-label" %>
              <%= form.text_area :description, 
                    class: "form-input", 
                    rows: 3,
                    placeholder: "Describe what this pipeline does..." %>
              <p class="form-help">Optional description of the pipeline's purpose</p>
            </div>
          </div>
        </section>

        <!-- Source Configuration -->
        <section style="margin-bottom: var(--space-8);">
          <h3 style="font-size: var(--text-xl); font-weight: var(--font-weight-semibold); color: var(--color-gray-900); margin: 0 0 var(--space-4) 0;">
            Data Source
          </h3>
          
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- Source Type -->
            <div class="form-group">
              <%= form.label :source_type, "Source Type", class: "form-label" %>
              <%= form.select :source_type, 
                    options_for_select([
                      ['Select a source type', ''],
                      ['Database', 'database'],
                      ['API', 'api'],
                      ['File Upload', 'file'],
                      ['Webhook', 'webhook'],
                      ['Cloud Storage', 'cloud_storage']
                    ], params[:source_type]), 
                    {}, 
                    { class: "form-select", required: true, data: { action: "change->pipeline-form#updateSourceConfig" } } %>
            </div>

            <!-- Source Connection -->
            <div class="form-group">
              <%= form.label :source_connector_id, "Source Connection", class: "form-label" %>
              <%= form.select :source_connector_id, 
                    options_from_collection_for_select(@data_connectors, :id, :name, @pipeline.source_connector_id),
                    { prompt: "Select a connection" },
                    { class: "form-select" } %>
              <p class="form-help">
                <%= link_to "Create new connection", new_data_connector_path, class: "text-primary hover:underline" %>
              </p>
            </div>
          </div>
        </section>

        <!-- Destination Configuration -->
        <section style="margin-bottom: var(--space-8);">
          <h3 style="font-size: var(--text-xl); font-weight: var(--font-weight-semibold); color: var(--color-gray-900); margin: 0 0 var(--space-4) 0;">
            Data Destination
          </h3>
          
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- Destination Type -->
            <div class="form-group">
              <%= form.label :destination_type, "Destination Type", class: "form-label" %>
              <%= form.select :destination_type, 
                    options_for_select([
                      ['Select a destination type', ''],
                      ['Database', 'database'],
                      ['Data Warehouse', 'warehouse'],
                      ['API', 'api'],
                      ['File Export', 'file'],
                      ['Cloud Storage', 'cloud_storage']
                    ], params[:destination_type]), 
                    {}, 
                    { class: "form-select", required: true, data: { action: "change->pipeline-form#updateDestinationConfig" } } %>
            </div>

            <!-- Destination Connection -->
            <div class="form-group">
              <%= form.label :destination_connector_id, "Destination Connection", class: "form-label" %>
              <%= form.select :destination_connector_id, 
                    options_from_collection_for_select(@data_connectors, :id, :name, @pipeline.destination_connector_id),
                    { prompt: "Select a connection" },
                    { class: "form-select" } %>
              <p class="form-help">
                <%= link_to "Create new connection", new_data_connector_path, class: "text-primary hover:underline" %>
              </p>
            </div>
          </div>
        </section>

        <!-- Schedule Configuration -->
        <section style="margin-bottom: var(--space-8);">
          <h3 style="font-size: var(--text-xl); font-weight: var(--font-weight-semibold); color: var(--color-gray-900); margin: 0 0 var(--space-4) 0;">
            Schedule
          </h3>
          
          <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <!-- Schedule Type -->
            <div class="form-group">
              <%= form.label :schedule_type, "Schedule Type", class: "form-label" %>
              <%= form.select :schedule_type, 
                    options_for_select([
                      ['Manual', 'manual'],
                      ['Interval', 'interval'],
                      ['Cron', 'cron'],
                      ['Real-time', 'realtime']
                    ], 'manual'), 
                    {}, 
                    { class: "form-select", data: { action: "change->pipeline-form#updateScheduleConfig" } } %>
            </div>

            <!-- Schedule Interval -->
            <div class="form-group" data-pipeline-form-target="intervalConfig" style="display: none;">
              <%= form.label :schedule_interval, "Interval (minutes)", class: "form-label" %>
              <%= form.number_field :schedule_interval, 
                    class: "form-input", 
                    min: 1,
                    placeholder: "60" %>
            </div>

            <!-- Cron Expression -->
            <div class="form-group" data-pipeline-form-target="cronConfig" style="display: none;">
              <%= form.label :schedule_cron, "Cron Expression", class: "form-label" %>
              <%= form.text_field :schedule_cron, 
                    class: "form-input", 
                    placeholder: "0 0 * * *" %>
              <p class="form-help">Daily at midnight</p>
            </div>
          </div>
        </section>

        <!-- Advanced Options -->
        <section style="margin-bottom: var(--space-8);">
          <h3 style="font-size: var(--text-xl); font-weight: var(--font-weight-semibold); color: var(--color-gray-900); margin: 0 0 var(--space-4) 0;">
            Advanced Options
          </h3>
          
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- Auto Start -->
            <div class="form-group">
              <div class="flex items-center">
                <%= form.check_box :auto_start, class: "form-checkbox" %>
                <%= form.label :auto_start, "Auto-start pipeline", class: "form-label-inline" %>
              </div>
              <p class="form-help">Automatically start the pipeline after creation</p>
            </div>

            <!-- Error Handling -->
            <div class="form-group">
              <%= form.label :error_handling, "Error Handling", class: "form-label" %>
              <%= form.select :error_handling, 
                    options_for_select([
                      ['Stop on error', 'stop'],
                      ['Continue on error', 'continue'],
                      ['Retry on error', 'retry']
                    ], 'stop'), 
                    {}, 
                    { class: "form-select" } %>
            </div>
          </div>
        </section>

        <!-- Form Actions -->
        <div class="flex items-center justify-between" style="padding-top: var(--space-6); border-top: 1px solid var(--color-gray-200);">
          <div class="flex items-center gap-4">
            <%= form.submit "Create Pipeline", class: "btn btn-primary", data: { disable_with: "Creating..." } %>
            <%= link_to "Cancel", pipelines_path, class: "btn btn-secondary" %>
          </div>
          
          <div class="text-sm text-gray-500">
            <span data-pipeline-form-target="validationStatus">Fill in required fields to create pipeline</span>
          </div>
        </div>

      <% end %>
    </div>
  </div>
</div>

<!-- Pipeline Form JavaScript Controller -->
<script>
document.addEventListener('DOMContentLoaded', function() {
  // Simple form validation and dynamic field updates
  const form = document.querySelector('.pipeline-form');
  if (form) {
    // Add any additional form logic here
    console.log('Pipeline form loaded');
  }
});
</script>

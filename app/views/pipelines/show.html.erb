<% content_for :page_title, @pipeline.name %>

<div class="pipeline-detail-container">
  <!-- Header -->
  <header style="margin-bottom: var(--space-6);">
    <div class="flex items-center justify-between">
      <div class="flex items-center gap-4">
        <div>
          <h1 style="font-size: var(--text-3xl); font-weight: var(--font-weight-bold); color: var(--color-gray-900); margin: 0 0 var(--space-2) 0;">
            <%= @pipeline.name %>
          </h1>
          <div class="flex items-center gap-3">
            <span class="badge badge-<%= @pipeline.status == 'active' ? 'success' : 'secondary' %>">
              <%= @pipeline.status.humanize %>
            </span>
            <span style="font-size: var(--text-sm); color: var(--color-gray-600);">
              Created <%= time_ago_in_words(@pipeline.created_at) %> ago
            </span>
          </div>
        </div>
      </div>
      
      <div class="flex items-center gap-3">
        <!-- Execute Pipeline -->
        <%= button_to execute_pipeline_path(@pipeline), 
              method: :post, 
              class: "btn btn-primary",
              data: { confirm: "Execute this pipeline now?" } do %>
          <svg style="width: 1rem; height: 1rem; margin-right: var(--space-2);" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h1m4 0h1m6-10V4a2 2 0 00-2-2H5a2 2 0 00-2 2v16l4-2 4 2 4-2 4 2V4z"/>
          </svg>
          Execute Now
        <% end %>
        
        <!-- Edit Pipeline -->
        <%= link_to edit_pipeline_path(@pipeline), class: "btn btn-secondary" do %>
          <svg style="width: 1rem; height: 1rem; margin-right: var(--space-2);" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"/>
          </svg>
          Edit
        <% end %>
        
        <!-- Back to Pipelines -->
        <%= link_to pipelines_path, class: "btn btn-outline" do %>
          <svg style="width: 1rem; height: 1rem; margin-right: var(--space-2);" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"/>
          </svg>
          Back
        <% end %>
      </div>
    </div>
  </header>

  <!-- Pipeline Overview -->
  <div class="grid grid-cols-1 lg:grid-cols-3 gap-6" style="margin-bottom: var(--space-8);">
    <!-- Pipeline Info -->
    <div class="lg:col-span-2">
      <div class="card">
        <div class="card-header">
          <h3 class="card-title">Pipeline Configuration</h3>
        </div>
        <div class="card-body">
          <% if @pipeline.description.present? %>
            <p style="margin-bottom: var(--space-4); color: var(--color-gray-600);">
              <%= @pipeline.description %>
            </p>
          <% end %>
          
          <dl class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <dt style="font-size: var(--text-sm); font-weight: var(--font-weight-medium); color: var(--color-gray-500);">Source Type</dt>
              <dd style="margin-top: var(--space-1); font-size: var(--text-sm); color: var(--color-gray-900);">
                <%= @pipeline.source_type&.humanize || 'Not configured' %>
              </dd>
            </div>
            <div>
              <dt style="font-size: var(--text-sm); font-weight: var(--font-weight-medium); color: var(--color-gray-500);">Destination Type</dt>
              <dd style="margin-top: var(--space-1); font-size: var(--text-sm); color: var(--color-gray-900);">
                <%= @pipeline.destination_type&.humanize || 'Not configured' %>
              </dd>
            </div>
            <div>
              <dt style="font-size: var(--text-sm); font-weight: var(--font-weight-medium); color: var(--color-gray-500);">Schedule</dt>
              <dd style="margin-top: var(--space-1); font-size: var(--text-sm); color: var(--color-gray-900);">
                <%= @pipeline.schedule_type&.humanize || 'Manual' %>
              </dd>
            </div>
            <div>
              <dt style="font-size: var(--text-sm); font-weight: var(--font-weight-medium); color: var(--color-gray-500);">Created By</dt>
              <dd style="margin-top: var(--space-1); font-size: var(--text-sm); color: var(--color-gray-900);">
                <%= @pipeline.created_by&.full_name || 'Unknown' %>
              </dd>
            </div>
          </dl>
        </div>
      </div>
    </div>

    <!-- Quick Stats -->
    <div>
      <div class="card">
        <div class="card-header">
          <h3 class="card-title">Quick Stats</h3>
        </div>
        <div class="card-body">
          <div class="space-y-4">
            <div>
              <div style="font-size: var(--text-2xl); font-weight: var(--font-weight-bold); color: var(--color-gray-900);">
                <%= @pipeline.pipeline_executions.count %>
              </div>
              <div style="font-size: var(--text-sm); color: var(--color-gray-500);">Total Executions</div>
            </div>
            
            <div>
              <div style="font-size: var(--text-2xl); font-weight: var(--font-weight-bold); color: var(--color-green-600);">
                <%= @pipeline.pipeline_executions.where(status: 'success').count %>
              </div>
              <div style="font-size: var(--text-sm); color: var(--color-gray-500);">Successful</div>
            </div>
            
            <div>
              <div style="font-size: var(--text-2xl); font-weight: var(--font-weight-bold); color: var(--color-red-600);">
                <%= @pipeline.pipeline_executions.where(status: 'failed').count %>
              </div>
              <div style="font-size: var(--text-sm); color: var(--color-gray-500);">Failed</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Recent Executions -->
  <section>
    <div class="flex items-center justify-between" style="margin-bottom: var(--space-4);">
      <h2 style="font-size: var(--text-xl); font-weight: var(--font-weight-semibold); color: var(--color-gray-900);">
        Recent Executions
      </h2>
      <%= link_to pipeline_executions_path(pipeline_id: @pipeline.id), class: "btn btn-outline btn-sm" do %>
        View All
      <% end %>
    </div>

    <div class="card">
      <div class="card-body" style="padding: 0;">
        <% if @recent_executions.any? %>
          <div class="overflow-x-auto">
            <table class="table">
              <thead>
                <tr>
                  <th>Status</th>
                  <th>Started</th>
                  <th>Duration</th>
                  <th>Records</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                <% @recent_executions.each do |execution| %>
                  <tr>
                    <td>
                      <span class="badge badge-<%= execution.status == 'success' ? 'success' : execution.status == 'failed' ? 'danger' : 'warning' %>">
                        <%= execution.status.humanize %>
                      </span>
                    </td>
                    <td>
                      <div style="font-size: var(--text-sm); color: var(--color-gray-900);">
                        <%= execution.started_at&.strftime("%b %d, %Y") %>
                      </div>
                      <div style="font-size: var(--text-xs); color: var(--color-gray-500);">
                        <%= execution.started_at&.strftime("%I:%M %p") %>
                      </div>
                    </td>
                    <td>
                      <% if execution.execution_time %>
                        <%= pluralize(execution.execution_time.to_i, 'second') %>
                      <% else %>
                        -
                      <% end %>
                    </td>
                    <td>
                      <div style="font-size: var(--text-sm);">
                        <span style="color: var(--color-green-600);"><%= execution.records_success || 0 %></span> /
                        <span style="color: var(--color-gray-600);"><%= execution.records_processed || 0 %></span>
                      </div>
                    </td>
                    <td>
                      <%= link_to pipeline_execution_path(execution), 
                            class: "btn btn-outline btn-sm" do %>
                        View
                      <% end %>
                    </td>
                  </tr>
                <% end %>
              </tbody>
            </table>
          </div>
        <% else %>
          <div class="text-center" style="padding: var(--space-8);">
            <svg style="width: 3rem; height: 3rem; margin: 0 auto var(--space-4) auto; color: var(--color-gray-400);" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"/>
            </svg>
            <h3 style="font-size: var(--text-lg); font-weight: var(--font-weight-medium); color: var(--color-gray-900); margin: 0 0 var(--space-2) 0;">
              No executions yet
            </h3>
            <p style="color: var(--color-gray-500); margin: 0;">
              This pipeline hasn't been executed yet. Click "Execute Now" to run it.
            </p>
          </div>
        <% end %>
      </div>
    </div>
  </section>
</div>

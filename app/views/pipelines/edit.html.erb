<% content_for :page_title, "Edit #{@pipeline.name}" %>

<div class="pipeline-form-container">
  <!-- Header -->
  <header style="margin-bottom: var(--space-6);">
    <div class="flex items-center justify-between">
      <div>
        <h1 style="font-size: var(--text-3xl); font-weight: var(--font-weight-bold); color: var(--color-gray-900); margin: 0 0 var(--space-2) 0;">
          Edit Pipeline
        </h1>
        <p style="font-size: var(--text-lg); color: var(--color-gray-600); margin: 0;">
          Update the configuration for "<%= @pipeline.name %>"
        </p>
      </div>
      <div class="flex items-center gap-3">
        <%= link_to @pipeline, class: "btn btn-outline" do %>
          <svg style="width: 1rem; height: 1rem; margin-right: var(--space-2);" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"/>
          </svg>
          View Pipeline
        <% end %>
        <%= link_to pipelines_path, class: "btn btn-secondary" do %>
          <svg style="width: 1rem; height: 1rem; margin-right: var(--space-2);" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"/>
          </svg>
          Back to Pipelines
        <% end %>
      </div>
    </div>
  </header>

  <!-- Pipeline Form -->
  <div class="card">
    <div class="card-body">
      <%= form_with model: @pipeline, local: true, class: "pipeline-form", data: { controller: "pipeline-form" } do |form| %>
        
        <!-- Basic Information -->
        <section style="margin-bottom: var(--space-8);">
          <h3 style="font-size: var(--text-xl); font-weight: var(--font-weight-semibold); color: var(--color-gray-900); margin: 0 0 var(--space-4) 0;">
            Basic Information
          </h3>
          
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- Pipeline Name -->
            <div class="form-group">
              <%= form.label :name, "Pipeline Name", class: "form-label" %>
              <%= form.text_field :name, 
                    class: "form-input", 
                    required: true,
                    data: { action: "input->pipeline-form#validateName" } %>
              <p class="form-help">Choose a descriptive name for your pipeline</p>
            </div>

            <!-- Pipeline Description -->
            <div class="form-group">
              <%= form.label :description, "Description", class: "form-label" %>
              <%= form.text_area :description, 
                    class: "form-input", 
                    rows: 3 %>
              <p class="form-help">Optional description of the pipeline's purpose</p>
            </div>
          </div>
        </section>

        <!-- Status -->
        <section style="margin-bottom: var(--space-8);">
          <h3 style="font-size: var(--text-xl); font-weight: var(--font-weight-semibold); color: var(--color-gray-900); margin: 0 0 var(--space-4) 0;">
            Status
          </h3>
          
          <div class="form-group">
            <%= form.label :status, "Pipeline Status", class: "form-label" %>
            <%= form.select :status, 
                  options_for_select([
                    ['Active', 'active'],
                    ['Paused', 'paused'],
                    ['Disabled', 'disabled']
                  ], @pipeline.status), 
                  {}, 
                  { class: "form-select" } %>
            <p class="form-help">Control whether this pipeline can be executed</p>
          </div>
        </section>

        <!-- Source Configuration -->
        <section style="margin-bottom: var(--space-8);">
          <h3 style="font-size: var(--text-xl); font-weight: var(--font-weight-semibold); color: var(--color-gray-900); margin: 0 0 var(--space-4) 0;">
            Data Source
          </h3>
          
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- Source Type -->
            <div class="form-group">
              <%= form.label :source_type, "Source Type", class: "form-label" %>
              <%= form.select :source_type, 
                    options_for_select([
                      ['Database', 'database'],
                      ['API', 'api'],
                      ['File Upload', 'file'],
                      ['Webhook', 'webhook'],
                      ['Cloud Storage', 'cloud_storage']
                    ], @pipeline.source_type), 
                    { prompt: "Select a source type" }, 
                    { class: "form-select", required: true, data: { action: "change->pipeline-form#updateSourceConfig" } } %>
            </div>

            <!-- Source Connection -->
            <div class="form-group">
              <%= form.label :source_connector_id, "Source Connection", class: "form-label" %>
              <%= form.select :source_connector_id, 
                    options_from_collection_for_select(@data_connectors, :id, :name, @pipeline.source_connector_id),
                    { prompt: "Select a connection" },
                    { class: "form-select" } %>
              <p class="form-help">
                <%= link_to "Manage connections", data_connectors_path, class: "text-primary hover:underline" %>
              </p>
            </div>
          </div>
        </section>

        <!-- Destination Configuration -->
        <section style="margin-bottom: var(--space-8);">
          <h3 style="font-size: var(--text-xl); font-weight: var(--font-weight-semibold); color: var(--color-gray-900); margin: 0 0 var(--space-4) 0;">
            Data Destination
          </h3>
          
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- Destination Type -->
            <div class="form-group">
              <%= form.label :destination_type, "Destination Type", class: "form-label" %>
              <%= form.select :destination_type, 
                    options_for_select([
                      ['Database', 'database'],
                      ['Data Warehouse', 'warehouse'],
                      ['API', 'api'],
                      ['File Export', 'file'],
                      ['Cloud Storage', 'cloud_storage']
                    ], @pipeline.destination_type), 
                    { prompt: "Select a destination type" }, 
                    { class: "form-select", required: true, data: { action: "change->pipeline-form#updateDestinationConfig" } } %>
            </div>

            <!-- Destination Connection -->
            <div class="form-group">
              <%= form.label :destination_connector_id, "Destination Connection", class: "form-label" %>
              <%= form.select :destination_connector_id, 
                    options_from_collection_for_select(@data_connectors, :id, :name, @pipeline.destination_connector_id),
                    { prompt: "Select a connection" },
                    { class: "form-select" } %>
              <p class="form-help">
                <%= link_to "Manage connections", data_connectors_path, class: "text-primary hover:underline" %>
              </p>
            </div>
          </div>
        </section>

        <!-- Schedule Configuration -->
        <section style="margin-bottom: var(--space-8);">
          <h3 style="font-size: var(--text-xl); font-weight: var(--font-weight-semibold); color: var(--color-gray-900); margin: 0 0 var(--space-4) 0;">
            Schedule
          </h3>
          
          <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <!-- Schedule Type -->
            <div class="form-group">
              <%= form.label :schedule_type, "Schedule Type", class: "form-label" %>
              <%= form.select :schedule_type, 
                    options_for_select([
                      ['Manual', 'manual'],
                      ['Interval', 'interval'],
                      ['Cron', 'cron'],
                      ['Real-time', 'realtime']
                    ], @pipeline.schedule_type || 'manual'), 
                    {}, 
                    { class: "form-select", data: { action: "change->pipeline-form#updateScheduleConfig" } } %>
            </div>

            <!-- Schedule Interval -->
            <div class="form-group" data-pipeline-form-target="intervalConfig" style="<%= @pipeline.schedule_type == 'interval' ? '' : 'display: none;' %>">
              <%= form.label :schedule_interval, "Interval (minutes)", class: "form-label" %>
              <%= form.number_field :schedule_interval, 
                    class: "form-input", 
                    min: 1,
                    value: @pipeline.schedule_interval %>
            </div>

            <!-- Cron Expression -->
            <div class="form-group" data-pipeline-form-target="cronConfig" style="<%= @pipeline.schedule_type == 'cron' ? '' : 'display: none;' %>">
              <%= form.label :schedule_cron, "Cron Expression", class: "form-label" %>
              <%= form.text_field :schedule_cron, 
                    class: "form-input",
                    value: @pipeline.schedule_cron %>
              <p class="form-help">e.g., "0 0 * * *" for daily at midnight</p>
            </div>
          </div>
        </section>

        <!-- Form Actions -->
        <div class="flex items-center justify-between" style="padding-top: var(--space-6); border-top: 1px solid var(--color-gray-200);">
          <div class="flex items-center gap-4">
            <%= form.submit "Update Pipeline", class: "btn btn-primary", data: { disable_with: "Updating..." } %>
            <%= link_to @pipeline, class: "btn btn-secondary" do %>
              Cancel
            <% end %>
          </div>
          
          <div class="flex items-center gap-4">
            <%= link_to @pipeline, 
                  method: :delete, 
                  class: "btn btn-danger btn-outline",
                  data: { 
                    confirm: "Are you sure you want to delete this pipeline? This action cannot be undone.",
                    disable_with: "Deleting..." 
                  } do %>
              <svg style="width: 1rem; height: 1rem; margin-right: var(--space-2);" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1-1H8a1 1 0 00-1 1v3M4 7h16"/>
              </svg>
              Delete Pipeline
            <% end %>
          </div>
        </div>

      <% end %>
    </div>
  </div>
</div>

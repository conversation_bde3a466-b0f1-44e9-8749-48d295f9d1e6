<% content_for :page_title, "Sidebar Test" %>

<div class="content-section">
  <div class="content-header">
    <h1 class="page-title">Sidebar Functionality Test</h1>
    <p class="page-description">Use this page to test if the sidebar collapsible sections are working properly.</p>
  </div>

  <div class="content-body">
    <div class="card">
      <div class="card-header">
        <h2 class="card-title">Testing Instructions</h2>
      </div>
      <div class="card-body">
        <ol style="list-style: decimal; margin-left: 2rem; line-height: 1.6;">
          <li><strong>Check the sidebar on the left</strong> - You should see collapsible sections like "Overview", "Data Management", etc.</li>
          <li><strong>Click on section headers</strong> - Each section header should be clickable and toggle the content below it.</li>
          <li><strong>Look for animations</strong> - The sections should expand/collapse with smooth animations.</li>
          <li><strong>Check the browser console</strong> - Open Developer Tools (F12) and look for debug messages starting with 🎯 or 🔄.</li>
          <li><strong>Test keyboard navigation</strong> - Try using Tab to focus on section headers and Enter/Space to toggle them.</li>
        </ol>

        <div style="margin-top: 2rem; padding: 1rem; background-color: var(--color-warning-50); border-left: 4px solid var(--color-warning-500); border-radius: var(--radius-md);">
          <h3 style="font-weight: var(--font-weight-semibold); color: var(--color-warning-800); margin-bottom: 0.5rem;">Expected Behavior:</h3>
          <ul style="color: var(--color-warning-700); line-height: 1.5;">
            <li>• "Overview" and "Data Management" sections should be expanded by default</li>
            <li>• Other sections should be collapsed initially</li>
            <li>• Clicking a section header should toggle its content</li>
            <li>• The arrow icon should rotate when toggling</li>
            <li>• State should persist when navigating between pages</li>
          </ul>
        </div>

        <div style="margin-top: 2rem; padding: 1rem; background-color: var(--color-info-50); border-left: 4px solid var(--color-info-500); border-radius: var(--radius-md);">
          <h3 style="font-weight: var(--font-weight-semibold); color: var(--color-info-800); margin-bottom: 0.5rem;">Troubleshooting:</h3>
          <ul style="color: var(--color-info-700); line-height: 1.5;">
            <li>• If nothing happens when clicking, check the browser console for JavaScript errors</li>
            <li>• If you see "Sidebar controller connected" in the console, the controller is loading properly</li>
            <li>• If sections don't animate, there might be a CSS issue</li>
            <li>• Try refreshing the page if the behavior seems inconsistent</li>
          </ul>
        </div>

        <!-- Direct Test Button -->
        <div style="margin-top: 2rem; padding: 1rem; background-color: var(--color-success-50); border-left: 4px solid var(--color-success-500); border-radius: var(--radius-md);">
          <h3 style="font-weight: var(--font-weight-semibold); color: var(--color-success-800); margin-bottom: 0.5rem;">Direct Test:</h3>
          <button id="direct-test-btn"
                  style="padding: 0.5rem 1rem; background-color: var(--color-primary-500); color: white; border: none; border-radius: var(--radius-md); cursor: pointer;">
            Test Sidebar Controller Directly
          </button>
          <p style="color: var(--color-success-700); margin-top: 0.5rem; font-size: 0.875rem;">
            This button will directly call the sidebar controller to test if it's working.
          </p>
        </div>
      </div>
    </div>

    <!-- Debug Information Card -->
    <div class="card" style="margin-top: 2rem;">
      <div class="card-header">
        <h2 class="card-title">Debug Information</h2>
      </div>
      <div class="card-body">
        <div id="debug-info" style="font-family: monospace; font-size: 0.875rem; background-color: var(--color-gray-50); padding: 1rem; border-radius: var(--radius-md);">
          <p>Loading debug information...</p>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
  const debugInfo = document.getElementById('debug-info');
  
  function updateDebugInfo() {
    const sidebarElement = document.querySelector('[data-controller="sidebar"]');
    const toggleButtons = document.querySelectorAll('[data-action*="sidebar#toggleSection"]');
    const sections = document.querySelectorAll('[data-sidebar-target="section"]');
    
    let info = `
<strong>🔍 Sidebar Debug Information:</strong><br><br>
<strong>Sidebar Controller:</strong> ${sidebarElement ? '✅ Found' : '❌ Not Found'}<br>
<strong>Toggle Buttons:</strong> ${toggleButtons.length} found<br>
<strong>Sections:</strong> ${sections.length} found<br><br>
    `;
    
    if (sidebarElement) {
      info += `<strong>Controller Data:</strong><br>`;
      info += `- Default Expanded: ${sidebarElement.dataset.sidebarDefaultExpandedValue || 'Not set'}<br>`;
      info += `- Persist State: ${sidebarElement.dataset.sidebarPersistStateValue || 'Not set'}<br><br>`;
    }
    
    info += `<strong>Sections Details:</strong><br>`;
    sections.forEach((section, index) => {
      const sectionId = section.dataset.sectionId;
      const toggle = section.querySelector('[data-sidebar-target="toggle"]');
      const content = section.querySelector('[data-sidebar-target="content"]');
      const isExpanded = toggle ? toggle.getAttribute('aria-expanded') : 'unknown';
      
      info += `${index + 1}. ${sectionId}: ${isExpanded === 'true' ? 'Expanded' : 'Collapsed'}<br>`;
    });
    
    info += `<br><strong>Stimulus Controllers Registered:</strong><br>`;
    if (window.Stimulus && window.Stimulus.application) {
      const controllers = Object.keys(window.Stimulus.application.router.modulesByIdentifier);
      info += controllers.join(', ') || 'None found';
    } else {
      info += 'Stimulus not available';
    }
    
    debugInfo.innerHTML = info;
  }
  
  // Update debug info immediately and every 2 seconds
  updateDebugInfo();
  setInterval(updateDebugInfo, 2000);
  
  // Add click listeners to buttons for additional debugging
  document.querySelectorAll('[data-action*="sidebar#toggleSection"]').forEach((button, index) => {
    button.addEventListener('click', function(e) {
      console.log(`🖱️ Manual click detected on button ${index + 1}:`, e.target);
      setTimeout(updateDebugInfo, 100); // Update debug info after click
    });
  });

  // Direct test button
  const directTestBtn = document.getElementById('direct-test-btn');
  if (directTestBtn) {
    directTestBtn.addEventListener('click', function() {
      console.log('🧪 Direct test button clicked');

      // Find the sidebar controller
      const sidebarElement = document.querySelector('[data-controller="sidebar"]');
      if (sidebarElement && sidebarElement.stimulus) {
        console.log('✅ Found sidebar controller, testing...');

        // Get the first section to test
        const firstSection = sidebarElement.querySelector('[data-sidebar-target="section"]');
        if (firstSection) {
          const controller = sidebarElement.stimulus.sidebar;
          if (controller && controller.setSectionState) {
            console.log('🎯 Calling setSectionState directly...');
            const currentState = firstSection.querySelector('[data-sidebar-target="toggle"]').getAttribute('aria-expanded') === 'true';
            controller.setSectionState(firstSection, !currentState, true);
          } else {
            console.error('❌ Controller or setSectionState method not found');
          }
        }
      } else {
        console.error('❌ Sidebar controller not found or not connected');
      }
    });
  }
});
</script>

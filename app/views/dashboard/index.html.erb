<% content_for :page_title, "Dashboard" %>

<div data-controller="dashboard dashboard-websocket"
     data-dashboard-auto-refresh-value="true"
     data-dashboard-websocket-user-id-value="<%= current_user.id %>"
     data-dashboard-websocket-account-id-value="<%= current_account.id %>"
     class="dashboard-content">

<!-- Dashboard Header -->
<header style="margin-bottom: var(--space-4);">
  <div class="flex items-center justify-between">
    <div>
      <h1 style="font-size: var(--text-4xl); font-weight: var(--font-weight-bold); color: var(--color-gray-900); margin: 0 0 var(--space-2) 0;">
        Welcome back, <%= @user.full_name %>!
      </h1>
      <p style="font-size: var(--text-lg); color: var(--color-gray-600); margin: 0;">
        <% if @user.owner? %>
          Manage your team and monitor your data pipelines.
        <% elsif @user.admin? %>
          Monitor pipelines and manage team operations.
        <% elsif @user.member? %>
          Here's what's happening with your data pipelines today.
        <% else %>
          View your team's data pipeline activity.
        <% end %>
      </p>
    </div>
    <div class="flex items-center gap-4">
      <button data-action="click->dashboard#refreshMetrics"
              class="btn btn-secondary btn-icon"
              data-dashboard-target="refreshButton"
              aria-label="Refresh dashboard data">
        <svg style="width: 1.25rem; height: 1.25rem;" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"/>
        </svg>
        Refresh
      </button>
      <% if @account.subscription&.plan == 'free' %>
        <%= link_to subscription_path, class: "btn btn-primary btn-icon" do %>
          <svg style="width: 1.25rem; height: 1.25rem;" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"/>
          </svg>
          Upgrade Plan
        <% end %>
      <% end %>
    </div>
  </div>
</header>

<!-- WebSocket Connection Status -->
<div class="flex items-center justify-between" style="margin-bottom: var(--space-4); padding: var(--space-2); background-color: var(--color-gray-50); border-radius: var(--border-radius);">
  <div class="flex items-center gap-2">
    <span class="text-sm text-gray-600">Real-time updates:</span>
    <span data-websocket-status class="text-sm text-yellow-600">● Connecting...</span>
  </div>
  <div class="text-xs text-gray-500" data-dashboard-websocket-target="lastUpdated">
    <!-- Last updated timestamp will appear here -->
  </div>
</div>

<!-- Alerts Section -->
<% if @alerts&.any? %>
  <section style="margin-bottom: var(--space-8);" aria-labelledby="alerts-heading">
    <h2 id="alerts-heading" class="sr-only">System Alerts</h2>
    <div style="display: flex; flex-direction: column; gap: var(--space-3);">
      <% @alerts.each do |alert| %>
        <div class="alert alert-<%= alert[:type] %>">
          <div class="alert-icon">
            <% case alert[:type] %>
            <% when 'warning' %>
              ⚠
            <% when 'error' %>
              ✕
            <% when 'success' %>
              ✓
            <% else %>
              ℹ
            <% end %>
          </div>
          <div class="alert-content">
            <h4 class="alert-title"><%= alert[:title] %></h4>
            <p class="alert-message"><%= alert[:message] %></p>
            <% if alert[:action_url] %>
              <div style="margin-top: var(--space-2);">
                <%= link_to alert[:action_url], class: "btn btn-sm btn-outline" do %>
                  <%= alert[:action] %>
                <% end %>
              </div>
            <% end %>
          </div>
        </div>
      <% end %>
    </div>
  </section>
<% end %>

<!-- Key Metrics -->
<section style="margin-bottom: var(--space-6);" aria-labelledby="key-metrics-heading">
  <div class="flex items-center justify-between" style="margin-bottom: var(--space-4);">
    <h2 id="key-metrics-heading" style="font-size: var(--text-2xl); font-weight: var(--font-weight-semibold); color: var(--color-gray-900); margin: 0;">
      Key Metrics
    </h2>
  </div>

  <!-- Primary Metrics Grid -->
  <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6" style="margin-bottom: var(--space-6);">
    <!-- Pipelines Metric -->
    <div class="card card-hover"
         data-dashboard-target="metricCard"
         data-metric="pipelines"
         role="button"
         tabindex="0"
         aria-label="Pipelines metric: <%= @pipeline_metrics[:total_pipelines] %> total, <%= @pipeline_metrics[:active_pipelines] %> active"
         onkeydown="if(event.key==='Enter'||event.key===' '){this.click()}">
      <div class="card-body">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="feature-icon" style="background-color: var(--color-primary-100); color: var(--color-primary-600);" aria-hidden="true">
              <svg style="width: 1.5rem; height: 1.5rem;" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"/>
              </svg>
            </div>
          </div>
          <div style="margin-left: var(--space-4); flex: 1;">
            <p style="font-size: var(--text-sm); font-weight: var(--font-weight-medium); color: var(--color-gray-500); margin: 0;" id="pipelines-label">Total Pipelines</p>
            <div class="flex items-baseline" style="margin-top: var(--space-1);">
              <p style="font-size: var(--text-3xl); font-weight: var(--font-weight-bold); color: var(--color-gray-900); margin: 0;"
                 data-controller="metrics"
                 data-metrics-current-value-value="<%= @pipeline_metrics[:total_pipelines] %>"
                 data-metrics-metric-value="total_pipelines"
                 data-dashboard-websocket-target="pipelineCount"
                 aria-labelledby="pipelines-label"
                 aria-describedby="pipelines-description">
                <%= @pipeline_metrics[:total_pipelines] %>
              </p>
              <span class="badge badge-success" style="margin-left: var(--space-2);" id="pipelines-description" data-dashboard-websocket-target="activeCount">
                <%= @pipeline_metrics[:active_pipelines] %> active
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Success Rate Metric -->
    <div class="card card-hover"
         data-dashboard-target="metricCard"
         data-metric="success_rate"
         role="button"
         tabindex="0"
         aria-label="Success rate metric: <%= number_to_percentage(@pipeline_metrics[:avg_success_rate] * 100, precision: 1) %> over last 30 days"
         onkeydown="if(event.key==='Enter'||event.key===' '){this.click()}">
      <div class="card-body">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="feature-icon" style="background-color: var(--color-success-100); color: var(--color-success-600);" aria-hidden="true">
              <svg style="width: 1.5rem; height: 1.5rem;" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
              </svg>
            </div>
          </div>
          <div style="margin-left: var(--space-4); flex: 1;">
            <p style="font-size: var(--text-sm); font-weight: var(--font-weight-medium); color: var(--color-gray-500); margin: 0;" id="success-rate-label">Success Rate</p>
            <div class="flex items-baseline" style="margin-top: var(--space-1);">
              <p style="font-size: var(--text-3xl); font-weight: var(--font-weight-bold); color: var(--color-gray-900); margin: 0;"
                 data-controller="metrics"
                 data-metrics-current-value-value="<%= @pipeline_metrics[:avg_success_rate] * 100 %>"
                 data-metrics-metric-value="success_rate"
                 data-metrics-format-value="percentage"
                 data-dashboard-websocket-target="successRate"
                 aria-labelledby="success-rate-label"
                 aria-describedby="success-rate-description">
                <%= number_to_percentage(@pipeline_metrics[:avg_success_rate] * 100, precision: 1) %>
              </p>
              <span style="margin-left: var(--space-2); font-size: var(--text-sm); color: var(--color-gray-600);" id="success-rate-description">last 30 days</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Connections Metric -->
    <div class="card card-hover">
      <div class="card-body">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="feature-icon" style="background-color: var(--color-secondary-100); color: var(--color-secondary-600);">
              <svg style="width: 1.5rem; height: 1.5rem;" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 9l3 3-3 3m5 0h3M5 20h14a2 2 0 002-2V6a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"/>
              </svg>
            </div>
          </div>
          <div style="margin-left: var(--space-4); flex: 1;">
            <p style="font-size: var(--text-sm); font-weight: var(--font-weight-medium); color: var(--color-gray-500); margin: 0;">Connections</p>
            <div class="flex items-baseline" style="margin-top: var(--space-1);">
              <p style="font-size: var(--text-3xl); font-weight: var(--font-weight-bold); color: var(--color-gray-900); margin: 0;" data-dashboard-websocket-target="connectorCount"><%= @connector_metrics[:total] %></p>
              <span class="badge badge-success" style="margin-left: var(--space-2);" data-dashboard-websocket-target="healthyConnectors">
                <%= @connector_metrics[:healthy] %> healthy
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Data Processed Metric -->
    <div class="card card-hover">
      <div class="card-body">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="feature-icon" style="background-color: var(--color-accent-100); color: var(--color-accent-600);">
              <svg style="width: 1.5rem; height: 1.5rem;" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
              </svg>
            </div>
          </div>
          <div style="margin-left: var(--space-4); flex: 1;">
            <p style="font-size: var(--text-sm); font-weight: var(--font-weight-medium); color: var(--color-gray-500); margin: 0;">Data Processed</p>
            <div class="flex items-baseline" style="margin-top: var(--space-1);">
              <p style="font-size: var(--text-3xl); font-weight: var(--font-weight-bold); color: var(--color-gray-900); margin: 0;" data-dashboard-websocket-target="dataProcessed">
                <%= number_to_human(@usage_metrics[:data_processed_mb], units: { thousand: 'K', million: 'M' }) %>
              </p>
              <span style="margin-left: var(--space-2); font-size: var(--text-sm); color: var(--color-gray-600);">rows</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Secondary Metrics Grid -->
  <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
    <!-- Average Execution Time -->
    <div class="card">
      <div class="card-body">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="feature-icon" style="background-color: var(--color-info-100); color: var(--color-info-600);">
              <svg style="width: 1.5rem; height: 1.5rem;" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
              </svg>
            </div>
          </div>
          <div style="margin-left: var(--space-4); flex: 1;">
            <p style="font-size: var(--text-sm); font-weight: var(--font-weight-medium); color: var(--color-gray-500); margin: 0;">Avg Execution Time</p>
            <p style="font-size: var(--text-2xl); font-weight: var(--font-weight-bold); color: var(--color-gray-900); margin: var(--space-1) 0 0 0;">
              <%= @performance_metrics&.dig(:avg_execution_time) || "0s" %>
            </p>
          </div>
        </div>
      </div>
    </div>

    <!-- Error Rate -->
    <div class="card">
      <div class="card-body">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="feature-icon" style="background-color: var(--color-warning-100); color: var(--color-warning-600);">
              <svg style="width: 1.5rem; height: 1.5rem;" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"/>
              </svg>
            </div>
          </div>
          <div style="margin-left: var(--space-4); flex: 1;">
            <p style="font-size: var(--text-sm); font-weight: var(--font-weight-medium); color: var(--color-gray-500); margin: 0;">Error Rate</p>
            <p style="font-size: var(--text-2xl); font-weight: var(--font-weight-bold); color: var(--color-gray-900); margin: var(--space-1) 0 0 0;">
              <%= @performance_metrics&.dig(:error_rate) || 0 %>%
            </p>
          </div>
        </div>
      </div>
    </div>

    <!-- Monthly Cost -->
    <div class="card">
      <div class="card-body">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="feature-icon" style="background-color: var(--color-success-100); color: var(--color-success-600);">
              <svg style="width: 1.5rem; height: 1.5rem;" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"/>
              </svg>
            </div>
          </div>
          <div style="margin-left: var(--space-4); flex: 1;">
            <p style="font-size: var(--text-sm); font-weight: var(--font-weight-medium); color: var(--color-gray-500); margin: 0;">Monthly Cost</p>
            <p style="font-size: var(--text-2xl); font-weight: var(--font-weight-bold); color: var(--color-gray-900); margin: var(--space-1) 0 0 0;">
              $<%= @cost_metrics&.dig(:monthly_cost) || 0 %>
            </p>
          </div>
        </div>
      </div>
    </div>

    <!-- Data Freshness -->
    <div class="card">
      <div class="card-body">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="feature-icon" style="background-color: var(--color-accent-100); color: var(--color-accent-600);">
              <svg style="width: 1.5rem; height: 1.5rem;" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"/>
              </svg>
            </div>
          </div>
          <div style="margin-left: var(--space-4); flex: 1;">
            <p style="font-size: var(--text-sm); font-weight: var(--font-weight-medium); color: var(--color-gray-500); margin: 0;">Data Freshness</p>
            <p style="font-size: var(--text-lg); font-weight: var(--font-weight-semibold); color: var(--color-gray-900); margin: var(--space-1) 0 0 0;">
              <%= @performance_metrics&.dig(:data_freshness) || "No data" %>
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Include Enhanced Sections -->
<%= render 'quick_actions' %>
<%= render 'recent_activity' %>
<%= render 'recent_pipelines' %>

<!-- Plan Usage & Limits (for owners and admins) -->
<% if can_manage_account? %>
  <section style="margin-bottom: var(--space-8);" aria-labelledby="plan-usage-heading">
    <h2 id="plan-usage-heading" style="font-size: var(--text-2xl); font-weight: var(--font-weight-semibold); color: var(--color-gray-900); margin: 0 0 var(--space-6) 0;">Plan Usage</h2>
    <div class="card">
      <div class="card-body">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
          <!-- Team Members Usage -->
          <div>
            <div class="flex items-center justify-between" style="margin-bottom: var(--space-2);">
              <span style="font-size: var(--text-sm); font-weight: var(--font-weight-medium); color: var(--color-gray-700);">Team Members</span>
              <span style="font-size: var(--text-sm); color: var(--color-gray-500);">
                <%= @account.users.count %> / <%= @account.max_team_members == -1 ? '∞' : @account.max_team_members + 1 %>
              </span>
            </div>
            <div class="progress">
              <% team_usage = @account.max_team_members == -1 ? 0 : usage_percentage(@account.users.count, @account.max_team_members + 1) %>
              <div class="progress-bar progress-bar-success" style="width: <%= [team_usage, 100].min %>%"></div>
            </div>
          </div>

          <!-- Pipelines Usage -->
          <div>
            <div class="flex items-center justify-between" style="margin-bottom: var(--space-2);">
              <span style="font-size: var(--text-sm); font-weight: var(--font-weight-medium); color: var(--color-gray-700);">Pipelines</span>
              <span style="font-size: var(--text-sm); color: var(--color-gray-500);">
                <%= @pipeline_metrics[:total_pipelines] %> / <%= @account.plan_pipeline_limit == -1 ? '∞' : @account.plan_pipeline_limit %>
              </span>
            </div>
            <div class="progress">
              <% pipeline_usage = @account.plan_pipeline_limit == -1 ? 0 : usage_percentage(@pipeline_metrics[:total_pipelines], @account.plan_pipeline_limit) %>
              <div class="progress-bar progress-bar-primary" style="width: <%= [pipeline_usage, 100].min %>%"></div>
            </div>
          </div>

          <!-- Storage Usage -->
          <div>
            <div class="flex items-center justify-between" style="margin-bottom: var(--space-2);">
              <span style="font-size: var(--text-sm); font-weight: var(--font-weight-medium); color: var(--color-gray-700);">Storage</span>
              <span style="font-size: var(--text-sm); color: var(--color-gray-500);">
                <%= number_to_human_size(@usage_metrics[:storage_used_mb] * 1024 * 1024) %> /
                <%= @account.subscription&.plan == 'enterprise' ? '∞' : '10 GB' %>
              </span>
            </div>
            <div class="progress">
              <% storage_limit_mb = @account.subscription&.plan == 'enterprise' ? -1 : 10240 %>
              <% storage_usage = storage_limit_mb == -1 ? 0 : usage_percentage(@usage_metrics[:storage_used_mb], storage_limit_mb) %>
              <div class="progress-bar progress-bar-accent" style="width: <%= [storage_usage, 100].min %>%"></div>
            </div>
          </div>
        </div>

        <% if @account.subscription&.plan == 'free' %>
          <div class="alert alert-info" style="margin-top: var(--space-6);">
            <div class="alert-icon">ℹ</div>
            <div class="alert-content">
              <h4 class="alert-title">Upgrade for more features</h4>
              <p class="alert-message">Get unlimited pipelines, advanced analytics, and priority support.</p>
              <div style="margin-top: var(--space-3);">
                <%= link_to subscription_path, class: "btn btn-primary btn-sm" do %>
                  View Plans
                <% end %>
              </div>
            </div>
          </div>
        <% end %>
      </div>
    </div>
  </section>
<% end %>

<!-- Account Summary -->
<section style="margin-bottom: var(--space-8);" aria-labelledby="account-summary-heading">
  <h2 id="account-summary-heading" style="font-size: var(--text-2xl); font-weight: var(--font-weight-semibold); color: var(--color-gray-900); margin: 0 0 var(--space-6) 0;">Account Summary</h2>
  <div class="card">
    <div class="card-body">
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div>
          <dt style="font-size: var(--text-sm); font-weight: var(--font-weight-medium); color: var(--color-gray-500);">Account Name</dt>
          <dd style="margin-top: var(--space-1); font-size: var(--text-sm); color: var(--color-gray-900);"><%= @account.name %></dd>
        </div>
        <div>
          <dt style="font-size: var(--text-sm); font-weight: var(--font-weight-medium); color: var(--color-gray-500);">Subdomain</dt>
          <dd style="margin-top: var(--space-1); font-size: var(--text-sm); color: var(--color-gray-900);"><%= @account.subdomain %>.dataReflow.io</dd>
        </div>
        <div>
          <dt style="font-size: var(--text-sm); font-weight: var(--font-weight-medium); color: var(--color-gray-500);">Current Plan</dt>
          <dd style="margin-top: var(--space-1);">
            <span class="badge badge-primary">
              <%= @account.subscription&.plan&.humanize || 'Free' %>
            </span>
          </dd>
        </div>
        <div>
          <dt style="font-size: var(--text-sm); font-weight: var(--font-weight-medium); color: var(--color-gray-500);">Team Members</dt>
          <dd style="margin-top: var(--space-1); font-size: var(--text-sm); color: var(--color-gray-900);">
            <%= @account.users.count %> / <%= @account.max_team_members == -1 ? '∞' : @account.max_team_members + 1 %>
          </dd>
        </div>
      </div>
    </div>
  </div>
</section>

</div> <!-- End dashboard-content -->
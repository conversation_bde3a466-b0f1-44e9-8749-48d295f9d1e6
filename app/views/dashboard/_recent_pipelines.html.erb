<!-- Recent Pipelines & Trending Connectors -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-8" style="margin-bottom: var(--space-8);">
  <!-- Recent Pipelines -->
  <div class="card">
    <div class="card-header">
      <h3 class="card-title">Recent Pipelines</h3>
      <p class="card-subtitle">Your most recently updated data pipelines</p>
    </div>
    <div class="card-body">
      <% if @recent_pipelines&.any? %>
        <div style="display: flex; flex-direction: column; gap: var(--space-4);">
          <% @recent_pipelines.each do |pipeline| %>
            <div class="flex items-center justify-between" style="padding: var(--space-3); border: 1px solid var(--color-gray-200); border-radius: var(--radius-md); background-color: var(--color-gray-50);">
              <div class="flex items-center" style="gap: var(--space-3);">
                <div style="height: 2.5rem; width: 2.5rem; background-color: var(--color-primary-100); border-radius: var(--radius-md); display: flex; align-items: center; justify-content: center;">
                  <svg style="width: 1.25rem; height: 1.25rem; color: var(--color-primary-600);" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"/>
                  </svg>
                </div>
                <div>
                  <h4 style="font-size: var(--text-sm); font-weight: var(--font-weight-semibold); color: var(--color-gray-900); margin: 0;">
                    <%= link_to pipeline[:name], pipeline_path(pipeline[:id]), style: "text-decoration: none; color: inherit;" %>
                  </h4>
                  <p style="font-size: var(--text-xs); color: var(--color-gray-600); margin: var(--space-1) 0 0 0;">
                    <% if pipeline[:last_run] %>
                      Last run <%= time_ago_in_words(pipeline[:last_run]) %> ago
                    <% else %>
                      Never executed
                    <% end %>
                  </p>
                </div>
              </div>
              <div class="flex items-center" style="gap: var(--space-2);">
                <% if pipeline[:success_rate] %>
                  <span style="font-size: var(--text-xs); color: var(--color-gray-600);"><%= pipeline[:success_rate].round(1) %>%</span>
                <% end %>
                <% case pipeline[:status] %>
                <% when 'active' %>
                  <span class="badge badge-success">Active</span>
                <% when 'paused' %>
                  <span class="badge badge-warning">Paused</span>
                <% when 'error' %>
                  <span class="badge badge-error">Error</span>
                <% when 'draft' %>
                  <span class="badge badge-secondary">Draft</span>
                <% else %>
                  <span class="badge badge-secondary"><%= pipeline[:status].humanize %></span>
                <% end %>
              </div>
            </div>
          <% end %>
        </div>
        
        <div style="margin-top: var(--space-6); text-align: center;">
          <%= link_to pipelines_path, class: "btn btn-outline btn-sm" do %>
            View All Pipelines
          <% end %>
        </div>
      <% else %>
        <div style="text-align: center; padding: var(--space-8) 0;">
          <svg style="margin: 0 auto var(--space-4) auto; width: 3rem; height: 3rem; color: var(--color-gray-400);" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"/>
          </svg>
          <h3 style="font-size: var(--text-base); font-weight: var(--font-weight-medium); color: var(--color-gray-900); margin: 0 0 var(--space-1) 0;">No pipelines yet</h3>
          <p style="font-size: var(--text-sm); color: var(--color-gray-500); margin: 0;">Create your first data pipeline to get started.</p>
          <div style="margin-top: var(--space-4);">
            <%= link_to new_pipeline_path, class: "btn btn-primary btn-sm" do %>
              Create Pipeline
            <% end %>
          </div>
        </div>
      <% end %>
    </div>
  </div>

  <!-- Trending Connectors -->
  <div class="card">
    <div class="card-header">
      <h3 class="card-title">Active Connections</h3>
      <p class="card-subtitle">Your data source connections and their status</p>
    </div>
    <div class="card-body">
      <% if @trending_connectors&.any? %>
        <div style="display: flex; flex-direction: column; gap: var(--space-4);">
          <% @trending_connectors.each do |connector| %>
            <div class="flex items-center justify-between" style="padding: var(--space-3); border: 1px solid var(--color-gray-200); border-radius: var(--radius-md); background-color: var(--color-gray-50);">
              <div class="flex items-center" style="gap: var(--space-3);">
                <div style="height: 2.5rem; width: 2.5rem; background-color: var(--color-secondary-100); border-radius: var(--radius-md); display: flex; align-items: center; justify-content: center;">
                  <% case connector[:type] %>
                  <% when 'postgresql', 'mysql', 'sqlite' %>
                    <svg style="width: 1.25rem; height: 1.25rem; color: var(--color-secondary-600);" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"/>
                    </svg>
                  <% when 'rest_api', 'graphql_api' %>
                    <svg style="width: 1.25rem; height: 1.25rem; color: var(--color-secondary-600);" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 9l3 3-3 3m5 0h3"/>
                    </svg>
                  <% when 'csv_file', 'json_file' %>
                    <svg style="width: 1.25rem; height: 1.25rem; color: var(--color-secondary-600);" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                    </svg>
                  <% else %>
                    <svg style="width: 1.25rem; height: 1.25rem; color: var(--color-secondary-600);" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"/>
                    </svg>
                  <% end %>
                </div>
                <div>
                  <h4 style="font-size: var(--text-sm); font-weight: var(--font-weight-semibold); color: var(--color-gray-900); margin: 0;">
                    <%= link_to connector[:name], data_connector_path(connector[:id]), style: "text-decoration: none; color: inherit;" %>
                  </h4>
                  <p style="font-size: var(--text-xs); color: var(--color-gray-600); margin: var(--space-1) 0 0 0;">
                    <%= connector[:type].humanize %>
                  </p>
                </div>
              </div>
              <div class="flex items-center" style="gap: var(--space-2);">
                <% if connector[:health] %>
                  <div style="height: 0.5rem; width: 0.5rem; background-color: var(--color-success-500); border-radius: var(--radius-full);"></div>
                <% else %>
                  <div style="height: 0.5rem; width: 0.5rem; background-color: var(--color-warning-500); border-radius: var(--radius-full);"></div>
                <% end %>
                <% case connector[:status] %>
                <% when 'active' %>
                  <span class="badge badge-success">Active</span>
                <% when 'inactive' %>
                  <span class="badge badge-secondary">Inactive</span>
                <% when 'error' %>
                  <span class="badge badge-error">Error</span>
                <% when 'testing' %>
                  <span class="badge badge-info">Testing</span>
                <% else %>
                  <span class="badge badge-secondary"><%= connector[:status].humanize %></span>
                <% end %>
              </div>
            </div>
          <% end %>
        </div>
        
        <div style="margin-top: var(--space-6); text-align: center;">
          <%= link_to data_connectors_path, class: "btn btn-outline btn-sm" do %>
            View All Connections
          <% end %>
        </div>
      <% else %>
        <div style="text-align: center; padding: var(--space-8) 0;">
          <svg style="margin: 0 auto var(--space-4) auto; width: 3rem; height: 3rem; color: var(--color-gray-400);" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"/>
          </svg>
          <h3 style="font-size: var(--text-base); font-weight: var(--font-weight-medium); color: var(--color-gray-900); margin: 0 0 var(--space-1) 0;">No connections yet</h3>
          <p style="font-size: var(--text-sm); color: var(--color-gray-500); margin: 0;">Connect your first data source to get started.</p>
          <div style="margin-top: var(--space-4);">
            <%= link_to new_data_connector_path, class: "btn btn-primary btn-sm" do %>
              Add Connection
            <% end %>
          </div>
        </div>
      <% end %>
    </div>
  </div>
</div>

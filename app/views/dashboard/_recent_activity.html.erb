<!-- Recent Activity & System Health -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-8" style="margin-bottom: var(--space-8);">
  <!-- Recent Activity -->
  <div class="card">
    <div class="card-header">
      <h3 class="card-title">Recent Activity</h3>
      <p class="card-subtitle">Latest pipeline executions and system events</p>
    </div>
    <div class="card-body">
      <% if @recent_activity&.any? %>
        <div style="display: flex; flex-direction: column; gap: var(--space-4);">
          <% @recent_activity.each do |activity| %>
            <div class="flex items-start" style="gap: var(--space-3);">
              <div class="flex-shrink-0">
                <div style="height: 2rem; width: 2rem; background-color: var(--color-gray-100); border-radius: var(--radius-full); display: flex; align-items: center; justify-content: center;">
                  <% case activity[:type] %>
                  <% when 'pipeline_execution' %>
                    <svg style="width: 1rem; height: 1rem; color: var(--color-primary-600);" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"/>
                    </svg>
                  <% when 'connector_update' %>
                    <svg style="width: 1rem; height: 1rem; color: var(--color-secondary-600);" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 9l3 3-3 3m5 0h3"/>
                    </svg>
                  <% else %>
                    <svg style="width: 1rem; height: 1rem; color: var(--color-gray-600);" fill="currentColor" viewBox="0 0 20 20">
                      <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                    </svg>
                  <% end %>
                </div>
              </div>
              <div class="flex-1 min-w-0">
                <p style="font-size: var(--text-sm); color: var(--color-gray-900); margin: 0; font-weight: var(--font-weight-medium);">
                  <%= activity[:title] || activity[:message] %>
                </p>
                <% if activity[:description] %>
                  <p style="font-size: var(--text-sm); color: var(--color-gray-600); margin: var(--space-1) 0 0 0;">
                    <%= activity[:description] %>
                  </p>
                <% end %>
                <p style="font-size: var(--text-xs); color: var(--color-gray-500); margin: var(--space-1) 0 0 0;">
                  <%= time_ago_in_words(activity[:timestamp]) %> ago
                </p>
              </div>
              <div class="flex-shrink-0">
                <% if activity[:status] %>
                  <% case activity[:status] %>
                  <% when 'completed', 'success', 'active' %>
                    <span class="badge badge-success">Success</span>
                  <% when 'failed', 'error' %>
                    <span class="badge badge-error">Failed</span>
                  <% when 'running', 'in_progress' %>
                    <span class="badge badge-info">Running</span>
                  <% else %>
                    <span class="badge badge-secondary"><%= activity[:status].humanize %></span>
                  <% end %>
                <% end %>
              </div>
            </div>
          <% end %>
        </div>
        
        <div style="margin-top: var(--space-6); text-align: center;">
          <%= link_to analytics_path, class: "btn btn-outline btn-sm" do %>
            View All Activity
          <% end %>
        </div>
      <% else %>
        <div style="text-align: center; padding: var(--space-8) 0;">
          <svg style="margin: 0 auto var(--space-4) auto; width: 3rem; height: 3rem; color: var(--color-gray-400);" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"/>
          </svg>
          <h3 style="font-size: var(--text-base); font-weight: var(--font-weight-medium); color: var(--color-gray-900); margin: 0 0 var(--space-1) 0;">No recent activity</h3>
          <p style="font-size: var(--text-sm); color: var(--color-gray-500); margin: 0;">Get started by creating your first pipeline or connection.</p>
          <div style="margin-top: var(--space-4);">
            <%= link_to new_pipeline_path, class: "btn btn-primary btn-sm" do %>
              Create Pipeline
            <% end %>
          </div>
        </div>
      <% end %>
    </div>
  </div>

  <!-- System Health -->
  <div class="card">
    <div class="card-header">
      <h3 class="card-title">System Health</h3>
      <p class="card-subtitle">Real-time system status and performance</p>
    </div>
    <div class="card-body">
      <div style="display: flex; flex-direction: column; gap: var(--space-4);">
        <!-- Overall Status -->
        <div class="flex items-center justify-between">
          <div class="flex items-center">
            <div style="height: 0.75rem; width: 0.75rem; background-color: var(--color-success-500); border-radius: var(--radius-full); margin-right: var(--space-3);"></div>
            <span style="font-size: var(--text-sm); font-weight: var(--font-weight-medium); color: var(--color-gray-900);">Overall Status</span>
          </div>
          <span class="badge badge-success">Healthy</span>
        </div>

        <!-- Pipeline Health -->
        <div class="flex items-center justify-between">
          <span style="font-size: var(--text-sm); color: var(--color-gray-600);">Pipeline Health</span>
          <div class="flex items-center" style="gap: var(--space-2);">
            <div class="progress" style="width: 4rem;">
              <div class="progress-bar progress-bar-success" style="width: <%= @system_health[:pipeline_health] || 95 %>%"></div>
            </div>
            <span style="font-size: var(--text-sm); font-weight: var(--font-weight-medium); color: var(--color-gray-900);"><%= @system_health[:pipeline_health] || 95 %>%</span>
          </div>
        </div>

        <!-- Connector Health -->
        <div class="flex items-center justify-between">
          <span style="font-size: var(--text-sm); color: var(--color-gray-600);">Connector Health</span>
          <div class="flex items-center" style="gap: var(--space-2);">
            <div class="progress" style="width: 4rem;">
              <div class="progress-bar progress-bar-success" style="width: <%= @system_health[:connector_health] || 98 %>%"></div>
            </div>
            <span style="font-size: var(--text-sm); font-weight: var(--font-weight-medium); color: var(--color-gray-900);"><%= @system_health[:connector_health] || 98 %>%</span>
          </div>
        </div>

        <!-- Uptime -->
        <div class="flex items-center justify-between">
          <span style="font-size: var(--text-sm); color: var(--color-gray-600);">Uptime</span>
          <span style="font-size: var(--text-sm); font-weight: var(--font-weight-medium); color: var(--color-gray-900);"><%= @system_health[:uptime_percentage] || 99.9 %>%</span>
        </div>

        <!-- Response Time -->
        <div class="flex items-center justify-between">
          <span style="font-size: var(--text-sm); color: var(--color-gray-600);">Avg Response Time</span>
          <span style="font-size: var(--text-sm); font-weight: var(--font-weight-medium); color: var(--color-gray-900);"><%= @system_health[:response_time_ms] || 120 %>ms</span>
        </div>

        <!-- Last Backup -->
        <div class="flex items-center justify-between">
          <span style="font-size: var(--text-sm); color: var(--color-gray-600);">Last Backup</span>
          <span style="font-size: var(--text-sm); font-weight: var(--font-weight-medium); color: var(--color-gray-900);">
            <%= time_ago_in_words(@system_health[:last_backup] || 2.hours.ago) %> ago
          </span>
        </div>
      </div>
      
      <div style="margin-top: var(--space-6); text-align: center;">
        <%= link_to analytics_path, class: "btn btn-outline btn-sm" do %>
          View Detailed Health
        <% end %>
      </div>
    </div>
  </div>
</div>

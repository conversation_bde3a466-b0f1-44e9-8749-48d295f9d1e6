<!-- Quick Actions Section -->
<section style="margin-bottom: var(--space-8);" aria-labelledby="quick-actions-heading">
  <h2 id="quick-actions-heading" style="font-size: var(--text-2xl); font-weight: var(--font-weight-semibold); color: var(--color-gray-900); margin: 0 0 var(--space-6) 0;">
    Quick Actions
  </h2>
  
  <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4">
    <!-- Create Pipeline -->
    <%= link_to new_pipeline_path,
        class: "card card-hover",
        style: "text-decoration: none; border: 2px dashed var(--color-gray-300); background-color: var(--color-gray-50);",
        data: {
          dashboard_target: "quickAction",
          action: "create_pipeline"
        },
        'aria-label': "Create a new data pipeline",
        'aria-describedby': "create-pipeline-desc" do %>
      <div class="card-body" style="text-align: center;">
        <div class="feature-icon" style="background-color: var(--color-primary-100); color: var(--color-primary-600); margin: 0 auto var(--space-4) auto;">
          <svg style="width: 1.5rem; height: 1.5rem;" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
          </svg>
        </div>
        <h3 style="font-size: var(--text-base); font-weight: var(--font-weight-semibold); color: var(--color-gray-900); margin: 0 0 var(--space-1) 0;">Create Pipeline</h3>
        <p style="font-size: var(--text-sm); color: var(--color-gray-600); margin: 0;" id="create-pipeline-desc">Build a new data pipeline</p>
      </div>
    <% end %>

    <!-- Add Connection -->
    <%= link_to new_data_connector_path,
        class: "card card-hover",
        style: "text-decoration: none; border: 2px dashed var(--color-gray-300); background-color: var(--color-gray-50);",
        data: {
          dashboard_target: "quickAction",
          action: "add_connection"
        },
        'aria-label': "Add a new data connection",
        'aria-describedby': "add-connection-desc" do %>
      <div class="card-body" style="text-align: center;">
        <div class="feature-icon" style="background-color: var(--color-success-100); color: var(--color-success-600); margin: 0 auto var(--space-4) auto;">
          <svg style="width: 1.5rem; height: 1.5rem;" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"/>
          </svg>
        </div>
        <h3 style="font-size: var(--text-base); font-weight: var(--font-weight-semibold); color: var(--color-gray-900); margin: 0 0 var(--space-1) 0;">Add Connection</h3>
        <p style="font-size: var(--text-sm); color: var(--color-gray-600); margin: 0;" id="add-connection-desc">Connect a data source</p>
      </div>
    <% end %>

    <!-- View Analytics -->
    <%= link_to analytics_path,
        class: "card card-hover",
        style: "text-decoration: none; border: 2px dashed var(--color-gray-300); background-color: var(--color-gray-50);",
        data: {
          dashboard_target: "quickAction",
          action: "view_analytics"
        },
        'aria-label': "View analytics and insights",
        'aria-describedby': "view-analytics-desc" do %>
      <div class="card-body" style="text-align: center;">
        <div class="feature-icon" style="background-color: var(--color-accent-100); color: var(--color-accent-600); margin: 0 auto var(--space-4) auto;">
          <svg style="width: 1.5rem; height: 1.5rem;" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
          </svg>
        </div>
        <h3 style="font-size: var(--text-base); font-weight: var(--font-weight-semibold); color: var(--color-gray-900); margin: 0 0 var(--space-1) 0;">View Analytics</h3>
        <p style="font-size: var(--text-sm); color: var(--color-gray-600); margin: 0;" id="view-analytics-desc">Explore data insights</p>
      </div>
    <% end %>

    <!-- Manage Team -->
    <%= link_to team_members_path,
        class: "card card-hover",
        style: "text-decoration: none; border: 2px dashed var(--color-gray-300); background-color: var(--color-gray-50);",
        data: {
          dashboard_target: "quickAction",
          action: "manage_team"
        },
        'aria-label': "Manage team members",
        'aria-describedby': "manage-team-desc" do %>
      <div class="card-body" style="text-align: center;">
        <div class="feature-icon" style="background-color: var(--color-secondary-100); color: var(--color-secondary-600); margin: 0 auto var(--space-4) auto;">
          <svg style="width: 1.5rem; height: 1.5rem;" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"/>
          </svg>
        </div>
        <h3 style="font-size: var(--text-base); font-weight: var(--font-weight-semibold); color: var(--color-gray-900); margin: 0 0 var(--space-1) 0;">Manage Team</h3>
        <p style="font-size: var(--text-sm); color: var(--color-gray-600); margin: 0;" id="manage-team-desc">Add team members</p>
      </div>
    <% end %>

    <!-- Billing & Settings -->
    <%= link_to subscription_path,
        class: "card card-hover",
        style: "text-decoration: none; border: 2px dashed var(--color-gray-300); background-color: var(--color-gray-50);",
        data: {
          dashboard_target: "quickAction",
          action: "manage_billing"
        },
        'aria-label': "Manage billing and subscription",
        'aria-describedby': "manage-billing-desc" do %>
      <div class="card-body" style="text-align: center;">
        <div class="feature-icon" style="background-color: var(--color-warning-100); color: var(--color-warning-600); margin: 0 auto var(--space-4) auto;">
          <svg style="width: 1.5rem; height: 1.5rem;" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"/>
          </svg>
        </div>
        <h3 style="font-size: var(--text-base); font-weight: var(--font-weight-semibold); color: var(--color-gray-900); margin: 0 0 var(--space-1) 0;">Billing</h3>
        <p style="font-size: var(--text-sm); color: var(--color-gray-600); margin: 0;" id="manage-billing-desc">Manage subscription</p>
      </div>
    <% end %>
  </div>
</section>

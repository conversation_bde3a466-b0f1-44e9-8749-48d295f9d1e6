<turbo-frame id="<%= chart_type %>-chart" class="bg-white overflow-hidden shadow rounded-lg">
  <div class="px-4 py-5 sm:p-6">
    <!-- Debug Info (Development Only) -->
    <% if Rails.env.development? %>
      <div class="mb-3 p-2 bg-blue-50 border border-blue-200 rounded text-xs">
        <strong>Chart Debug:</strong> 
        Type: <%= chart_type %> | 
        Data Present: <%= chart_data.present? ? 'Yes' : 'No' %> |
        <% if chart_data.present? %>
          Labels: <%= chart_data[:labels]&.length || 0 %> |
          Datasets: <%= chart_data[:datasets]&.length || 0 %>
        <% end %>
      </div>
    <% end %>
    
    <!-- Enhanced Header with Hotwire Controls -->
    <div class="flex items-center justify-between mb-4">
      <h3 class="text-lg leading-6 font-medium text-gray-900">
        <%= chart_type == 'execution_trends' ? 'Execution Trends' : 'Data Volume' %>
      </h3>
      
      <!-- Hotwire-powered Chart Controls -->
      <div data-controller="chart-controls" 
           data-chart-controls-frame-value="<%= chart_type %>-chart"
           data-chart-controls-type-value="<%= chart_type %>"
           class="flex items-center space-x-2">
        
        <!-- Auto-refresh Toggle -->
        <button data-chart-controls-target="refreshToggle"
                data-action="click->chart-controls#toggleAutoRefresh"
                data-chart-controls-auto-refresh-value="true"
                class="inline-flex items-center px-2 py-1 border border-gray-300 rounded-md text-xs bg-white hover:bg-gray-50 transition-colors">
          <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
          </svg>
          <span data-chart-controls-target="refreshText" class="text-green-600">Auto</span>
        </button>
        
        <!-- Manual Refresh -->
        <button data-action="click->chart-controls#refreshChart"
                class="inline-flex items-center px-2 py-1 border border-gray-300 rounded-md text-xs bg-white hover:bg-gray-50 transition-colors">
          <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
          </svg>
        </button>
      </div>
    </div>
    
    <!-- Enhanced Chart Container with Hotwire Integration -->
    <div data-controller="hotwire-chart" 
         data-hotwire-chart-type-value="<%= chart_type == 'execution_trends' ? 'line' : 'area' %>"
         data-hotwire-chart-data-value="<%= (chart_data || {}).to_json.html_safe %>"
         data-hotwire-chart-height-value="320"
         data-hotwire-chart-frame-id-value="<%= chart_type %>-chart"
         data-hotwire-chart-refresh-interval-value="300"
         class="relative analytics-chart hotwire-enhanced">
      
      <!-- Chart Canvas -->
      <div data-hotwire-chart-target="container" class="w-full h-80"></div>
      
      <!-- Enhanced Loading State -->
      <div data-hotwire-chart-target="loading" 
           class="absolute inset-0 flex items-center justify-center bg-white bg-opacity-90 z-10">
        <div class="flex items-center space-x-3">
          <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-<%= chart_type == 'execution_trends' ? 'indigo' : 'blue' %>-600"></div>
          <span class="text-sm text-gray-600">Loading chart data...</span>
        </div>
      </div>
      
      <!-- Enhanced Error State with Recovery -->
      <div data-hotwire-chart-target="error" 
           class="hidden absolute inset-0 flex items-center justify-center bg-red-50 z-10">
        <div class="text-center p-4">
          <svg class="mx-auto h-8 w-8 text-red-400 mb-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.728-.833-2.498 0L4.316 16.5c-.77.833.192 2.5 1.732 2.5z" />
          </svg>
          <p class="text-sm text-red-800 mb-3">Chart loading failed</p>
          <button data-action="click->hotwire-chart#retryLoad"
                  class="inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded text-red-700 bg-red-100 hover:bg-red-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
            Try Again
          </button>
        </div>
      </div>
      
      <!-- Performance Metrics (Development Only) -->
      <% if Rails.env.development? %>
        <div data-hotwire-chart-target="metrics" 
             class="absolute top-2 right-2 bg-black bg-opacity-75 text-white text-xs px-2 py-1 rounded opacity-60 font-mono">
          <span data-hotwire-chart-target="loadTime">Load: --ms</span> |
          <span data-hotwire-chart-target="renderTime">Render: --ms</span>
        </div>
      <% end %>
    </div>

    <!-- Real-time Chart Insights -->
    <div id="<%= chart_type %>-insights" class="mt-4">
      <% if chart_data.present? && chart_data[:datasets].present? && chart_data[:datasets].any? %>
        <div class="grid grid-cols-2 md:grid-cols-4 gap-4 pt-4 border-t border-gray-200">
          <% if chart_type == 'execution_trends' %>
            <div class="text-center">
              <div class="text-xl font-bold text-green-600">
                <%= chart_data[:datasets][0][:data].sum %>
              </div>
              <div class="text-xs text-gray-500">Total Success</div>
            </div>
            <div class="text-center">
              <div class="text-xl font-bold text-red-600">
                <%= chart_data[:datasets][1][:data].sum %>
              </div>
              <div class="text-xs text-gray-500">Total Failed</div>
            </div>
            <div class="text-center">
              <div class="text-xl font-bold text-blue-600">
                <%= number_to_percentage((chart_data[:datasets][0][:data].sum.to_f / (chart_data[:datasets][0][:data].sum + chart_data[:datasets][1][:data].sum)) * 100, precision: 1) %>
              </div>
              <div class="text-xs text-gray-500">Success Rate</div>
            </div>
            <div class="text-center">
              <div class="text-xl font-bold text-purple-600">
                <%= chart_data[:datasets][0][:data].max %>
              </div>
              <div class="text-xs text-gray-500">Peak Success</div>
            </div>
          <% else %>
            <div class="text-center">
              <div class="text-xl font-bold text-blue-600">
                <%= number_to_human(chart_data[:datasets][0][:data].sum) %>
              </div>
              <div class="text-xs text-gray-500">Total Volume</div>
            </div>
            <div class="text-center">
              <div class="text-xl font-bold text-green-600">
                <%= number_to_human(chart_data[:datasets][0][:data].sum / chart_data[:datasets][0][:data].length) %>
              </div>
              <div class="text-xs text-gray-500">Daily Average</div>
            </div>
            <div class="text-center">
              <div class="text-xl font-bold text-purple-600">
                <%= number_to_human(chart_data[:datasets][0][:data].max) %>
              </div>
              <div class="text-xs text-gray-500">Peak Volume</div>
            </div>
            <div class="text-center">
              <div class="text-xl font-bold text-orange-600">
                <%= number_to_human(chart_data[:datasets][0][:data].min) %>
              </div>
              <div class="text-xs text-gray-500">Min Volume</div>
            </div>
          <% end %>
        </div>
      <% end %>
    </div>
  </div>
</turbo-frame>

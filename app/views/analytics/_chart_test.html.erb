<!-- Simple Chart Test - Direct rendering without Turbo Frames -->
<div class="bg-white p-6 rounded-lg shadow">
  <h3 class="text-lg font-medium mb-4">Chart Test: <%= chart_type.humanize %></h3>
  
  <!-- Debug Info -->
  <div class="mb-4 p-3 bg-gray-100 rounded text-sm">
    <strong>Debug Info:</strong><br>
    Chart Type: <%= chart_type %><br>
    Data Present: <%= chart_data.present? ? 'Yes' : 'No' %><br>
    <% if chart_data.present? %>
      Labels Count: <%= chart_data[:labels]&.length || 0 %><br>
      Datasets Count: <%= chart_data[:datasets]&.length || 0 %><br>
      First Dataset Data: <%= chart_data.dig(:datasets, 0, :data)&.first(3) %><br>
    <% end %>
  </div>

  <!-- Simplified Chart Container -->
  <div data-controller="hotwire-chart" 
       data-hotwire-chart-type-value="<%= chart_type == 'execution_trends' ? 'line' : 'area' %>"
       data-hotwire-chart-data-value="<%= chart_data.to_json.html_safe %>"
       data-hotwire-chart-height-value="300"
       class="chart-test-container">
    
    <!-- Chart Canvas -->
    <div data-hotwire-chart-target="container" class="w-full h-64 border border-gray-300 rounded"></div>
    
    <!-- Loading State -->
    <div data-hotwire-chart-target="loading" 
         class="absolute inset-0 flex items-center justify-center bg-gray-50">
      <div class="text-center">
        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2"></div>
        <span class="text-sm text-gray-600">Loading chart...</span>
      </div>
    </div>
    
    <!-- Error State -->
    <div data-hotwire-chart-target="error" 
         class="hidden absolute inset-0 flex items-center justify-center bg-red-50">
      <div class="text-center">
        <div class="text-red-600 mb-2">❌ Chart Failed to Load</div>
        <button data-action="click->hotwire-chart#retryLoad"
                class="px-3 py-1 bg-red-100 text-red-700 rounded text-sm hover:bg-red-200">
          Retry
        </button>
      </div>
    </div>
  </div>
</div>

<!-- Test the old chart controller for comparison -->
<div class="bg-white p-6 rounded-lg shadow mt-4">
  <h3 class="text-lg font-medium mb-4">Legacy Chart Test: <%= chart_type.humanize %></h3>
  
  <!-- Legacy Chart Container -->
  <div data-controller="chart" 
       data-chart-type-value="<%= chart_type == 'execution_trends' ? 'line' : 'area' %>"
       data-chart-data-value="<%= chart_data.to_json.html_safe %>"
       class="legacy-chart-container">
    
    <div data-chart-target="container" class="w-full h-64 border border-gray-300 rounded"></div>
    
    <div data-chart-target="loading" 
         class="absolute inset-0 flex items-center justify-center bg-gray-50">
      <div class="text-center">
        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-green-600 mx-auto mb-2"></div>
        <span class="text-sm text-gray-600">Loading legacy chart...</span>
      </div>
    </div>
  </div>
</div>
<script>
// Comprehensive Analytics Debug Script
window.AnalyticsDebugger = {
  init() {
    console.log('🔧 Analytics Debugger Initialized');
    this.checkDependencies();
    this.monitorControllers();
    this.setupGlobalErrorHandling();
  },

  checkDependencies() {
    const checks = {
      ApexCharts: typeof window.ApexCharts !== 'undefined',
      Stimulus: typeof window.Stimulus !== 'undefined',
      Turbo: typeof window.Turbo !== 'undefined'
    };

    console.log('📦 Dependency Check:', checks);
    
    if (!checks.ApexCharts) {
      console.error('❌ ApexCharts not loaded! This will cause chart failures.');
    }
    
    return checks;
  },

  monitorControllers() {
    // Monitor Stimulus controller connections
    document.addEventListener('stimulus:connect', (event) => {
      console.log('⚡ Stimulus Controller Connected:', {
        identifier: event.detail.identifier,
        element: event.target,
        controller: event.detail.controller
      });
    });

    // Monitor Turbo Frame loads
    document.addEventListener('turbo:frame-load', (event) => {
      console.log('🖼️ Turbo Frame Loaded:', {
        frameId: event.target.id,
        src: event.target.src
      });
    });
  },

  setupGlobalErrorHandling() {
    window.addEventListener('error', (event) => {
      if (event.filename && event.filename.includes('chart')) {
        console.error('🚨 Chart-related JavaScript Error:', {
          message: event.message,
          filename: event.filename,
          lineno: event.lineno,
          colno: event.colno,
          error: event.error
        });
      }
    });
  },

  testApexCharts() {
    console.log('🧪 Testing ApexCharts directly...');
    
    if (typeof window.ApexCharts === 'undefined') {
      console.error('❌ ApexCharts is not available');
      return false;
    }

    try {
      // Create a simple test chart
      const testContainer = document.createElement('div');
      testContainer.style.width = '100px';
      testContainer.style.height = '100px';
      document.body.appendChild(testContainer);

      const chart = new ApexCharts(testContainer, {
        chart: { type: 'line', height: 100 },
        series: [{ name: 'Test', data: [1, 2, 3] }],
        xaxis: { categories: ['A', 'B', 'C'] }
      });

      chart.render().then(() => {
        console.log('✅ ApexCharts test successful');
        chart.destroy();
        document.body.removeChild(testContainer);
      }).catch((error) => {
        console.error('❌ ApexCharts test failed:', error);
        document.body.removeChild(testContainer);
      });

      return true;
    } catch (error) {
      console.error('❌ ApexCharts instantiation failed:', error);
      return false;
    }
  },

  analyzeChartElements() {
    const chartElements = document.querySelectorAll('[data-controller*="chart"]');
    console.log('📊 Chart Elements Analysis:', {
      totalElements: chartElements.length,
      elements: Array.from(chartElements).map(el => ({
        id: el.id,
        controller: el.getAttribute('data-controller'),
        hasContainer: !!el.querySelector('[data-hotwire-chart-target="container"]'),
        dataValue: el.getAttribute('data-hotwire-chart-data-value')
      }))
    });

    return chartElements;
  },

  runFullDiagnostic() {
    console.log('🔍 Running Full Analytics Diagnostic...');
    
    const results = {
      dependencies: this.checkDependencies(),
      apexChartsTest: this.testApexCharts(),
      chartElements: this.analyzeChartElements(),
      timestamp: new Date().toISOString()
    };

    console.log('📋 Diagnostic Results:', results);
    return results;
  }
};

// Auto-initialize when DOM is ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', () => AnalyticsDebugger.init());
} else {
  AnalyticsDebugger.init();
}

// Make available globally for manual testing
window.debugAnalytics = () => AnalyticsDebugger.runFullDiagnostic();
</script>
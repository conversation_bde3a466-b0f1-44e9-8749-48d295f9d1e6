<%= turbo_stream.replace "#{@chart_type}-chart" do %>
  <%= render 'chart', chart_type: @chart_type, chart_data: @chart_data, date_range: @date_range %>
<% end %>

<%= turbo_stream.update "#{@chart_type}-insights" do %>
  <% if @chart_data && @chart_data[:datasets] && @chart_data[:datasets].any? %>
    <div class="grid grid-cols-2 md:grid-cols-4 gap-4 pt-4 border-t border-gray-200">
      <% if @chart_type == 'execution_trends' %>
        <div class="text-center">
          <div class="text-xl font-bold text-green-600">
            <%= @chart_data[:datasets][0][:data].sum %>
          </div>
          <div class="text-xs text-gray-500">Total Success</div>
        </div>
        <div class="text-center">
          <div class="text-xl font-bold text-red-600">
            <%= @chart_data[:datasets][1][:data].sum %>
          </div>
          <div class="text-xs text-gray-500">Total Failed</div>
        </div>
        <div class="text-center">
          <div class="text-xl font-bold text-blue-600">
            <%= number_to_percentage((@chart_data[:datasets][0][:data].sum.to_f / (@chart_data[:datasets][0][:data].sum + @chart_data[:datasets][1][:data].sum)) * 100, precision: 1) %>
          </div>
          <div class="text-xs text-gray-500">Success Rate</div>
        </div>
        <div class="text-center">
          <div class="text-xl font-bold text-purple-600">
            <%= @chart_data[:datasets][0][:data].max %>
          </div>
          <div class="text-xs text-gray-500">Peak Success</div>
        </div>
      <% else %>
        <div class="text-center">
          <div class="text-xl font-bold text-blue-600">
            <%= number_to_human(@chart_data[:datasets][0][:data].sum) %>
          </div>
          <div class="text-xs text-gray-500">Total Volume</div>
        </div>
        <div class="text-center">
          <div class="text-xl font-bold text-green-600">
            <%= number_to_human(@chart_data[:datasets][0][:data].sum / @chart_data[:datasets][0][:data].length) %>
          </div>
          <div class="text-xs text-gray-500">Daily Average</div>
        </div>
        <div class="text-center">
          <div class="text-xl font-bold text-purple-600">
            <%= number_to_human(@chart_data[:datasets][0][:data].max) %>
          </div>
          <div class="text-xs text-gray-500">Peak Volume</div>
        </div>
        <div class="text-center">
          <div class="text-xl font-bold text-orange-600">
            <%= number_to_human(@chart_data[:datasets][0][:data].min) %>
          </div>
          <div class="text-xs text-gray-500">Min Volume</div>
        </div>
      <% end %>
    </div>
  <% end %>
<% end %>
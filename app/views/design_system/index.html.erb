<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>DataReflow Design System</title>
  <%= stylesheet_link_tag "design-tokens", "components", "data-turbo-track": "reload" %>
  <style>
    .design-system-page {
      font-family: var(--font-family-primary);
      line-height: var(--leading-normal);
      color: var(--color-gray-900);
    }
    
    .section {
      margin-bottom: var(--space-16);
    }
    
    .color-swatch {
      width: 4rem;
      height: 4rem;
      border-radius: var(--radius-lg);
      border: 1px solid var(--color-gray-200);
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: var(--text-xs);
      font-weight: var(--font-weight-medium);
      color: var(--color-white);
      text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
    }
    
    .color-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: var(--space-4);
    }
    
    .color-family {
      background: var(--color-white);
      border: 1px solid var(--color-gray-200);
      border-radius: var(--radius-lg);
      padding: var(--space-4);
    }
    
    .color-family-title {
      font-size: var(--text-sm);
      font-weight: var(--font-weight-semibold);
      margin-bottom: var(--space-3);
      color: var(--color-gray-700);
    }
    
    .color-swatches {
      display: grid;
      grid-template-columns: repeat(5, 1fr);
      gap: var(--space-2);
    }
    
    .typography-example {
      margin-bottom: var(--space-4);
      padding: var(--space-4);
      border: 1px solid var(--color-gray-200);
      border-radius: var(--radius-lg);
    }
    
    .component-example {
      margin-bottom: var(--space-6);
      padding: var(--space-6);
      border: 1px solid var(--color-gray-200);
      border-radius: var(--radius-lg);
      background: var(--color-white);
    }
    
    .component-title {
      font-size: var(--text-lg);
      font-weight: var(--font-weight-semibold);
      margin-bottom: var(--space-4);
      color: var(--color-gray-900);
    }
    
    .example-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: var(--space-4);
    }
  </style>
</head>
<body class="design-system-page">
  <div class="container">
    <!-- Header -->
    <header class="section">
      <h1 style="font-size: var(--text-5xl); font-weight: var(--font-weight-bold); margin-bottom: var(--space-4); background: linear-gradient(135deg, var(--color-primary-600), var(--color-secondary-600)); -webkit-background-clip: text; background-clip: text; -webkit-text-fill-color: transparent;">
        DataReflow Design System
      </h1>
      <p style="font-size: var(--text-xl); color: var(--color-gray-600); max-width: 600px;">
        A comprehensive design system for building consistent, accessible, and beautiful interfaces across all DataReflow applications.
      </p>
    </header>

    <!-- Color Palette -->
    <section class="section">
      <h2 style="font-size: var(--text-3xl); font-weight: var(--font-weight-bold); margin-bottom: var(--space-6);">Color Palette</h2>
      
      <div class="color-grid">
        <!-- Primary Blues -->
        <div class="color-family">
          <div class="color-family-title">Primary Blues</div>
          <div class="color-swatches">
            <div class="color-swatch" style="background-color: var(--color-primary-50); color: var(--color-gray-900);">50</div>
            <div class="color-swatch" style="background-color: var(--color-primary-100); color: var(--color-gray-900);">100</div>
            <div class="color-swatch" style="background-color: var(--color-primary-500);">500</div>
            <div class="color-swatch" style="background-color: var(--color-primary-600);">600</div>
            <div class="color-swatch" style="background-color: var(--color-primary-700);">700</div>
          </div>
        </div>

        <!-- Secondary Indigos -->
        <div class="color-family">
          <div class="color-family-title">Secondary Indigos</div>
          <div class="color-swatches">
            <div class="color-swatch" style="background-color: var(--color-secondary-50); color: var(--color-gray-900);">50</div>
            <div class="color-swatch" style="background-color: var(--color-secondary-100); color: var(--color-gray-900);">100</div>
            <div class="color-swatch" style="background-color: var(--color-secondary-500);">500</div>
            <div class="color-swatch" style="background-color: var(--color-secondary-600);">600</div>
            <div class="color-swatch" style="background-color: var(--color-secondary-700);">700</div>
          </div>
        </div>

        <!-- Grays -->
        <div class="color-family">
          <div class="color-family-title">Neutral Grays</div>
          <div class="color-swatches">
            <div class="color-swatch" style="background-color: var(--color-gray-100); color: var(--color-gray-900);">100</div>
            <div class="color-swatch" style="background-color: var(--color-gray-300); color: var(--color-gray-900);">300</div>
            <div class="color-swatch" style="background-color: var(--color-gray-500);">500</div>
            <div class="color-swatch" style="background-color: var(--color-gray-700);">700</div>
            <div class="color-swatch" style="background-color: var(--color-gray-900);">900</div>
          </div>
        </div>

        <!-- Status Colors -->
        <div class="color-family">
          <div class="color-family-title">Status Colors</div>
          <div class="color-swatches">
            <div class="color-swatch" style="background-color: var(--color-success-500);">Success</div>
            <div class="color-swatch" style="background-color: var(--color-warning-500);">Warning</div>
            <div class="color-swatch" style="background-color: var(--color-error-500);">Error</div>
            <div class="color-swatch" style="background-color: var(--color-info-500);">Info</div>
            <div class="color-swatch" style="background-color: var(--color-gray-400);">Neutral</div>
          </div>
        </div>
      </div>
    </section>

    <!-- Typography -->
    <section class="section">
      <h2 style="font-size: var(--text-3xl); font-weight: var(--font-weight-bold); margin-bottom: var(--space-6);">Typography</h2>
      
      <div class="typography-example">
        <h1 style="font-size: var(--text-6xl); font-weight: var(--font-weight-bold); margin: 0;">Hero Title (6xl)</h1>
      </div>
      
      <div class="typography-example">
        <h2 style="font-size: var(--text-4xl); font-weight: var(--font-weight-bold); margin: 0;">Page Title (4xl)</h2>
      </div>
      
      <div class="typography-example">
        <h3 style="font-size: var(--text-2xl); font-weight: var(--font-weight-semibold); margin: 0;">Section Title (2xl)</h3>
      </div>
      
      <div class="typography-example">
        <p style="font-size: var(--text-lg); line-height: var(--leading-relaxed); margin: 0;">Large body text (lg) - Perfect for introductory paragraphs and important content that needs to stand out.</p>
      </div>
      
      <div class="typography-example">
        <p style="font-size: var(--text-base); line-height: var(--leading-relaxed); margin: 0;">Regular body text (base) - The default text size for most content, optimized for readability and comfortable reading.</p>
      </div>
      
      <div class="typography-example">
        <p style="font-size: var(--text-sm); color: var(--color-gray-600); margin: 0;">Small text (sm) - Used for captions, helper text, and secondary information.</p>
      </div>
    </section>

    <!-- Buttons -->
    <section class="section">
      <h2 style="font-size: var(--text-3xl); font-weight: var(--font-weight-bold); margin-bottom: var(--space-6);">Buttons</h2>
      
      <div class="component-example">
        <div class="component-title">Button Variants</div>
        <div style="display: flex; gap: var(--space-4); flex-wrap: wrap;">
          <button class="btn btn-primary">Primary</button>
          <button class="btn btn-secondary">Secondary</button>
          <button class="btn btn-outline">Outline</button>
          <button class="btn btn-ghost">Ghost</button>
          <button class="btn btn-primary" disabled>Disabled</button>
        </div>
      </div>
      
      <div class="component-example">
        <div class="component-title">Button Sizes</div>
        <div style="display: flex; gap: var(--space-4); align-items: center; flex-wrap: wrap;">
          <button class="btn btn-primary btn-sm">Small</button>
          <button class="btn btn-primary">Medium</button>
          <button class="btn btn-primary btn-lg">Large</button>
          <button class="btn btn-primary btn-xl">Extra Large</button>
        </div>
      </div>
    </section>

    <!-- Cards -->
    <section class="section">
      <h2 style="font-size: var(--text-3xl); font-weight: var(--font-weight-bold); margin-bottom: var(--space-6);">Cards</h2>
      
      <div class="example-grid">
        <div class="card">
          <div class="card-header">
            <h3 class="card-title">Basic Card</h3>
            <p class="card-subtitle">With header and body</p>
          </div>
          <div class="card-body">
            <p class="card-text">This is a basic card with a header and body section. Perfect for displaying structured content.</p>
          </div>
        </div>
        
        <div class="card card-hover">
          <div class="card-body">
            <h3 class="card-title">Hoverable Card</h3>
            <p class="card-text">This card has hover effects that provide visual feedback when users interact with it.</p>
          </div>
        </div>
        
        <div class="feature-card">
          <div class="feature-icon">
            <svg width="24" height="24" fill="currentColor" viewBox="0 0 24 24">
              <path d="M13 10V3L4 14h7v7l9-11h-7z"/>
            </svg>
          </div>
          <h3 class="feature-title">Feature Card</h3>
          <p class="feature-description">Feature cards are perfect for highlighting key features or benefits with an icon and description.</p>
        </div>
      </div>
    </section>

    <!-- Forms -->
    <section class="section">
      <h2 style="font-size: var(--text-3xl); font-weight: var(--font-weight-bold); margin-bottom: var(--space-6);">Forms</h2>
      
      <div class="component-example">
        <div class="component-title">Form Elements</div>
        <form style="max-width: 400px;">
          <div class="form-group">
            <label class="form-label form-label-required" for="demo-email">Email Address</label>
            <input type="email" id="demo-email" class="form-input" placeholder="Enter your email">
            <p class="form-help">We'll never share your email with anyone else.</p>
          </div>
          
          <div class="form-group">
            <label class="form-label" for="demo-select">Category</label>
            <select id="demo-select" class="form-select">
              <option value="">Choose a category</option>
              <option value="general">General Inquiry</option>
              <option value="support">Support</option>
            </select>
          </div>
          
          <div class="form-group">
            <label class="form-label" for="demo-textarea">Message</label>
            <textarea id="demo-textarea" class="form-textarea" placeholder="Enter your message" rows="4"></textarea>
          </div>
          
          <button type="submit" class="btn btn-primary">Submit Form</button>
        </form>
      </div>
    </section>

    <!-- Alerts -->
    <section class="section">
      <h2 style="font-size: var(--text-3xl); font-weight: var(--font-weight-bold); margin-bottom: var(--space-6);">Alerts</h2>
      
      <div class="alert alert-success">
        <div class="alert-icon">✓</div>
        <div class="alert-content">
          <h4 class="alert-title">Success!</h4>
          <p class="alert-message">Your changes have been saved successfully.</p>
        </div>
      </div>
      
      <div class="alert alert-warning">
        <div class="alert-icon">⚠</div>
        <div class="alert-content">
          <h4 class="alert-title">Warning</h4>
          <p class="alert-message">Please review your settings before proceeding.</p>
        </div>
      </div>
      
      <div class="alert alert-error">
        <div class="alert-icon">✕</div>
        <div class="alert-content">
          <h4 class="alert-title">Error</h4>
          <p class="alert-message">Something went wrong. Please try again.</p>
        </div>
      </div>
      
      <div class="alert alert-info">
        <div class="alert-icon">ℹ</div>
        <div class="alert-content">
          <h4 class="alert-title">Information</h4>
          <p class="alert-message">Here's some helpful information for you.</p>
        </div>
      </div>
    </section>

    <!-- Badges -->
    <section class="section">
      <h2 style="font-size: var(--text-3xl); font-weight: var(--font-weight-bold); margin-bottom: var(--space-6);">Badges</h2>
      
      <div class="component-example">
        <div class="component-title">Badge Variants</div>
        <div style="display: flex; gap: var(--space-4); flex-wrap: wrap; align-items: center;">
          <span class="badge badge-primary">Primary</span>
          <span class="badge badge-secondary">Secondary</span>
          <span class="badge badge-success">Success</span>
          <span class="badge badge-warning">Warning</span>
          <span class="badge badge-error">Error</span>
          <span class="badge badge-info">Info</span>
        </div>
      </div>
    </section>

    <!-- Footer -->
    <footer class="section" style="border-top: 1px solid var(--color-gray-200); padding-top: var(--space-8);">
      <p style="color: var(--color-gray-600); text-align: center;">
        DataReflow Design System - Built with consistency and accessibility in mind
      </p>
    </footer>
  </div>
</body>
</html>

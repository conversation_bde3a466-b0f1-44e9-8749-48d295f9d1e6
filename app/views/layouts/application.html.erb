<!DOCTYPE html>
<html lang="en">
  <head>
    <title><%= content_for(:title) || "DataReflow - No-Code Data Integration Platform" %></title>
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="mobile-web-app-capable" content="yes">

    <%# SEO Meta Tags %>
    <meta name="description" content="<%= content_for(:description) || 'Connect, transform, and synchronize data across your business systems in under 10 minutes. No-code data integration platform for SMEs.' %>">
    <meta name="keywords" content="<%= content_for(:keywords) || 'data integration, no-code, business automation, data pipelines, API integration' %>">
    <meta name="author" content="DataReflow">
    <meta name="robots" content="index, follow">

    <%# Open Graph Meta Tags %>
    <meta property="og:title" content="<%= content_for(:title) || 'DataReflow - No-Code Data Integration Platform' %>">
    <meta property="og:description" content="<%= content_for(:description) || 'Connect, transform, and synchronize data across your business systems in under 10 minutes.' %>">
    <meta property="og:type" content="website">
    <meta property="og:url" content="<%= request.original_url %>">
    <meta property="og:image" content="<%= request.base_url %>/icon.png">
    <meta property="og:site_name" content="DataReflow">

    <%# Twitter Card Meta Tags %>
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="<%= content_for(:title) || 'DataReflow - No-Code Data Integration Platform' %>">
    <meta name="twitter:description" content="<%= content_for(:description) || 'Connect, transform, and synchronize data across your business systems in under 10 minutes.' %>">
    <meta name="twitter:image" content="<%= request.base_url %>/icon.png">

    <%= csrf_meta_tags %>
    <%= csp_meta_tag %>

    <%= yield :head %>

    <%# Enable PWA manifest for installable apps (make sure to enable in config/routes.rb too!) %>
    <%#= tag.link rel: "manifest", href: pwa_manifest_path(format: :json) %>

    <link rel="icon" href="/icon.png" type="image/png">
    <link rel="icon" href="/icon.svg" type="image/svg+xml">
    <link rel="apple-touch-icon" href="/icon.png">

    <%# Design System CSS %>
    <%= stylesheet_link_tag "design-tokens", "components", "data-turbo-track": "reload" %>

    <%# Includes all stylesheet files in app/assets/stylesheets %>
    <%= stylesheet_link_tag :app, "data-turbo-track": "reload" %>
    <%= javascript_importmap_tags %>

    <%# Custom styles for authentication pages %>
    <style>
      @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

      .auth-gradient {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      }

      .auth-card {
        backdrop-filter: blur(10px);
        background: rgba(255, 255, 255, 0.95);
      }

      .password-strength-weak { color: #ef4444; }
      .password-strength-medium { color: #f59e0b; }
      .password-strength-strong { color: #10b981; }

      .form-transition {
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      }

      .input-focus {
        transition: all 0.2s ease-in-out;
      }

      .input-focus:focus {
        transform: translateY(-1px);
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
      }

      .btn-loading {
        position: relative;
        pointer-events: none;
      }

      .btn-loading::after {
        content: '';
        position: absolute;
        width: 16px;
        height: 16px;
        margin: auto;
        border: 2px solid transparent;
        border-top-color: currentColor;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }

      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }

      .fade-in {
        animation: fadeIn 0.5s ease-in-out;
      }

      @keyframes fadeIn {
        from { opacity: 0; transform: translateY(10px); }
        to { opacity: 1; transform: translateY(0); }
      }
    </style>
  </head>

  <body>
    <main class="">
      <%= yield %>
    </main>
  </body>
</html>

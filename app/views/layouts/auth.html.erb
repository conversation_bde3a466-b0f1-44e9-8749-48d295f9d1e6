<!DOCTYPE html>
<html>
  <head>
    <title><%= content_for(:title) || "DataReflow - Authentication" %></title>
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="mobile-web-app-capable" content="yes">
    <%= csrf_meta_tags %>
    <%= csp_meta_tag %>

    <%= yield :head %>

    <link rel="icon" href="/icon.png" type="image/png">
    <link rel="icon" href="/icon.svg" type="image/svg+xml">
    <link rel="apple-touch-icon" href="/icon.png">

    <%# Includes all stylesheet files in app/assets/stylesheets %>
    <%= stylesheet_link_tag :app, "data-turbo-track": "reload" %>
    <%= javascript_importmap_tags %>
    
    <%# Custom styles for authentication pages %>
    <style>
      @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
      
      body {
        font-family: 'Inter', sans-serif;
      }
      
      .auth-gradient {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      }
      
      .auth-card {
        backdrop-filter: blur(10px);
        background: rgba(255, 255, 255, 0.95);
      }
      
      .password-strength-weak { color: #ef4444; }
      .password-strength-medium { color: #f59e0b; }
      .password-strength-strong { color: #10b981; }
      
      .form-transition {
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      }
      
      .input-focus {
        transition: all 0.2s ease-in-out;
      }
      
      .input-focus:focus {
        transform: translateY(-1px);
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
      }
      
      .btn-loading {
        position: relative;
        pointer-events: none;
      }
      
      .btn-loading::after {
        content: '';
        position: absolute;
        width: 16px;
        height: 16px;
        margin: auto;
        border: 2px solid transparent;
        border-top-color: currentColor;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }
      
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
      
      .fade-in {
        animation: fadeIn 0.5s ease-in-out;
      }
      
      @keyframes fadeIn {
        from { opacity: 0; transform: translateY(10px); }
        to { opacity: 1; transform: translateY(0); }
      }
      
      .slide-in-right {
        animation: slideInRight 0.6s ease-out;
      }
      
      @keyframes slideInRight {
        from { opacity: 0; transform: translateX(30px); }
        to { opacity: 1; transform: translateX(0); }
      }
      
      .pulse-subtle {
        animation: pulseSubtle 2s infinite;
      }
      
      @keyframes pulseSubtle {
        0%, 100% { opacity: 1; }
        50% { opacity: 0.8; }
      }

      /* Form validation states */
      .form-group.valid input {
        border-color: #10b981;
        box-shadow: 0 0 0 1px #10b981;
      }

      .form-group.error input {
        border-color: #ef4444;
        box-shadow: 0 0 0 1px #ef4444;
      }

      .form-group.focused label {
        color: #3b82f6;
      }

      /* Loading button animation */
      .btn-loading {
        position: relative;
        color: transparent;
      }

      .btn-loading::after {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        width: 20px;
        height: 20px;
        margin-top: -10px;
        margin-left: -10px;
        border: 2px solid #ffffff;
        border-top-color: transparent;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }

      /* Smooth hover effects */
      .hover-lift {
        transition: transform 0.2s ease-in-out;
      }

      .hover-lift:hover {
        transform: translateY(-2px);
      }

      /* Focus ring improvements */
      input:focus, select:focus, textarea:focus {
        outline: none;
        ring: 2px;
        ring-color: #3b82f6;
        ring-offset: 2px;
      }

      /* Checkbox and radio improvements */
      input[type="checkbox"]:checked {
        background-color: #3b82f6;
        border-color: #3b82f6;
      }

      /* SVG icon improvements */
      .auth-icon {
        color: white;
        stroke: currentColor;
        fill: none;
      }

      .auth-icon.filled {
        fill: currentColor;
        stroke: none;
      }

      .auth-icon-container {
        background: rgba(255, 255, 255, 0.2);
        backdrop-filter: blur(10px);
        transition: all 0.3s ease;
      }

      .auth-icon-container:hover {
        background: rgba(255, 255, 255, 0.3);
        transform: scale(1.05);
      }

      /* Subdomain field styling */
      .subdomain-input-group {
        display: flex;
        border-radius: 0.5rem;
        overflow: hidden;
        border: 1px solid #d1d5db;
        transition: all 0.2s ease-in-out;
      }

      .subdomain-input-group:focus-within {
        border-color: #3b82f6;
        box-shadow: 0 0 0 1px #3b82f6;
      }

      .subdomain-input {
        border: none !important;
        border-radius: 0 !important;
        box-shadow: none !important;
      }

      .subdomain-input:focus {
        border: none !important;
        box-shadow: none !important;
      }

      .subdomain-suffix {
        background-color: #f9fafb;
        border-left: 1px solid #e5e7eb;
        color: #6b7280;
        font-size: 0.875rem;
        display: flex;
        align-items: center;
        padding: 0 1rem;
        white-space: nowrap;
      }

      /* Mobile responsive improvements */
      @media (max-width: 640px) {
        .auth-card {
          margin: 1rem;
          padding: 1.5rem;
        }

        .grid-cols-1.md\\:grid-cols-2 {
          grid-template-columns: 1fr;
        }

        .subdomain-suffix {
          font-size: 0.75rem;
          padding: 0 0.75rem;
        }
      }
    </style>
  </head>

  <body class="min-h-screen auth-gradient">
    <div class="min-h-screen flex">
      <!-- Left Column - Form -->
      <div class="flex-1 flex items-center justify-center px-4 sm:px-6 lg:px-8">
        <div class="max-w-md w-full">
          <div class="auth-card rounded-2xl shadow-2xl p-8 fade-in">
            <%= yield %>
          </div>
        </div>
      </div>
      
      <!-- Right Column - Branding/Info -->
      <div class="hidden lg:flex lg:flex-1 lg:items-center lg:justify-center lg:px-8">
        <div class="max-w-lg text-white slide-in-right">
          <div class="text-center">
            <h1 class="text-4xl font-bold mb-6">
              Welcome to DataReflow
            </h1>
            <p class="text-xl mb-8 opacity-90">
              Connect, transform, and synchronize data across your business systems in under 10 minutes.
            </p>
            
            <div class="space-y-6">
              <div class="flex items-center space-x-4">
                <div class="w-12 h-12 auth-icon-container rounded-lg flex items-center justify-center">
                  <svg class="w-6 h-6 auth-icon" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                  </svg>
                </div>
                <div class="text-left">
                  <h3 class="font-semibold">10-Minute Setup</h3>
                  <p class="text-sm opacity-80">From signup to first data sync</p>
                </div>
              </div>

              <div class="flex items-center space-x-4">
                <div class="w-12 h-12 auth-icon-container rounded-lg flex items-center justify-center">
                  <svg class="w-6 h-6 auth-icon" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                  </svg>
                </div>
                <div class="text-left">
                  <h3 class="font-semibold">200+ Connectors</h3>
                  <p class="text-sm opacity-80">Connect to all your favorite tools</p>
                </div>
              </div>

              <div class="flex items-center space-x-4">
                <div class="w-12 h-12 auth-icon-container rounded-lg flex items-center justify-center">
                  <svg class="w-6 h-6 auth-icon" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4"></path>
                  </svg>
                </div>
                <div class="text-left">
                  <h3 class="font-semibold">Visual Pipeline Builder</h3>
                  <p class="text-sm opacity-80">Drag-and-drop interface</p>
                </div>
              </div>
            </div>
            
            <div class="mt-12 pulse-subtle">
              <div class="w-16 h-16 auth-icon-container rounded-full flex items-center justify-center mx-auto">
                <svg class="w-8 h-8 auth-icon filled" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                </svg>
              </div>
              <p class="text-sm mt-4 opacity-70">Trusted by 1000+ businesses</p>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Mobile responsive branding footer -->
    <div class="lg:hidden fixed bottom-0 left-0 right-0 bg-black bg-opacity-20 text-white text-center py-4">
      <p class="text-sm">DataReflow - No-code data integration for SMEs</p>
    </div>
  </body>
</html>

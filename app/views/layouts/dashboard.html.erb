<!DOCTYPE html>
<html>
  <head>
    <title><%= content_for(:title) || "Dashboard - DataReflow" %></title>
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="mobile-web-app-capable" content="yes">
    <%= csrf_meta_tags %>
    <%= csp_meta_tag %>

    <%= yield :head %>

    <link rel="icon" href="/icon.png" type="image/png">
    <link rel="icon" href="/icon.svg" type="image/svg+xml">
    <link rel="apple-touch-icon" href="/icon.png">

    <%# Design System CSS %>
    <%= stylesheet_link_tag "design-tokens", "components", "dashboard", "data-turbo-track": "reload" %>

    <%# Dashboard specific styles %>
    <style>
      :root {
        /* Essential CSS Variables */
        --sidebar-width: 256px;
        --header-height: 64px;

        --space-1: 4px;
        --space-2: 8px;
        --space-3: 12px;
        --space-4: 16px;
        --space-5: 20px;
        --space-6: 24px;
        --space-7: 28px;
        --space-8: 32px;
        --space-10: 40px;
        --space-12: 48px;

        --color-white: #ffffff;
        --color-gray-50: #f9fafb;
        --color-gray-100: #f3f4f6;
        --color-gray-200: #e5e7eb;
        --color-gray-300: #d1d5db;
        --color-gray-400: #9ca3af;
        --color-gray-500: #6b7280;
        --color-gray-600: #4b5563;
        --color-gray-700: #374151;
        --color-gray-800: #1f2937;
        --color-gray-900: #111827;

        --color-primary-50: #eff6ff;
        --color-primary-500: #3b82f6;
        --color-primary-600: #2563eb;
        --color-primary-700: #1d4ed8;
        
        --color-secondary-50: #faf5ff;
        --color-secondary-600: #9333ea;

        --text-xs: 12px;
        --text-sm: 14px;
        --text-base: 16px;
        --text-lg: 18px;
        --text-xl: 20px;
        --text-2xl: 24px;
        --text-3xl: 30px;
        --text-4xl: 36px;
        --text-5xl: 48px;

        --font-weight-normal: 400;
        --font-weight-medium: 500;
        --font-weight-semibold: 600;
        --font-weight-bold: 700;

        --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
        --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
        --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
        
        --radius-sm: 0.125rem;
        --radius-md: 0.375rem;
        --radius-lg: 0.5rem;
        --radius-xl: 0.75rem;
        --radius-full: 9999px;

        --font-family-primary: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      }
      
      * {
        box-sizing: border-box;
      }
      
      body {
        margin: 0;
        padding: 0;
        font-family: var(--font-family-primary);
        color: var(--color-gray-900);
        background-color: var(--color-gray-50);
      }
      
      /* Mobile responsiveness for sidebar */
      @media (max-width: 1023px) {
        .dashboard-wrapper .sidebar-section {
          display: none;
        }
        
        .dashboard-wrapper .content-area {
          padding: 16px;
        }
      }
    </style>

    <%= stylesheet_link_tag :app, "data-turbo-track": "reload" %>

    <!-- ApexCharts CDN - Load before importmap -->
    <script src="https://cdn.jsdelivr.net/npm/apexcharts@3.44.0/dist/apexcharts.min.js"></script>

    <%= javascript_importmap_tags %>
  </head>

  <body>
    <!-- Skip to main content link for keyboard users -->
    <a href="#main-content" class="sr-only">
      Skip to main content
    </a>

    <!-- Main Dashboard Layout Container -->
    <div class="dashboard-wrapper">
      
      <!-- Sidebar Section -->
      <aside class="sidebar-section">
        <%= render 'shared/sidebar' %>
      </aside>

      <!-- Main Content Section -->
      <div class="content-section">
        
        <!-- Flash messages -->
        <% if notice || alert %>
          <div class="flash-container">
            <%= render 'shared/flash_messages' %>
          </div>
        <% end %>

        <!-- Main content area -->
        <main id="main-content" class="content-area">
          <%= yield %>
        </main>

      </div>
    </div>
  </body>
</html>

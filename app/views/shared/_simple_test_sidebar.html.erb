<div class="flex flex-col h-full" 
     style="background-color: var(--color-white);"
     data-controller="simple-sidebar">

  <div style="padding: var(--space-4); border-bottom: 1px solid var(--color-gray-200);">
    <h2>Simple Test Sidebar</h2>
    <p style="font-size: 0.875rem; color: var(--color-gray-600);">Testing basic toggle functionality</p>
  </div>

  <nav class="flex-1 overflow-y-auto" style="padding: var(--space-4);">
    
    <!-- Test Section 1 -->
    <div data-simple-sidebar-target="section" style="margin-bottom: var(--space-4);">
      <button type="button"
              data-simple-sidebar-target="toggle"
              data-action="click->simple-sidebar#toggleSection"
              style="display: flex; align-items: center; width: 100%; padding: var(--space-2); background: #f3f4f6; border: 1px solid #d1d5db; border-radius: 4px; cursor: pointer;"
              aria-expanded="true"
              aria-controls="test-content-1">
        <svg data-simple-sidebar-target="icon" 
             style="width: 1rem; height: 1rem; margin-right: var(--space-2); transition: transform 0.3s ease; transform: rotate(90deg);" 
             fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
        </svg>
        <span>Test Section 1 (Expanded)</span>
      </button>
      
      <div data-simple-sidebar-target="content" id="test-content-1" aria-hidden="false" style="margin-top: var(--space-2); padding: var(--space-2); background: #e5e7eb;">
        <p>✅ This content should be visible initially</p>
        <p>🔄 Click the button above to collapse this section</p>
      </div>
    </div>

    <!-- Test Section 2 -->
    <div data-simple-sidebar-target="section" style="margin-bottom: var(--space-4);">
      <button type="button"
              data-simple-sidebar-target="toggle"
              data-action="click->simple-sidebar#toggleSection"
              style="display: flex; align-items: center; width: 100%; padding: var(--space-2); background: #f3f4f6; border: 1px solid #d1d5db; border-radius: 4px; cursor: pointer;"
              aria-expanded="false"
              aria-controls="test-content-2">
        <svg data-simple-sidebar-target="icon" 
             style="width: 1rem; height: 1rem; margin-right: var(--space-2); transition: transform 0.3s ease;" 
             fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
        </svg>
        <span>Test Section 2 (Collapsed)</span>
      </button>
      
      <div data-simple-sidebar-target="content" id="test-content-2" aria-hidden="true" style="display: none; margin-top: var(--space-2); padding: var(--space-2); background: #e5e7eb;">
        <p>🔄 This content should be hidden initially</p>
        <p>✅ Click the button above to expand this section</p>
      </div>
    </div>

    <!-- Test Section 3 -->
    <div data-simple-sidebar-target="section" style="margin-bottom: var(--space-4);">
      <button type="button"
              data-simple-sidebar-target="toggle"
              data-action="click->simple-sidebar#toggleSection"
              style="display: flex; align-items: center; width: 100%; padding: var(--space-2); background: #f3f4f6; border: 1px solid #d1d5db; border-radius: 4px; cursor: pointer;"
              aria-expanded="false"
              aria-controls="test-content-3">
        <svg data-simple-sidebar-target="icon" 
             style="width: 1rem; height: 1rem; margin-right: var(--space-2); transition: transform 0.3s ease;" 
             fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
        </svg>
        <span>Test Section 3 (Collapsed)</span>
      </button>
      
      <div data-simple-sidebar-target="content" id="test-content-3" aria-hidden="true" style="display: none; margin-top: var(--space-2); padding: var(--space-2); background: #e5e7eb;">
        <p>🔄 This content should be hidden initially</p>
        <p>✅ Click the button above to expand this section</p>
      </div>
    </div>

  </nav>

  <!-- Debug info -->
  <div style="padding: var(--space-4); border-top: 1px solid var(--color-gray-200); background: #fef3c7;">
    <h3 style="font-size: var(--text-sm); font-weight: var(--font-weight-semibold); margin-bottom: var(--space-2);">Simple Test Debug:</h3>
    <ul style="font-size: var(--text-xs); color: var(--color-gray-600);">
      <li>Controller: <code>data-controller="simple-sidebar"</code></li>
      <li>Action: <code>data-action="click->simple-sidebar#toggleSection"</code></li>
      <li>Check console for "✅ Simple sidebar controller connected"</li>
      <li>Each click should log "🔄 Toggle section called!"</li>
    </ul>
  </div>
</div>

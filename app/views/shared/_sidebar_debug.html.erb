<!-- Debug version of sidebar for testing -->
<div class="flex flex-col h-full" 
     style="background-color: var(--color-white);"
     data-controller="sidebar"
     data-sidebar-default-expanded-value='["overview"]'
     data-sidebar-persist-state-value="false">

  <div style="padding: var(--space-4); border-bottom: 1px solid var(--color-gray-200);">
    <h2>Sidebar Debug Test</h2>
  </div>

  <nav class="flex-1 overflow-y-auto" style="padding: var(--space-4);">
    <!-- Simple test section -->
    <div data-sidebar-target="section" data-section-id="test-section" style="margin-bottom: var(--space-4);">
      <button type="button"
              data-sidebar-target="toggle"
              data-action="click->sidebar#toggleSection"
              style="display: flex; align-items: center; padding: var(--space-2); background: #f3f4f6; border: 1px solid #d1d5db; border-radius: 4px; cursor: pointer; width: 100%;"
              aria-expanded="true"
              aria-controls="test-content">
        <svg data-sidebar-target="icon" 
             style="width: 1rem; height: 1rem; margin-right: var(--space-2); transition: transform 0.3s ease; transform: rotate(90deg);" 
             fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
        </svg>
        <span>Test Section (Click Me)</span>
      </button>
      
      <div data-sidebar-target="content" id="test-content" aria-hidden="false" style="margin-top: var(--space-2); padding: var(--space-2); background: #e5e7eb;">
        <p>This content should toggle when you click the button above.</p>
        <p>If this works, the sidebar controller is functioning properly.</p>
      </div>
    </div>

    <!-- Another test section -->
    <div data-sidebar-target="section" data-section-id="test-section-2" style="margin-bottom: var(--space-4);">
      <button type="button"
              data-sidebar-target="toggle"
              data-action="click->sidebar#toggleSection"
              style="display: flex; align-items: center; padding: var(--space-2); background: #f3f4f6; border: 1px solid #d1d5db; border-radius: 4px; cursor: pointer; width: 100%;"
              aria-expanded="false"
              aria-controls="test-content-2">
        <svg data-sidebar-target="icon" 
             style="width: 1rem; height: 1rem; margin-right: var(--space-2); transition: transform 0.3s ease;" 
             fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
        </svg>
        <span>Test Section 2 (Initially Collapsed)</span>
      </button>
      
      <div data-sidebar-target="content" id="test-content-2" aria-hidden="true" style="display: none; margin-top: var(--space-2); padding: var(--space-2); background: #e5e7eb;">
        <p>This section starts collapsed.</p>
        <p>Click the button above to expand it.</p>
      </div>
    </div>
  </nav>

  <!-- Debug info -->
  <div style="padding: var(--space-4); border-top: 1px solid var(--color-gray-200); background: #fef3c7;">
    <h3 style="font-size: var(--text-sm); font-weight: var(--font-weight-semibold); margin-bottom: var(--space-2);">Debug Info:</h3>
    <ul style="font-size: var(--text-xs); color: var(--color-gray-600);">
      <li>Controller: <code>data-controller="sidebar"</code></li>
      <li>Action: <code>data-action="click->sidebar#toggleSection"</code></li>
      <li>Targets: <code>data-sidebar-target="section|toggle|content|icon"</code></li>
      <li>Check browser console for debug messages</li>
    </ul>
  </div>
</div>

<script>
  // Additional debugging
  document.addEventListener('DOMContentLoaded', function() {
    console.log('🔍 Debug: DOM loaded, checking sidebar controller...')
    
    const sidebarElement = document.querySelector('[data-controller="sidebar"]')
    if (sidebarElement) {
      console.log('✅ Found sidebar element:', sidebarElement)
    } else {
      console.log('❌ Sidebar element not found')
    }
    
    const toggleButtons = document.querySelectorAll('[data-action*="sidebar#toggleSection"]')
    console.log('🔘 Found toggle buttons:', toggleButtons.length)
    
    toggleButtons.forEach((button, index) => {
      button.addEventListener('click', function(e) {
        console.log(`🖱️ Button ${index + 1} clicked:`, e.target)
      })
    })
  })
</script>

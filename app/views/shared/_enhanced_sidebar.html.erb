<div class="flex flex-col h-full" 
     style="background-color: var(--color-white);"
     data-controller="sidebar"
     data-sidebar-default-expanded-value='["overview", "data-management"]'
     data-sidebar-persist-state-value="true">
  
  <!-- Mobile close button -->
  <div class="lg:hidden flex items-center justify-between" 
       style="height: var(--header-height); padding: 0 var(--space-4); border-bottom: 1px solid var(--color-gray-200);">
    <h2 style="font-size: var(--text-lg); font-weight: var(--font-weight-semibold); color: var(--color-gray-900);">Menu</h2>
    <button type="button"
            class="btn-ghost"
            style="padding: var(--space-2); border-radius: var(--radius-md); color: var(--color-gray-400);"
            data-action="click->sidebar#closeMobile"
            aria-label="Close navigation menu">
      <span class="sr-only">Close menu</span>
      <svg style="width: 1.5rem; height: 1.5rem;" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
      </svg>
    </button>
  </div>

  <!-- Logo section -->
  <div class="hidden lg:flex items-center flex-shrink-0" 
       style="height: var(--header-height); padding: 0 var(--space-6); background: linear-gradient(135deg, var(--color-primary-600), var(--color-secondary-600));">
    <div class="flex items-center">
      <div class="flex-shrink-0">
        <div style="height: 2rem; width: 2rem; background-color: var(--color-white); border-radius: var(--radius-lg); display: flex; align-items: center; justify-content: center;">
          <svg style="height: 1.25rem; width: 1.25rem; color: var(--color-primary-600);" fill="currentColor" viewBox="0 0 20 20" aria-hidden="true">
            <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"/>
          </svg>
        </div>
      </div>
      <div style="margin-left: var(--space-3);">
        <h1 class="nav-logo" style="color: var(--color-white); font-size: var(--text-xl); font-weight: var(--font-weight-bold);">DataReflow</h1>
      </div>
    </div>
  </div>

  <!-- Navigation -->
  <nav class="flex-1 overflow-y-auto"
       style="padding: var(--space-4) var(--space-3); display: flex; flex-direction: column;"
       role="navigation"
       aria-label="Main navigation">

    <!-- Overview Section -->
    <div data-sidebar-target="section" data-section-id="overview" style="margin-bottom: var(--space-4);">
      <button type="button"
              data-sidebar-target="toggle"
              data-action="click->sidebar#toggleSection"
              class="nav-section-toggle"
              aria-expanded="true"
              aria-controls="overview-content">
        <div class="flex items-center">
          <svg data-sidebar-target="icon" 
               style="width: 1rem; height: 1rem; margin-right: var(--space-2); transition: transform 0.3s ease; transform: rotate(90deg);" 
               fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
          </svg>
          <span style="font-size: var(--text-xs); font-weight: var(--font-weight-semibold); text-transform: uppercase; letter-spacing: 0.05em;">Overview</span>
        </div>
      </button>
      
      <div data-sidebar-target="content" id="overview-content" aria-hidden="false">
        <%= link_to subdomain_root_path,
            'aria-current': (request.path == subdomain_root_path ? 'page' : nil),
            'aria-label': 'Dashboard - Main overview page',
            style: nav_item_styles(request.path == subdomain_root_path) do %>
          <svg style="margin-right: var(--space-3); width: 1.25rem; height: 1.25rem;" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2zm0 0V9a2 2 0 012-2h6l2 2h6a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2z"/>
          </svg>
          <span style="font-weight: var(--font-weight-medium); flex: 1;">Dashboard</span>
        <% end %>

        <%= link_to notifications_path,
            'aria-current': (request.path.start_with?('/notifications') ? 'page' : nil),
            'aria-label': 'Notifications - View alerts and updates',
            style: nav_item_styles(request.path.start_with?('/notifications')) do %>
          <svg style="margin-right: var(--space-3); width: 1.25rem; height: 1.25rem;" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5v-5zM4 19h6v-6H4v6zM16 3h5v5h-5V3zM4 3h6v6H4V3z"/>
          </svg>
          <span style="font-weight: var(--font-weight-medium); flex: 1;">Notifications</span>
          <% if unread_notifications_count > 0 %>
            <%= nav_badge(unread_notifications_count, 'primary') %>
          <% end %>
        <% end %>
      </div>
    </div>

    <!-- Data Management Section -->
    <div data-sidebar-target="section" data-section-id="data-management" style="margin-bottom: var(--space-4);">
      <button type="button"
              data-sidebar-target="toggle"
              data-action="click->sidebar#toggleSection"
              class="nav-section-toggle"
              aria-expanded="true"
              aria-controls="data-management-content">
        <div class="flex items-center">
          <svg data-sidebar-target="icon" 
               style="width: 1rem; height: 1rem; margin-right: var(--space-2); transition: transform 0.3s ease; transform: rotate(90deg);" 
               fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
          </svg>
          <span style="font-size: var(--text-xs); font-weight: var(--font-weight-semibold); text-transform: uppercase; letter-spacing: 0.05em;">Data Management</span>
        </div>
      </button>
      
      <div data-sidebar-target="content" id="data-management-content" aria-hidden="false">
        <%= link_to pipelines_path,
            'aria-current': (request.path.start_with?('/pipelines') ? 'page' : nil),
            'aria-label': 'Pipelines - Manage data pipelines',
            style: nav_item_styles(request.path.start_with?('/pipelines')) do %>
          <svg style="margin-right: var(--space-3); width: 1.25rem; height: 1.25rem;" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"/>
          </svg>
          <span style="font-weight: var(--font-weight-medium); flex: 1;">Pipelines</span>
          <%= nav_badge(@pipeline_metrics&.dig(:total_pipelines), 'secondary') %>
        <% end %>

        <%= link_to data_connectors_path,
            'aria-current': (request.path.start_with?('/connectors') ? 'page' : nil),
            'aria-label': 'Connections - Manage data connections',
            style: nav_item_styles(request.path.start_with?('/connectors')) do %>
          <svg style="margin-right: var(--space-3); width: 1.25rem; height: 1.25rem;" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 9l3 3-3 3m5 0h3M5 20h14a2 2 0 002-2V6a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"/>
          </svg>
          <span style="font-weight: var(--font-weight-medium); flex: 1;">Connections</span>
          <%= nav_badge(@connector_metrics&.dig(:total), 'success') %>
        <% end %>

        <%= link_to dashboards_path,
            'aria-current': (request.path.start_with?('/dashboards') ? 'page' : nil),
            'aria-label': 'Custom Dashboards - Create and manage visualization dashboards',
            style: nav_item_styles(request.path.start_with?('/dashboards')) do %>
          <svg style="margin-right: var(--space-3); width: 1.25rem; height: 1.25rem;" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 5a1 1 0 011-1h14a1 1 0 011 1v2a1 1 0 01-1 1H5a1 1 0 01-1-1V5zM4 13a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H5a1 1 0 01-1-1v-6zM16 13a1 1 0 011-1h2a1 1 0 011 1v6a1 1 0 01-1 1h-2a1 1 0 01-1-1v-6z"/>
          </svg>
          <span style="font-weight: var(--font-weight-medium); flex: 1;">Dashboards</span>
          <span class="badge badge-primary" style="margin-left: auto; font-size: var(--text-xs);">New</span>
        <% end %>
      </div>
    </div>

    <!-- Analytics & Insights Section -->
    <div data-sidebar-target="section" data-section-id="analytics" style="margin-bottom: var(--space-4);">
      <button type="button"
              data-sidebar-target="toggle"
              data-action="click->sidebar#toggleSection"
              class="nav-section-toggle"
              aria-expanded="false"
              aria-controls="analytics-content">
        <div class="flex items-center">
          <svg data-sidebar-target="icon" 
               style="width: 1rem; height: 1rem; margin-right: var(--space-2); transition: transform 0.3s ease;" 
               fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
          </svg>
          <span style="font-size: var(--text-xs); font-weight: var(--font-weight-semibold); text-transform: uppercase; letter-spacing: 0.05em;">Analytics & Insights</span>
        </div>
      </button>
      
      <div data-sidebar-target="content" id="analytics-content" aria-hidden="true" style="display: none;">
        <%= link_to analytics_path,
            'aria-current': (request.path.start_with?('/analytics') ? 'page' : nil),
            'aria-label': 'Analytics - View data insights and reports',
            style: nav_item_styles(request.path.start_with?('/analytics')) do %>
          <svg style="margin-right: var(--space-3); width: 1.25rem; height: 1.25rem;" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
          </svg>
          <span style="font-weight: var(--font-weight-medium); flex: 1;">Analytics</span>
          <% if feature_available?(:advanced_analytics) %>
            <span class="badge badge-info" style="margin-left: auto; font-size: var(--text-xs);">Pro</span>
          <% end %>
        <% end %>

        <% if feature_available?(:advanced_analytics) && current_account.pipelines.any? %>
          <%= link_to ai_pipeline_insight_path(current_account.pipelines.first),
              'aria-current': (request.path.start_with?('/ai') ? 'page' : nil),
              'aria-label': 'AI Insights - Intelligent data recommendations',
              style: nav_item_styles(request.path.start_with?('/ai'), true) do %>
            <svg style="margin-right: var(--space-3); width: 1.25rem; height: 1.25rem;" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"/>
            </svg>
            <span style="font-weight: var(--font-weight-medium); flex: 1;">AI Insights</span>
            <span class="badge badge-warning" style="margin-left: auto; font-size: var(--text-xs);">AI</span>
          <% end %>
        <% end %>
      </div>
    </div>

    <!-- Agent & Revenue Section -->
    <div data-sidebar-target="section" data-section-id="agent" style="margin-bottom: var(--space-4);">
      <button type="button"
              data-sidebar-target="toggle"
              data-action="click->sidebar#toggleSection"
              style="<%= nav_section_header_styles(nav_section_active?(['/agent'])) %>"
              aria-expanded="false"
              aria-controls="agent-content">
        <div class="flex items-center">
          <svg data-sidebar-target="icon"
               style="width: 1rem; height: 1rem; margin-right: var(--space-2); transition: transform 0.3s ease;"
               fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
          </svg>
          <span style="font-size: var(--text-xs); font-weight: var(--font-weight-semibold); text-transform: uppercase; letter-spacing: 0.05em;">Agent & Revenue</span>
        </div>
      </button>

      <div data-sidebar-target="content" id="agent-content" aria-hidden="true" style="display: none;">
        <%= link_to agent_recommendations_path,
            'aria-current': (request.path.start_with?('/agent/recommendations') ? 'page' : nil),
            'aria-label': 'Agent Recommendations - AI-powered business suggestions',
            style: nav_item_styles(request.path.start_with?('/agent/recommendations')) do %>
          <svg style="margin-right: var(--space-3); width: 1.25rem; height: 1.25rem;" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"/>
          </svg>
          <span style="font-weight: var(--font-weight-medium); flex: 1;">Recommendations</span>
          <span class="badge badge-warning" style="margin-left: auto; font-size: var(--text-xs);">AI</span>
        <% end %>

        <%= link_to agent_templates_path,
            'aria-current': (request.path.start_with?('/agent/templates') ? 'page' : nil),
            'aria-label': 'Templates - Browse and manage data templates',
            style: nav_item_styles(request.path.start_with?('/agent/templates')) do %>
          <svg style="margin-right: var(--space-3); width: 1.25rem; height: 1.25rem;" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"/>
          </svg>
          <span style="font-weight: var(--font-weight-medium); flex: 1;">Templates</span>
        <% end %>

        <%= link_to agent_revenue_index_path,
            'aria-current': (request.path.start_with?('/agent/revenue') ? 'page' : nil),
            'aria-label': 'Revenue Analytics - Track passive income and earnings',
            style: nav_item_styles(request.path.start_with?('/agent/revenue')) do %>
          <svg style="margin-right: var(--space-3); width: 1.25rem; height: 1.25rem;" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
          </svg>
          <span style="font-weight: var(--font-weight-medium); flex: 1;">Revenue</span>
          <span class="badge badge-success" style="margin-left: auto; font-size: var(--text-xs);">$</span>
        <% end %>
      </div>
    </div>

    <!-- Team & Settings Section -->
    <div data-sidebar-target="section" data-section-id="settings" style="margin-bottom: var(--space-4);">
      <button type="button"
              data-sidebar-target="toggle"
              data-action="click->sidebar#toggleSection"
              style="<%= nav_section_header_styles(nav_section_active?(['/team_members', '/subscription', '/onboarding'])) %>"
              aria-expanded="false"
              aria-controls="settings-content">
        <div class="flex items-center">
          <svg data-sidebar-target="icon"
               style="width: 1rem; height: 1rem; margin-right: var(--space-2); transition: transform 0.3s ease;"
               fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
          </svg>
          <span style="font-size: var(--text-xs); font-weight: var(--font-weight-semibold); text-transform: uppercase; letter-spacing: 0.05em;">Team & Settings</span>
        </div>
      </button>

      <div data-sidebar-target="content" id="settings-content" aria-hidden="true" style="display: none;">
        <%= link_to team_members_path,
            'aria-current': (request.path.start_with?('/team_members') ? 'page' : nil),
            'aria-label': 'Team Members - Manage team and permissions',
            style: nav_item_styles(request.path.start_with?('/team_members')) do %>
          <svg style="margin-right: var(--space-3); width: 1.25rem; height: 1.25rem;" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"/>
          </svg>
          <span style="font-weight: var(--font-weight-medium); flex: 1;">Team</span>
          <%= nav_badge(current_account&.users&.count, 'secondary') %>
        <% end %>

        <%= link_to subscription_path,
            'aria-current': (request.path == subscription_path ? 'page' : nil),
            'aria-label': 'Billing & Subscription - Manage your plan and payments',
            style: nav_item_styles(request.path == subscription_path) do %>
          <svg style="margin-right: var(--space-3); width: 1.25rem; height: 1.25rem;" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"/>
          </svg>
          <span style="font-weight: var(--font-weight-medium); flex: 1;">Billing</span>
        <% end %>

        <% unless current_account&.onboarding_completed? %>
          <%= link_to onboarding_index_path,
              'aria-current': (request.path.start_with?('/onboarding') ? 'page' : nil),
              'aria-label': 'Onboarding - Complete your account setup',
              style: nav_item_styles(request.path.start_with?('/onboarding')) do %>
            <svg style="margin-right: var(--space-3); width: 1.25rem; height: 1.25rem;" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"/>
            </svg>
            <span style="font-weight: var(--font-weight-medium); flex: 1;">Setup Guide</span>
            <span class="badge badge-warning" style="margin-left: auto; font-size: var(--text-xs);">!</span>
          <% end %>
        <% end %>
      </div>
    </div>
  </nav>

  <!-- Account info -->
  <div class="flex-shrink-0" style="border-top: 1px solid var(--color-gray-200); padding: var(--space-4);">
    <div class="flex items-center">
      <div class="flex-shrink-0">
        <div style="height: 2rem; width: 2rem; background: linear-gradient(135deg, var(--color-primary-500), var(--color-secondary-500)); border-radius: var(--radius-full); display: flex; align-items: center; justify-content: center;">
          <span style="font-size: var(--text-sm); font-weight: var(--font-weight-medium); color: var(--color-white);">
            <%= @account&.name&.first&.upcase || current_user&.email&.first&.upcase || 'U' %>
          </span>
        </div>
      </div>
      <div style="margin-left: var(--space-3); min-width: 0; flex: 1;">
        <p style="font-size: var(--text-sm); font-weight: var(--font-weight-medium); color: var(--color-gray-900); overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">
          <%= @account&.name || current_user&.email %>
        </p>
        <p style="font-size: var(--text-xs); color: var(--color-gray-500); overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">
          <%= @account&.subscription&.plan&.humanize || 'Free Plan' %>
        </p>
      </div>
    </div>
  </div>
</div>

<div class="flex flex-col h-full" style="background-color: var(--color-white);">
  <!-- Mobile close button -->
  <div class="lg:hidden flex items-center justify-between" style="height: var(--header-height); padding: 0 var(--space-4); border-bottom: 1px solid var(--color-gray-200);">
    <h2 style="font-size: var(--text-lg); font-weight: var(--font-weight-semibold); color: var(--color-gray-900);">Menu</h2>
    <button type="button"
            class="btn-ghost"
            style="padding: var(--space-2); border-radius: var(--radius-md); color: var(--color-gray-400);"
            data-action="click->navigation#closeMobile">
      <span class="sr-only">Close menu</span>
      <svg style="width: 1.5rem; height: 1.5rem;" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
      </svg>
    </button>
  </div>

  <!-- Logo -->
  <div class="hidden lg:flex items-center flex-shrink-0" style="height: var(--header-height); padding: 0 var(--space-6); background: linear-gradient(135deg, var(--color-primary-600), var(--color-secondary-600));">
    <div class="flex items-center">
      <div class="flex-shrink-0">
        <div style="height: 2rem; width: 2rem; background-color: var(--color-white); border-radius: var(--radius-lg); display: flex; align-items: center; justify-content: center;">
          <svg style="height: 1.25rem; width: 1.25rem; color: var(--color-primary-600);" fill="currentColor" viewBox="0 0 20 20">
            <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"/>
          </svg>
        </div>
      </div>
      <div style="margin-left: var(--space-3);">
        <h1 class="nav-logo" style="color: var(--color-white);">DataReflow</h1>
      </div>
    </div>
  </div>

  <!-- Navigation -->
  <nav class="flex-1 overflow-y-auto"
       style="padding: var(--space-4) var(--space-3); gap: var(--space-1); display: flex; flex-direction: column;"
       role="navigation"
       aria-label="Main navigation">

    <!-- Dashboard Section -->
    <div style="margin-bottom: var(--space-6);">
      <h3 style="font-size: var(--text-xs); font-weight: var(--font-weight-semibold); color: var(--color-gray-500); text-transform: uppercase; letter-spacing: 0.05em; margin-bottom: var(--space-3); padding: 0 var(--space-2);">
        Overview
      </h3>
      <%= link_to subdomain_root_path,
          class: "nav-link #{nav_link_classes(request.path == subdomain_root_path)}",
          'aria-current': (request.path == subdomain_root_path ? 'page' : nil),
          'aria-label': 'Dashboard - Main overview page',
          style: nav_link_styles(request.path == subdomain_root_path) do %>
        <svg style="margin-right: var(--space-3); width: 1.25rem; height: 1.25rem;" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"/>
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5a2 2 0 012-2h2a2 2 0 012 2v2H8V5z"/>
        </svg>
        <span style="font-weight: var(--font-weight-medium);">Dashboard</span>
      <% end %>
    </div>

    <!-- Data Management Section -->
    <div style="margin-bottom: var(--space-6);">
      <h3 style="font-size: var(--text-xs); font-weight: var(--font-weight-semibold); color: var(--color-gray-500); text-transform: uppercase; letter-spacing: 0.05em; margin-bottom: var(--space-3); padding: 0 var(--space-2);">
        Data Management
      </h3>

      <%= link_to pipelines_path,
          class: "nav-link #{nav_link_classes(request.path.start_with?('/pipelines'))}",
          'aria-current': (request.path.start_with?('/pipelines') ? 'page' : nil),
          'aria-label': 'Pipelines - Manage data pipelines',
          style: nav_link_styles(request.path.start_with?('/pipelines')) do %>
        <svg style="margin-right: var(--space-3); width: 1.25rem; height: 1.25rem;" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"/>
        </svg>
        <span style="font-weight: var(--font-weight-medium); flex: 1;">Pipelines</span>
        <span class="badge badge-primary">
          <%= @pipeline_metrics&.dig(:total_pipelines) || current_account&.pipelines&.count || 0 %>
        </span>
      <% end %>

      <%= link_to data_connectors_path,
          class: "nav-link #{nav_link_classes(request.path.start_with?('/connectors'))}",
          'aria-current': (request.path.start_with?('/connectors') ? 'page' : nil),
          'aria-label': 'Connections - Manage data connections',
          style: nav_link_styles(request.path.start_with?('/connectors')) do %>
        <svg style="margin-right: var(--space-3); width: 1.25rem; height: 1.25rem;" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 9l3 3-3 3m5 0h3M5 20h14a2 2 0 002-2V6a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"/>
        </svg>
        <span style="font-weight: var(--font-weight-medium); flex: 1;">Connections</span>
        <span class="badge badge-success">
          <%= @connector_metrics&.dig(:total) || current_account&.data_connectors&.count || 0 %>
        </span>
      <% end %>
    </div>

    <!-- Analytics Section -->
    <div style="margin-bottom: var(--space-6);">
      <h3 style="font-size: var(--text-xs); font-weight: var(--font-weight-semibold); color: var(--color-gray-500); text-transform: uppercase; letter-spacing: 0.05em; margin-bottom: var(--space-3); padding: 0 var(--space-2);">
        Analytics & Insights
      </h3>

      <%= link_to dashboards_path,
          class: "nav-link #{nav_link_classes(request.path.start_with?('/dashboards'))}",
          'aria-current': (request.path.start_with?('/dashboards') ? 'page' : nil),
          'aria-label': 'Custom Dashboards - Create and manage visualization dashboards',
          style: nav_link_styles(request.path.start_with?('/dashboards')) do %>
        <svg style="margin-right: var(--space-3); width: 1.25rem; height: 1.25rem;" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 5a1 1 0 011-1h14a1 1 0 011 1v2a1 1 0 01-1 1H5a1 1 0 01-1-1V5zM4 13a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H5a1 1 0 01-1-1v-6zM16 13a1 1 0 011-1h2a1 1 0 011 1v6a1 1 0 01-1 1h-2a1 1 0 01-1-1v-6z"/>
        </svg>
        <span style="font-weight: var(--font-weight-medium); flex: 1;">Dashboards</span>
        <span class="badge badge-primary">New</span>
      <% end %>

      <%= link_to analytics_path,
          class: "nav-link #{nav_link_classes(request.path.start_with?('/analytics'))}",
          'aria-current': (request.path.start_with?('/analytics') ? 'page' : nil),
          'aria-label': 'Analytics - View data insights and reports',
          style: nav_link_styles(request.path.start_with?('/analytics')) do %>
        <svg style="margin-right: var(--space-3); width: 1.25rem; height: 1.25rem;" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
        </svg>
        <span style="font-weight: var(--font-weight-medium); flex: 1;">Analytics</span>
        <% if feature_available?(:advanced_analytics) %>
          <span class="badge badge-info">Pro</span>
        <% end %>
      <% end %>
    </div>

    <!-- Team & Settings Section -->
    <div style="margin-bottom: var(--space-6);">
      <h3 style="font-size: var(--text-xs); font-weight: var(--font-weight-semibold); color: var(--color-gray-500); text-transform: uppercase; letter-spacing: 0.05em; margin-bottom: var(--space-3); padding: 0 var(--space-2);">
        Team & Settings
      </h3>

      <%= link_to team_members_path,
          class: "nav-link #{nav_link_classes(request.path.start_with?('/team_members'))}",
          style: nav_link_styles(request.path.start_with?('/team_members')) do %>
        <svg style="margin-right: var(--space-3); width: 1.25rem; height: 1.25rem;" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"/>
        </svg>
        <span style="font-weight: var(--font-weight-medium); flex: 1;">Team</span>
        <span class="badge badge-secondary">
          <%= current_account&.users&.count || 0 %>
        </span>
      <% end %>

      <%= link_to subscription_path,
          class: "nav-link #{nav_link_classes(request.path == subscription_path)}",
          style: nav_link_styles(request.path == subscription_path) do %>
        <svg style="margin-right: var(--space-3); width: 1.25rem; height: 1.25rem;" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"/>
        </svg>
        <span style="font-weight: var(--font-weight-medium);">Billing</span>
      <% end %>
    </div>
  </nav>

  <!-- Account info -->
  <div class="flex-shrink-0" style="border-top: 1px solid var(--color-gray-200); padding: var(--space-4);">
    <div class="flex items-center">
      <div class="flex-shrink-0">
        <div style="height: 2rem; width: 2rem; background: linear-gradient(135deg, var(--color-primary-500), var(--color-secondary-500)); border-radius: var(--radius-full); display: flex; align-items: center; justify-content: center;">
          <span style="font-size: var(--text-sm); font-weight: var(--font-weight-medium); color: var(--color-white);">
            <%= @account.name&.first&.upcase || 'A' %>
          </span>
        </div>
      </div>
      <div style="margin-left: var(--space-3); min-width: 0; flex: 1;">
        <p style="font-size: var(--text-sm); font-weight: var(--font-weight-medium); color: var(--color-gray-900); overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">
          <%= @account.name %>
        </p>
        <p style="font-size: var(--text-xs); color: var(--color-gray-500); overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">
          <%= @account.subscription&.plan&.humanize || 'Free Plan' %>
        </p>
      </div>
    </div>
  </div>
</div>

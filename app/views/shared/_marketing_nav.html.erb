<%# Marketing Navigation Component %>
<nav class="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
  <div class="flex justify-between items-center">
    <%# Logo %>
    <%= link_to root_path, class: "flex items-center space-x-2" do %>
      <div class="w-8 h-8 bg-gradient-to-br from-blue-600 to-indigo-600 rounded-lg flex items-center justify-center">
        <%= svg_icon('lightning', class: 'w-5 h-5 text-white') %>
      </div>
      <span class="text-2xl font-bold text-gray-900">DataReflow</span>
    <% end %>
    
    <%# Desktop Navigation %>
    <div class="hidden md:flex items-center space-x-8">
      <%= link_to root_path(anchor: 'features'),
          class: "text-gray-600 hover:text-gray-900 font-medium transition-colors" do %>
        Features
      <% end %>
      <%= link_to integrations_path,
          class: "text-gray-600 hover:text-gray-900 font-medium transition-colors #{'text-blue-600 font-semibold' if current_page?(integrations_path)}" do %>
        Integrations
      <% end %>
      <%= link_to root_path(anchor: 'pricing'),
          class: "text-gray-600 hover:text-gray-900 font-medium transition-colors" do %>
        Pricing
      <% end %>
      <%= link_to about_path,
          class: "text-gray-600 hover:text-gray-900 font-medium transition-colors #{'text-blue-600 font-semibold' if current_page?(about_path)}" do %>
        About
      <% end %>
      <%= link_to docs_path,
          class: "text-gray-600 hover:text-gray-900 font-medium transition-colors #{'text-blue-600 font-semibold' if current_page?(docs_path)}" do %>
        Docs
      <% end %>
      <%= link_to community_path,
          class: "text-gray-600 hover:text-gray-900 font-medium transition-colors #{'text-blue-600 font-semibold' if current_page?(community_path)}" do %>
        Community
      <% end %>
      <%= link_to new_user_session_path,
          class: "text-gray-600 hover:text-gray-900 font-medium transition-colors" do %>
        Sign In
      <% end %>
      <%= link_to new_user_registration_path,
          class: "bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg font-semibold transition-all duration-200 transform hover:scale-105 shadow-lg hover:shadow-xl" do %>
        Get Started
      <% end %>
    </div>

    <%# Mobile Menu Button %>
    <div class="md:hidden">
      <button id="mobile-menu-button" class="text-gray-600 hover:text-gray-900 focus:outline-none focus:text-gray-900">
        <%= svg_icon('menu', class: 'w-6 h-6') %>
      </button>
    </div>
  </div>

  <%# Mobile Navigation Menu %>
  <div id="mobile-menu" class="hidden md:hidden mt-4 pb-4">
    <div class="flex flex-col space-y-4">
      <%= link_to root_path(anchor: 'features'),
          class: "text-gray-600 hover:text-gray-900 font-medium transition-colors py-2" do %>
        Features
      <% end %>
      <%= link_to integrations_path,
          class: "text-gray-600 hover:text-gray-900 font-medium transition-colors py-2 #{'text-blue-600 font-semibold' if current_page?(integrations_path)}" do %>
        Integrations
      <% end %>
      <%= link_to root_path(anchor: 'pricing'),
          class: "text-gray-600 hover:text-gray-900 font-medium transition-colors py-2" do %>
        Pricing
      <% end %>
      <%= link_to about_path,
          class: "text-gray-600 hover:text-gray-900 font-medium transition-colors py-2 #{'text-blue-600 font-semibold' if current_page?(about_path)}" do %>
        About
      <% end %>
      <%= link_to docs_path,
          class: "text-gray-600 hover:text-gray-900 font-medium transition-colors py-2 #{'text-blue-600 font-semibold' if current_page?(docs_path)}" do %>
        Docs
      <% end %>
      <%= link_to community_path,
          class: "text-gray-600 hover:text-gray-900 font-medium transition-colors py-2 #{'text-blue-600 font-semibold' if current_page?(community_path)}" do %>
        Community
      <% end %>
      <%= link_to help_path,
          class: "text-gray-600 hover:text-gray-900 font-medium transition-colors py-2 #{'text-blue-600 font-semibold' if current_page?(help_path)}" do %>
        Help
      <% end %>
      <%= link_to new_user_session_path,
          class: "text-gray-600 hover:text-gray-900 font-medium transition-colors py-2" do %>
        Sign In
      <% end %>
      <%= link_to new_user_registration_path,
          class: "bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-semibold transition-colors text-center mt-4" do %>
        Get Started
      <% end %>
    </div>
  </div>
</nav>

<%# Mobile Menu JavaScript %>
<script>
document.addEventListener('DOMContentLoaded', function() {
  const mobileMenuButton = document.getElementById('mobile-menu-button');
  const mobileMenu = document.getElementById('mobile-menu');
  
  if (mobileMenuButton && mobileMenu) {
    mobileMenuButton.addEventListener('click', function() {
      mobileMenu.classList.toggle('hidden');
    });
  }
});
</script>

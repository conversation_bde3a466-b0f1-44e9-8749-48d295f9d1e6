<% content_for :page_title, @dashboard.name %>

<div class="dashboard-header" style="padding: var(--space-6); background: linear-gradient(135deg, var(--color-primary-50), var(--color-secondary-50)); border-bottom: 1px solid var(--color-gray-200);">
  <div class="container mx-auto">
    <div class="flex justify-between items-start">
      <div>
        <nav class="breadcrumb" aria-label="Breadcrumb" style="margin-bottom: var(--space-2);">
          <%= link_to "Dashboards", dashboards_path, class: "text-primary hover:text-primary-dark" %>
          <span style="margin: 0 var(--space-2); color: var(--color-gray-400);">/</span>
          <span style="color: var(--color-gray-700);"><%= @dashboard.name %></span>
        </nav>
        
        <h1 style="font-size: var(--text-3xl); font-weight: var(--font-weight-bold); color: var(--color-gray-900); margin-bottom: var(--space-2);">
          <%= @dashboard.name %>
        </h1>
        
        <% if @dashboard.description.present? %>
          <p style="color: var(--color-gray-600); font-size: var(--text-lg);">
            <%= @dashboard.description %>
          </p>
        <% end %>
        
        <div class="flex items-center gap-4" style="margin-top: var(--space-3);">
          <span class="badge badge-<%= @dashboard.is_public ? 'success' : 'secondary' %>">
            <%= @dashboard.is_public ? 'Public' : 'Private' %>
          </span>
          
          <% if @dashboard.refresh_interval.present? %>
            <span class="badge badge-info">
              Auto-refresh: <%= @dashboard.refresh_interval %>s
            </span>
          <% end %>
          
          <span style="color: var(--color-gray-500); font-size: var(--text-sm);">
            Last updated: <%= time_ago_in_words(@dashboard.updated_at) %> ago
          </span>
        </div>
      </div>
      
      <div class="flex gap-2">
        <% if can? :edit, @dashboard %>
          <%= link_to edit_dashboard_path(@dashboard), 
              class: "btn btn-secondary",
              'aria-label': "Edit dashboard" do %>
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"/>
            </svg>
            Edit Dashboard
          <% end %>
        <% end %>
        
        <button type="button" 
                class="btn btn-secondary"
                data-action="click->dashboard#toggleFullscreen"
                aria-label="Toggle fullscreen">
          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5m5 11v-4m0 4h-4m4 0l-5-5m-6 5l5-5m-5 5v-4m0 4h4"/>
          </svg>
        </button>
        
        <button type="button"
                class="btn btn-primary"
                data-action="click->dashboard#refresh"
                aria-label="Refresh dashboard">
          <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"/>
          </svg>
          Refresh
        </button>
      </div>
    </div>
  </div>
</div>

<div class="dashboard-container" 
     data-controller="dashboard"
     data-dashboard-id="<%= @dashboard.id %>"
     data-dashboard-auto-refresh="<%= @dashboard.refresh_interval.present? %>"
     data-dashboard-refresh-interval="<%= @dashboard.refresh_interval || 30 %>"
     style="padding: var(--space-6); background-color: var(--color-gray-50); min-height: calc(100vh - 200px);">
  
  <div class="container mx-auto">
    <!-- DEBUG: @widgets = <%= @widgets.inspect %> -->
    <!-- DEBUG: @widgets.any? = <%= @widgets&.any? %> -->
    <!-- DEBUG: dashboard.widgets.count = <%= @dashboard.widgets.count %> -->
    <% if @widgets && @widgets.any? %>
      <div class="dashboard-grid" 
           style="display: grid; 
                  grid-template-columns: repeat(<%= @dashboard.layout_config['cols'] || 12 %>, 1fr);
                  gap: var(--space-4);
                  auto-rows: minmax(300px, auto);">
        
        <% @widgets.each do |widget| %>
          <div class="widget-container" 
               id="widget-<%= widget.id %>"
               data-widget-id="<%= widget.id %>"
               data-widget-type="<%= widget.widget_type %>"
               style="grid-column: span <%= widget.position['w'] || 6 %>; 
                      grid-row: span <%= widget.position['h'] || 1 %>;" >
            
            <div class="widget-card" 
                 style="background: white; 
                        border-radius: var(--radius-lg); 
                        box-shadow: var(--shadow-sm); 
                        height: 100%; 
                        display: flex; 
                        flex-direction: column;
                        overflow: hidden;">
              
              <!-- Widget Header -->
              <div class="widget-header" 
                   style="padding: var(--space-4); 
                          border-bottom: 1px solid var(--color-gray-200);
                          display: flex;
                          justify-content: space-between;
                          align-items: center;">
                <h3 style="font-size: var(--text-lg); 
                           font-weight: var(--font-weight-semibold); 
                           color: var(--color-gray-900);">
                  <%= widget.name %>
                </h3>
                
                <div class="widget-actions flex gap-2">
                  <% if widget.config['refreshable'] %>
                    <button type="button"
                            class="btn-ghost p-2"
                            data-action="click->dashboard#refreshWidget"
                            data-widget-id="<%= widget.id %>"
                            aria-label="Refresh widget">
                      <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"/>
                      </svg>
                    </button>
                  <% end %>
                  
                  <% if can? :edit, @dashboard %>
                    <button type="button"
                            class="btn-ghost p-2"
                            data-action="click->dashboard#editWidget"
                            data-widget-id="<%= widget.id %>"
                            aria-label="Edit widget">
                      <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"/>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                      </svg>
                    </button>
                  <% end %>
                </div>
              </div>
              
              <!-- Widget Body -->
              <div class="widget-body" 
                   style="flex: 1; 
                          padding: var(--space-4); 
                          overflow: auto;">
                
                <% case widget.widget_type %>
                <% when 'line_chart', 'bar_chart', 'pie_chart', 'area_chart' %>
                  <div class="chart-container" 
                       data-controller="chart"
                       data-chart-type="<%= widget.widget_type %>"
                       data-chart-widget-id="<%= widget.id %>"
                       data-chart-config="<%= widget.chart_config.to_json %>"
                       style="height: 100%; min-height: 250px;">
                    <canvas id="chart-<%= widget.id %>"></canvas>
                  </div>
                
                <% when 'gauge_chart' %>
                  <div class="gauge-container" 
                       data-controller="gauge"
                       data-gauge-widget-id="<%= widget.id %>"
                       data-gauge-config="<%= widget.config.to_json %>"
                       style="height: 100%; min-height: 200px; display: flex; align-items: center; justify-content: center;">
                    <canvas id="gauge-<%= widget.id %>"></canvas>
                  </div>
                
                <% when 'metric_card', 'kpi_card' %>
                  <div class="metric-container" style="text-align: center; padding: var(--space-8) 0;">
                    <div class="metric-value" 
                         style="font-size: var(--text-5xl); 
                                font-weight: var(--font-weight-bold); 
                                color: var(--color-<%= widget.config['color'] || 'primary' %>-600);
                                margin-bottom: var(--space-2);">
                      <span data-metric-value="<%= widget.id %>">--</span>
                    </div>
                    <div class="metric-label" 
                         style="font-size: var(--text-lg); 
                                color: var(--color-gray-600);">
                      <%= widget.config['label'] %>
                    </div>
                    <% if widget.config['show_trend'] %>
                      <div class="metric-trend" 
                           style="margin-top: var(--space-4); 
                                  display: flex; 
                                  align-items: center; 
                                  justify-content: center;
                                  gap: var(--space-2);">
                        <span data-metric-trend="<%= widget.id %>">--</span>
                      </div>
                    <% end %>
                  </div>
                
                <% when 'data_table' %>
                  <div class="table-container" style="overflow-x: auto;">
                    <table class="table table-striped" data-table-widget="<%= widget.id %>">
                      <thead>
                        <tr>
                          <% (widget.config['columns'] || []).each do |column| %>
                            <th><%= column['label'] %></th>
                          <% end %>
                        </tr>
                      </thead>
                      <tbody>
                        <tr>
                          <td colspan="<%= widget.config['columns']&.size || 1 %>" style="text-align: center; padding: var(--space-8);">
                            <div class="spinner-border" role="status">
                              <span class="sr-only">Loading data...</span>
                            </div>
                          </td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                
                <% when 'text' %>
                  <div class="text-widget" style="padding: var(--space-4);">
                    <%= widget.config['content']&.html_safe %>
                  </div>
                
                <% else %>
                  <div class="widget-placeholder" style="padding: var(--space-8); text-align: center; color: var(--color-gray-500);">
                    Widget type '<%= widget.widget_type %>' is not yet implemented
                  </div>
                <% end %>
              </div>
              
              <!-- Widget Footer (optional) -->
              <% if widget.config['show_footer'] %>
                <div class="widget-footer" 
                     style="padding: var(--space-3) var(--space-4); 
                            border-top: 1px solid var(--color-gray-200);
                            font-size: var(--text-sm);
                            color: var(--color-gray-500);">
                  <span data-widget-updated="<%= widget.id %>">
                    Last updated: <%= time_ago_in_words(widget.updated_at) %> ago
                  </span>
                </div>
              <% end %>
            </div>
          </div>
        <% end %>
      </div>
    <% else %>
      <!-- Empty State -->
      <div class="empty-state" style="text-align: center; padding: var(--space-12) var(--space-4);">
        <svg style="width: 8rem; height: 8rem; margin: 0 auto var(--space-4); color: var(--color-gray-300);" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M9 17V7m0 10a2 2 0 01-2 2H5a2 2 0 01-2-2V7a2 2 0 012-2h2a2 2 0 012 2m0 10a2 2 0 002 2h2a2 2 0 002-2M9 7a2 2 0 012-2h2a2 2 0 012 2m0 10V7m0 10a2 2 0 002 2h2a2 2 0 002-2V7a2 2 0 00-2-2h-2a2 2 0 00-2 2"/>
        </svg>
        
        <h2 style="font-size: var(--text-2xl); font-weight: var(--font-weight-semibold); color: var(--color-gray-900); margin-bottom: var(--space-2);">
          No widgets yet
        </h2>
        
        <p style="color: var(--color-gray-600); margin-bottom: var(--space-6); max-width: 32rem; margin-left: auto; margin-right: auto;">
          This dashboard doesn't have any widgets. Add charts, metrics, tables, and other visualizations to bring your data to life.
        </p>
        
        <% if can? :edit, @dashboard %>
          <%= link_to add_widget_dashboard_path(@dashboard), 
              class: "btn btn-primary btn-lg",
              data: { turbo_frame: "modal" } do %>
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
            </svg>
            Add Your First Widget
          <% end %>
        <% end %>
      </div>
    <% end %>
  </div>
</div>

<!-- Auto-refresh indicator -->
<% if @dashboard.refresh_interval.present? %>
  <div class="auto-refresh-indicator"
       data-dashboard-target="refreshIndicator"
       style="position: fixed; 
              bottom: var(--space-4); 
              right: var(--space-4); 
              background: var(--color-white); 
              border: 1px solid var(--color-gray-200);
              border-radius: var(--radius-full);
              padding: var(--space-2) var(--space-3);
              box-shadow: var(--shadow-lg);
              display: flex;
              align-items: center;
              gap: var(--space-2);
              font-size: var(--text-sm);">
    <div class="spinner-border spinner-border-sm" role="status">
      <span class="sr-only">Auto-refreshing...</span>
    </div>
    <span>Auto-refresh in <span data-dashboard-target="countdown"><%= @dashboard.refresh_interval %></span>s</span>
  </div>
<% end %>

<!-- Page Header -->
<div class="content-header">
  <h1 class="page-title">Create New Dashboard</h1>
  <p class="page-subtitle">Choose from our quick start templates or create a custom dashboard</p>
</div>

<!-- Quick Start Templates Section -->
<div class="templates-section">
  <h2 class="section-title">Quick Start Templates</h2>
  <div class="templates-grid">
      <!-- Sales Dashboard Template -->
      <div class="template-card">
        <div class="template-icon">
          <i class="fas fa-chart-line"></i>
        </div>
        <h3 class="template-title">Sales Dashboard</h3>
        <p class="template-description">Track revenue, orders, conversion rates, and sales performance metrics</p>
        <ul class="template-features">
          <li>Revenue Overview</li>
          <li>Sales Funnel</li>
          <li>Top Products</li>
          <li>Customer Segments</li>
        </ul>
        <%= button_to "Use Template", dashboards_path, 
                    params: { 
                      dashboard: { 
                        name: "Sales Dashboard", 
                        description: "Track revenue, orders, conversion rates, and sales performance metrics",
                        dashboard_type: "template"
                      },
                      template: "sales" 
                    },
                    method: :post, 
                    class: "btn btn-primary template-btn" %>
      </div>

      <!-- Marketing Dashboard Template -->
      <div class="template-card">
        <div class="template-icon">
          <i class="fas fa-bullhorn"></i>
        </div>
        <h3 class="template-title">Marketing Dashboard</h3>
        <p class="template-description">Monitor campaigns, traffic sources, engagement, and ROI</p>
        <ul class="template-features">
          <li>Campaign Performance</li>
          <li>Traffic Analytics</li>
          <li>Lead Generation</li>
          <li>Social Media Metrics</li>
        </ul>
        <%= button_to "Use Template", dashboards_path, 
                    params: { 
                      dashboard: { 
                        name: "Marketing Dashboard", 
                        description: "Monitor campaigns, traffic sources, engagement, and ROI",
                        dashboard_type: "template"
                      },
                      template: "marketing" 
                    },
                    method: :post, 
                    class: "btn btn-primary template-btn" %>
      </div>

      <!-- Operations Dashboard Template -->
      <div class="template-card">
        <div class="template-icon">
          <i class="fas fa-cogs"></i>
        </div>
        <h3 class="template-title">Operations Dashboard</h3>
        <p class="template-description">Track inventory, fulfillment, support tickets, and operational KPIs</p>
        <ul class="template-features">
          <li>Inventory Levels</li>
          <li>Order Fulfillment</li>
          <li>Support Metrics</li>
          <li>System Performance</li>
        </ul>
        <%= button_to "Use Template", dashboards_path, 
                    params: { 
                      dashboard: { 
                        name: "Operations Dashboard", 
                        description: "Track inventory, fulfillment, support tickets, and operational KPIs",
                        dashboard_type: "template"
                      },
                      template: "operational" 
                    },
                    method: :post, 
                    class: "btn btn-primary template-btn" %>
      </div>

      <!-- Financial Dashboard Template -->
      <div class="template-card">
        <div class="template-icon">
          <i class="fas fa-dollar-sign"></i>
        </div>
        <h3 class="template-title">Financial Dashboard</h3>
        <p class="template-description">Monitor cash flow, expenses, profit margins, and financial health</p>
        <ul class="template-features">
          <li>P&L Statement</li>
          <li>Cash Flow</li>
          <li>Budget vs Actual</li>
          <li>Financial Ratios</li>
        </ul>
        <%= button_to "Use Template", dashboards_path, 
                    params: { 
                      dashboard: { 
                        name: "Financial Dashboard", 
                        description: "Monitor cash flow, expenses, profit margins, and financial health",
                        dashboard_type: "template"
                      },
                      template: "financial" 
                    },
                    method: :post, 
                    class: "btn btn-primary template-btn" %>
      </div>

      <!-- Customer Success Template -->
      <div class="template-card">
        <div class="template-icon">
          <i class="fas fa-users"></i>
        </div>
        <h3 class="template-title">Customer Success</h3>
        <p class="template-description">Track NPS, churn, retention, and customer satisfaction metrics</p>
        <ul class="template-features">
          <li>Customer Health Score</li>
          <li>Churn Analysis</li>
          <li>NPS Tracking</li>
          <li>Support Tickets</li>
        </ul>
        <%= button_to "Use Template", dashboards_path, 
                    params: { 
                      dashboard: { 
                        name: "Customer Success Dashboard", 
                        description: "Track NPS, churn, retention, and customer satisfaction metrics",
                        dashboard_type: "template"
                      },
                      template: "customer_success" 
                    },
                    method: :post, 
                    class: "btn btn-primary template-btn" %>
      </div>

      <!-- Executive Overview Template -->
      <div class="template-card">
        <div class="template-icon">
          <i class="fas fa-briefcase"></i>
        </div>
        <h3 class="template-title">Executive Overview</h3>
        <p class="template-description">High-level KPIs and business metrics for executive decision making</p>
        <ul class="template-features">
          <li>Company KPIs</li>
          <li>Department Performance</li>
          <li>Strategic Goals</li>
          <li>Risk Indicators</li>
        </ul>
        <%= button_to "Use Template", dashboards_path, 
                    params: { 
                      dashboard: { 
                        name: "Executive Overview", 
                        description: "High-level KPIs and business metrics for executive decision making",
                        dashboard_type: "template"
                      },
                      template: "executive" 
                    },
                    method: :post, 
                    class: "btn btn-primary template-btn" %>
      </div>
    </div> <!-- End templates-grid -->
</div> <!-- End templates-section -->

<!-- Custom Dashboard Section -->
<div class="custom-dashboard-section">
  <h2 class="section-title">Or Create a Custom Dashboard</h2>
  <div class="custom-dashboard-card">
          <p class="custom-description">Start with a blank dashboard and add your own widgets and visualizations</p>
          <%= form_with model: Dashboard.new, url: dashboards_path, local: true, class: "custom-form" do |f| %>
            <div class="form-group">
              <%= f.label :name, "Dashboard Name", class: "form-label" %>
              <%= f.text_field :name, 
                              placeholder: "e.g., Q4 Performance Dashboard", 
                              class: "form-control",
                              required: true %>
            </div>
            <div class="form-group">
              <%= f.label :description, "Description", class: "form-label" %>
              <%= f.text_area :description, 
                             placeholder: "Brief description of your dashboard's purpose...", 
                             class: "form-control",
                             rows: 3 %>
            </div>
            <div class="form-actions">
              <%= f.submit "Create Custom Dashboard", class: "btn btn-success" %>
              <%= link_to "Cancel", dashboards_path, class: "btn btn-secondary" %>
            </div>
          <% end %>
        </div>
</div>

<%= stylesheet_link_tag 'dashboard', media: 'all' %>

<style>
  /* Template-specific styles */
  .content-header {
    margin-bottom: 2rem;
  }

  .page-title {
    font-size: 2rem;
    font-weight: 600;
    color: #1a1a1a;
    margin-bottom: 0.5rem;
  }

  .page-subtitle {
    font-size: 1.1rem;
    color: #666;
  }

  .templates-section {
    margin-bottom: 3rem;
  }

  .section-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 1.5rem;
  }

  .templates-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
  }

  .template-card {
    background: white;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    padding: 1.5rem;
    transition: all 0.3s ease;
    position: relative;
  }

  .template-card:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
  }

  .template-icon {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1rem;
  }

  .template-icon i {
    font-size: 1.5rem;
    color: white;
  }

  .template-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 0.5rem;
  }

  .template-description {
    color: #666;
    margin-bottom: 1rem;
    line-height: 1.5;
  }

  .template-features {
    list-style: none;
    padding: 0;
    margin: 0 0 1.5rem 0;
  }

  .template-features li {
    padding: 0.25rem 0;
    color: #555;
    font-size: 0.9rem;
  }

  .template-features li:before {
    content: "✓ ";
    color: #4caf50;
    font-weight: bold;
    margin-right: 0.5rem;
  }

  .template-btn {
    width: 100%;
    padding: 0.75rem;
    background: #667eea;
    color: white;
    border: none;
    border-radius: 6px;
    font-weight: 500;
    cursor: pointer;
    transition: background 0.3s ease;
    text-align: center;
    text-decoration: none;
    display: inline-block;
  }

  .template-btn:hover {
    background: #5a67d8;
    color: white;
    text-decoration: none;
  }

  .custom-dashboard-section {
    margin-top: 3rem;
  }

  .custom-dashboard-card {
    background: white;
    border: 2px dashed #d0d0d0;
    border-radius: 8px;
    padding: 2rem;
    max-width: 600px;
  }

  .custom-description {
    color: #666;
    margin-bottom: 1.5rem;
  }

  .custom-form .form-group {
    margin-bottom: 1.5rem;
  }

  .form-label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: #333;
  }

  .form-control {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #d0d0d0;
    border-radius: 6px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
  }

  .form-control:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
  }

  .form-actions {
    display: flex;
    gap: 1rem;
    margin-top: 1.5rem;
  }

  .btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 6px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
  }

  .btn-success {
    background: #4caf50;
    color: white;
  }

  .btn-success:hover {
    background: #45a049;
  }

  .btn-secondary {
    background: #f0f0f0;
    color: #333;
  }

  .btn-secondary:hover {
    background: #e0e0e0;
  }
</style>

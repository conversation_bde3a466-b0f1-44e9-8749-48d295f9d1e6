<% content_for :page_title, "Dashboards" %>

<div style="max-width: 1280px; margin: 0 auto; padding: var(--space-6);">
  <!-- Header Section -->
  <div style="margin-bottom: var(--space-8);">
    <div style="display: flex; align-items: center; justify-content: space-between;">
      <div>
        <h1 style="font-size: var(--text-3xl); font-weight: var(--font-weight-bold); color: var(--color-gray-900);">Analytics Dashboards</h1>
        <p style="margin-top: var(--space-2); font-size: var(--text-sm); color: var(--color-gray-600);">
          Create custom dashboards to visualize your data and track key metrics
        </p>
      </div>
      <div style="display: flex; align-items: center; gap: var(--space-3);">
        <%= link_to new_dashboard_path, class: "btn btn-primary" do %>
          <svg style="width: 1.25rem; height: 1.25rem; margin-right: var(--space-2);" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"/>
          </svg>
          Create Dashboard
        <% end %>
      </div>
    </div>
  </div>

  <!-- Quick Stats -->
  <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: var(--space-6); margin-bottom: var(--space-8);">
    <div style="background-color: var(--color-white); border-radius: var(--radius-lg); box-shadow: var(--shadow-sm); border: 1px solid var(--color-gray-200); padding: var(--space-6);">
      <div style="display: flex; align-items: center;">
        <div style="flex-shrink: 0;">
          <div style="height: 3rem; width: 3rem; background-color: #dbeafe; border-radius: var(--radius-lg); display: flex; align-items: center; justify-content: center;">
            <svg style="height: 1.5rem; width: 1.5rem; color: #2563eb;" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
            </svg>
          </div>
        </div>
        <div style="margin-left: var(--space-4);">
          <p style="font-size: var(--text-sm); font-weight: var(--font-weight-medium); color: var(--color-gray-500);">Total Dashboards</p>
          <p style="font-size: var(--text-2xl); font-weight: var(--font-weight-semibold); color: var(--color-gray-900);"><%= @dashboards.count %></p>
        </div>
      </div>
    </div>

    <div style="background-color: var(--color-white); border-radius: var(--radius-lg); box-shadow: var(--shadow-sm); border: 1px solid var(--color-gray-200); padding: var(--space-6);">
      <div style="display: flex; align-items: center;">
        <div style="flex-shrink: 0;">
          <div style="height: 3rem; width: 3rem; background-color: #d1fae5; border-radius: var(--radius-lg); display: flex; align-items: center; justify-content: center;">
            <svg style="height: 1.5rem; width: 1.5rem; color: #059669;" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"/>
            </svg>
          </div>
        </div>
        <div style="margin-left: var(--space-4);">
          <p style="font-size: var(--text-sm); font-weight: var(--font-weight-medium); color: var(--color-gray-500);">Total Widgets</p>
          <p style="font-size: var(--text-2xl); font-weight: var(--font-weight-semibold); color: var(--color-gray-900);"><%= @sidebar_metrics[:total_widgets] || 0 %></p>
        </div>
      </div>
    </div>

    <div style="background-color: var(--color-white); border-radius: var(--radius-lg); box-shadow: var(--shadow-sm); border: 1px solid var(--color-gray-200); padding: var(--space-6);">
      <div style="display: flex; align-items: center;">
        <div style="flex-shrink: 0;">
          <div style="height: 3rem; width: 3rem; background-color: #e9d5ff; border-radius: var(--radius-lg); display: flex; align-items: center; justify-content: center;">
            <svg style="height: 1.5rem; width: 1.5rem; color: #7c3aed;" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m9.032 4.026a9.001 9.001 0 01-7.432 0m9.032-4.026A9.001 9.001 0 0112 3c-4.474 0-8.268 3.12-9.032 7.326m0 0A9.001 9.001 0 0012 21c4.474 0 8.268-3.12 9.032-7.326"/>
            </svg>
          </div>
        </div>
        <div style="margin-left: var(--space-4);">
          <p style="font-size: var(--text-sm); font-weight: var(--font-weight-medium); color: var(--color-gray-500);">Shared Dashboards</p>
          <p style="font-size: var(--text-2xl); font-weight: var(--font-weight-semibold); color: var(--color-gray-900);"><%= @sidebar_metrics[:shared_dashboards] || 0 %></p>
        </div>
      </div>
    </div>

    <div style="background-color: var(--color-white); border-radius: var(--radius-lg); box-shadow: var(--shadow-sm); border: 1px solid var(--color-gray-200); padding: var(--space-6);">
      <div style="display: flex; align-items: center;">
        <div style="flex-shrink: 0;">
          <div style="height: 3rem; width: 3rem; background-color: #fef3c7; border-radius: var(--radius-lg); display: flex; align-items: center; justify-content: center;">
            <svg style="height: 1.5rem; width: 1.5rem; color: #d97706;" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"/>
            </svg>
          </div>
        </div>
        <div style="margin-left: var(--space-4);">
          <p style="font-size: var(--text-sm); font-weight: var(--font-weight-medium); color: var(--color-gray-500);">Default Dashboard</p>
          <p style="font-size: var(--text-lg); font-weight: var(--font-weight-semibold); color: var(--color-gray-900); text-overflow: ellipsis; overflow: hidden; white-space: nowrap;">
            <%= @default_dashboard&.name || "Not Set" %>
          </p>
        </div>
      </div>
    </div>
  </div>

  <!-- Dashboard Templates -->
  <div class="mb-8">
    <h2 class="text-lg font-semibold text-gray-900 mb-4">Quick Start Templates</h2>
    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
      <% [
        { name: 'Executive Dashboard', template: 'executive', icon: 'chart-bar', color: 'blue', description: 'High-level KPIs and business metrics' },
        { name: 'Operational Dashboard', template: 'operational', icon: 'cog', color: 'green', description: 'Real-time pipeline performance and monitoring' },
        { name: 'Analytics Dashboard', template: 'analytics', icon: 'chart-line', color: 'purple', description: 'Deep dive into data trends and patterns' }
      ].each do |template| %>
        <div class="bg-white rounded-lg border border-gray-200 p-6 hover:shadow-md transition-shadow">
          <div class="flex items-start">
            <div class="flex-shrink-0">
              <div class="h-10 w-10 bg-<%= template[:color] %>-100 rounded-lg flex items-center justify-center">
                <svg class="h-6 w-6 text-<%= template[:color] %>-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <% if template[:icon] == 'chart-bar' %>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
                  <% elsif template[:icon] == 'cog' %>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"/>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                  <% else %>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 12l3-3 3 3 4-4M8 21l4-4 4 4M3 4h18M4 4h16v12a1 1 0 01-1 1H5a1 1 0 01-1-1V4z"/>
                  <% end %>
                </svg>
              </div>
            </div>
            <div class="ml-4 flex-1">
              <h3 class="text-sm font-medium text-gray-900"><%= template[:name] %></h3>
              <p class="mt-1 text-sm text-gray-500"><%= template[:description] %></p>
              <%= link_to "Use Template", new_dashboard_path(template: template[:template]), 
                  class: "mt-3 inline-flex items-center text-sm font-medium text-#{template[:color]}-600 hover:text-#{template[:color]}-500" %>
            </div>
          </div>
        </div>
      <% end %>
    </div>
  </div>

  <!-- Dashboards Grid -->
  <div>
    <h2 class="text-lg font-semibold text-gray-900 mb-4">Your Dashboards</h2>
    <% if @dashboards.any? %>
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <% @dashboards.each do |dashboard| %>
          <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-shadow cursor-pointer" onclick="window.location.href='<%= dashboard_path(dashboard) %>'">
            <!-- Dashboard Preview Area -->
            <div class="h-32 bg-gradient-to-br from-gray-50 to-gray-100 relative">
              <div class="absolute inset-0 flex items-center justify-center">
                <div class="grid grid-cols-3 gap-2 p-4">
                  <% (1..6).each do |i| %>
                    <div class="bg-white bg-opacity-60 rounded h-8 w-12"></div>
                  <% end %>
                </div>
              </div>
              <% if dashboard.is_default? %>
                <div class="absolute top-2 right-2">
                  <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                    Default
                  </span>
                </div>
              <% end %>
              <% if dashboard.is_public? %>
                <div class="absolute top-2 left-2">
                  <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                    Shared
                  </span>
                </div>
              <% end %>
            </div>
            
            <!-- Dashboard Info -->
            <div class="p-6">
              <h3 class="text-lg font-medium text-gray-900 mb-2">
                <%= link_to dashboard.name, dashboard, class: "hover:text-blue-600" %>
              </h3>
              <p class="text-sm text-gray-500 mb-4 line-clamp-2">
                <%= dashboard.description || "No description provided" %>
              </p>
              
              <!-- Metadata -->
              <div class="flex items-center justify-between text-xs text-gray-500 mb-4">
                <div class="flex items-center">
                  <svg class="h-4 w-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z"/>
                  </svg>
                  <%= dashboard.widgets.count %> widgets
                </div>
                <div class="flex items-center">
                  <svg class="h-4 w-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"/>
                  </svg>
                  <%= dashboard.view_count %> views
                </div>
              </div>
              
              <!-- Actions -->
              <div class="flex items-center justify-between">
                <%= link_to "View Dashboard", dashboard, 
                    class: "text-sm font-medium text-blue-600 hover:text-blue-500" %>
                <div class="flex items-center space-x-2">
                  <%= link_to edit_dashboard_path(dashboard), 
                      class: "text-gray-400 hover:text-gray-500",
                      title: "Edit",
                      onclick: "event.stopPropagation();" do %>
                    <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"/>
                    </svg>
                  <% end %>
                  <%= link_to duplicate_dashboard_path(dashboard), 
                      method: :post,
                      class: "text-gray-400 hover:text-gray-500",
                      title: "Duplicate",
                      onclick: "event.stopPropagation();",
                      data: { turbo_method: :post } do %>
                    <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"/>
                    </svg>
                  <% end %>
                </div>
              </div>
            </div>
          </div>
        <% end %>
      </div>
    <% else %>
      <!-- Empty State -->
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-12">
        <div class="text-center">
          <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
          </svg>
          <h3 class="mt-2 text-sm font-medium text-gray-900">No dashboards yet</h3>
          <p class="mt-1 text-sm text-gray-500">Get started by creating your first dashboard.</p>
          <div class="mt-6">
            <%= link_to new_dashboard_path, class: "btn btn-primary" do %>
              <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"/>
              </svg>
              Create Dashboard
            <% end %>
          </div>
        </div>
      </div>
    <% end %>
  </div>
</div>

🚨 CRITICAL ERROR ALERT 🚨

A critical error has occurred in the DataReflow application:

ERROR DETAILS:
=============
Error ID: <%= @notification[:error_id] %>
Severity: <%= @notification[:severity].to_s.upcase %>
Exception: <%= @notification[:error_class] %>
Message: <%= @notification[:error_message] %>

LOCATION:
=========
Controller: <%= @notification[:controller] %>
Action: <%= @notification[:action] %>
Method & URL: <%= @notification[:method] %> <%= @notification[:url] %>
Environment: <%= @notification[:environment] %>
Timestamp: <%= @notification[:timestamp] %>

<% if @notification[:user_email] -%>
USER CONTEXT:
=============
User: <%= @notification[:user_email] %> (ID: <%= @notification[:user_id] %>)

<% end -%>
<% if @notification[:account_subdomain] -%>
ACCOUNT CONTEXT:
================
Account: <%= @notification[:account_subdomain] %> (ID: <%= @notification[:account_id] %>)

<% end -%>
REQUEST DETAILS:
================
IP Address: <%= @notification[:ip_address] %>
User Agent: <%= @notification[:user_agent] %>

<% if @notification[:backtrace] && @notification[:backtrace].any? -%>
BACKTRACE (Top 5 lines):
========================
<% @notification[:backtrace].each do |line| -%>
<%= line %>
<% end -%>

<% end -%>
==========================================
This is an automated alert from DataReflow Error Monitoring System.
Error ID: <%= @notification[:error_id] %>
Generated at: <%= Time.current.strftime("%Y-%m-%d %H:%M:%S %Z") %>
==========================================
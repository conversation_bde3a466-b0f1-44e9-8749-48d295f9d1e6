<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title>Critical Error Alert</title>
  <style>
    body { font-family: Arial, sans-serif; line-height: 1.6; margin: 0; padding: 20px; }
    .container { max-width: 600px; margin: 0 auto; }
    .header { background: #dc3545; color: white; padding: 20px; border-radius: 5px 5px 0 0; }
    .content { background: #f8f9fa; padding: 20px; border: 1px solid #dee2e6; }
    .footer { background: #e9ecef; padding: 15px; border-radius: 0 0 5px 5px; font-size: 12px; color: #6c757d; }
    .detail-table { width: 100%; border-collapse: collapse; margin: 15px 0; }
    .detail-table td { padding: 8px; border-bottom: 1px solid #dee2e6; }
    .detail-table td:first-child { font-weight: bold; width: 120px; }
    .error-box { background: #f8d7da; border: 1px solid #f5c6cb; padding: 15px; border-radius: 5px; margin: 15px 0; }
    .backtrace { background: #f8f9fa; border: 1px solid #dee2e6; padding: 10px; font-family: monospace; font-size: 12px; overflow-x: auto; }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>🚨 Critical Error Alert</h1>
      <p>A critical error has occurred in the DataReflow application</p>
    </div>
    
    <div class="content">
      <div class="error-box">
        <h3><%= @notification[:error_class] %></h3>
        <p><%= @notification[:error_message] %></p>
      </div>
      
      <table class="detail-table">
        <tr>
          <td>Error ID:</td>
          <td><%= @notification[:error_id] %></td>
        </tr>
        <tr>
          <td>Severity:</td>
          <td><strong style="color: #dc3545;"><%= @notification[:severity].to_s.upcase %></strong></td>
        </tr>
        <tr>
          <td>Environment:</td>
          <td><%= @notification[:environment] %></td>
        </tr>
        <tr>
          <td>Timestamp:</td>
          <td><%= @notification[:timestamp] %></td>
        </tr>
        <tr>
          <td>Location:</td>
          <td><%= @notification[:controller] %>#<%= @notification[:action] %></td>
        </tr>
        <tr>
          <td>Method & URL:</td>
          <td><%= @notification[:method] %> <%= @notification[:url] %></td>
        </tr>
        <% if @notification[:user_email] %>
        <tr>
          <td>User:</td>
          <td><%= @notification[:user_email] %> (ID: <%= @notification[:user_id] %>)</td>
        </tr>
        <% end %>
        <% if @notification[:account_subdomain] %>
        <tr>
          <td>Account:</td>
          <td><%= @notification[:account_subdomain] %> (ID: <%= @notification[:account_id] %>)</td>
        </tr>
        <% end %>
        <tr>
          <td>IP Address:</td>
          <td><%= @notification[:ip_address] %></td>
        </tr>
        <tr>
          <td>User Agent:</td>
          <td style="word-break: break-all;"><%= @notification[:user_agent] %></td>
        </tr>
      </table>
      
      <% if @notification[:backtrace] && @notification[:backtrace].any? %>
      <h4>Backtrace (Top 5 lines):</h4>
      <div class="backtrace">
        <% @notification[:backtrace].each do |line| %>
          <%= line %><br>
        <% end %>
      </div>
      <% end %>
    </div>
    
    <div class="footer">
      <p>This is an automated alert from DataReflow Error Monitoring System.</p>
      <p>Error ID: <%= @notification[:error_id] %> | Generated at: <%= Time.current.strftime("%Y-%m-%d %H:%M:%S %Z") %></p>
    </div>
  </div>
</body>
</html>
<% content_for :page_title, "Notifications" %>

<div class="max-w-4xl mx-auto">
  <!-- Header -->
  <div class="mb-8">
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-2xl font-bold text-gray-900">Notifications</h1>
        <p class="mt-1 text-sm text-gray-500">
          Stay updated with your pipeline activities and system alerts
        </p>
      </div>
      <div class="flex items-center space-x-3">
        <% if @unread_count > 0 %>
          <%= link_to mark_all_as_read_notifications_path, 
              method: :patch,
              class: "inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-indigo-700 bg-indigo-100 hover:bg-indigo-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500",
              data: { confirm: "Mark all notifications as read?" } do %>
            <svg class="mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
            </svg>
            Mark All Read
          <% end %>
        <% end %>
        
        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
          <%= @unread_count %> unread
        </span>
      </div>
    </div>
  </div>

  <!-- Notifications List -->
  <div class="bg-white shadow overflow-hidden sm:rounded-md">
    <% if @notifications.any? %>
      <ul class="divide-y divide-gray-200">
        <% @notifications.each do |notification| %>
          <li class="<%= notification.read? ? 'bg-white' : 'bg-blue-50' %> hover:bg-gray-50 transition-colors duration-150">
            <div class="px-4 py-4 flex items-start space-x-4">
              <!-- Icon -->
              <div class="flex-shrink-0">
                <div class="h-10 w-10 rounded-full flex items-center justify-center <%= notification.icon_class.split.last.gsub('text-', 'bg-').gsub('500', '100') %>">
                  <%= notification.icon_svg.html_safe %>
                </div>
              </div>
              
              <!-- Content -->
              <div class="flex-1 min-w-0">
                <div class="flex items-start justify-between">
                  <div class="flex-1">
                    <p class="text-sm font-medium text-gray-900 flex items-center">
                      <%= notification.title %>
                      <% unless notification.read? %>
                        <span class="ml-2 h-2 w-2 bg-blue-600 rounded-full"></span>
                      <% end %>
                    </p>
                    <p class="mt-1 text-sm text-gray-600">
                      <%= notification.message %>
                    </p>
                    <div class="mt-2 flex items-center text-xs text-gray-500 space-x-4">
                      <span><%= notification.time_ago %></span>
                      <span class="capitalize"><%= notification.priority %> priority</span>
                      <span class="capitalize"><%= notification.notification_type.humanize %></span>
                    </div>
                  </div>
                  
                  <!-- Actions -->
                  <div class="flex items-center space-x-2 ml-4">
                    <% if notification.action_url.present? %>
                      <%= link_to notification.action_url,
                          class: "inline-flex items-center px-2.5 py-1.5 border border-gray-300 shadow-sm text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500" do %>
                        View Details
                      <% end %>
                    <% end %>
                    
                    <div class="relative" data-controller="dropdown">
                      <button type="button"
                              class="inline-flex items-center p-1.5 border border-transparent rounded-full text-gray-400 hover:text-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                              data-action="click->dropdown#toggle"
                              data-dropdown-target="button">
                        <span class="sr-only">Open options</span>
                        <svg class="h-4 w-4" fill="currentColor" viewBox="0 0 20 20">
                          <path d="M10 6a2 2 0 110-4 2 2 0 010 4zM10 12a2 2 0 110-4 2 2 0 010 4zM10 18a2 2 0 110-4 2 2 0 010 4z"/>
                        </svg>
                      </button>
                      
                      <div class="hidden absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg ring-1 ring-black ring-opacity-5 z-10"
                           data-dropdown-target="menu">
                        <div class="py-1">
                          <% if notification.unread? %>
                            <%= link_to mark_as_read_notification_path(notification),
                                method: :patch,
                                class: "block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100" do %>
                              Mark as read
                            <% end %>
                          <% else %>
                            <%= link_to mark_as_unread_notification_path(notification),
                                method: :patch,
                                class: "block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100" do %>
                              Mark as unread
                            <% end %>
                          <% end %>
                          
                          <%= link_to notification_path(notification),
                              method: :delete,
                              class: "block px-4 py-2 text-sm text-red-700 hover:bg-red-50",
                              data: { confirm: "Are you sure you want to delete this notification?" } do %>
                            Delete
                          <% end %>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </li>
        <% end %>
      </ul>
    <% else %>
      <!-- Empty State -->
      <div class="text-center py-12">
        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5v-5zM10.5 3.5a6 6 0 0 1 6 6v2l1.5 3h-15l1.5-3v-2a6 6 0 0 1 6-6z"/>
        </svg>
        <h3 class="mt-2 text-sm font-medium text-gray-900">No notifications</h3>
        <p class="mt-1 text-sm text-gray-500">
          You're all caught up! New notifications will appear here.
        </p>
      </div>
    <% end %>
  </div>

  <!-- Load More (if needed) -->
  <% if @notifications.count == 20 %>
    <div class="mt-6 text-center">
      <%= link_to notifications_path(page: (params[:page].to_i + 1)),
          class: "inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500" do %>
        Load More Notifications
      <% end %>
    </div>
  <% end %>
</div>

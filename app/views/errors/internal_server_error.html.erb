<div class="text-center">
  <!-- Error illustration -->
  <div class="mb-8">
    <div class="mx-auto w-32 h-32 bg-red-100 rounded-full flex items-center justify-center">
      <svg class="w-16 h-16 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
      </svg>
    </div>
  </div>

  <!-- Error code -->
  <div class="mb-4">
    <h1 class="text-9xl font-bold text-gray-200">500</h1>
  </div>

  <!-- Error message -->
  <div class="mb-8">
    <h2 class="text-3xl font-bold text-gray-900 mb-4"><%= @error_title %></h2>
    <p class="text-xl text-gray-600 max-w-lg mx-auto">
      <%= @error_message %>
    </p>
  </div>

  <!-- Actions -->
  <div class="space-y-4 sm:space-y-0 sm:space-x-4 sm:flex sm:justify-center">
    <%= link_to "Go Home", root_path, 
        class: "inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500" %>
    
    <button onclick="window.location.reload()" 
            class="inline-flex items-center px-6 py-3 border border-gray-300 text-base font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
      Try Again
    </button>
  </div>

  <!-- Status information -->
  <div class="mt-12 pt-8 border-t border-gray-200">
    <div class="bg-yellow-50 border border-yellow-200 rounded-md p-4 max-w-md mx-auto">
      <div class="flex">
        <div class="flex-shrink-0">
          <svg class="h-5 w-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
          </svg>
        </div>
        <div class="ml-3">
          <h3 class="text-sm font-medium text-yellow-800">
            Temporary Service Issue
          </h3>
          <div class="mt-2 text-sm text-yellow-700">
            <p>Our engineering team has been automatically notified and is working to resolve this issue.</p>
          </div>
        </div>
      </div>
    </div>

    <p class="text-sm text-gray-500 mt-6 mb-4">
      Need immediate assistance?
    </p>
    
    <div class="flex justify-center space-x-6">
      <%= link_to "Contact Support", contact_path, class: "text-indigo-600 hover:text-indigo-500" %>
      <%= link_to "System Status", "#", class: "text-indigo-600 hover:text-indigo-500" %>
      <%= link_to "Help Center", help_path, class: "text-indigo-600 hover:text-indigo-500" %>
    </div>
  </div>
</div>
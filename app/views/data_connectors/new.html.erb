<% content_for :page_title, "Create New Data Connection" %>

<div class="connector-form-container">
  <!-- Header -->
  <header style="margin-bottom: var(--space-6);">
    <div class="flex items-center justify-between">
      <div>
        <h1 style="font-size: var(--text-3xl); font-weight: var(--font-weight-bold); color: var(--color-gray-900); margin: 0 0 var(--space-2) 0;">
          Create New Data Connection
        </h1>
        <p style="font-size: var(--text-lg); color: var(--color-gray-600); margin: 0;">
          Set up a new connection to your data sources or destinations.
        </p>
      </div>
      <div>
        <%= link_to data_connectors_path, class: "btn btn-secondary" do %>
          <svg style="width: 1rem; height: 1rem; margin-right: var(--space-2);" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"/>
          </svg>
          Back to Connections
        <% end %>
      </div>
    </div>
  </header>

  <!-- Connection Form -->
  <div class="card">
    <div class="card-body">
      <%= form_with model: [@data_connector], local: true, class: "connector-form" do |form| %>
        
        <!-- Basic Information -->
        <section style="margin-bottom: var(--space-8);">
          <h3 style="font-size: var(--text-xl); font-weight: var(--font-weight-semibold); color: var(--color-gray-900); margin: 0 0 var(--space-4) 0;">
            Basic Information
          </h3>
          
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- Connection Name -->
            <div class="form-group">
              <%= form.label :name, "Connection Name", class: "form-label" %>
              <%= form.text_field :name, 
                    class: "form-input", 
                    placeholder: "e.g., Production Database",
                    required: true %>
              <p class="form-help">Choose a descriptive name for this connection</p>
            </div>

            <!-- Connection Type -->
            <div class="form-group">
              <%= form.label :connector_type, "Connection Type", class: "form-label" %>
              <%= form.select :connector_type, 
                    options_for_select([
                      ['Select a type', ''],
                      ['PostgreSQL Database', 'postgresql'],
                      ['MySQL Database', 'mysql'],
                      ['MongoDB', 'mongodb'],
                      ['REST API', 'rest_api'],
                      ['GraphQL API', 'graphql'],
                      ['AWS S3', 's3'],
                      ['Google Cloud Storage', 'gcs'],
                      ['Webhook', 'webhook']
                    ]), 
                    {}, 
                    { class: "form-select", required: true } %>
            </div>
          </div>

          <!-- Description -->
          <div class="form-group">
            <%= form.label :description, "Description", class: "form-label" %>
            <%= form.text_area :description, 
                  class: "form-input", 
                  rows: 3,
                  placeholder: "Describe what this connection is used for..." %>
            <p class="form-help">Optional description of the connection's purpose</p>
          </div>
        </section>

        <!-- Connection Configuration -->
        <section style="margin-bottom: var(--space-8);">
          <h3 style="font-size: var(--text-xl); font-weight: var(--font-weight-semibold); color: var(--color-gray-900); margin: 0 0 var(--space-4) 0;">
            Connection Configuration
          </h3>
          
          <div class="alert alert-info" style="margin-bottom: var(--space-4);">
            <div class="alert-icon">ℹ</div>
            <div class="alert-content">
              <p class="alert-message">Configuration options will appear here based on the connection type selected above.</p>
            </div>
          </div>

          <!-- Placeholder for dynamic configuration fields -->
          <div id="connection-config">
            <p style="color: var(--color-gray-500); text-align: center; padding: var(--space-8);">
              Select a connection type to see configuration options
            </p>
          </div>
        </section>

        <!-- Form Actions -->
        <div class="flex items-center justify-between" style="padding-top: var(--space-6); border-top: 1px solid var(--color-gray-200);">
          <div class="flex items-center gap-4">
            <%= form.submit "Create Connection", class: "btn btn-primary", data: { disable_with: "Creating..." } %>
            <%= link_to "Cancel", data_connectors_path, class: "btn btn-secondary" %>
          </div>
          
          <div class="text-sm text-gray-500">
            <span>Fill in required fields to create connection</span>
          </div>
        </div>

      <% end %>
    </div>
  </div>
</div>

<% content_for :page_title, "Data Connections" %>

<div class="max-w-7xl mx-auto">
  <!-- Header -->
  <div class="mb-8">
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-2xl font-bold text-gray-900">Data Connections</h1>
        <p class="mt-1 text-sm text-gray-500">
          Manage your data source and destination connections
        </p>
      </div>
      <div class="flex items-center space-x-3">
        <%= link_to new_data_connector_path,
            class: "inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500" do %>
          <svg class="mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"/>
          </svg>
          Add Connection
        <% end %>
      </div>
    </div>
  </div>

  <!-- Connections List -->
  <div class="bg-white shadow overflow-hidden sm:rounded-md">
    <% if @data_connectors&.any? %>
      <ul class="divide-y divide-gray-200">
        <% @data_connectors.each do |connector| %>
          <li class="hover:bg-gray-50">
            <div class="px-4 py-4 flex items-center justify-between">
              <div class="flex items-center">
                <div class="flex-shrink-0">
                  <div class="h-10 w-10 bg-green-100 rounded-lg flex items-center justify-center">
                    <svg class="h-6 w-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 9l3 3-3 3m5 0h3M5 20h14a2 2 0 002-2V6a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"/>
                    </svg>
                  </div>
                </div>
                <div class="ml-4">
                  <div class="flex items-center">
                    <div class="text-sm font-medium text-gray-900">
                      <%= connector.name %>
                    </div>
                    <span class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                      <%= connector.respond_to?(:status) ? connector.status.humanize : 'Active' %>
                    </span>
                  </div>
                  <div class="text-sm text-gray-500">
                    <%= connector.respond_to?(:connector_type) ? connector.connector_type.humanize : 'Database' %> • 
                    Last updated <%= time_ago_in_words(connector.updated_at) %> ago
                  </div>
                </div>
              </div>
              <div class="flex items-center space-x-2">
                <%= link_to data_connector_path(connector),
                    class: "text-indigo-600 hover:text-indigo-900 text-sm font-medium" do %>
                  View
                <% end %>
                <%= link_to edit_data_connector_path(connector),
                    class: "text-gray-600 hover:text-gray-900 text-sm font-medium" do %>
                  Edit
                <% end %>
              </div>
            </div>
          </li>
        <% end %>
      </ul>
    <% else %>
      <!-- Empty State -->
      <div class="text-center py-12">
        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 9l3 3-3 3m5 0h3M5 20h14a2 2 0 002-2V6a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"/>
        </svg>
        <h3 class="mt-2 text-sm font-medium text-gray-900">No connections</h3>
        <p class="mt-1 text-sm text-gray-500">
          Get started by creating your first data connection.
        </p>
        <div class="mt-6">
          <%= link_to new_data_connector_path,
              class: "inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500" do %>
            <svg class="mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"/>
            </svg>
            Add Connection
          <% end %>
        </div>
      </div>
    <% end %>
  </div>
</div>

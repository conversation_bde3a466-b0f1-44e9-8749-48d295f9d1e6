<%# SEO Meta Tags %>
<% content_for :title, "DataReflow Help Center - Support & Documentation" %>
<% content_for :description, "Find answers to common questions, browse our knowledge base, and get help with DataReflow's data integration platform." %>
<% content_for :keywords, "dataflow help, support documentation, FAQ, knowledge base, integration help, technical support" %>

<%# Hero Section %>
<section class="relative bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 overflow-hidden">
  <%= render 'shared/marketing_nav' %>

  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24">
    <div class="text-center">
      <h1 class="text-5xl md:text-6xl font-bold text-gray-900 mb-6">
        How can we
        <span class="bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
          help you?
        </span>
      </h1>
      <p class="text-xl md:text-2xl text-gray-600 max-w-4xl mx-auto leading-relaxed mb-10">
        Find answers to common questions, browse our knowledge base, or get in touch with our support team.
      </p>
      
      <%# Search Bar %>
      <div class="max-w-2xl mx-auto">
        <div class="relative">
          <input type="text" 
                 placeholder="Search for help articles, guides, or FAQs..." 
                 class="w-full px-6 py-4 pl-12 text-lg border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 shadow-lg">
          <%= svg_icon('search', class: 'absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400') %>
        </div>
      </div>
    </div>
  </div>
</section>

<%# Quick Help Section %>
<section class="py-16 bg-white">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="text-center mb-12">
      <h2 class="text-3xl font-bold text-gray-900 mb-4">Popular Help Topics</h2>
      <p class="text-lg text-gray-600">Quick access to the most requested information</p>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      <% @getting_started_guides.each do |guide| %>
        <div class="bg-gray-50 hover:bg-gray-100 p-6 rounded-xl transition-colors duration-200 cursor-pointer group">
          <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-4 group-hover:bg-blue-200 transition-colors">
            <%= svg_icon(guide[:icon], class: 'w-6 h-6 text-blue-600') %>
          </div>
          <h3 class="font-semibold text-gray-900 mb-2"><%= guide[:title] %></h3>
          <p class="text-sm text-gray-600 mb-3"><%= guide[:description] %></p>
          <div class="flex items-center text-sm text-blue-600">
            <span><%= guide[:steps] %> steps</span>
            <%= svg_icon('arrow-right', class: 'w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform') %>
          </div>
        </div>
      <% end %>
    </div>
  </div>
</section>

<%# FAQ Section %>
<section class="py-16 bg-gray-50">
  <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="text-center mb-12">
      <h2 class="text-3xl font-bold text-gray-900 mb-4">Frequently Asked Questions</h2>
      <p class="text-lg text-gray-600">Find answers to the most common questions about DataReflow</p>
    </div>

    <div class="space-y-8">
      <% @faq_categories.each do |category_name, questions| %>
        <div class="bg-white rounded-2xl shadow-sm overflow-hidden">
          <div class="bg-blue-50 px-6 py-4 border-b border-blue-100">
            <h3 class="text-xl font-semibold text-gray-900"><%= category_name %></h3>
          </div>
          
          <div class="divide-y divide-gray-100">
            <% questions.each_with_index do |faq, index| %>
              <div class="faq-item">
                <button class="faq-toggle w-full px-6 py-4 text-left hover:bg-gray-50 focus:outline-none focus:bg-gray-50 transition-colors"
                        data-target="faq-<%= category_name.parameterize %>-<%= index %>">
                  <div class="flex items-center justify-between">
                    <span class="font-medium text-gray-900"><%= faq[:question] %></span>
                    <%= svg_icon('chevron-down', class: 'w-5 h-5 text-gray-400 transform transition-transform faq-chevron') %>
                  </div>
                </button>
                <div id="faq-<%= category_name.parameterize %>-<%= index %>" class="faq-content hidden px-6 pb-4">
                  <p class="text-gray-600 leading-relaxed"><%= faq[:answer] %></p>
                </div>
              </div>
            <% end %>
          </div>
        </div>
      <% end %>
    </div>
  </div>
</section>

<%# Popular Articles %>
<section class="py-16 bg-white">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="text-center mb-12">
      <h2 class="text-3xl font-bold text-gray-900 mb-4">Popular Articles</h2>
      <p class="text-lg text-gray-600">In-depth guides and tutorials to help you get the most out of DataReflow</p>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
      <% @popular_articles.each do |article| %>
        <div class="bg-gray-50 hover:bg-gray-100 p-6 rounded-xl transition-colors duration-200 cursor-pointer group">
          <div class="flex items-center justify-between mb-3">
            <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
              <%= article[:category] %>
            </span>
            <span class="text-sm text-gray-500"><%= article[:read_time] %></span>
          </div>
          <h3 class="text-lg font-semibold text-gray-900 mb-2 group-hover:text-blue-600 transition-colors">
            <%= article[:title] %>
          </h3>
          <p class="text-gray-600 mb-4"><%= article[:description] %></p>
          <div class="flex items-center text-blue-600 text-sm font-medium">
            <span>Read article</span>
            <%= svg_icon('arrow-right', class: 'w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform') %>
          </div>
        </div>
      <% end %>
    </div>

    <div class="text-center mt-12">
      <button class="bg-blue-600 hover:bg-blue-700 text-white px-8 py-3 rounded-lg font-semibold transition-colors">
        Browse All Articles
      </button>
    </div>
  </div>
</section>

<%# Contact Support %>
<section class="py-16 bg-blue-600">
  <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
    <div class="bg-white rounded-2xl p-8 md:p-12">
      <h2 class="text-3xl font-bold text-gray-900 mb-4">Still need help?</h2>
      <p class="text-lg text-gray-600 mb-8">
        Can't find what you're looking for? Our support team is here to help you succeed.
      </p>
      
      <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        <%# Live Chat %>
        <div class="text-center p-6 bg-blue-50 rounded-xl">
          <div class="w-12 h-12 bg-blue-600 rounded-lg flex items-center justify-center mx-auto mb-4">
            <%= svg_icon('users', class: 'w-6 h-6 text-white') %>
          </div>
          <h3 class="font-semibold text-gray-900 mb-2">Live Chat</h3>
          <p class="text-sm text-gray-600 mb-4">Get instant help from our support team</p>
          <button class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors">
            Start Chat
          </button>
        </div>

        <%# Email Support %>
        <div class="text-center p-6 bg-green-50 rounded-xl">
          <div class="w-12 h-12 bg-green-600 rounded-lg flex items-center justify-center mx-auto mb-4">
            <%= svg_icon('globe', class: 'w-6 h-6 text-white') %>
          </div>
          <h3 class="font-semibold text-gray-900 mb-2">Email Support</h3>
          <p class="text-sm text-gray-600 mb-4">Send us a detailed message</p>
          <%= link_to contact_path, 
              class: "bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors inline-block" do %>
            Contact Us
          <% end %>
        </div>

        <%# Documentation %>
        <div class="text-center p-6 bg-purple-50 rounded-xl">
          <div class="w-12 h-12 bg-purple-600 rounded-lg flex items-center justify-center mx-auto mb-4">
            <%= svg_icon('code', class: 'w-6 h-6 text-white') %>
          </div>
          <h3 class="font-semibold text-gray-900 mb-2">API Docs</h3>
          <p class="text-sm text-gray-600 mb-4">Technical documentation and guides</p>
          <button class="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors">
            View Docs
          </button>
        </div>
      </div>
    </div>
  </div>
</section>

<%# FAQ JavaScript %>
<script>
document.addEventListener('DOMContentLoaded', function() {
  const faqToggles = document.querySelectorAll('.faq-toggle');
  
  faqToggles.forEach(toggle => {
    toggle.addEventListener('click', function() {
      const targetId = this.getAttribute('data-target');
      const content = document.getElementById(targetId);
      const chevron = this.querySelector('.faq-chevron');
      
      if (content.classList.contains('hidden')) {
        content.classList.remove('hidden');
        chevron.style.transform = 'rotate(180deg)';
      } else {
        content.classList.add('hidden');
        chevron.style.transform = 'rotate(0deg)';
      }
    });
  });
});
</script>

<%# Footer %>
<%= render 'shared/marketing_footer' %>

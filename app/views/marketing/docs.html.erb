<%# SEO Meta Tags %>
<% content_for :title, "DataReflow API Documentation - Developer Resources" %>
<% content_for :description, "Complete API documentation for DataReflow's data integration platform. SDKs, code examples, and comprehensive guides for developers." %>
<% content_for :keywords, "API documentation, developer resources, SDK, REST API, data integration API, webhooks" %>

<%# Hero Section %>
<section class="relative bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 overflow-hidden">
  <%= render 'shared/marketing_nav' %>
  
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24">
    <div class="text-center">
      <h1 class="text-5xl md:text-6xl font-bold text-gray-900 mb-6">
        API
        <span class="bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
          Documentation
        </span>
      </h1>
      <p class="text-xl md:text-2xl text-gray-600 max-w-4xl mx-auto leading-relaxed mb-10">
        Build powerful data integration workflows with our comprehensive REST API. Complete with SDKs, code examples, and detailed guides.
      </p>
      
      <%# Quick Links %>
      <div class="flex flex-wrap justify-center gap-4 mb-12">
        <a href="#getting-started" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-semibold transition-colors">
          Quick Start
        </a>
        <a href="#api-reference" class="bg-white hover:bg-gray-50 text-gray-900 px-6 py-3 rounded-lg font-semibold border border-gray-200 transition-colors">
          API Reference
        </a>
        <a href="#sdks" class="bg-white hover:bg-gray-50 text-gray-900 px-6 py-3 rounded-lg font-semibold border border-gray-200 transition-colors">
          SDKs
        </a>
        <a href="#examples" class="bg-white hover:bg-gray-50 text-gray-900 px-6 py-3 rounded-lg font-semibold border border-gray-200 transition-colors">
          Code Examples
        </a>
      </div>

      <%# API Stats %>
      <div class="grid grid-cols-2 md:grid-cols-4 gap-6 max-w-4xl mx-auto">
        <div class="bg-white/80 backdrop-blur-sm rounded-xl p-4 shadow-sm">
          <div class="text-2xl font-bold text-blue-600">99.9%</div>
          <div class="text-sm text-gray-600">API Uptime</div>
        </div>
        <div class="bg-white/80 backdrop-blur-sm rounded-xl p-4 shadow-sm">
          <div class="text-2xl font-bold text-indigo-600">< 100ms</div>
          <div class="text-sm text-gray-600">Avg Response</div>
        </div>
        <div class="bg-white/80 backdrop-blur-sm rounded-xl p-4 shadow-sm">
          <div class="text-2xl font-bold text-purple-600">50+</div>
          <div class="text-sm text-gray-600">API Endpoints</div>
        </div>
        <div class="bg-white/80 backdrop-blur-sm rounded-xl p-4 shadow-sm">
          <div class="text-2xl font-bold text-green-600">4</div>
          <div class="text-sm text-gray-600">Official SDKs</div>
        </div>
      </div>
    </div>
  </div>
</section>

<%# API Sections %>
<section id="getting-started" class="py-16 bg-white">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="text-center mb-12">
      <h2 class="text-3xl font-bold text-gray-900 mb-4">API Documentation</h2>
      <p class="text-lg text-gray-600">Everything you need to integrate with DataReflow</p>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
      <% @api_sections.each do |section| %>
        <div class="bg-gray-50 rounded-2xl p-8 hover:shadow-lg transition-shadow duration-200">
          <div class="w-16 h-16 bg-blue-100 rounded-xl flex items-center justify-center mb-6">
            <%= svg_icon(section[:icon], class: 'w-8 h-8 text-blue-600') %>
          </div>
          
          <h3 class="text-xl font-semibold text-gray-900 mb-3"><%= section[:title] %></h3>
          <p class="text-gray-600 mb-6"><%= section[:description] %></p>
          
          <ul class="space-y-3">
            <% section[:items].each do |item| %>
              <li class="group cursor-pointer">
                <div class="flex items-center justify-between p-3 bg-white rounded-lg hover:bg-blue-50 transition-colors">
                  <div>
                    <h4 class="font-medium text-gray-900 group-hover:text-blue-600"><%= item[:title] %></h4>
                    <p class="text-sm text-gray-600"><%= item[:description] %></p>
                  </div>
                  <%= svg_icon('arrow-right', class: 'w-4 h-4 text-gray-400 group-hover:text-blue-600 group-hover:translate-x-1 transition-all') %>
                </div>
              </li>
            <% end %>
          </ul>
        </div>
      <% end %>
    </div>
  </div>
</section>

<%# Code Examples %>
<section id="examples" class="py-16 bg-gray-50">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="text-center mb-12">
      <h2 class="text-3xl font-bold text-gray-900 mb-4">Code Examples</h2>
      <p class="text-lg text-gray-600">Get started quickly with these practical examples</p>
    </div>

    <div class="space-y-8">
      <% @code_examples.each do |example| %>
        <div class="bg-white rounded-2xl shadow-sm overflow-hidden">
          <div class="bg-gray-900 px-6 py-4 flex items-center justify-between">
            <h3 class="text-white font-semibold"><%= example[:title] %></h3>
            <span class="bg-gray-700 text-gray-300 px-3 py-1 rounded-full text-sm font-medium">
              <%= example[:language] %>
            </span>
          </div>
          
          <div class="p-6">
            <pre class="bg-gray-900 text-gray-100 p-4 rounded-lg overflow-x-auto text-sm"><code><%= example[:code] %></code></pre>
            
            <div class="mt-4 flex items-center justify-between">
              <p class="text-gray-600 text-sm">Click to copy code snippet</p>
              <button class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors">
                Copy Code
              </button>
            </div>
          </div>
        </div>
      <% end %>
    </div>
  </div>
</section>

<%# API Reference %>
<section id="api-reference" class="py-16 bg-white">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="text-center mb-12">
      <h2 class="text-3xl font-bold text-gray-900 mb-4">API Reference</h2>
      <p class="text-lg text-gray-600">Complete reference for all API endpoints</p>
    </div>

    <div class="space-y-8">
      <% @api_endpoints.each do |category| %>
        <div class="bg-gray-50 rounded-2xl overflow-hidden">
          <div class="bg-gradient-to-r from-blue-50 to-indigo-50 px-6 py-4 border-b border-blue-100">
            <h3 class="text-xl font-semibold text-gray-900"><%= category[:category] %></h3>
          </div>
          
          <div class="divide-y divide-gray-200">
            <% category[:endpoints].each do |endpoint| %>
              <div class="p-6 hover:bg-gray-100 transition-colors cursor-pointer">
                <div class="flex items-center justify-between">
                  <div class="flex items-center space-x-4">
                    <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium <%= endpoint[:method] == 'GET' ? 'bg-green-100 text-green-800' : endpoint[:method] == 'POST' ? 'bg-blue-100 text-blue-800' : endpoint[:method] == 'PUT' ? 'bg-yellow-100 text-yellow-800' : 'bg-red-100 text-red-800' %>">
                      <%= endpoint[:method] %>
                    </span>
                    <code class="text-gray-900 font-mono"><%= endpoint[:path] %></code>
                  </div>
                  <%= svg_icon('arrow-right', class: 'w-4 h-4 text-gray-400') %>
                </div>
                <p class="text-gray-600 mt-2"><%= endpoint[:description] %></p>
              </div>
            <% end %>
          </div>
        </div>
      <% end %>
    </div>
  </div>
</section>

<%# SDKs %>
<section id="sdks" class="py-16 bg-gray-50">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="text-center mb-12">
      <h2 class="text-3xl font-bold text-gray-900 mb-4">Official SDKs</h2>
      <p class="text-lg text-gray-600">Use our official SDKs to integrate DataReflow into your applications</p>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
      <% @sdk_languages.each do |sdk| %>
        <div class="bg-white p-8 rounded-2xl shadow-sm hover:shadow-md transition-shadow duration-200">
          <div class="flex items-center justify-between mb-4">
            <h3 class="text-xl font-semibold text-gray-900"><%= sdk[:name] %></h3>
            <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium <%= sdk[:status] == 'stable' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800' %>">
              <%= sdk[:status].capitalize %>
            </span>
          </div>
          
          <p class="text-gray-600 mb-6"><%= sdk[:description] %></p>
          
          <div class="bg-gray-900 rounded-lg p-4 mb-4">
            <code class="text-green-400 text-sm"><%= sdk[:install_command] %></code>
          </div>
          
          <div class="flex items-center justify-between">
            <a href="<%= sdk[:github_url] %>" class="text-blue-600 hover:text-blue-700 font-medium text-sm flex items-center">
              <%= svg_icon('code', class: 'w-4 h-4 mr-2') %>
              View on GitHub
            </a>
            <button class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors">
              Get Started
            </button>
          </div>
        </div>
      <% end %>
    </div>
  </div>
</section>

<%# Developer Resources %>
<section class="py-16 bg-blue-600">
  <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
    <div class="bg-white rounded-2xl p-8 md:p-12">
      <h2 class="text-3xl font-bold text-gray-900 mb-6">Need Help Getting Started?</h2>
      
      <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
        <div class="text-center">
          <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-4">
            <%= svg_icon('users', class: 'w-6 h-6 text-blue-600') %>
          </div>
          <h3 class="font-semibold text-gray-900 mb-2">Developer Community</h3>
          <p class="text-sm text-gray-600 mb-4">Join our Discord for real-time help</p>
          <%= link_to community_path, 
              class: "bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors inline-block" do %>
            Join Community
          <% end %>
        </div>

        <div class="text-center">
          <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-4">
            <%= svg_icon('globe', class: 'w-6 h-6 text-green-600') %>
          </div>
          <h3 class="font-semibold text-gray-900 mb-2">Support Team</h3>
          <p class="text-sm text-gray-600 mb-4">Get help from our technical team</p>
          <%= link_to contact_path, 
              class: "bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors inline-block" do %>
            Contact Support
          <% end %>
        </div>

        <div class="text-center">
          <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-4">
            <%= svg_icon('code', class: 'w-6 h-6 text-purple-600') %>
          </div>
          <h3 class="font-semibold text-gray-900 mb-2">API Playground</h3>
          <p class="text-sm text-gray-600 mb-4">Test API calls in your browser</p>
          <button class="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors">
            Try API
          </button>
        </div>
      </div>

      <div class="mt-10">
        <%= link_to new_user_registration_path, 
            class: "bg-blue-600 hover:bg-blue-700 text-white px-8 py-4 rounded-xl font-semibold text-lg transition-all duration-200 transform hover:scale-105 shadow-lg" do %>
          Get API Key - Free
        <% end %>
      </div>
    </div>
  </div>
</section>

<%# Footer %>
<%= render 'shared/marketing_footer' %>

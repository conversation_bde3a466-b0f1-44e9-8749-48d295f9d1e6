<%# SEO Meta Tags %>
<% content_for :title, "DataReflow Community - Connect with Developers & Users" %>
<% content_for :description, "Join the DataReflow community of 5,200+ developers and users. Get help, share knowledge, and connect with fellow data integration enthusiasts." %>
<% content_for :keywords, "developer community, Discord, GitHub, forum, data integration community, developer support" %>

<%# Hero Section %>
<section class="relative bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 overflow-hidden">
  <%= render 'shared/marketing_nav' %>
  
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24">
    <div class="text-center">
      <h1 class="text-5xl md:text-6xl font-bold text-gray-900 mb-6">
        Join the
        <span class="bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
          Community
        </span>
      </h1>
      <p class="text-xl md:text-2xl text-gray-600 max-w-4xl mx-auto leading-relaxed mb-10">
        Connect with <%= @community_stats[:members] %> developers, data engineers, and business users building amazing things with DataReflow.
      </p>
      
      <%# Community Stats %>
      <div class="grid grid-cols-2 md:grid-cols-4 gap-6 max-w-4xl mx-auto mb-12">
        <div class="bg-white/80 backdrop-blur-sm rounded-xl p-4 shadow-sm">
          <div class="text-2xl font-bold text-blue-600"><%= @community_stats[:members] %></div>
          <div class="text-sm text-gray-600">Community Members</div>
        </div>
        <div class="bg-white/80 backdrop-blur-sm rounded-xl p-4 shadow-sm">
          <div class="text-2xl font-bold text-indigo-600"><%= @community_stats[:discussions] %></div>
          <div class="text-sm text-gray-600">Discussions</div>
        </div>
        <div class="bg-white/80 backdrop-blur-sm rounded-xl p-4 shadow-sm">
          <div class="text-2xl font-bold text-purple-600"><%= @community_stats[:countries] %></div>
          <div class="text-sm text-gray-600">Countries</div>
        </div>
        <div class="bg-white/80 backdrop-blur-sm rounded-xl p-4 shadow-sm">
          <div class="text-2xl font-bold text-green-600"><%= @community_stats[:avg_response_time] %></div>
          <div class="text-sm text-gray-600">Avg Response</div>
        </div>
      </div>

      <div class="flex flex-col sm:flex-row justify-center gap-4">
        <button class="bg-blue-600 hover:bg-blue-700 text-white px-8 py-4 rounded-xl font-semibold text-lg transition-all duration-200 transform hover:scale-105 shadow-lg">
          Join Discord Server
        </button>
        
        <button class="bg-white hover:bg-gray-50 text-gray-900 px-8 py-4 rounded-xl font-semibold text-lg border border-gray-200 transition-all duration-200 shadow-sm hover:shadow-md">
          Browse Discussions
        </button>
      </div>
    </div>
  </div>
</section>

<%# Community Channels %>
<section class="py-16 bg-white">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="text-center mb-12">
      <h2 class="text-3xl font-bold text-gray-900 mb-4">Community Channels</h2>
      <p class="text-lg text-gray-600">Choose the platform that works best for you</p>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
      <% @community_channels.each do |channel| %>
        <div class="bg-gray-50 p-8 rounded-2xl hover:shadow-lg transition-shadow duration-200 group">
          <div class="flex items-center justify-between mb-6">
            <div class="w-16 h-16 bg-blue-100 rounded-xl flex items-center justify-center group-hover:bg-blue-200 transition-colors">
              <%= svg_icon(channel[:icon], class: 'w-8 h-8 text-blue-600') %>
            </div>
            <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium <%= channel[:activity] == 'Very Active' ? 'bg-green-100 text-green-800' : channel[:activity] == 'Active' ? 'bg-blue-100 text-blue-800' : 'bg-yellow-100 text-yellow-800' %>">
              <%= channel[:activity] %>
            </span>
          </div>
          
          <h3 class="text-xl font-semibold text-gray-900 mb-3"><%= channel[:name] %></h3>
          <p class="text-gray-600 mb-4"><%= channel[:description] %></p>
          
          <div class="flex items-center justify-between">
            <span class="text-sm text-gray-500"><%= channel[:members] %> members</span>
            <button class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors group-hover:scale-105">
              Join Channel
            </button>
          </div>
        </div>
      <% end %>
    </div>
  </div>
</section>

<%# Featured Discussions %>
<section class="py-16 bg-gray-50">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="text-center mb-12">
      <h2 class="text-3xl font-bold text-gray-900 mb-4">Featured Discussions</h2>
      <p class="text-lg text-gray-600">Popular topics and conversations from our community</p>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
      <% @featured_discussions.each do |discussion| %>
        <div class="bg-white p-6 rounded-xl shadow-sm hover:shadow-md transition-shadow duration-200 cursor-pointer group">
          <div class="flex items-center justify-between mb-4">
            <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
              <%= discussion[:category] %>
            </span>
            <span class="text-sm text-gray-500"><%= discussion[:last_activity] %></span>
          </div>
          
          <h3 class="text-lg font-semibold text-gray-900 mb-3 group-hover:text-blue-600 transition-colors">
            <%= discussion[:title] %>
          </h3>
          
          <div class="flex items-center justify-between text-sm text-gray-600">
            <span>by <%= discussion[:author] %></span>
            <div class="flex items-center space-x-4">
              <span><%= discussion[:replies] %> replies</span>
              <span><%= discussion[:views] %> views</span>
            </div>
          </div>
        </div>
      <% end %>
    </div>

    <div class="text-center mt-12">
      <button class="bg-blue-600 hover:bg-blue-700 text-white px-8 py-3 rounded-lg font-semibold transition-colors">
        View All Discussions
      </button>
    </div>
  </div>
</section>

<%# Community Events %>
<section class="py-16 bg-white">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="text-center mb-12">
      <h2 class="text-3xl font-bold text-gray-900 mb-4">Upcoming Events</h2>
      <p class="text-lg text-gray-600">Join us for meetups, workshops, and community events</p>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
      <% @community_events.each do |event| %>
        <div class="bg-gradient-to-br from-blue-50 to-indigo-50 p-6 rounded-xl border border-blue-100">
          <div class="flex items-center justify-between mb-4">
            <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
              <%= event[:type] %>
            </span>
            <span class="text-sm text-gray-600"><%= event[:attendees] %> attending</span>
          </div>
          
          <h3 class="text-lg font-semibold text-gray-900 mb-2"><%= event[:title] %></h3>
          <p class="text-gray-600 text-sm mb-4"><%= event[:description] %></p>
          
          <div class="space-y-2 mb-4">
            <div class="flex items-center text-sm text-gray-600">
              <%= svg_icon('clock', class: 'w-4 h-4 mr-2') %>
              <%= event[:date] %> at <%= event[:time] %>
            </div>
          </div>
          
          <button class="w-full bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-lg font-medium transition-colors">
            Register Now
          </button>
        </div>
      <% end %>
    </div>
  </div>
</section>

<%# Contributor Spotlights %>
<section class="py-16 bg-gray-50">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="text-center mb-12">
      <h2 class="text-3xl font-bold text-gray-900 mb-4">Community Spotlights</h2>
      <p class="text-lg text-gray-600">Celebrating our amazing community contributors</p>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
      <% @contributor_spotlights.each do |contributor| %>
        <div class="bg-white p-8 rounded-2xl shadow-sm text-center hover:shadow-md transition-shadow duration-200">
          <div class="w-20 h-20 bg-gradient-to-br from-blue-600 to-indigo-600 rounded-full flex items-center justify-center mx-auto mb-6">
            <span class="text-white text-xl font-bold"><%= contributor[:avatar] %></span>
          </div>
          
          <h3 class="text-xl font-semibold text-gray-900 mb-1"><%= contributor[:name] %></h3>
          <p class="text-blue-600 font-medium mb-2"><%= contributor[:role] %></p>
          <p class="text-sm text-gray-500 mb-4"><%= contributor[:location] %></p>
          
          <p class="text-gray-600 text-sm leading-relaxed"><%= contributor[:contributions] %></p>
        </div>
      <% end %>
    </div>
  </div>
</section>

<%# Community Guidelines %>
<section class="py-16 bg-blue-600">
  <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
    <div class="bg-white rounded-2xl p-8 md:p-12">
      <h2 class="text-3xl font-bold text-gray-900 mb-6">Community Guidelines</h2>
      <p class="text-lg text-gray-600 mb-8">
        Our community thrives on respect, collaboration, and shared learning. Here's how we keep it awesome:
      </p>
      
      <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
        <div class="text-center">
          <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-4">
            <%= svg_icon('users', class: 'w-6 h-6 text-green-600') %>
          </div>
          <h3 class="font-semibold text-gray-900 mb-2">Be Respectful</h3>
          <p class="text-sm text-gray-600">Treat everyone with kindness and respect, regardless of experience level</p>
        </div>

        <div class="text-center">
          <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-4">
            <%= svg_icon('globe', class: 'w-6 h-6 text-blue-600') %>
          </div>
          <h3 class="font-semibold text-gray-900 mb-2">Share Knowledge</h3>
          <p class="text-sm text-gray-600">Help others learn and grow by sharing your experiences and insights</p>
        </div>

        <div class="text-center">
          <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-4">
            <%= svg_icon('shield-check', class: 'w-6 h-6 text-purple-600') %>
          </div>
          <h3 class="font-semibold text-gray-900 mb-2">Stay On Topic</h3>
          <p class="text-sm text-gray-600">Keep discussions relevant to DataReflow and data integration topics</p>
        </div>
      </div>

      <div class="mt-10">
        <button class="bg-blue-600 hover:bg-blue-700 text-white px-8 py-4 rounded-xl font-semibold text-lg transition-all duration-200 transform hover:scale-105 shadow-lg">
          Join Our Community Today
        </button>
      </div>
    </div>
  </div>
</section>

<%# Footer %>
<%= render 'shared/marketing_footer' %>

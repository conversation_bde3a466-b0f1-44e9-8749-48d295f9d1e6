<%# SEO Meta Tags %>
<% content_for :title, "Contact DataReflow - Get in Touch with Our Team" %>
<% content_for :description, "Contact DataReflow for sales inquiries, technical support, or general questions. Multiple ways to reach our team with fast response times." %>
<% content_for :keywords, "contact dataflow, customer support, sales inquiry, technical help, business contact" %>

<%# Hero Section %>
<section class="relative bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 overflow-hidden">
  <%= render 'shared/marketing_nav' %>

  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24">
    <div class="text-center">
      <h1 class="text-5xl md:text-6xl font-bold text-gray-900 mb-6">
        Get in
        <span class="bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
          Touch
        </span>
      </h1>
      <p class="text-xl md:text-2xl text-gray-600 max-w-4xl mx-auto leading-relaxed">
        Have questions about <PERSON><PERSON><PERSON><PERSON>? We're here to help. Reach out to our team and we'll get back to you within 24 hours.
      </p>
    </div>
  </div>
</section>

<%# Contact Methods Section %>
<section class="py-16 bg-white">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
      <%# Sales %>
      <div class="text-center p-8 bg-blue-50 rounded-2xl">
        <div class="w-16 h-16 bg-blue-600 rounded-xl flex items-center justify-center mx-auto mb-6">
          <%= svg_icon('users', class: 'w-8 h-8 text-white') %>
        </div>
        <h3 class="text-xl font-semibold text-gray-900 mb-3">Sales & Pricing</h3>
        <p class="text-gray-600 mb-4">Questions about plans, pricing, or enterprise features?</p>
        <a href="mailto:<EMAIL>" class="text-blue-600 hover:text-blue-700 font-medium"><EMAIL></a>
        <p class="text-sm text-gray-500 mt-2">Response within 2 hours</p>
      </div>

      <%# Support %>
      <div class="text-center p-8 bg-green-50 rounded-2xl">
        <div class="w-16 h-16 bg-green-600 rounded-xl flex items-center justify-center mx-auto mb-6">
          <%= svg_icon('shield-check', class: 'w-8 h-8 text-white') %>
        </div>
        <h3 class="text-xl font-semibold text-gray-900 mb-3">Technical Support</h3>
        <p class="text-gray-600 mb-4">Need help with integrations or troubleshooting?</p>
        <a href="mailto:<EMAIL>" class="text-green-600 hover:text-green-700 font-medium"><EMAIL></a>
        <p class="text-sm text-gray-500 mt-2">Response within 4 hours</p>
      </div>

      <%# General %>
      <div class="text-center p-8 bg-purple-50 rounded-2xl">
        <div class="w-16 h-16 bg-purple-600 rounded-xl flex items-center justify-center mx-auto mb-6">
          <%= svg_icon('globe', class: 'w-8 h-8 text-white') %>
        </div>
        <h3 class="text-xl font-semibold text-gray-900 mb-3">General Inquiries</h3>
        <p class="text-gray-600 mb-4">Partnerships, press, or other questions?</p>
        <a href="mailto:<EMAIL>" class="text-purple-600 hover:text-purple-700 font-medium"><EMAIL></a>
        <p class="text-sm text-gray-500 mt-2">Response within 24 hours</p>
      </div>
    </div>
  </div>
</section>

<%# Contact Form Section %>
<section class="py-16 bg-gray-50">
  <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="text-center mb-12">
      <h2 class="text-4xl font-bold text-gray-900 mb-6">Send us a message</h2>
      <p class="text-xl text-gray-600">
        Fill out the form below and we'll get back to you as soon as possible.
      </p>
    </div>

    <div class="bg-white rounded-2xl shadow-lg p-8 md:p-12">
      <%= form_with model: @contact_form, url: contact_path, local: true, html: { class: "space-y-6" } do |f| %>
        <%# Display flash messages %>
        <% if flash[:notice] %>
          <div class="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
            <div class="flex items-center">
              <%= svg_icon('check-circle', class: 'w-5 h-5 text-green-600 mr-3') %>
              <p class="text-green-800"><%= flash[:notice] %></p>
            </div>
          </div>
        <% end %>

        <% if flash[:alert] %>
          <div class="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
            <div class="flex items-center">
              <%= svg_icon('x-circle', class: 'w-5 h-5 text-red-600 mr-3') %>
              <p class="text-red-800"><%= flash[:alert] %></p>
            </div>
          </div>
        <% end %>

        <%# Name and Email Row %>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <%= f.label :name, class: "block text-sm font-medium text-gray-700 mb-2" %>
            <%= f.text_field :name, 
                class: "w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors #{'border-red-300' if @contact_form.errors[:name].any?}",
                placeholder: "Your full name" %>
            <% if @contact_form.errors[:name].any? %>
              <p class="mt-1 text-sm text-red-600"><%= @contact_form.errors[:name].first %></p>
            <% end %>
          </div>

          <div>
            <%= f.label :email, class: "block text-sm font-medium text-gray-700 mb-2" %>
            <%= f.email_field :email, 
                class: "w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors #{'border-red-300' if @contact_form.errors[:email].any?}",
                placeholder: "<EMAIL>" %>
            <% if @contact_form.errors[:email].any? %>
              <p class="mt-1 text-sm text-red-600"><%= @contact_form.errors[:email].first %></p>
            <% end %>
          </div>
        </div>

        <%# Company and Inquiry Type Row %>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <%= f.label :company, class: "block text-sm font-medium text-gray-700 mb-2" %>
            <%= f.text_field :company, 
                class: "w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors #{'border-red-300' if @contact_form.errors[:company].any?}",
                placeholder: "Your company name" %>
            <% if @contact_form.errors[:company].any? %>
              <p class="mt-1 text-sm text-red-600"><%= @contact_form.errors[:company].first %></p>
            <% end %>
          </div>

          <div>
            <%= f.label :inquiry_type, "Inquiry Type", class: "block text-sm font-medium text-gray-700 mb-2" %>
            <%= f.select :inquiry_type, 
                options_for_select([['Select inquiry type', '']] + ContactForm.inquiry_types, @contact_form.inquiry_type),
                {},
                class: "w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors #{'border-red-300' if @contact_form.errors[:inquiry_type].any?}" %>
            <% if @contact_form.errors[:inquiry_type].any? %>
              <p class="mt-1 text-sm text-red-600"><%= @contact_form.errors[:inquiry_type].first %></p>
            <% end %>
          </div>
        </div>

        <%# Message %>
        <div>
          <%= f.label :message, class: "block text-sm font-medium text-gray-700 mb-2" %>
          <%= f.text_area :message, 
              rows: 6,
              class: "w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors resize-none #{'border-red-300' if @contact_form.errors[:message].any?}",
              placeholder: "Tell us about your project, questions, or how we can help..." %>
          <% if @contact_form.errors[:message].any? %>
            <p class="mt-1 text-sm text-red-600"><%= @contact_form.errors[:message].first %></p>
          <% end %>
        </div>

        <%# Submit Button %>
        <div class="text-center">
          <%= f.submit "Send Message", 
              class: "bg-blue-600 hover:bg-blue-700 text-white px-8 py-4 rounded-xl font-semibold text-lg transition-all duration-200 transform hover:scale-105 shadow-lg hover:shadow-xl cursor-pointer" %>
        </div>
      <% end %>
    </div>
  </div>
</section>

<%# Office Information %>
<section class="py-16 bg-white">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="text-center mb-12">
      <h2 class="text-4xl font-bold text-gray-900 mb-6">Visit Our Office</h2>
      <p class="text-xl text-gray-600">
        We're a remote-first company, but you can find us at our headquarters in San Francisco.
      </p>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
      <%# Office Details %>
      <div>
        <div class="bg-gray-50 rounded-2xl p-8">
          <h3 class="text-2xl font-semibold text-gray-900 mb-6">DataReflow Headquarters</h3>
          
          <div class="space-y-4">
            <div class="flex items-start space-x-3">
              <%= svg_icon('globe', class: 'w-5 h-5 text-blue-600 mt-1') %>
              <div>
                <p class="font-medium text-gray-900">Address</p>
                <p class="text-gray-600">123 Innovation Drive<br>San Francisco, CA 94105</p>
              </div>
            </div>

            <div class="flex items-start space-x-3">
              <%= svg_icon('clock', class: 'w-5 h-5 text-blue-600 mt-1') %>
              <div>
                <p class="font-medium text-gray-900">Office Hours</p>
                <p class="text-gray-600">Monday - Friday: 9:00 AM - 6:00 PM PST</p>
              </div>
            </div>

            <div class="flex items-start space-x-3">
              <%= svg_icon('users', class: 'w-5 h-5 text-blue-600 mt-1') %>
              <div>
                <p class="font-medium text-gray-900">Support Hours</p>
                <p class="text-gray-600">24/7 for Enterprise customers<br>Business hours for all others</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <%# Map Placeholder %>
      <div class="bg-gray-100 rounded-2xl h-96 flex items-center justify-center">
        <div class="text-center">
          <%= svg_icon('globe', class: 'w-16 h-16 text-gray-400 mx-auto mb-4') %>
          <p class="text-gray-500">Interactive map would be integrated here</p>
          <p class="text-sm text-gray-400">Google Maps or similar service</p>
        </div>
      </div>
    </div>
  </div>
</section>

<%# Footer %>
<%= render 'shared/marketing_footer' %>

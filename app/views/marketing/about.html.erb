<%# SEO Meta Tags %>
<% content_for :title, "About DataReflow - Our Mission to Simplify Data Integration" %>
<% content_for :description, "Learn about DataReflow's mission to make data integration accessible to every business. Meet our team and discover the values that drive our no-code platform." %>
<% content_for :keywords, "about dataflow, company mission, data integration team, no-code platform, business automation" %>

<%# Hero Section %>
<section class="relative bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 overflow-hidden">
  <%= render 'shared/marketing_nav' %>

  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24">
    <div class="text-center">
      <h1 class="text-5xl md:text-6xl font-bold text-gray-900 mb-6">
        We're building the future of
        <span class="bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
          data integration
        </span>
      </h1>
      <p class="text-xl md:text-2xl text-gray-600 max-w-4xl mx-auto leading-relaxed">
        <PERSON><PERSON><PERSON><PERSON> was born from a simple belief: connecting your business data shouldn't require 
        weeks of development or a team of engineers.
      </p>
    </div>
  </div>
</section>

<%# Mission Section %>
<section class="py-24 bg-white">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
      <div>
        <h2 class="text-4xl font-bold text-gray-900 mb-6">Our Mission</h2>
        <p class="text-lg text-gray-600 mb-6 leading-relaxed">
          We believe every business, regardless of size or technical expertise, should have access to 
          powerful data integration tools. Our mission is to democratize data connectivity and make 
          it as simple as connecting two puzzle pieces.
        </p>
        <p class="text-lg text-gray-600 mb-8 leading-relaxed">
          Founded in 2022 by engineers who spent countless hours building custom integrations, 
          DataReflow represents our commitment to solving this problem once and for all.
        </p>
        
        <%# Company Stats %>
        <div class="grid grid-cols-2 gap-6">
          <div class="text-center p-4 bg-blue-50 rounded-xl">
            <div class="text-3xl font-bold text-blue-600"><%= @stats[:customers] %></div>
            <div class="text-sm text-gray-600">Happy Customers</div>
          </div>
          <div class="text-center p-4 bg-indigo-50 rounded-xl">
            <div class="text-3xl font-bold text-indigo-600"><%= @stats[:countries] %></div>
            <div class="text-sm text-gray-600">Countries Served</div>
          </div>
        </div>
      </div>
      
      <%# Mission Visual %>
      <div class="relative">
        <div class="bg-gradient-to-br from-blue-100 to-indigo-100 rounded-3xl p-8 h-96 flex items-center justify-center">
          <div class="text-center">
            <%= svg_icon('globe', class: 'w-24 h-24 text-blue-600 mx-auto mb-4') %>
            <h3 class="text-xl font-semibold text-gray-900 mb-2">Global Impact</h3>
            <p class="text-gray-600">Connecting businesses worldwide</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<%# Values Section %>
<section class="py-24 bg-gray-50">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="text-center mb-16">
      <h2 class="text-4xl font-bold text-gray-900 mb-6">Our Values</h2>
      <p class="text-xl text-gray-600 max-w-3xl mx-auto">
        These principles guide every decision we make and every feature we build.
      </p>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
      <% @company_values.each do |value| %>
        <div class="bg-white p-8 rounded-2xl shadow-sm hover:shadow-md transition-shadow duration-200 text-center">
          <div class="w-16 h-16 bg-blue-100 rounded-xl flex items-center justify-center mx-auto mb-6">
            <%= svg_icon(value[:icon], class: 'w-8 h-8 text-blue-600') %>
          </div>
          <h3 class="text-xl font-semibold text-gray-900 mb-3"><%= value[:title] %></h3>
          <p class="text-gray-600 leading-relaxed"><%= value[:description] %></p>
        </div>
      <% end %>
    </div>
  </div>
</section>

<%# Team Section %>
<section class="py-24 bg-white">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="text-center mb-16">
      <h2 class="text-4xl font-bold text-gray-900 mb-6">Meet Our Team</h2>
      <p class="text-xl text-gray-600 max-w-3xl mx-auto">
        We're a diverse team of engineers, designers, and business experts united by our passion for solving complex problems with simple solutions.
      </p>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
      <% @team_members.each do |member| %>
        <div class="text-center group">
          <div class="relative mb-6">
            <div class="w-32 h-32 bg-gradient-to-br from-blue-600 to-indigo-600 rounded-full flex items-center justify-center mx-auto group-hover:scale-105 transition-transform duration-200">
              <span class="text-white text-2xl font-bold"><%= member[:image] %></span>
            </div>
          </div>
          <h3 class="text-xl font-semibold text-gray-900 mb-1"><%= member[:name] %></h3>
          <p class="text-blue-600 font-medium mb-3"><%= member[:role] %></p>
          <p class="text-gray-600 text-sm leading-relaxed"><%= member[:bio] %></p>
        </div>
      <% end %>
    </div>
  </div>
</section>

<%# Timeline Section %>
<section class="py-24 bg-gray-50">
  <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="text-center mb-16">
      <h2 class="text-4xl font-bold text-gray-900 mb-6">Our Journey</h2>
      <p class="text-xl text-gray-600">
        From a simple idea to serving thousands of businesses worldwide.
      </p>
    </div>

    <div class="relative">
      <%# Timeline Line %>
      <div class="absolute left-1/2 transform -translate-x-px h-full w-0.5 bg-blue-200"></div>
      
      <% @milestones.each_with_index do |milestone, index| %>
        <div class="relative flex items-center <%= index.even? ? 'justify-start' : 'justify-end' %> mb-12">
          <%# Timeline Dot %>
          <div class="absolute left-1/2 transform -translate-x-1/2 w-4 h-4 bg-blue-600 rounded-full border-4 border-white shadow-lg"></div>
          
          <%# Content %>
          <div class="<%= index.even? ? 'mr-8 text-right' : 'ml-8 text-left' %> w-5/12">
            <div class="bg-white p-6 rounded-xl shadow-sm">
              <div class="text-2xl font-bold text-blue-600 mb-2"><%= milestone[:year] %></div>
              <h3 class="text-lg font-semibold text-gray-900 mb-2"><%= milestone[:title] %></h3>
              <p class="text-gray-600"><%= milestone[:description] %></p>
            </div>
          </div>
        </div>
      <% end %>
    </div>
  </div>
</section>

<%# CTA Section %>
<section class="py-24 bg-gradient-to-br from-blue-600 to-indigo-600">
  <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
    <h2 class="text-4xl md:text-5xl font-bold text-white mb-6">
      Ready to join our mission?
    </h2>
    <p class="text-xl text-blue-100 mb-10">
      Whether you're looking to integrate your business data or join our team, we'd love to hear from you.
    </p>
    
    <div class="flex flex-col sm:flex-row justify-center gap-4">
      <%= link_to new_user_registration_path, 
          class: "bg-white hover:bg-gray-50 text-blue-600 px-8 py-4 rounded-xl font-semibold text-lg transition-all duration-200 transform hover:scale-105 shadow-lg" do %>
        Start Your Free Trial
      <% end %>
      
      <%= link_to contact_path,
          class: "bg-blue-500 hover:bg-blue-400 text-white px-8 py-4 rounded-xl font-semibold text-lg transition-colors border border-blue-400" do %>
        Contact Our Team
      <% end %>
    </div>
  </div>
</section>

<%# Footer %>
<%= render 'shared/marketing_footer' %>

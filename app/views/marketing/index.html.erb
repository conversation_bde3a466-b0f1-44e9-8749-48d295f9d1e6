<%# SEO Meta Tags %>
<% content_for :title, "DataReflow - No-Code Data Integration Platform for SMEs" %>
<% content_for :description, "Connect, transform, and synchronize data across your business systems in under 10 minutes. 200+ integrations, visual pipeline builder, enterprise security." %>
<% content_for :keywords, "data integration, no-code, business automation, data pipelines, API integration, SME tools" %>

<%# Structured Data for SEO %>
<% content_for :head do %>
  <script type="application/ld+json">
  {
    "@context": "https://schema.org",
    "@type": "SoftwareApplication",
    "name": "DataReflow",
    "description": "No-code data integration platform for small and medium enterprises",
    "url": "<%= request.base_url %>",
    "applicationCategory": "BusinessApplication",
    "operatingSystem": "Web",
    "offers": {
      "@type": "Offer",
      "price": "0",
      "priceCurrency": "USD",
      "description": "Free trial available"
    },
    "aggregateRating": {
      "@type": "AggregateRating",
      "ratingValue": "4.8",
      "ratingCount": "150"
    }
  }
  </script>
<% end %>

<%# Hero Section %>
<section class="relative min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 overflow-hidden">
  <%# Background Pattern %>
  <div class="absolute inset-0 bg-grid-pattern opacity-5"></div>

  <%# Navigation %>
  <nav class="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
    <div class="flex justify-between items-center">
      <div class="flex items-center space-x-2">
        <div class="w-8 h-8 bg-gradient-to-br from-blue-600 to-indigo-600 rounded-lg flex items-center justify-center">
          <%= svg_icon('lightning', class: 'w-5 h-5 text-white') %>
        </div>
        <span class="text-2xl font-bold text-gray-900">DataReflow</span>
      </div>

      <div class="hidden md:flex items-center space-x-8">
        <a href="#features" class="text-gray-600 hover:text-gray-900 font-medium transition-colors">Features</a>
        <a href="#integrations" class="text-gray-600 hover:text-gray-900 font-medium transition-colors">Integrations</a>
        <a href="#pricing" class="text-gray-600 hover:text-gray-900 font-medium transition-colors">Pricing</a>
        <%= link_to "About", about_path,
            class: "text-gray-600 hover:text-gray-900 font-medium transition-colors" %>
        <%= link_to "Help", help_path,
            class: "text-gray-600 hover:text-gray-900 font-medium transition-colors" %>
        <%= link_to "Sign In", new_user_session_path,
            class: "text-gray-600 hover:text-gray-900 font-medium transition-colors" %>
        <%= link_to "Get Started", new_user_registration_path,
            class: "bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg font-semibold transition-all duration-200 transform hover:scale-105 shadow-lg hover:shadow-xl" %>
      </div>
    </div>
  </nav>

  <%# Hero Content %>
  <div class="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-20 pb-32">
    <div class="text-center">
      <%# Main Headline %>
      <h1 class="text-5xl md:text-7xl font-bold text-gray-900 leading-tight">
        Connect Your
        <span class="bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
          Business Data
        </span>
        in Minutes
      </h1>

      <%# Subheadline %>
      <p class="mt-8 text-xl md:text-2xl text-gray-600 max-w-4xl mx-auto leading-relaxed">
        No-code data integration platform that connects, transforms, and synchronizes data across your business systems.
        <strong class="text-gray-900">Setup in under 10 minutes.</strong>
      </p>

      <%# CTA Buttons %>
      <div class="mt-12 flex flex-col sm:flex-row justify-center gap-4 sm:gap-6">
        <%= link_to new_user_registration_path,
            class: "group bg-blue-600 hover:bg-blue-700 text-white px-8 py-4 rounded-xl font-semibold text-lg transition-all duration-200 transform hover:scale-105 shadow-lg hover:shadow-xl flex items-center justify-center space-x-2" do %>
          <span>Start Free Trial</span>
          <%= svg_icon('arrow-right', class: 'w-5 h-5 group-hover:translate-x-1 transition-transform') %>
        <% end %>

        <button class="group bg-white hover:bg-gray-50 text-gray-900 px-8 py-4 rounded-xl font-semibold text-lg border border-gray-200 transition-all duration-200 flex items-center justify-center space-x-2 shadow-sm hover:shadow-md">
          <%= svg_icon('play', class: 'w-5 h-5') %>
          <span>Watch Demo</span>
        </button>
      </div>

      <%# Trust Indicators %>
      <div class="mt-16 flex flex-col items-center">
        <p class="text-sm text-gray-500 mb-6">Trusted by <%= @stats[:customers] %> businesses worldwide</p>
        <div class="flex items-center space-x-8 opacity-60">
          <div class="text-2xl font-bold text-gray-400">Shopify</div>
          <div class="text-2xl font-bold text-gray-400">HubSpot</div>
          <div class="text-2xl font-bold text-gray-400">QuickBooks</div>
          <div class="text-2xl font-bold text-gray-400">Salesforce</div>
        </div>
      </div>
    </div>
  </div>

  <%# Floating Stats %>
  <div class="absolute bottom-8 left-1/2 transform -translate-x-1/2 hidden lg:flex items-center space-x-8 bg-white/80 backdrop-blur-sm rounded-2xl px-8 py-4 shadow-lg">
    <div class="text-center">
      <div class="text-2xl font-bold text-gray-900"><%= @stats[:customers] %></div>
      <div class="text-sm text-gray-600">Active Users</div>
    </div>
    <div class="w-px h-8 bg-gray-200"></div>
    <div class="text-center">
      <div class="text-2xl font-bold text-gray-900"><%= @stats[:data_synced] %></div>
      <div class="text-sm text-gray-600">Records Synced</div>
    </div>
    <div class="w-px h-8 bg-gray-200"></div>
    <div class="text-center">
      <div class="text-2xl font-bold text-gray-900"><%= @stats[:uptime] %></div>
      <div class="text-sm text-gray-600">Uptime</div>
    </div>
  </div>
</section>

<%# Features Section %>
<section id="features" class="py-24 bg-white">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <%# Section Header %>
    <div class="text-center mb-20">
      <h2 class="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
        Everything you need to
        <span class="bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">automate your data</span>
      </h2>
      <p class="text-xl text-gray-600 max-w-3xl mx-auto">
        Powerful features designed for businesses that want to move fast without compromising on security or reliability.
      </p>
    </div>

    <%# Features Grid %>
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
      <% @features.each do |feature| %>
        <div class="group relative bg-white p-8 rounded-2xl border border-gray-100 hover:border-gray-200 transition-all duration-300 hover:shadow-xl hover:-translate-y-1">
          <%# Icon %>
          <div class="<%= color_classes(feature[:color], 'bg') %> w-14 h-14 rounded-xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
            <%= svg_icon(feature[:icon], class: "w-7 h-7 #{color_classes(feature[:color], 'text')}") %>
          </div>

          <%# Content %>
          <h3 class="text-xl font-semibold text-gray-900 mb-3"><%= feature[:title] %></h3>
          <p class="text-gray-600 leading-relaxed"><%= feature[:description] %></p>

          <%# Hover Effect %>
          <div class="absolute inset-0 bg-gradient-to-br from-blue-50/50 to-indigo-50/50 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300 -z-10"></div>
        </div>
      <% end %>
    </div>

    <%# Feature Highlight %>
    <div class="mt-20 bg-gradient-to-br from-blue-50 to-indigo-50 rounded-3xl p-8 md:p-12">
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
        <div>
          <h3 class="text-3xl font-bold text-gray-900 mb-6">
            Visual Pipeline Builder
          </h3>
          <p class="text-lg text-gray-600 mb-8">
            Create complex data workflows with our intuitive drag-and-drop interface.
            No coding required - just connect your data sources and define your transformations.
          </p>
          <ul class="space-y-4">
            <li class="flex items-center space-x-3">
              <%= svg_icon('check-circle', class: 'w-5 h-5 text-green-600') %>
              <span class="text-gray-700">Real-time data transformation</span>
            </li>
            <li class="flex items-center space-x-3">
              <%= svg_icon('check-circle', class: 'w-5 h-5 text-green-600') %>
              <span class="text-gray-700">Error handling and retry logic</span>
            </li>
            <li class="flex items-center space-x-3">
              <%= svg_icon('check-circle', class: 'w-5 h-5 text-green-600') %>
              <span class="text-gray-700">Performance monitoring</span>
            </li>
          </ul>
        </div>

        <%# Pipeline Visualization %>
        <div class="relative">
          <div class="bg-white rounded-2xl shadow-lg p-6 border border-gray-100">
            <div class="flex items-center justify-between mb-4">
              <h4 class="font-semibold text-gray-900">Customer Data Pipeline</h4>
              <span class="bg-green-100 text-green-800 text-xs font-medium px-2 py-1 rounded-full">Active</span>
            </div>

            <%# Pipeline Steps %>
            <div class="space-y-3">
              <div class="flex items-center space-x-3 p-3 bg-blue-50 rounded-lg">
                <div class="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
                  <span class="text-white text-sm font-bold">1</span>
                </div>
                <span class="text-sm font-medium text-gray-900">Shopify Orders</span>
              </div>

              <div class="flex justify-center">
                <%= svg_icon('arrow-right', class: 'w-4 h-4 text-gray-400 transform rotate-90') %>
              </div>

              <div class="flex items-center space-x-3 p-3 bg-purple-50 rounded-lg">
                <div class="w-8 h-8 bg-purple-600 rounded-lg flex items-center justify-center">
                  <span class="text-white text-sm font-bold">2</span>
                </div>
                <span class="text-sm font-medium text-gray-900">Transform Data</span>
              </div>

              <div class="flex justify-center">
                <%= svg_icon('arrow-right', class: 'w-4 h-4 text-gray-400 transform rotate-90') %>
              </div>

              <div class="flex items-center space-x-3 p-3 bg-green-50 rounded-lg">
                <div class="w-8 h-8 bg-green-600 rounded-lg flex items-center justify-center">
                  <span class="text-white text-sm font-bold">3</span>
                </div>
                <span class="text-sm font-medium text-gray-900">HubSpot CRM</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<%# Integrations Section %>
<section id="integrations" class="py-24 bg-gray-50">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="text-center mb-16">
      <h2 class="text-4xl font-bold text-gray-900 mb-6">
        Connect to <span class="text-blue-600"><%= @stats[:integrations] %></span> popular tools
      </h2>
      <p class="text-xl text-gray-600 max-w-3xl mx-auto">
        Pre-built connectors for all your favorite business applications. No custom development required.
      </p>
    </div>

    <%# Integration Categories %>
    <div class="grid grid-cols-2 md:grid-cols-4 gap-6 mb-16">
      <% @integrations.each do |integration| %>
        <div class="bg-white p-6 rounded-xl shadow-sm hover:shadow-md transition-shadow duration-200 text-center group">
          <div class="w-12 h-12 bg-gray-100 rounded-lg mx-auto mb-3 flex items-center justify-center group-hover:bg-blue-50 transition-colors">
            <%= svg_icon('globe', class: 'w-6 h-6 text-gray-600 group-hover:text-blue-600') %>
          </div>
          <h3 class="font-semibold text-gray-900 mb-1"><%= integration[:name] %></h3>
          <p class="text-sm text-gray-500"><%= integration[:category] %></p>
        </div>
      <% end %>
    </div>

    <%# Integration CTA %>
    <div class="text-center">
      <p class="text-gray-600 mb-6">Don't see your tool? We add new integrations every week.</p>
      <%= link_to  new_user_registration_path,
          class: "inline-flex items-center space-x-2 bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-semibold transition-colors" do %>
        <span>Request Integration</span>
        <%= svg_icon('arrow-right', class: 'w-4 h-4') %>
      <% end %>
    </div>
  </div>
</section>

<%# Testimonials Section %>
<section id="testimonials" class="py-24 bg-white">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="text-center mb-16">
      <h2 class="text-4xl font-bold text-gray-900 mb-6">
        Loved by businesses worldwide
      </h2>
      <p class="text-xl text-gray-600">
        See what our customers are saying about DataReflow
      </p>
    </div>

    <%# Testimonials Grid %>
    <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
      <% @testimonials.each do |testimonial| %>
        <div class="bg-gray-50 p-8 rounded-2xl relative">
          <%# Quote %>
          <div class="mb-6">
            <div class="flex mb-4">
              <% 5.times do %>
                <%= svg_icon('star', class: 'w-5 h-5 text-yellow-400 fill-current') %>
              <% end %>
            </div>
            <p class="text-gray-700 leading-relaxed">
              "<%= testimonial[:content] %>"
            </p>
          </div>

          <%# Author %>
          <div class="flex items-center space-x-3">
            <div class="w-12 h-12 bg-gradient-to-br from-blue-600 to-indigo-600 rounded-full flex items-center justify-center">
              <span class="text-white font-semibold text-sm"><%= testimonial[:avatar] %></span>
            </div>
            <div>
              <div class="font-semibold text-gray-900"><%= testimonial[:name] %></div>
              <div class="text-sm text-gray-600"><%= testimonial[:role] %>, <%= testimonial[:company] %></div>
            </div>
          </div>
        </div>
      <% end %>
    </div>

    <%# Social Proof Stats %>
    <div class="mt-20 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-3xl p-8 md:p-12 text-white">
      <div class="grid grid-cols-2 md:grid-cols-4 gap-8 text-center">
        <div>
          <div class="text-3xl md:text-4xl font-bold mb-2"><%= @stats[:customers] %></div>
          <div class="text-blue-100">Happy Customers</div>
        </div>
        <div>
          <div class="text-3xl md:text-4xl font-bold mb-2"><%= @stats[:data_synced] %></div>
          <div class="text-blue-100">Records Processed</div>
        </div>
        <div>
          <div class="text-3xl md:text-4xl font-bold mb-2"><%= @stats[:uptime] %></div>
          <div class="text-blue-100">Uptime</div>
        </div>
        <div>
          <div class="text-3xl md:text-4xl font-bold mb-2">4.8/5</div>
          <div class="text-blue-100">Customer Rating</div>
        </div>
      </div>
    </div>
  </div>
</section>

<%# Pricing Section %>
<section id="pricing" class="py-24 bg-gray-50">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="text-center mb-16">
      <h2 class="text-4xl font-bold text-gray-900 mb-6">
        Simple, transparent pricing
      </h2>
      <p class="text-xl text-gray-600">
        Start free, scale as you grow. No hidden fees or surprise charges.
      </p>
    </div>

    <%# Pricing Cards %>
    <div class="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-5xl mx-auto">
      <%# Starter Plan %>
      <div class="bg-white p-8 rounded-2xl shadow-sm border border-gray-200">
        <div class="text-center mb-8">
          <h3 class="text-xl font-semibold text-gray-900 mb-2">Starter</h3>
          <div class="text-4xl font-bold text-gray-900 mb-1">Free</div>
          <p class="text-gray-600">Perfect for getting started</p>
        </div>

        <ul class="space-y-4 mb-8">
          <li class="flex items-center space-x-3">
            <%= svg_icon('check-circle', class: 'w-5 h-5 text-green-600') %>
            <span class="text-gray-700">Up to 1,000 records/month</span>
          </li>
          <li class="flex items-center space-x-3">
            <%= svg_icon('check-circle', class: 'w-5 h-5 text-green-600') %>
            <span class="text-gray-700">5 integrations</span>
          </li>
          <li class="flex items-center space-x-3">
            <%= svg_icon('check-circle', class: 'w-5 h-5 text-green-600') %>
            <span class="text-gray-700">Basic support</span>
          </li>
        </ul>

        <%= link_to "Get Started", new_user_registration_path,
            class: "w-full bg-gray-900 hover:bg-gray-800 text-white py-3 px-6 rounded-lg font-semibold text-center block transition-colors" %>
      </div>

      <%# Professional Plan %>
      <div class="bg-white p-8 rounded-2xl shadow-lg border-2 border-blue-600 relative">
        <div class="absolute -top-4 left-1/2 transform -translate-x-1/2">
          <span class="bg-blue-600 text-white px-4 py-1 rounded-full text-sm font-semibold">Most Popular</span>
        </div>

        <div class="text-center mb-8">
          <h3 class="text-xl font-semibold text-gray-900 mb-2">Professional</h3>
          <div class="text-4xl font-bold text-gray-900 mb-1">$49<span class="text-lg text-gray-600">/month</span></div>
          <p class="text-gray-600">For growing businesses</p>
        </div>

        <ul class="space-y-4 mb-8">
          <li class="flex items-center space-x-3">
            <%= svg_icon('check-circle', class: 'w-5 h-5 text-green-600') %>
            <span class="text-gray-700">Up to 100,000 records/month</span>
          </li>
          <li class="flex items-center space-x-3">
            <%= svg_icon('check-circle', class: 'w-5 h-5 text-green-600') %>
            <span class="text-gray-700">Unlimited integrations</span>
          </li>
          <li class="flex items-center space-x-3">
            <%= svg_icon('check-circle', class: 'w-5 h-5 text-green-600') %>
            <span class="text-gray-700">Priority support</span>
          </li>
          <li class="flex items-center space-x-3">
            <%= svg_icon('check-circle', class: 'w-5 h-5 text-green-600') %>
            <span class="text-gray-700">Advanced analytics</span>
          </li>
        </ul>

        <%= link_to "Start Free Trial", new_user_registration_path,
            class: "w-full bg-blue-600 hover:bg-blue-700 text-white py-3 px-6 rounded-lg font-semibold text-center block transition-colors" %>
      </div>

      <%# Enterprise Plan %>
      <div class="bg-white p-8 rounded-2xl shadow-sm border border-gray-200">
        <div class="text-center mb-8">
          <h3 class="text-xl font-semibold text-gray-900 mb-2">Enterprise</h3>
          <div class="text-4xl font-bold text-gray-900 mb-1">Custom</div>
          <p class="text-gray-600">For large organizations</p>
        </div>

        <ul class="space-y-4 mb-8">
          <li class="flex items-center space-x-3">
            <%= svg_icon('check-circle', class: 'w-5 h-5 text-green-600') %>
            <span class="text-gray-700">Unlimited records</span>
          </li>
          <li class="flex items-center space-x-3">
            <%= svg_icon('check-circle', class: 'w-5 h-5 text-green-600') %>
            <span class="text-gray-700">Custom integrations</span>
          </li>
          <li class="flex items-center space-x-3">
            <%= svg_icon('check-circle', class: 'w-5 h-5 text-green-600') %>
            <span class="text-gray-700">Dedicated support</span>
          </li>
          <li class="flex items-center space-x-3">
            <%= svg_icon('check-circle', class: 'w-5 h-5 text-green-600') %>
            <span class="text-gray-700">SLA guarantee</span>
          </li>
        </ul>

        <%= link_to "Contact Sales", new_user_registration_path,
            class: "w-full bg-gray-900 hover:bg-gray-800 text-white py-3 px-6 rounded-lg font-semibold text-center block transition-colors" %>
      </div>
    </div>
  </div>
</section>

<%# Final CTA Section %>
<section class="py-24 bg-gradient-to-br from-blue-600 to-indigo-600">
  <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
    <h2 class="text-4xl md:text-5xl font-bold text-white mb-6">
      Ready to automate your data workflows?
    </h2>
    <p class="text-xl text-blue-100 mb-10">
      Join thousands of businesses that trust DataReflow to keep their data in sync.
    </p>

    <div class="flex flex-col sm:flex-row justify-center gap-4">
      <%= link_to new_user_registration_path,
          class: "bg-white hover:bg-gray-50 text-blue-600 px-8 py-4 rounded-xl font-semibold text-lg transition-all duration-200 transform hover:scale-105 shadow-lg" do %>
        Start Your Free Trial
      <% end %>

      <button class="bg-blue-500 hover:bg-blue-400 text-white px-8 py-4 rounded-xl font-semibold text-lg transition-colors border border-blue-400">
        Schedule Demo
      </button>
    </div>

    <p class="text-blue-200 text-sm mt-6">
      No credit card required • 14-day free trial • Cancel anytime
    </p>
  </div>
</section>

<%# Footer %>
<footer class="bg-gray-900 text-white">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
    <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
      <%# Company Info %>
      <div class="md:col-span-1">
        <div class="flex items-center space-x-2 mb-6">
          <div class="w-8 h-8 bg-gradient-to-br from-blue-600 to-indigo-600 rounded-lg flex items-center justify-center">
            <%= svg_icon('lightning', class: 'w-5 h-5 text-white') %>
          </div>
          <span class="text-xl font-bold">DataReflow</span>
        </div>
        <p class="text-gray-400 mb-6">
          The no-code data integration platform that connects your business systems in minutes, not months.
        </p>
        <div class="flex space-x-4">
          <a href="#" class="text-gray-400 hover:text-white transition-colors">
            <%= svg_icon('globe', class: 'w-5 h-5') %>
          </a>
          <a href="#" class="text-gray-400 hover:text-white transition-colors">
            <%= svg_icon('users', class: 'w-5 h-5') %>
          </a>
        </div>
      </div>

      <%# Product Links %>
      <div>
        <h3 class="font-semibold mb-4">Product</h3>
        <ul class="space-y-3 text-gray-400">
          <li><a href="#features" class="hover:text-white transition-colors">Features</a></li>
          <li><a href="#integrations" class="hover:text-white transition-colors">Integrations</a></li>
          <li><a href="#pricing" class="hover:text-white transition-colors">Pricing</a></li>
          <li><a href="#" class="hover:text-white transition-colors">API Documentation</a></li>
          <li><a href="#" class="hover:text-white transition-colors">Status Page</a></li>
        </ul>
      </div>

      <%# Company Links %>
      <div>
        <h3 class="font-semibold mb-4">Company</h3>
        <ul class="space-y-3 text-gray-400">
          <li><%= link_to "About Us", about_path, class: "hover:text-white transition-colors" %></li>
          <li><a href="#" class="hover:text-white transition-colors">Careers</a></li>
          <li><a href="#" class="hover:text-white transition-colors">Blog</a></li>
          <li><a href="#" class="hover:text-white transition-colors">Press</a></li>
          <li><a href="#" class="hover:text-white transition-colors">Partners</a></li>
        </ul>
      </div>

      <%# Support Links %>
      <div>
        <h3 class="font-semibold mb-4">Support</h3>
        <ul class="space-y-3 text-gray-400">
          <li><%= link_to "Help Center", help_path, class: "hover:text-white transition-colors" %></li>
          <li><%= link_to "Contact Support", contact_path, class: "hover:text-white transition-colors" %></li>
          <li><a href="#" class="hover:text-white transition-colors">Community</a></li>
          <li><a href="#" class="hover:text-white transition-colors">Security</a></li>
          <li><a href="#" class="hover:text-white transition-colors">Privacy Policy</a></li>
        </ul>
      </div>
    </div>

    <%# Footer Bottom %>
    <div class="border-t border-gray-800 mt-12 pt-8 flex flex-col md:flex-row justify-between items-center">
      <p class="text-gray-400 text-sm">
        © <%= Date.current.year %> DataReflow. All rights reserved.
      </p>
      <div class="flex items-center space-x-6 mt-4 md:mt-0">
        <a href="#" class="text-gray-400 hover:text-white text-sm transition-colors">Terms of Service</a>
        <a href="#" class="text-gray-400 hover:text-white text-sm transition-colors">Privacy Policy</a>
        <a href="#" class="text-gray-400 hover:text-white text-sm transition-colors">Cookie Policy</a>
      </div>
    </div>
  </div>
</footer>

<%# Custom Styles for Landing Page %>
<style>
  .bg-grid-pattern {
    background-image:
      linear-gradient(rgba(59, 130, 246, 0.1) 1px, transparent 1px),
      linear-gradient(90deg, rgba(59, 130, 246, 0.1) 1px, transparent 1px);
    background-size: 20px 20px;
  }

  @keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
  }

  .animate-float {
    animation: float 6s ease-in-out infinite;
  }

  .animate-float-delayed {
    animation: float 6s ease-in-out infinite;
    animation-delay: 2s;
  }

  /* Smooth scrolling for anchor links */
  html {
    scroll-behavior: smooth;
  }

  /* Custom gradient text */
  .gradient-text {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }
</style>
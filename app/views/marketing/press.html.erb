<%# SEO Meta Tags %>
<% content_for :title, "DataReflow Press & Media - News, Coverage & Press Kit" %>
<% content_for :description, "Latest news, press releases, and media coverage about DataReflow. Download our press kit with logos, images, and executive information." %>
<% content_for :keywords, "press releases, media coverage, news, press kit, company news, funding announcement" %>

<%# Hero Section %>
<section class="relative bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 overflow-hidden">
  <%= render 'shared/marketing_nav' %>
  
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24">
    <div class="text-center">
      <h1 class="text-5xl md:text-6xl font-bold text-gray-900 mb-6">
        Press &
        <span class="bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
          Media
        </span>
      </h1>
      <p class="text-xl md:text-2xl text-gray-600 max-w-4xl mx-auto leading-relaxed mb-10">
        Latest news, press releases, and media coverage about DataReflow's mission to democratize data integration.
      </p>
      
      <%# Quick Stats %>
      <div class="grid grid-cols-2 md:grid-cols-4 gap-6 max-w-4xl mx-auto mb-12">
        <div class="bg-white/80 backdrop-blur-sm rounded-xl p-4 shadow-sm">
          <div class="text-2xl font-bold text-blue-600"><%= @company_facts[:founded] %></div>
          <div class="text-sm text-gray-600">Founded</div>
        </div>
        <div class="bg-white/80 backdrop-blur-sm rounded-xl p-4 shadow-sm">
          <div class="text-2xl font-bold text-indigo-600"><%= @company_facts[:employees] %></div>
          <div class="text-sm text-gray-600">Team Members</div>
        </div>
        <div class="bg-white/80 backdrop-blur-sm rounded-xl p-4 shadow-sm">
          <div class="text-2xl font-bold text-purple-600"><%= @company_facts[:customers] %></div>
          <div class="text-sm text-gray-600">Customers</div>
        </div>
        <div class="bg-white/80 backdrop-blur-sm rounded-xl p-4 shadow-sm">
          <div class="text-2xl font-bold text-green-600"><%= @company_facts[:funding] %></div>
          <div class="text-sm text-gray-600">Funding Raised</div>
        </div>
      </div>

      <div class="flex flex-col sm:flex-row justify-center gap-4">
        <button class="bg-blue-600 hover:bg-blue-700 text-white px-8 py-4 rounded-xl font-semibold text-lg transition-all duration-200 transform hover:scale-105 shadow-lg">
          Download Press Kit
        </button>
        
        <a href="mailto:<EMAIL>" class="bg-white hover:bg-gray-50 text-gray-900 px-8 py-4 rounded-xl font-semibold text-lg border border-gray-200 transition-all duration-200 shadow-sm hover:shadow-md">
          Media Inquiries
        </a>
      </div>
    </div>
  </div>
</section>

<%# Latest Press Releases %>
<section class="py-16 bg-white">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="text-center mb-12">
      <h2 class="text-3xl font-bold text-gray-900 mb-4">Latest Press Releases</h2>
      <p class="text-lg text-gray-600">Recent announcements and company news</p>
    </div>

    <div class="space-y-8">
      <% @press_releases.each do |release| %>
        <div class="bg-gray-50 p-8 rounded-2xl hover:shadow-lg transition-shadow duration-200 cursor-pointer group">
          <div class="flex items-center justify-between mb-4">
            <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium <%= release[:category] == 'Funding' ? 'bg-green-100 text-green-800' : release[:category] == 'Product' ? 'bg-blue-100 text-blue-800' : release[:category] == 'Security' ? 'bg-purple-100 text-purple-800' : 'bg-gray-100 text-gray-800' %>">
              <%= release[:category] %>
            </span>
            <span class="text-sm text-gray-500"><%= release[:date] %></span>
          </div>
          
          <h3 class="text-xl font-semibold text-gray-900 mb-3 group-hover:text-blue-600 transition-colors">
            <%= release[:title] %>
          </h3>
          
          <p class="text-gray-600 leading-relaxed mb-4"><%= release[:summary] %></p>
          
          <div class="flex items-center justify-between">
            <button class="text-blue-600 hover:text-blue-700 font-medium text-sm flex items-center group-hover:translate-x-1 transition-transform">
              Read Full Release
              <%= svg_icon('arrow-right', class: 'w-4 h-4 ml-2') %>
            </button>
            <button class="bg-gray-200 hover:bg-gray-300 text-gray-700 px-4 py-2 rounded-lg text-sm font-medium transition-colors">
              Share
            </button>
          </div>
        </div>
      <% end %>
    </div>
  </div>
</section>

<%# Media Coverage %>
<section class="py-16 bg-gray-50">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="text-center mb-12">
      <h2 class="text-3xl font-bold text-gray-900 mb-4">Media Coverage</h2>
      <p class="text-lg text-gray-600">What the media is saying about DataReflow</p>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
      <% @media_coverage.each do |article| %>
        <div class="bg-white p-6 rounded-xl shadow-sm hover:shadow-md transition-shadow duration-200 cursor-pointer group">
          <div class="flex items-center justify-between mb-4">
            <span class="font-semibold text-blue-600"><%= article[:publication] %></span>
            <span class="text-sm text-gray-500"><%= article[:date] %></span>
          </div>
          
          <h3 class="text-lg font-semibold text-gray-900 mb-3 group-hover:text-blue-600 transition-colors">
            <%= article[:title] %>
          </h3>
          
          <div class="flex items-center justify-between text-sm text-gray-600">
            <span>by <%= article[:author] %></span>
            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
              <%= article[:type] %>
            </span>
          </div>
        </div>
      <% end %>
    </div>

    <div class="text-center mt-12">
      <button class="bg-blue-600 hover:bg-blue-700 text-white px-8 py-3 rounded-lg font-semibold transition-colors">
        View All Coverage
      </button>
    </div>
  </div>
</section>

<%# Executive Team %>
<section class="py-16 bg-white">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="text-center mb-12">
      <h2 class="text-3xl font-bold text-gray-900 mb-4">Executive Team</h2>
      <p class="text-lg text-gray-600">Meet the leadership team behind DataReflow</p>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
      <% @executive_bios.each do |executive| %>
        <div class="bg-gray-50 p-8 rounded-2xl text-center hover:shadow-lg transition-shadow duration-200">
          <div class="w-24 h-24 bg-gradient-to-br from-blue-600 to-indigo-600 rounded-full flex items-center justify-center mx-auto mb-6">
            <span class="text-white text-2xl font-bold"><%= executive[:image] %></span>
          </div>
          
          <h3 class="text-xl font-semibold text-gray-900 mb-1"><%= executive[:name] %></h3>
          <p class="text-blue-600 font-medium mb-4"><%= executive[:title] %></p>
          
          <p class="text-gray-600 text-sm leading-relaxed text-left"><%= executive[:bio] %></p>
        </div>
      <% end %>
    </div>
  </div>
</section>

<%# Press Kit %>
<section class="py-16 bg-gray-50">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="text-center mb-12">
      <h2 class="text-3xl font-bold text-gray-900 mb-4">Press Kit</h2>
      <p class="text-lg text-gray-600">Download high-quality assets for media coverage</p>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
      <% @press_kit_assets.each do |asset| %>
        <div class="bg-white p-6 rounded-xl shadow-sm hover:shadow-md transition-shadow duration-200">
          <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-semibold text-gray-900"><%= asset[:name] %></h3>
            <span class="text-sm text-gray-500"><%= asset[:file_size] %></span>
          </div>
          
          <p class="text-gray-600 mb-4"><%= asset[:description] %></p>
          
          <div class="flex items-center justify-between">
            <div class="flex space-x-2">
              <% asset[:formats].each do |format| %>
                <span class="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-gray-100 text-gray-800">
                  <%= format %>
                </span>
              <% end %>
            </div>
            <button class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors">
              Download
            </button>
          </div>
        </div>
      <% end %>
    </div>
  </div>
</section>

<%# Company Facts %>
<section class="py-16 bg-white">
  <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="text-center mb-12">
      <h2 class="text-3xl font-bold text-gray-900 mb-4">Company Facts</h2>
      <p class="text-lg text-gray-600">Key information about DataReflow</p>
    </div>

    <div class="bg-gray-50 rounded-2xl p-8">
      <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
        <div>
          <h3 class="font-semibold text-gray-900 mb-4">Company Information</h3>
          <dl class="space-y-3">
            <div class="flex justify-between">
              <dt class="text-gray-600">Founded:</dt>
              <dd class="font-medium text-gray-900"><%= @company_facts[:founded] %></dd>
            </div>
            <div class="flex justify-between">
              <dt class="text-gray-600">Headquarters:</dt>
              <dd class="font-medium text-gray-900"><%= @company_facts[:headquarters] %></dd>
            </div>
            <div class="flex justify-between">
              <dt class="text-gray-600">Employees:</dt>
              <dd class="font-medium text-gray-900"><%= @company_facts[:employees] %></dd>
            </div>
            <div class="flex justify-between">
              <dt class="text-gray-600">Investors:</dt>
              <dd class="font-medium text-gray-900"><%= @company_facts[:investors] %></dd>
            </div>
          </dl>
        </div>

        <div>
          <h3 class="font-semibold text-gray-900 mb-4">Business Metrics</h3>
          <dl class="space-y-3">
            <div class="flex justify-between">
              <dt class="text-gray-600">Customers:</dt>
              <dd class="font-medium text-gray-900"><%= @company_facts[:customers] %></dd>
            </div>
            <div class="flex justify-between">
              <dt class="text-gray-600">Integrations:</dt>
              <dd class="font-medium text-gray-900"><%= @company_facts[:integrations] %></dd>
            </div>
            <div class="flex justify-between">
              <dt class="text-gray-600">Data Processed:</dt>
              <dd class="font-medium text-gray-900"><%= @company_facts[:data_processed] %></dd>
            </div>
            <div class="flex justify-between">
              <dt class="text-gray-600">Total Funding:</dt>
              <dd class="font-medium text-gray-900"><%= @company_facts[:funding] %></dd>
            </div>
          </dl>
        </div>
      </div>
    </div>
  </div>
</section>

<%# Media Contact %>
<section class="py-16 bg-blue-600">
  <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
    <h2 class="text-4xl md:text-5xl font-bold text-white mb-6">
      Media Inquiries
    </h2>
    <p class="text-xl text-blue-100 mb-10">
      For press inquiries, interviews, or additional information, please contact our media team.
    </p>
    
    <div class="bg-white rounded-2xl p-8 md:p-12">
      <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
        <div class="text-center">
          <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-4">
            <%= svg_icon('globe', class: 'w-6 h-6 text-blue-600') %>
          </div>
          <h3 class="font-semibold text-gray-900 mb-2">Press Contact</h3>
          <p class="text-gray-600 mb-4">For all media inquiries and press requests</p>
          <a href="mailto:<EMAIL>" class="text-blue-600 hover:text-blue-700 font-medium">
            <EMAIL>
          </a>
        </div>

        <div class="text-center">
          <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-4">
            <%= svg_icon('users', class: 'w-6 h-6 text-green-600') %>
          </div>
          <h3 class="font-semibold text-gray-900 mb-2">Executive Interviews</h3>
          <p class="text-gray-600 mb-4">Schedule interviews with our leadership team</p>
          <%= link_to contact_path, class: "text-green-600 hover:text-green-700 font-medium" do %>
            Schedule Interview
          <% end %>
        </div>
      </div>
    </div>
  </div>
</section>

<%# Footer %>
<%= render 'shared/marketing_footer' %>

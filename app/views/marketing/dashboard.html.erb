<% content_for :title, "Dashboard - DataReflow" %>

<div class="min-h-screen bg-gray-50">
  <!-- Header -->
  <header class="bg-white shadow">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="flex justify-between items-center py-6">
        <div class="flex items-center">
          <h1 class="text-3xl font-bold text-gray-900">DataReflow</h1>
        </div>
        <div class="flex items-center space-x-4">
          <span class="text-sm text-gray-500">Welcome, <%= @user.first_name %></span>
          <%= button_to "Sign Out", destroy_user_session_path, method: :delete,
              class: "bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors border-0 cursor-pointer",
              form: { class: "inline-block" },
              data: { confirm: "Are you sure you want to sign out?" } %>
        </div>
      </div>
    </div>
  </header>

  <!-- Main Content -->
  <main class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
    <div class="px-4 py-6 sm:px-0">
      <!-- Welcome Section -->
      <div class="bg-white overflow-hidden shadow rounded-lg mb-6">
        <div class="px-4 py-5 sm:p-6">
          <h2 class="text-lg font-medium text-gray-900 mb-2">Welcome to your DataReflow workspace!</h2>
          <p class="text-gray-600 mb-4">
            You're successfully logged in to your <strong><%= @account.name %></strong> account.
          </p>
          
          <!-- Account Info -->
          <div class="bg-blue-50 border border-blue-200 rounded-md p-4 mb-4">
            <div class="flex">
              <div class="flex-shrink-0">
                <svg class="h-5 w-5 text-blue-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
                </svg>
              </div>
              <div class="ml-3">
                <h3 class="text-sm font-medium text-blue-800">Account Information</h3>
                <div class="mt-2 text-sm text-blue-700">
                  <p><strong>Account:</strong> <%= @account.name %></p>
                  <p><strong>Subdomain:</strong> <%= @account.subdomain %>.datareflow.io</p>
                  <p><strong>Email:</strong> <%= @user.email %></p>
                </div>
              </div>
            </div>
          </div>

          <!-- Quick Actions -->
          <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div class="bg-gray-50 p-4 rounded-lg">
              <h3 class="font-medium text-gray-900 mb-2">Data Pipelines</h3>
              <p class="text-sm text-gray-600 mb-3">Create and manage your data processing pipelines.</p>
              <button class="bg-blue-600 hover:bg-blue-700 text-white px-3 py-2 rounded text-sm font-medium transition-colors">
                View Pipelines
              </button>
            </div>
            
            <div class="bg-gray-50 p-4 rounded-lg">
              <h3 class="font-medium text-gray-900 mb-2">Data Connectors</h3>
              <p class="text-sm text-gray-600 mb-3">Connect to your data sources and destinations.</p>
              <button class="bg-green-600 hover:bg-green-700 text-white px-3 py-2 rounded text-sm font-medium transition-colors">
                View Connectors
              </button>
            </div>
            
            <div class="bg-gray-50 p-4 rounded-lg">
              <h3 class="font-medium text-gray-900 mb-2">Account Settings</h3>
              <p class="text-sm text-gray-600 mb-3">Manage your account and team settings.</p>
              <%= link_to "Account Settings", edit_user_registration_path,
                  class: "bg-gray-600 hover:bg-gray-700 text-white px-3 py-2 rounded text-sm font-medium transition-colors inline-block" %>
            </div>
          </div>
        </div>
      </div>

      <!-- Status Section -->
      <div class="bg-white overflow-hidden shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
          <h2 class="text-lg font-medium text-gray-900 mb-4">System Status</h2>
          <div class="space-y-3">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <div class="h-2 w-2 bg-green-400 rounded-full"></div>
              </div>
              <div class="ml-3">
                <p class="text-sm text-gray-900">Authentication System: <span class="text-green-600 font-medium">Operational</span></p>
              </div>
            </div>
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <div class="h-2 w-2 bg-green-400 rounded-full"></div>
              </div>
              <div class="ml-3">
                <p class="text-sm text-gray-900">Password Reset: <span class="text-green-600 font-medium">Functional</span></p>
              </div>
            </div>
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <div class="h-2 w-2 bg-green-400 rounded-full"></div>
              </div>
              <div class="ml-3">
                <p class="text-sm text-gray-900">User Registration: <span class="text-green-600 font-medium">Active</span></p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </main>
</div>

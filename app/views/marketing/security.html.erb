<%# SEO Meta Tags %>
<% content_for :title, "DataReflow Security - Enterprise-Grade Data Protection" %>
<% content_for :description, "Learn about DataReflow's comprehensive security measures, compliance certifications, and data protection practices. SOC 2, GDPR, and CCPA compliant." %>
<% content_for :keywords, "data security, SOC 2 compliance, GDPR compliant, enterprise security, data protection, encryption" %>

<%# Hero Section %>
<section class="relative bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 overflow-hidden">
  <%= render 'shared/marketing_nav' %>
  
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24">
    <div class="text-center">
      <h1 class="text-5xl md:text-6xl font-bold text-gray-900 mb-6">
        Your data is
        <span class="bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
          secure with us
        </span>
      </h1>
      <p class="text-xl md:text-2xl text-gray-600 max-w-4xl mx-auto leading-relaxed mb-10">
        Enterprise-grade security measures, comprehensive compliance certifications, and transparent practices to keep your business data safe and secure.
      </p>
      
      <%# Security Badges %>
      <div class="flex flex-wrap justify-center gap-6 mb-12">
        <div class="bg-white/80 backdrop-blur-sm rounded-xl px-6 py-3 shadow-sm flex items-center space-x-3">
          <%= svg_icon('shield-check', class: 'w-6 h-6 text-green-600') %>
          <span class="font-semibold text-gray-900">SOC 2 Certified</span>
        </div>
        <div class="bg-white/80 backdrop-blur-sm rounded-xl px-6 py-3 shadow-sm flex items-center space-x-3">
          <%= svg_icon('globe', class: 'w-6 h-6 text-blue-600') %>
          <span class="font-semibold text-gray-900">GDPR Compliant</span>
        </div>
        <div class="bg-white/80 backdrop-blur-sm rounded-xl px-6 py-3 shadow-sm flex items-center space-x-3">
          <%= svg_icon('users', class: 'w-6 h-6 text-purple-600') %>
          <span class="font-semibold text-gray-900">CCPA Compliant</span>
        </div>
      </div>

      <div class="flex flex-col sm:flex-row justify-center gap-4">
        <%= link_to new_user_registration_path, 
            class: "bg-blue-600 hover:bg-blue-700 text-white px-8 py-4 rounded-xl font-semibold text-lg transition-all duration-200 transform hover:scale-105 shadow-lg" do %>
          Start Secure Trial
        <% end %>
        
        <%= link_to contact_path,
            class: "bg-white hover:bg-gray-50 text-gray-900 px-8 py-4 rounded-xl font-semibold text-lg border border-gray-200 transition-all duration-200 shadow-sm hover:shadow-md" do %>
          Security Questionnaire
        <% end %>
      </div>
    </div>
  </div>
</section>

<%# Security Features %>
<section class="py-24 bg-white">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="text-center mb-16">
      <h2 class="text-4xl font-bold text-gray-900 mb-6">Comprehensive Security Framework</h2>
      <p class="text-xl text-gray-600 max-w-3xl mx-auto">
        Multi-layered security approach designed to protect your data at every level
      </p>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-2 gap-12">
      <% @security_features.each do |feature| %>
        <div class="bg-gray-50 p-8 rounded-2xl hover:shadow-lg transition-shadow duration-200">
          <div class="w-16 h-16 bg-blue-100 rounded-xl flex items-center justify-center mb-6">
            <%= svg_icon(feature[:icon], class: 'w-8 h-8 text-blue-600') %>
          </div>
          
          <h3 class="text-2xl font-semibold text-gray-900 mb-4"><%= feature[:title] %></h3>
          <p class="text-gray-600 mb-6 leading-relaxed"><%= feature[:description] %></p>
          
          <ul class="space-y-3">
            <% feature[:details].each do |detail| %>
              <li class="flex items-center text-gray-700">
                <%= svg_icon('check-circle', class: 'w-5 h-5 text-green-600 mr-3') %>
                <%= detail %>
              </li>
            <% end %>
          </ul>
        </div>
      <% end %>
    </div>
  </div>
</section>

<%# Compliance Certifications %>
<section class="py-24 bg-gray-50">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="text-center mb-16">
      <h2 class="text-4xl font-bold text-gray-900 mb-6">Compliance & Certifications</h2>
      <p class="text-xl text-gray-600 max-w-3xl mx-auto">
        We maintain the highest standards of compliance to meet your regulatory requirements
      </p>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
      <% @compliance_certifications.each do |cert| %>
        <div class="bg-white p-8 rounded-2xl shadow-sm text-center hover:shadow-md transition-shadow duration-200">
          <div class="w-16 h-16 <%= cert[:status] == 'Certified' || cert[:status] == 'Compliant' ? 'bg-green-100' : 'bg-yellow-100' %> rounded-xl flex items-center justify-center mx-auto mb-6">
            <%= svg_icon(cert[:icon], class: "w-8 h-8 #{cert[:status] == 'Certified' || cert[:status] == 'Compliant' ? 'text-green-600' : 'text-yellow-600'}") %>
          </div>
          
          <h3 class="text-lg font-semibold text-gray-900 mb-2"><%= cert[:name] %></h3>
          <p class="text-sm text-gray-600 mb-4"><%= cert[:description] %></p>
          
          <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium <%= cert[:status] == 'Certified' || cert[:status] == 'Compliant' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800' %>">
            <%= cert[:status] %>
          </span>
        </div>
      <% end %>
    </div>
  </div>
</section>

<%# Security Practices %>
<section class="py-24 bg-white">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="text-center mb-16">
      <h2 class="text-4xl font-bold text-gray-900 mb-6">Security Best Practices</h2>
      <p class="text-xl text-gray-600 max-w-3xl mx-auto">
        Our comprehensive approach to security covers every aspect of our operations
      </p>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
      <% @security_practices.each do |practice| %>
        <div class="bg-gradient-to-br from-blue-50 to-indigo-50 p-8 rounded-2xl border border-blue-100">
          <h3 class="text-xl font-semibold text-gray-900 mb-6"><%= practice[:category] %></h3>
          
          <ul class="space-y-4">
            <% practice[:practices].each do |item| %>
              <li class="flex items-start space-x-3">
                <%= svg_icon('check-circle', class: 'w-5 h-5 text-blue-600 mt-0.5') %>
                <span class="text-gray-700 text-sm leading-relaxed"><%= item %></span>
              </li>
            <% end %>
          </ul>
        </div>
      <% end %>
    </div>
  </div>
</section>

<%# Security Resources %>
<section class="py-24 bg-gray-50">
  <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
    <h2 class="text-4xl font-bold text-gray-900 mb-6">Security Resources</h2>
    <p class="text-xl text-gray-600 mb-12">
      Access our security documentation and reports
    </p>
    
    <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
      <div class="bg-white p-8 rounded-2xl shadow-sm">
        <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-4">
          <%= svg_icon('shield-check', class: 'w-6 h-6 text-blue-600') %>
        </div>
        <h3 class="font-semibold text-gray-900 mb-2">SOC 2 Report</h3>
        <p class="text-sm text-gray-600 mb-4">Download our latest SOC 2 Type II audit report</p>
        <button class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors">
          Request Report
        </button>
      </div>

      <div class="bg-white p-8 rounded-2xl shadow-sm">
        <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-4">
          <%= svg_icon('globe', class: 'w-6 h-6 text-green-600') %>
        </div>
        <h3 class="font-semibold text-gray-900 mb-2">Security Whitepaper</h3>
        <p class="text-sm text-gray-600 mb-4">Detailed overview of our security architecture</p>
        <button class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors">
          Download PDF
        </button>
      </div>

      <div class="bg-white p-8 rounded-2xl shadow-sm">
        <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-4">
          <%= svg_icon('users', class: 'w-6 h-6 text-purple-600') %>
        </div>
        <h3 class="font-semibold text-gray-900 mb-2">Security Questionnaire</h3>
        <p class="text-sm text-gray-600 mb-4">Complete security assessment for enterprise customers</p>
        <%= link_to contact_path, 
            class: "bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors inline-block" do %>
          Get Questionnaire
        <% end %>
      </div>
    </div>
  </div>
</section>

<%# Security Contact %>
<section class="py-24 bg-blue-600">
  <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
    <h2 class="text-4xl md:text-5xl font-bold text-white mb-6">
      Questions about security?
    </h2>
    <p class="text-xl text-blue-100 mb-10">
      Our security team is here to help with any questions or concerns about data protection.
    </p>
    
    <div class="flex flex-col sm:flex-row justify-center gap-4">
      <%= link_to contact_path, 
          class: "bg-white hover:bg-gray-50 text-blue-600 px-8 py-4 rounded-xl font-semibold text-lg transition-all duration-200 transform hover:scale-105 shadow-lg" do %>
        Contact Security Team
      <% end %>
      
      <a href="mailto:<EMAIL>" class="bg-blue-500 hover:bg-blue-400 text-white px-8 py-4 rounded-xl font-semibold text-lg transition-colors border border-blue-400">
        <EMAIL>
      </a>
    </div>
  </div>
</section>

<%# Footer %>
<%= render 'shared/marketing_footer' %>

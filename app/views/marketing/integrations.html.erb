<%# SEO Meta Tags %>
<% content_for :title, "DataReflow Integrations - 200+ Business App Connections" %>
<% content_for :description, "Connect DataReflow with 200+ popular business applications. Pre-built integrations for Shopify, Salesforce, QuickBooks, HubSpot, and more. Setup in minutes." %>
<% content_for :keywords, "business integrations, API connections, Shopify integration, Salesforce connector, QuickBooks sync, data integration" %>

<%# Hero Section %>
<section class="relative bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 overflow-hidden">
  <%= render 'shared/marketing_nav' %>
  
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24">
    <div class="text-center">
      <h1 class="text-5xl md:text-6xl font-bold text-gray-900 mb-6">
        <span class="bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
          <%= @integration_stats[:total_integrations] %>
        </span>
        Integrations
      </h1>
      <p class="text-xl md:text-2xl text-gray-600 max-w-4xl mx-auto leading-relaxed mb-10">
        Connect DataReflow with all your favorite business applications. Pre-built connectors for the tools you already use, with new integrations added every month.
      </p>
      
      <%# Integration Stats %>
      <div class="grid grid-cols-2 md:grid-cols-4 gap-6 max-w-4xl mx-auto mb-12">
        <div class="bg-white/80 backdrop-blur-sm rounded-xl p-4 shadow-sm">
          <div class="text-2xl font-bold text-blue-600"><%= @integration_stats[:total_integrations] %></div>
          <div class="text-sm text-gray-600">Total Integrations</div>
        </div>
        <div class="bg-white/80 backdrop-blur-sm rounded-xl p-4 shadow-sm">
          <div class="text-2xl font-bold text-indigo-600"><%= @integration_stats[:categories] %></div>
          <div class="text-sm text-gray-600">Categories</div>
        </div>
        <div class="bg-white/80 backdrop-blur-sm rounded-xl p-4 shadow-sm">
          <div class="text-2xl font-bold text-purple-600"><%= @integration_stats[:setup_time] %></div>
          <div class="text-sm text-gray-600">Avg Setup Time</div>
        </div>
        <div class="bg-white/80 backdrop-blur-sm rounded-xl p-4 shadow-sm">
          <div class="text-2xl font-bold text-green-600"><%= @integration_stats[:success_rate] %></div>
          <div class="text-sm text-gray-600">Success Rate</div>
        </div>
      </div>

      <%# Search Bar %>
      <div class="max-w-2xl mx-auto">
        <div class="relative">
          <input type="text" 
                 placeholder="Search for integrations (e.g., Shopify, Salesforce, QuickBooks)..." 
                 class="w-full px-6 py-4 pl-12 text-lg border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 shadow-lg">
          <%= svg_icon('search', class: 'absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400') %>
        </div>
      </div>
    </div>
  </div>
</section>

<%# Featured Integrations %>
<section class="py-16 bg-white">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="text-center mb-12">
      <h2 class="text-3xl font-bold text-gray-900 mb-4">Featured Integration Workflows</h2>
      <p class="text-lg text-gray-600">Popular pre-built workflows that connect your most important business tools</p>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
      <% @featured_integrations.each do |integration| %>
        <div class="bg-gradient-to-br from-blue-50 to-indigo-50 p-8 rounded-2xl border border-blue-100 hover:shadow-lg transition-shadow duration-200">
          <div class="flex items-center justify-between mb-4">
            <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
              <%= integration[:popularity] %>
            </span>
            <span class="text-sm text-gray-600"><%= integration[:setup_time] %> setup</span>
          </div>
          
          <h3 class="text-xl font-semibold text-gray-900 mb-3"><%= integration[:name] %></h3>
          <p class="text-gray-600 mb-6"><%= integration[:description] %></p>
          
          <div class="mb-6">
            <h4 class="text-sm font-medium text-gray-900 mb-2">Common Use Cases:</h4>
            <ul class="space-y-1">
              <% integration[:use_cases].each do |use_case| %>
                <li class="flex items-center text-sm text-gray-600">
                  <%= svg_icon('check-circle', class: 'w-4 h-4 text-green-500 mr-2') %>
                  <%= use_case %>
                </li>
              <% end %>
            </ul>
          </div>
          
          <button class="w-full bg-blue-600 hover:bg-blue-700 text-white py-3 px-4 rounded-lg font-semibold transition-colors">
            Set Up Integration
          </button>
        </div>
      <% end %>
    </div>
  </div>
</section>

<%# Integration Categories %>
<section class="py-16 bg-gray-50">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="text-center mb-12">
      <h2 class="text-3xl font-bold text-gray-900 mb-4">Browse by Category</h2>
      <p class="text-lg text-gray-600">Find the perfect integration for your business needs</p>
    </div>

    <div class="space-y-12">
      <% @integration_categories.each do |category_name, integrations| %>
        <div class="bg-white rounded-2xl shadow-sm overflow-hidden">
          <div class="bg-gradient-to-r from-blue-50 to-indigo-50 px-6 py-4 border-b border-blue-100">
            <h3 class="text-xl font-semibold text-gray-900"><%= category_name %></h3>
          </div>
          
          <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <% integrations.each do |integration| %>
                <div class="group relative bg-gray-50 hover:bg-gray-100 p-6 rounded-xl transition-colors duration-200 cursor-pointer">
                  <div class="flex items-center space-x-4">
                    <div class="w-12 h-12 bg-white rounded-lg shadow-sm flex items-center justify-center group-hover:shadow-md transition-shadow">
                      <%= svg_icon('globe', class: 'w-6 h-6 text-gray-600') %>
                    </div>
                    <div class="flex-1 min-w-0">
                      <div class="flex items-center space-x-2">
                        <h4 class="font-semibold text-gray-900 truncate"><%= integration[:name] %></h4>
                        <% if integration[:popular] %>
                          <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800">
                            Popular
                          </span>
                        <% end %>
                      </div>
                      <p class="text-sm text-gray-600 mt-1"><%= integration[:description] %></p>
                    </div>
                  </div>
                  
                  <div class="mt-4 flex items-center justify-between">
                    <span class="text-xs text-gray-500">Ready to use</span>
                    <%= svg_icon('arrow-right', class: 'w-4 h-4 text-gray-400 group-hover:text-blue-600 group-hover:translate-x-1 transition-all') %>
                  </div>
                </div>
              <% end %>
            </div>
          </div>
        </div>
      <% end %>
    </div>
  </div>
</section>

<%# Upcoming Integrations %>
<section class="py-16 bg-white">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="text-center mb-12">
      <h2 class="text-3xl font-bold text-gray-900 mb-4">Coming Soon</h2>
      <p class="text-lg text-gray-600">New integrations we're working on based on customer requests</p>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      <% @upcoming_integrations.each do |integration| %>
        <div class="bg-gradient-to-br from-purple-50 to-pink-50 p-6 rounded-xl border border-purple-100">
          <div class="flex items-center justify-between mb-4">
            <div class="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
              <%= svg_icon('clock', class: 'w-5 h-5 text-purple-600') %>
            </div>
            <span class="text-xs font-medium text-purple-600 bg-purple-100 px-2 py-1 rounded-full">
              <%= integration[:eta] %>
            </span>
          </div>
          
          <h3 class="font-semibold text-gray-900 mb-1"><%= integration[:name] %></h3>
          <p class="text-sm text-gray-600"><%= integration[:category] %></p>
          
          <button class="mt-4 w-full bg-purple-100 hover:bg-purple-200 text-purple-700 py-2 px-4 rounded-lg text-sm font-medium transition-colors">
            Request Early Access
          </button>
        </div>
      <% end %>
    </div>

    <div class="text-center mt-12">
      <p class="text-gray-600 mb-6">Don't see the integration you need?</p>
      <%= link_to contact_path, 
          class: "inline-flex items-center space-x-2 bg-blue-600 hover:bg-blue-700 text-white px-8 py-3 rounded-lg font-semibold transition-colors" do %>
        <span>Request Custom Integration</span>
        <%= svg_icon('arrow-right', class: 'w-4 h-4') %>
      <% end %>
    </div>
  </div>
</section>

<%# Integration Benefits %>
<section class="py-16 bg-blue-600">
  <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
    <div class="bg-white rounded-2xl p-8 md:p-12">
      <h2 class="text-3xl font-bold text-gray-900 mb-6">Why Choose DataReflow Integrations?</h2>
      
      <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
        <div class="text-center">
          <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-4">
            <%= svg_icon('lightning', class: 'w-6 h-6 text-blue-600') %>
          </div>
          <h3 class="font-semibold text-gray-900 mb-2">Quick Setup</h3>
          <p class="text-sm text-gray-600">Most integrations take less than 10 minutes to configure</p>
        </div>

        <div class="text-center">
          <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-4">
            <%= svg_icon('shield-check', class: 'w-6 h-6 text-green-600') %>
          </div>
          <h3 class="font-semibold text-gray-900 mb-2">Secure & Reliable</h3>
          <p class="text-sm text-gray-600">Enterprise-grade security with 99.9% uptime guarantee</p>
        </div>

        <div class="text-center">
          <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-4">
            <%= svg_icon('users', class: 'w-6 h-6 text-purple-600') %>
          </div>
          <h3 class="font-semibold text-gray-900 mb-2">Expert Support</h3>
          <p class="text-sm text-gray-600">Dedicated support team to help with setup and troubleshooting</p>
        </div>
      </div>

      <div class="mt-10">
        <%= link_to new_user_registration_path, 
            class: "bg-blue-600 hover:bg-blue-700 text-white px-8 py-4 rounded-xl font-semibold text-lg transition-all duration-200 transform hover:scale-105 shadow-lg" do %>
          Start Connecting Your Apps
        <% end %>
      </div>
    </div>
  </div>
</section>

<%# Footer %>
<%= render 'shared/marketing_footer' %>

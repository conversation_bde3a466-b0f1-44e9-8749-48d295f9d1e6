<%# SEO Meta Tags %>
<% content_for :title, "DataReflow Privacy Policy - How We Protect Your Data" %>
<% content_for :description, "Read DataReflow's comprehensive privacy policy. Learn how we collect, use, and protect your personal information in compliance with GDPR and CCPA." %>
<% content_for :keywords, "privacy policy, data protection, GDPR compliance, CCPA compliance, personal information, data privacy" %>

<%# Hero Section %>
<section class="relative bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 overflow-hidden">
  <%= render 'shared/marketing_nav' %>
  
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24">
    <div class="text-center">
      <h1 class="text-5xl md:text-6xl font-bold text-gray-900 mb-6">
        Privacy
        <span class="bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
          Policy
        </span>
      </h1>
      <p class="text-xl md:text-2xl text-gray-600 max-w-4xl mx-auto leading-relaxed mb-8">
        We're committed to protecting your privacy and being transparent about how we handle your data.
      </p>
      
      <div class="bg-white/80 backdrop-blur-sm rounded-xl px-6 py-4 shadow-sm inline-flex items-center space-x-3">
        <%= svg_icon('clock', class: 'w-5 h-5 text-blue-600') %>
        <span class="text-gray-900">Last updated: <%= @last_updated.strftime("%B %d, %Y") %></span>
      </div>
    </div>
  </div>
</section>

<%# Privacy Overview %>
<section class="py-16 bg-white">
  <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="bg-blue-50 rounded-2xl p-8 mb-12">
      <h2 class="text-2xl font-bold text-gray-900 mb-4">Privacy at a Glance</h2>
      <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div class="text-center">
          <div class="w-12 h-12 bg-blue-600 rounded-lg flex items-center justify-center mx-auto mb-3">
            <%= svg_icon('shield-check', class: 'w-6 h-6 text-white') %>
          </div>
          <h3 class="font-semibold text-gray-900 mb-2">Data Minimization</h3>
          <p class="text-sm text-gray-600">We only collect data necessary to provide our services</p>
        </div>
        
        <div class="text-center">
          <div class="w-12 h-12 bg-green-600 rounded-lg flex items-center justify-center mx-auto mb-3">
            <%= svg_icon('users', class: 'w-6 h-6 text-white') %>
          </div>
          <h3 class="font-semibold text-gray-900 mb-2">Your Rights</h3>
          <p class="text-sm text-gray-600">Access, update, or delete your data at any time</p>
        </div>
        
        <div class="text-center">
          <div class="w-12 h-12 bg-purple-600 rounded-lg flex items-center justify-center mx-auto mb-3">
            <%= svg_icon('globe', class: 'w-6 h-6 text-white') %>
          </div>
          <h3 class="font-semibold text-gray-900 mb-2">Global Compliance</h3>
          <p class="text-sm text-gray-600">GDPR, CCPA, and other privacy law compliant</p>
        </div>
      </div>
    </div>

    <div class="prose prose-lg max-w-none">
      <p class="text-lg text-gray-600 leading-relaxed mb-8">
        At DataReflow, we believe privacy is a fundamental right. This Privacy Policy explains how we collect, 
        use, disclose, and safeguard your information when you use our data integration platform and services.
      </p>
      
      <p class="text-gray-600 leading-relaxed mb-12">
        By using our services, you agree to the collection and use of information in accordance with this policy. 
        We will not use or share your information with anyone except as described in this Privacy Policy.
      </p>
    </div>
  </div>
</section>

<%# Privacy Sections %>
<section class="py-16 bg-gray-50">
  <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="space-y-8">
      <% @privacy_sections.each_with_index do |section, index| %>
        <div class="bg-white rounded-2xl shadow-sm overflow-hidden">
          <div class="bg-gradient-to-r from-blue-50 to-indigo-50 px-8 py-6 border-b border-blue-100">
            <h2 class="text-xl font-semibold text-gray-900 flex items-center">
              <span class="w-8 h-8 bg-blue-600 text-white rounded-lg flex items-center justify-center text-sm font-bold mr-4">
                <%= index + 1 %>
              </span>
              <%= section[:title] %>
            </h2>
          </div>
          
          <div class="px-8 py-6">
            <p class="text-gray-700 leading-relaxed"><%= section[:content] %></p>
          </div>
        </div>
      <% end %>
    </div>
  </div>
</section>

<%# Data Subject Rights %>
<section class="py-16 bg-white">
  <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="text-center mb-12">
      <h2 class="text-3xl font-bold text-gray-900 mb-4">Your Data Rights</h2>
      <p class="text-lg text-gray-600">
        You have control over your personal information. Here's how you can exercise your rights:
      </p>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
      <div class="bg-gradient-to-br from-blue-50 to-indigo-50 p-6 rounded-xl border border-blue-100">
        <div class="flex items-center mb-4">
          <%= svg_icon('users', class: 'w-6 h-6 text-blue-600 mr-3') %>
          <h3 class="font-semibold text-gray-900">Access Your Data</h3>
        </div>
        <p class="text-gray-600 text-sm mb-4">Request a copy of all personal information we have about you.</p>
        <button class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors">
          Request Data Export
        </button>
      </div>

      <div class="bg-gradient-to-br from-green-50 to-emerald-50 p-6 rounded-xl border border-green-100">
        <div class="flex items-center mb-4">
          <%= svg_icon('check-circle', class: 'w-6 h-6 text-green-600 mr-3') %>
          <h3 class="font-semibold text-gray-900">Update Information</h3>
        </div>
        <p class="text-gray-600 text-sm mb-4">Correct or update any inaccurate personal information.</p>
        <button class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors">
          Update Profile
        </button>
      </div>

      <div class="bg-gradient-to-br from-purple-50 to-violet-50 p-6 rounded-xl border border-purple-100">
        <div class="flex items-center mb-4">
          <%= svg_icon('globe', class: 'w-6 h-6 text-purple-600 mr-3') %>
          <h3 class="font-semibold text-gray-900">Data Portability</h3>
        </div>
        <p class="text-gray-600 text-sm mb-4">Export your data in a machine-readable format.</p>
        <button class="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors">
          Export Data
        </button>
      </div>

      <div class="bg-gradient-to-br from-red-50 to-rose-50 p-6 rounded-xl border border-red-100">
        <div class="flex items-center mb-4">
          <%= svg_icon('x-circle', class: 'w-6 h-6 text-red-600 mr-3') %>
          <h3 class="font-semibold text-gray-900">Delete Account</h3>
        </div>
        <p class="text-gray-600 text-sm mb-4">Request deletion of your account and all associated data.</p>
        <button class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors">
          Delete Account
        </button>
      </div>
    </div>

    <div class="mt-12 bg-yellow-50 border border-yellow-200 rounded-xl p-6">
      <div class="flex items-start">
        <%= svg_icon('clock', class: 'w-6 h-6 text-yellow-600 mr-3 mt-1') %>
        <div>
          <h3 class="font-semibold text-gray-900 mb-2">Response Time</h3>
          <p class="text-gray-700 text-sm">
            We will respond to your data rights requests within 30 days. For complex requests, 
            we may extend this period by an additional 60 days and will notify you of any delay.
          </p>
        </div>
      </div>
    </div>
  </div>
</section>

<%# Contact Information %>
<section class="py-16 bg-gray-50">
  <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="bg-white rounded-2xl p-8 shadow-sm">
      <div class="text-center mb-8">
        <h2 class="text-3xl font-bold text-gray-900 mb-4">Questions About Privacy?</h2>
        <p class="text-lg text-gray-600">
          Our privacy team is here to help with any questions or concerns.
        </p>
      </div>

      <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
        <div class="text-center">
          <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-4">
            <%= svg_icon('globe', class: 'w-6 h-6 text-blue-600') %>
          </div>
          <h3 class="font-semibold text-gray-900 mb-2">Privacy Officer</h3>
          <p class="text-gray-600 mb-4">Contact our Data Protection Officer directly</p>
          <a href="mailto:<EMAIL>" class="text-blue-600 hover:text-blue-700 font-medium">
            <EMAIL>
          </a>
        </div>

        <div class="text-center">
          <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-4">
            <%= svg_icon('users', class: 'w-6 h-6 text-green-600') %>
          </div>
          <h3 class="font-semibold text-gray-900 mb-2">General Support</h3>
          <p class="text-gray-600 mb-4">Get help with privacy settings and controls</p>
          <%= link_to contact_path, class: "text-green-600 hover:text-green-700 font-medium" do %>
            Contact Support
          <% end %>
        </div>
      </div>

      <div class="mt-8 pt-8 border-t border-gray-200 text-center">
        <p class="text-sm text-gray-500">
          This privacy policy is effective as of <%= @last_updated.strftime("%B %d, %Y") %> and will remain in effect 
          except with respect to any changes in its provisions in the future, which will be in effect immediately 
          after being posted on this page.
        </p>
      </div>
    </div>
  </div>
</section>

<%# Footer %>
<%= render 'shared/marketing_footer' %>

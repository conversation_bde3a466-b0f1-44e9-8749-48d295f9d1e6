<% content_for :title, "API Overview" %>

<div class="px-4 py-8">
  <!-- Hero Section -->
  <div class="text-center max-w-3xl mx-auto mb-12">
    <h1 class="text-4xl font-bold text-gray-900 sm:text-5xl">
      DataReflow API
    </h1>
    <p class="mt-4 text-xl text-gray-600">
      Build powerful data pipelines with our comprehensive REST API. 
      Automate data workflows, manage connectors, and monitor executions programmatically.
    </p>
    
    <div class="mt-8 flex flex-col sm:flex-row gap-4 justify-center">
      <%= link_to api_quickstart_path, class: "inline-flex items-center justify-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500" do %>
        Get Started
        <svg class="ml-2 -mr-1 w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
          <path fill-rule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clip-rule="evenodd"></path>
        </svg>
      <% end %>
      
      <%= link_to "/api-docs", target: "_blank", class: "inline-flex items-center justify-center px-6 py-3 border border-gray-300 text-base font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500" do %>
        <svg class="mr-2 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"></path>
        </svg>
        Interactive Docs
      <% end %>
    </div>
  </div>

  <!-- Stats Section -->
  <div class="grid grid-cols-1 md:grid-cols-4 gap-8 mb-12">
    <div class="bg-white rounded-lg p-6 shadow-sm border border-gray-200 text-center">
      <div class="text-2xl font-bold text-blue-600"><%= @total_endpoints %></div>
      <div class="text-sm font-medium text-gray-900">API Endpoints</div>
    </div>
    <div class="bg-white rounded-lg p-6 shadow-sm border border-gray-200 text-center">
      <div class="text-2xl font-bold text-green-600">99.9%</div>
      <div class="text-sm font-medium text-gray-900">Uptime</div>
    </div>
    <div class="bg-white rounded-lg p-6 shadow-sm border border-gray-200 text-center">
      <div class="text-2xl font-bold text-purple-600">&lt;200ms</div>
      <div class="text-sm font-medium text-gray-900">Response Time</div>
    </div>
    <div class="bg-white rounded-lg p-6 shadow-sm border border-gray-200 text-center">
      <div class="text-2xl font-bold text-indigo-600">v<%= @api_version %></div>
      <div class="text-sm font-medium text-gray-900">Current Version</div>
    </div>
  </div>

  <!-- Features Grid -->
  <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
    <div class="bg-white rounded-lg p-6 shadow-sm border border-gray-200">
      <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center mb-4">
        <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
        </svg>
      </div>
      <h3 class="text-lg font-semibold text-gray-900 mb-2">Pipeline Management</h3>
      <p class="text-gray-600">Create, update, and execute data pipelines programmatically with full CRUD operations.</p>
    </div>

    <div class="bg-white rounded-lg p-6 shadow-sm border border-gray-200">
      <div class="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center mb-4">
        <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
        </svg>
      </div>
      <h3 class="text-lg font-semibold text-gray-900 mb-2">Data Connectors</h3>
      <p class="text-gray-600">Connect to databases, APIs, files, and cloud services with our flexible connector system.</p>
    </div>

    <div class="bg-white rounded-lg p-6 shadow-sm border border-gray-200">
      <div class="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center mb-4">
        <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
        </svg>
      </div>
      <h3 class="text-lg font-semibold text-gray-900 mb-2">Execution Monitoring</h3>
      <p class="text-gray-600">Monitor pipeline executions in real-time with detailed logs and performance metrics.</p>
    </div>

    <div class="bg-white rounded-lg p-6 shadow-sm border border-gray-200">
      <div class="w-10 h-10 bg-indigo-100 rounded-lg flex items-center justify-center mb-4">
        <svg class="w-6 h-6 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
        </svg>
      </div>
      <h3 class="text-lg font-semibold text-gray-900 mb-2">Secure Authentication</h3>
      <p class="text-gray-600">JWT-based authentication with role-based access control and API key support.</p>
    </div>

    <div class="bg-white rounded-lg p-6 shadow-sm border border-gray-200">
      <div class="w-10 h-10 bg-red-100 rounded-lg flex items-center justify-center mb-4">
        <svg class="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
        </svg>
      </div>
      <h3 class="text-lg font-semibold text-gray-900 mb-2">Error Handling</h3>
      <p class="text-gray-600">Comprehensive error responses with detailed messages and recovery suggestions.</p>
    </div>

    <div class="bg-white rounded-lg p-6 shadow-sm border border-gray-200">
      <div class="w-10 h-10 bg-yellow-100 rounded-lg flex items-center justify-center mb-4">
        <svg class="w-6 h-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
        </svg>
      </div>
      <h3 class="text-lg font-semibold text-gray-900 mb-2">Rate Limiting</h3>
      <p class="text-gray-600">Built-in rate limiting with clear headers and upgrade paths for higher limits.</p>
    </div>
  </div>

  <!-- Quick Start Section -->
  <div class="bg-gray-900 rounded-lg p-8 mb-12">
    <div class="max-w-3xl">
      <h2 class="text-2xl font-bold text-white mb-4">Quick Example</h2>
      <p class="text-gray-300 mb-6">Get started with the DataReflow API in seconds. Here's how to authenticate and list your pipelines:</p>
      
      <div class="bg-gray-800 rounded-lg p-4 overflow-x-auto">
        <pre class="text-sm"><code class="language-bash"># 1. Authenticate and get a token
curl -X POST "https://api.datareflow.io/api/login" \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>", "password": "your-password"}'

# 2. Use the token to list your pipelines
curl -X GET "https://api.datareflow.io/api/v1/pipelines" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"

# 3. Execute a pipeline
curl -X POST "https://api.datareflow.io/api/v1/pipelines/123/execute" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"async": true}'</code></pre>
      </div>
    </div>
  </div>

  <!-- Base URLs Section -->
  <div class="bg-white rounded-lg p-8 shadow-sm border border-gray-200 mb-12">
    <h2 class="text-2xl font-bold text-gray-900 mb-4">Base URLs</h2>
    <div class="space-y-4">
      <div>
        <div class="font-medium text-gray-900">Production</div>
        <code class="text-sm text-blue-600 bg-blue-50 px-2 py-1 rounded">https://{your-subdomain}.datareflow.io/api/v1</code>
      </div>
      <div>
        <div class="font-medium text-gray-900">Development</div>
        <code class="text-sm text-green-600 bg-green-50 px-2 py-1 rounded">http://localhost:3000/api/v1</code>
      </div>
    </div>
    
    <div class="mt-6 p-4 bg-amber-50 border border-amber-200 rounded-lg">
      <div class="flex">
        <div class="flex-shrink-0">
          <svg class="h-5 w-5 text-amber-400" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
          </svg>
        </div>
        <div class="ml-3">
          <h3 class="text-sm font-medium text-amber-800">
            Subdomain Required
          </h3>
          <div class="mt-2 text-sm text-amber-700">
            <p>All API requests must be made to your account-specific subdomain. Replace <code>{your-subdomain}</code> with your actual subdomain.</p>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Next Steps -->
  <div class="text-center">
    <h2 class="text-2xl font-bold text-gray-900 mb-4">Ready to get started?</h2>
    <p class="text-lg text-gray-600 mb-8">Choose your path to building with DataReflow API</p>
    
    <div class="grid sm:grid-cols-3 gap-4 max-w-2xl mx-auto">
      <%= link_to api_authentication_docs_path, class: "p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors" do %>
        <div class="font-medium text-gray-900 mb-1">Authentication Guide</div>
        <div class="text-sm text-gray-600">Learn how to authenticate with our API</div>
      <% end %>
      
      <%= link_to api_quickstart_path, class: "p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors" do %>
        <div class="font-medium text-gray-900 mb-1">Quick Start</div>
        <div class="text-sm text-gray-600">Build your first integration in 5 minutes</div>
      <% end %>
      
      <%= link_to api_examples_path, class: "p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors" do %>
        <div class="font-medium text-gray-900 mb-1">Code Examples</div>
        <div class="text-sm text-gray-600">Copy-paste examples in multiple languages</div>
      <% end %>
    </div>
  </div>
</div>
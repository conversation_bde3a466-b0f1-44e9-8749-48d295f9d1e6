<% content_for :title, "Quick Start Guide" %>

<div class="px-4 py-8">
  <!-- Header -->
  <div class="max-w-3xl mx-auto text-center mb-12">
    <h1 class="text-4xl font-bold text-gray-900 mb-4">Quick Start Guide</h1>
    <p class="text-xl text-gray-600">Get up and running with the DataReflow API in 5 minutes. Build your first data pipeline integration.</p>
  </div>

  <!-- Prerequisites -->
  <div class="max-w-4xl mx-auto mb-12">
    <div class="bg-amber-50 border border-amber-200 rounded-lg p-6 mb-8">
      <div class="flex">
        <div class="flex-shrink-0">
          <svg class="h-5 w-5 text-amber-400" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"/>
          </svg>
        </div>
        <div class="ml-3">
          <h3 class="text-sm font-medium text-amber-800">
            Prerequisites
          </h3>
          <div class="mt-2 text-sm text-amber-700">
            <p>Before you begin, make sure you have:</p>
            <ul class="mt-2 space-y-1">
              <li>• A DataReflow account with your unique subdomain</li>
              <li>• Your login credentials (email and password)</li>
              <li>• A tool for making HTTP requests (curl, Postman, or your preferred HTTP client)</li>
            </ul>
          </div>
        </div>
      </div>
    </div>

    <!-- Step-by-step guide -->
    <div class="space-y-8">
      <!-- Step 1: Authentication -->
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center mb-4">
          <div class="flex items-center justify-center w-8 h-8 bg-blue-600 text-white rounded-full text-sm font-bold mr-4">1</div>
          <h2 class="text-2xl font-bold text-gray-900">Get Your API Token</h2>
        </div>
        
        <p class="text-gray-600 mb-4">First, authenticate with your DataReflow account to get a JWT token:</p>
        
        <div class="space-y-4">
          <div class="bg-gray-900 rounded-lg p-4 overflow-x-auto">
            <pre><code class="language-bash"># Replace 'your-subdomain' with your actual subdomain
curl -X POST "https://your-subdomain.datareflow.io/api/login" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "your-password"
  }'</code></pre>
          </div>
          
          <div class="bg-green-50 border border-green-200 rounded-lg p-4">
            <p class="text-sm font-medium text-green-800 mb-2">✅ Success Response:</p>
            <pre><code class="language-json text-sm">{
  "status": "success",
  "data": {
    "user": {
      "id": 123,
      "email": "<EMAIL>",
      "first_name": "John",
      "last_name": "Doe"
    },
    "token": "eyJhbGciOiJIUzI1NiJ9.*******************************************...",
    "expires_at": "2024-02-15T10:30:00Z"
  }
}</code></pre>
          </div>
          
          <div class="bg-blue-50 border border-blue-200 rounded-lg p-3">
            <p class="text-sm text-blue-800">💡 <strong>Pro Tip:</strong> Save the token value - you'll need it for all subsequent API calls. Tokens expire after 24 hours.</p>
          </div>
        </div>
      </div>

      <!-- Step 2: List Pipelines -->
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center mb-4">
          <div class="flex items-center justify-center w-8 h-8 bg-green-600 text-white rounded-full text-sm font-bold mr-4">2</div>
          <h2 class="text-2xl font-bold text-gray-900">List Your Pipelines</h2>
        </div>
        
        <p class="text-gray-600 mb-4">Now use your token to fetch your existing data pipelines:</p>
        
        <div class="space-y-4">
          <div class="bg-gray-900 rounded-lg p-4 overflow-x-auto">
            <pre><code class="language-bash"># Replace YOUR_JWT_TOKEN with the token from step 1
curl -X GET "https://your-subdomain.datareflow.io/api/v1/pipelines" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json"</code></pre>
          </div>
          
          <div class="bg-green-50 border border-green-200 rounded-lg p-4">
            <p class="text-sm font-medium text-green-800 mb-2">✅ Success Response:</p>
            <pre><code class="language-json text-sm">{
  "data": [
    {
      "id": 1,
      "name": "Customer Data ETL",
      "description": "Extract customer data from CRM and load to warehouse",
      "status": "active",
      "schedule": "0 2 * * *",
      "created_at": "2024-01-15T10:30:00Z",
      "updated_at": "2024-01-16T08:45:00Z",
      "source_connector": {
        "id": 10,
        "name": "Salesforce CRM",
        "type": "salesforce"
      },
      "destination_connector": {
        "id": 20,
        "name": "Data Warehouse",
        "type": "postgresql"
      }
    }
  ],
  "meta": {
    "total": 1,
    "page": 1,
    "per_page": 20
  }
}</code></pre>
          </div>
        </div>
      </div>

      <!-- Step 3: Create a Pipeline -->
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center mb-4">
          <div class="flex items-center justify-center w-8 h-8 bg-purple-600 text-white rounded-full text-sm font-bold mr-4">3</div>
          <h2 class="text-2xl font-bold text-gray-900">Create a New Pipeline</h2>
        </div>
        
        <p class="text-gray-600 mb-4">Create a simple data pipeline to move data between sources:</p>
        
        <div class="space-y-4">
          <div class="bg-gray-900 rounded-lg p-4 overflow-x-auto">
            <pre><code class="language-bash">curl -X POST "https://your-subdomain.datareflow.io/api/v1/pipelines" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "My First Pipeline",
    "description": "A simple data pipeline created via API",
    "source_connector_id": 10,
    "destination_connector_id": 20,
    "schedule": "0 * * * *",
    "config": {
      "batch_size": 1000,
      "retry_attempts": 3
    }
  }'</code></pre>
          </div>
          
          <div class="bg-green-50 border border-green-200 rounded-lg p-4">
            <p class="text-sm font-medium text-green-800 mb-2">✅ Success Response:</p>
            <pre><code class="language-json text-sm">{
  "data": {
    "id": 2,
    "name": "My First Pipeline",
    "description": "A simple data pipeline created via API",
    "status": "draft",
    "schedule": "0 * * * *",
    "config": {
      "batch_size": 1000,
      "retry_attempts": 3
    },
    "created_at": "2024-01-16T14:20:00Z",
    "source_connector": {
      "id": 10,
      "name": "Source Database",
      "type": "postgresql"
    },
    "destination_connector": {
      "id": 20,
      "name": "Data Warehouse",
      "type": "bigquery"
    }
  }
}</code></pre>
          </div>
        </div>
      </div>

      <!-- Step 4: Execute Pipeline -->
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center mb-4">
          <div class="flex items-center justify-center w-8 h-8 bg-orange-600 text-white rounded-full text-sm font-bold mr-4">4</div>
          <h2 class="text-2xl font-bold text-gray-900">Execute Your Pipeline</h2>
        </div>
        
        <p class="text-gray-600 mb-4">Trigger a pipeline execution and monitor its progress:</p>
        
        <div class="space-y-4">
          <div class="bg-gray-900 rounded-lg p-4 overflow-x-auto">
            <pre><code class="language-bash"># Execute the pipeline (use the ID from step 3)
curl -X POST "https://your-subdomain.datareflow.io/api/v1/pipelines/2/execute" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "async": true,
    "parameters": {
      "start_date": "2024-01-01",
      "end_date": "2024-01-16"
    }
  }'</code></pre>
          </div>
          
          <div class="bg-green-50 border border-green-200 rounded-lg p-4">
            <p class="text-sm font-medium text-green-800 mb-2">✅ Success Response:</p>
            <pre><code class="language-json text-sm">{
  "data": {
    "execution_id": "exec_abc123",
    "pipeline_id": 2,
    "status": "running",
    "started_at": "2024-01-16T14:25:00Z",
    "estimated_duration": 300,
    "progress": {
      "percentage": 0,
      "current_step": "initializing",
      "total_steps": 5
    }
  }
}</code></pre>
          </div>
          
          <div class="bg-gray-900 rounded-lg p-4 overflow-x-auto">
            <pre><code class="language-bash"># Monitor the execution progress
curl -X GET "https://your-subdomain.datareflow.io/api/v1/executions/exec_abc123" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"</code></pre>
          </div>
        </div>
      </div>

      <!-- Step 5: Handle Results -->
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center mb-4">
          <div class="flex items-center justify-center w-8 h-8 bg-indigo-600 text-white rounded-full text-sm font-bold mr-4">5</div>
          <h2 class="text-2xl font-bold text-gray-900">Handle Results & Errors</h2>
        </div>
        
        <p class="text-gray-600 mb-4">Check execution results and handle any errors:</p>
        
        <div class="space-y-4">
          <!-- Successful execution -->
          <div>
            <h3 class="font-semibold text-gray-900 mb-2">✅ Successful Execution:</h3>
            <div class="bg-green-50 border border-green-200 rounded-lg p-4">
              <pre><code class="language-json text-sm">{
  "data": {
    "execution_id": "exec_abc123",
    "pipeline_id": 2,
    "status": "completed",
    "started_at": "2024-01-16T14:25:00Z",
    "completed_at": "2024-01-16T14:28:30Z",
    "duration": 210,
    "statistics": {
      "records_processed": 15420,
      "records_success": 15420,
      "records_failed": 0,
      "data_transferred": "2.3 MB"
    }
  }
}</code></pre>
            </div>
          </div>
          
          <!-- Failed execution -->
          <div>
            <h3 class="font-semibold text-gray-900 mb-2">❌ Failed Execution:</h3>
            <div class="bg-red-50 border border-red-200 rounded-lg p-4">
              <pre><code class="language-json text-sm">{
  "data": {
    "execution_id": "exec_def456",
    "pipeline_id": 2,
    "status": "failed",
    "started_at": "2024-01-16T15:00:00Z",
    "failed_at": "2024-01-16T15:02:15Z",
    "error": {
      "code": "CONNECTION_ERROR",
      "message": "Unable to connect to source database",
      "details": "Timeout after 30 seconds"
    },
    "statistics": {
      "records_processed": 0,
      "records_success": 0,
      "records_failed": 0
    }
  }
}</code></pre>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Common Patterns -->
  <div class="max-w-4xl mx-auto mb-12">
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <h2 class="text-2xl font-bold text-gray-900 mb-6">Common Integration Patterns</h2>
      
      <div class="grid md:grid-cols-2 gap-6">
        <div class="border border-gray-200 rounded-lg p-4">
          <h3 class="font-semibold text-gray-900 mb-2">🔄 Scheduled Pipelines</h3>
          <p class="text-sm text-gray-600 mb-3">Run pipelines on a schedule for regular data updates</p>
          <div class="bg-gray-50 rounded p-3">
            <code class="text-xs">"schedule": "0 2 * * *"</code>
            <p class="text-xs text-gray-500 mt-1">Runs daily at 2 AM</p>
          </div>
        </div>
        
        <div class="border border-gray-200 rounded-lg p-4">
          <h3 class="font-semibold text-gray-900 mb-2">⚡ Real-time Processing</h3>
          <p class="text-sm text-gray-600 mb-3">Execute pipelines immediately when data changes</p>
          <div class="bg-gray-50 rounded p-3">
            <code class="text-xs">"async": true</code>
            <p class="text-xs text-gray-500 mt-1">Non-blocking execution</p>
          </div>
        </div>
        
        <div class="border border-gray-200 rounded-lg p-4">
          <h3 class="font-semibold text-gray-900 mb-2">🔁 Retry Logic</h3>
          <p class="text-sm text-gray-600 mb-3">Automatically retry failed executions</p>
          <div class="bg-gray-50 rounded p-3">
            <code class="text-xs">"retry_attempts": 3</code>
            <p class="text-xs text-gray-500 mt-1">Retry up to 3 times</p>
          </div>
        </div>
        
        <div class="border border-gray-200 rounded-lg p-4">
          <h3 class="font-semibold text-gray-900 mb-2">📊 Monitoring</h3>
          <p class="text-sm text-gray-600 mb-3">Track pipeline health and performance</p>
          <div class="bg-gray-50 rounded p-3">
            <code class="text-xs">GET /executions</code>
            <p class="text-xs text-gray-500 mt-1">Monitor all executions</p>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Next Steps -->
  <div class="max-w-3xl mx-auto text-center">
    <div class="bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg p-8 text-white">
      <h2 class="text-2xl font-bold mb-4">🎉 Congratulations!</h2>
      <p class="text-blue-100 mb-6">You've successfully created and executed your first data pipeline using the DataReflow API.</p>
      
      <div class="grid sm:grid-cols-3 gap-4">
        <%= link_to api_examples_path, class: "bg-white/20 hover:bg-white/30 backdrop-blur-sm rounded-lg p-4 transition-colors" do %>
          <div class="font-semibold mb-1">📚 Code Examples</div>
          <div class="text-sm text-blue-100">See examples in multiple programming languages</div>
        <% end %>
        
        <%= link_to "/api-docs", target: "_blank", class: "bg-white/20 hover:bg-white/30 backdrop-blur-sm rounded-lg p-4 transition-colors" do %>
          <div class="font-semibold mb-1">📖 Full API Docs</div>
          <div class="text-sm text-blue-100">Explore all available endpoints and schemas</div>
        <% end %>
        
        <%= link_to api_authentication_docs_path, class: "bg-white/20 hover:bg-white/30 backdrop-blur-sm rounded-lg p-4 transition-colors" do %>
          <div class="font-semibold mb-1">🔐 Security Guide</div>
          <div class="text-sm text-blue-100">Learn advanced authentication patterns</div>
        <% end %>
      </div>
    </div>
  </div>
</div>
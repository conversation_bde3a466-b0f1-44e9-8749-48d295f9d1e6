<% content_for :title, "Authentication Guide" %>

<div class="px-4 py-8">
  <!-- Header -->
  <div class="max-w-3xl mx-auto text-center mb-12">
    <h1 class="text-4xl font-bold text-gray-900 mb-4">Authentication Guide</h1>
    <p class="text-xl text-gray-600">Learn how to authenticate with the DataReflow API using JWT tokens and API keys.</p>
  </div>

  <!-- Overview -->
  <div class="max-w-4xl mx-auto mb-12">
    <div class="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-8">
      <div class="flex">
        <div class="flex-shrink-0">
          <svg class="h-5 w-5 text-blue-400" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"/>
          </svg>
        </div>
        <div class="ml-3">
          <h3 class="text-sm font-medium text-blue-800">
            API Authentication Overview
          </h3>
          <div class="mt-2 text-sm text-blue-700">
            <p>DataReflow API uses JWT (JSON Web Tokens) for authentication. All API requests must include a valid JWT token in the Authorization header.</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Authentication Methods -->
    <div class="space-y-8">
      <!-- JWT Authentication -->
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h2 class="text-2xl font-bold text-gray-900 mb-4 flex items-center">
          <span class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
            <svg class="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
            </svg>
          </span>
          JWT Token Authentication
        </h2>
        
        <div class="space-y-4">
          <h3 class="text-lg font-semibold text-gray-900">Step 1: Obtain a JWT Token</h3>
          <p class="text-gray-600">Make a POST request to the login endpoint with your email and password:</p>
          
          <div class="bg-gray-900 rounded-lg p-4 overflow-x-auto">
            <pre><code class="language-bash">curl -X POST "https://your-subdomain.datareflow.io/api/login" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "your-password"
  }'</code></pre>
          </div>
          
          <div class="bg-gray-50 rounded-lg p-4">
            <p class="text-sm font-medium text-gray-900 mb-2">Response:</p>
            <pre><code class="language-json">{
  "status": "success",
  "data": {
    "user": {
      "id": 123,
      "email": "<EMAIL>",
      "first_name": "John",
      "last_name": "Doe"
    },
    "token": "eyJhbGciOiJIUzI1NiJ9...",
    "expires_at": "2024-02-15T10:30:00Z"
  }
}</code></pre>
          </div>
          
          <h3 class="text-lg font-semibold text-gray-900 mt-6">Step 2: Use the Token in API Requests</h3>
          <p class="text-gray-600">Include the JWT token in the Authorization header for all subsequent API requests:</p>
          
          <div class="bg-gray-900 rounded-lg p-4 overflow-x-auto">
            <pre><code class="language-bash">curl -X GET "https://your-subdomain.datareflow.io/api/v1/pipelines" \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiJ9..." \
  -H "Content-Type: application/json"</code></pre>
          </div>
        </div>
      </div>

      <!-- Token Lifecycle -->
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h2 class="text-2xl font-bold text-gray-900 mb-4 flex items-center">
          <span class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center mr-3">
            <svg class="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
          </span>
          Token Lifecycle
        </h2>
        
        <div class="grid md:grid-cols-2 gap-6">
          <div>
            <h3 class="text-lg font-semibold text-gray-900 mb-3">Token Expiration</h3>
            <ul class="space-y-2 text-gray-600">
              <li class="flex items-start">
                <span class="w-1.5 h-1.5 bg-gray-400 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                JWT tokens expire after 24 hours
              </li>
              <li class="flex items-start">
                <span class="w-1.5 h-1.5 bg-gray-400 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                Check the <code>expires_at</code> field in login response
              </li>
              <li class="flex items-start">
                <span class="w-1.5 h-1.5 bg-gray-400 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                Expired tokens return 401 Unauthorized
              </li>
            </ul>
          </div>
          
          <div>
            <h3 class="text-lg font-semibold text-gray-900 mb-3">Token Refresh</h3>
            <ul class="space-y-2 text-gray-600">
              <li class="flex items-start">
                <span class="w-1.5 h-1.5 bg-gray-400 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                Re-authenticate to get a new token
              </li>
              <li class="flex items-start">
                <span class="w-1.5 h-1.5 bg-gray-400 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                Implement token refresh in your application
              </li>
              <li class="flex items-start">
                <span class="w-1.5 h-1.5 bg-gray-400 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                Store tokens securely (never in localStorage)
              </li>
            </ul>
          </div>
        </div>
      </div>

      <!-- Error Handling -->
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h2 class="text-2xl font-bold text-gray-900 mb-4 flex items-center">
          <span class="w-8 h-8 bg-red-100 rounded-lg flex items-center justify-center mr-3">
            <svg class="w-4 h-4 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 15.5c-.77.833.192 2.5 1.732 2.5z"></path>
            </svg>
          </span>
          Authentication Error Handling
        </h2>
        
        <div class="space-y-4">
          <div class="border border-gray-200 rounded-lg p-4">
            <h3 class="font-semibold text-gray-900 mb-2">401 Unauthorized</h3>
            <p class="text-gray-600 text-sm mb-2">Missing, invalid, or expired JWT token</p>
            <div class="bg-gray-50 rounded p-3">
              <pre><code class="language-json text-xs">{
  "error": "Unauthorized",
  "message": "Invalid or expired token",
  "code": "AUTH_001"
}</code></pre>
            </div>
          </div>
          
          <div class="border border-gray-200 rounded-lg p-4">
            <h3 class="font-semibold text-gray-900 mb-2">403 Forbidden</h3>
            <p class="text-gray-600 text-sm mb-2">Valid token but insufficient permissions</p>
            <div class="bg-gray-50 rounded p-3">
              <pre><code class="language-json text-xs">{
  "error": "Forbidden",
  "message": "Insufficient permissions for this resource",
  "code": "AUTH_002"
}</code></pre>
            </div>
          </div>
          
          <div class="border border-gray-200 rounded-lg p-4">
            <h3 class="font-semibold text-gray-900 mb-2">422 Invalid Credentials</h3>
            <p class="text-gray-600 text-sm mb-2">Incorrect email or password during login</p>
            <div class="bg-gray-50 rounded p-3">
              <pre><code class="language-json text-xs">{
  "error": "Invalid credentials",
  "message": "Email or password is incorrect",
  "code": "AUTH_003"
}</code></pre>
            </div>
          </div>
        </div>
      </div>

      <!-- Security Best Practices -->
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h2 class="text-2xl font-bold text-gray-900 mb-4 flex items-center">
          <span class="w-8 h-8 bg-yellow-100 rounded-lg flex items-center justify-center mr-3">
            <svg class="w-4 h-4 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
            </svg>
          </span>
          Security Best Practices
        </h2>
        
        <div class="grid md:grid-cols-2 gap-6">
          <div>
            <h3 class="text-lg font-semibold text-gray-900 mb-3">Token Storage</h3>
            <ul class="space-y-2 text-gray-600">
              <li class="flex items-start">
                <span class="text-green-500 mr-2">✓</span>
                Store tokens in secure HTTP-only cookies
              </li>
              <li class="flex items-start">
                <span class="text-green-500 mr-2">✓</span>
                Use encrypted storage in mobile apps
              </li>
              <li class="flex items-start">
                <span class="text-red-500 mr-2">✗</span>
                Never store tokens in localStorage
              </li>
              <li class="flex items-start">
                <span class="text-red-500 mr-2">✗</span>
                Never log tokens to console or files
              </li>
            </ul>
          </div>
          
          <div>
            <h3 class="text-lg font-semibold text-gray-900 mb-3">Network Security</h3>
            <ul class="space-y-2 text-gray-600">
              <li class="flex items-start">
                <span class="text-green-500 mr-2">✓</span>
                Always use HTTPS for API calls
              </li>
              <li class="flex items-start">
                <span class="text-green-500 mr-2">✓</span>
                Implement proper CORS policies
              </li>
              <li class="flex items-start">
                <span class="text-green-500 mr-2">✓</span>
                Use certificate pinning in mobile apps
              </li>
              <li class="flex items-start">
                <span class="text-green-500 mr-2">✓</span>
                Validate SSL certificates
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Next Steps -->
  <div class="max-w-3xl mx-auto text-center">
    <div class="bg-gray-900 rounded-lg p-8">
      <h2 class="text-2xl font-bold text-white mb-4">Ready to authenticate?</h2>
      <p class="text-gray-300 mb-6">Try making your first authenticated API call</p>
      <div class="flex flex-col sm:flex-row gap-4 justify-center">
        <%= link_to api_quickstart_path, class: "inline-flex items-center justify-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-gray-900 bg-white hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-white" do %>
          Quick Start Guide
        <% end %>
        <%= link_to api_examples_path, class: "inline-flex items-center justify-center px-6 py-3 border border-gray-300 text-base font-medium rounded-md text-white bg-transparent hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500" do %>
          Code Examples
        <% end %>
      </div>
    </div>
  </div>
</div>
<% content_for :title, "Code Examples" %>

<div class="px-4 py-8">
  <!-- Header -->
  <div class="max-w-3xl mx-auto text-center mb-12">
    <h1 class="text-4xl font-bold text-gray-900 mb-4">Code Examples</h1>
    <p class="text-xl text-gray-600">Ready-to-use code examples in popular programming languages. Copy, paste, and customize for your needs.</p>
  </div>

  <!-- Language Selection -->
  <div class="max-w-4xl mx-auto mb-8">
    <div class="flex flex-wrap justify-center gap-2" data-controller="tabs" data-tabs-active-class="bg-blue-600 text-white" data-tabs-inactive-class="bg-white text-gray-700 hover:bg-gray-50">
      <button data-tabs-target="tab" data-action="click->tabs#switch" data-tab="javascript" class="px-4 py-2 rounded-lg border border-gray-300 font-medium text-sm transition-colors bg-blue-600 text-white">JavaScript</button>
      <button data-tabs-target="tab" data-action="click->tabs#switch" data-tab="python" class="px-4 py-2 rounded-lg border border-gray-300 font-medium text-sm transition-colors bg-white text-gray-700 hover:bg-gray-50">Python</button>
      <button data-tabs-target="tab" data-action="click->tabs#switch" data-tab="ruby" class="px-4 py-2 rounded-lg border border-gray-300 font-medium text-sm transition-colors bg-white text-gray-700 hover:bg-gray-50">Ruby</button>
      <button data-tabs-target="tab" data-action="click->tabs#switch" data-tab="php" class="px-4 py-2 rounded-lg border border-gray-300 font-medium text-sm transition-colors bg-white text-gray-700 hover:bg-gray-50">PHP</button>
      <button data-tabs-target="tab" data-action="click->tabs#switch" data-tab="go" class="px-4 py-2 rounded-lg border border-gray-300 font-medium text-sm transition-colors bg-white text-gray-700 hover:bg-gray-50">Go</button>
      <button data-tabs-target="tab" data-action="click->tabs#switch" data-tab="curl" class="px-4 py-2 rounded-lg border border-gray-300 font-medium text-sm transition-colors bg-white text-gray-700 hover:bg-gray-50">cURL</button>
    </div>
  </div>

  <div class="max-w-4xl mx-auto space-y-8">
    <!-- JavaScript Examples -->
    <div data-tabs-target="panel" data-tab="javascript" class="space-y-8">
      <!-- Authentication -->
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h2 class="text-2xl font-bold text-gray-900 mb-4">🔐 Authentication</h2>
        <p class="text-gray-600 mb-4">Login and get a JWT token for API authentication:</p>
        
        <div class="bg-gray-900 rounded-lg p-4 overflow-x-auto">
          <pre><code class="language-javascript">// DataReflow API Client
class DataReflowAPI {
  constructor(baseUrl) {
    this.baseUrl = baseUrl;
    this.token = null;
  }

  async login(email, password) {
    const response = await fetch(`${this.baseUrl}/api/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ email, password })
    });

    if (!response.ok) {
      throw new Error(`Login failed: ${response.statusText}`);
    }

    const data = await response.json();
    this.token = data.data.token;
    return data.data;
  }

  async makeRequest(endpoint, options = {}) {
    if (!this.token) {
      throw new Error('No token available. Please login first.');
    }

    const response = await fetch(`${this.baseUrl}/api/v1${endpoint}`, {
      ...options,
      headers: {
        'Authorization': `Bearer ${this.token}`,
        'Content-Type': 'application/json',
        ...options.headers
      }
    });

    if (!response.ok) {
      throw new Error(`Request failed: ${response.statusText}`);
    }

    return response.json();
  }
}

// Usage
const api = new DataReflowAPI('https://your-subdomain.datareflow.io');

// Login
try {
  const user = await api.login('<EMAIL>', 'your-password');
  console.log('Logged in:', user);
} catch (error) {
  console.error('Login failed:', error.message);
}</code></pre>
        </div>
      </div>

      <!-- Pipeline Operations -->
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h2 class="text-2xl font-bold text-gray-900 mb-4">🔧 Pipeline Operations</h2>
        
        <div class="space-y-6">
          <div>
            <h3 class="text-lg font-semibold text-gray-900 mb-2">List Pipelines</h3>
            <div class="bg-gray-900 rounded-lg p-4 overflow-x-auto">
              <pre><code class="language-javascript">// List all pipelines
async function listPipelines() {
  try {
    const response = await api.makeRequest('/pipelines');
    console.log('Pipelines:', response.data);
    return response.data;
  } catch (error) {
    console.error('Failed to list pipelines:', error.message);
  }
}</code></pre>
            </div>
          </div>

          <div>
            <h3 class="text-lg font-semibold text-gray-900 mb-2">Create Pipeline</h3>
            <div class="bg-gray-900 rounded-lg p-4 overflow-x-auto">
              <pre><code class="language-javascript">// Create a new pipeline
async function createPipeline(pipelineData) {
  try {
    const response = await api.makeRequest('/pipelines', {
      method: 'POST',
      body: JSON.stringify({
        name: pipelineData.name,
        description: pipelineData.description,
        source_connector_id: pipelineData.sourceId,
        destination_connector_id: pipelineData.destinationId,
        schedule: pipelineData.schedule || null,
        config: pipelineData.config || {}
      })
    });
    
    console.log('Pipeline created:', response.data);
    return response.data;
  } catch (error) {
    console.error('Failed to create pipeline:', error.message);
  }
}

// Usage
const newPipeline = await createPipeline({
  name: 'Customer Data Sync',
  description: 'Sync customer data from CRM to warehouse',
  sourceId: 10,
  destinationId: 20,
  schedule: '0 2 * * *',
  config: { batch_size: 1000 }
});</code></pre>
            </div>
          </div>

          <div>
            <h3 class="text-lg font-semibold text-gray-900 mb-2">Execute Pipeline</h3>
            <div class="bg-gray-900 rounded-lg p-4 overflow-x-auto">
              <pre><code class="language-javascript">// Execute a pipeline
async function executePipeline(pipelineId, parameters = {}) {
  try {
    const response = await api.makeRequest(`/pipelines/${pipelineId}/execute`, {
      method: 'POST',
      body: JSON.stringify({
        async: true,
        parameters
      })
    });
    
    console.log('Pipeline execution started:', response.data);
    return response.data;
  } catch (error) {
    console.error('Failed to execute pipeline:', error.message);
  }
}

// Monitor execution
async function monitorExecution(executionId) {
  try {
    const response = await api.makeRequest(`/executions/${executionId}`);
    const execution = response.data;
    
    console.log(`Status: ${execution.status} (${execution.progress.percentage}%)`);
    
    if (execution.status === 'completed') {
      console.log('Execution completed:', execution.statistics);
    } else if (execution.status === 'failed') {
      console.error('Execution failed:', execution.error);
    } else if (execution.status === 'running') {
      // Poll again after 5 seconds
      setTimeout(() => monitorExecution(executionId), 5000);
    }
    
    return execution;
  } catch (error) {
    console.error('Failed to get execution status:', error.message);
  }
}</code></pre>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Python Examples -->
    <div data-tabs-target="panel" data-tab="python" class="space-y-8 hidden">
      <!-- Authentication -->
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h2 class="text-2xl font-bold text-gray-900 mb-4">🔐 Authentication</h2>
        
        <div class="bg-gray-900 rounded-lg p-4 overflow-x-auto">
          <pre><code class="language-python">import requests
import json
from typing import Optional, Dict, Any

class DataReflowAPI:
    def __init__(self, base_url: str):
        self.base_url = base_url.rstrip('/')
        self.token = None
        self.session = requests.Session()
    
    def login(self, email: str, password: str) -> Dict[str, Any]:
        """Login and store JWT token"""
        url = f"{self.base_url}/api/login"
        payload = {"email": email, "password": password}
        
        response = self.session.post(url, json=payload)
        response.raise_for_status()
        
        data = response.json()
        self.token = data["data"]["token"]
        
        # Set authorization header for future requests
        self.session.headers.update({
            "Authorization": f"Bearer {self.token}"
        })
        
        return data["data"]
    
    def make_request(self, endpoint: str, method: str = "GET", 
                    data: Optional[Dict] = None) -> Dict[str, Any]:
        """Make authenticated API request"""
        if not self.token:
            raise Exception("No token available. Please login first.")
        
        url = f"{self.base_url}/api/v1{endpoint}"
        
        if method.upper() == "GET":
            response = self.session.get(url)
        elif method.upper() == "POST":
            response = self.session.post(url, json=data)
        elif method.upper() == "PUT":
            response = self.session.put(url, json=data)
        elif method.upper() == "PATCH":
            response = self.session.patch(url, json=data)
        elif method.upper() == "DELETE":
            response = self.session.delete(url)
        else:
            raise ValueError(f"Unsupported HTTP method: {method}")
        
        response.raise_for_status()
        return response.json()

# Usage
api = DataReflowAPI("https://your-subdomain.datareflow.io")

try:
    user_data = api.login("<EMAIL>", "your-password")
    print(f"Logged in as: {user_data['user']['first_name']}")
except requests.RequestException as e:
    print(f"Login failed: {e}")
</code></pre>
        </div>
      </div>

      <!-- Pipeline Operations -->
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h2 class="text-2xl font-bold text-gray-900 mb-4">🔧 Pipeline Operations</h2>
        
        <div class="bg-gray-900 rounded-lg p-4 overflow-x-auto">
          <pre><code class="language-python">def list_pipelines(api: DataReflowAPI) -> List[Dict]:
    """List all pipelines"""
    response = api.make_request("/pipelines")
    return response["data"]

def create_pipeline(api: DataReflowAPI, pipeline_data: Dict) -> Dict:
    """Create a new pipeline"""
    payload = {
        "name": pipeline_data["name"],
        "description": pipeline_data["description"],
        "source_connector_id": pipeline_data["source_id"],
        "destination_connector_id": pipeline_data["destination_id"],
        "schedule": pipeline_data.get("schedule"),
        "config": pipeline_data.get("config", {})
    }
    
    response = api.make_request("/pipelines", "POST", payload)
    return response["data"]

def execute_pipeline(api: DataReflowAPI, pipeline_id: int, 
                    parameters: Optional[Dict] = None) -> Dict:
    """Execute a pipeline"""
    payload = {
        "async": True,
        "parameters": parameters or {}
    }
    
    response = api.make_request(f"/pipelines/{pipeline_id}/execute", "POST", payload)
    return response["data"]

def monitor_execution(api: DataReflowAPI, execution_id: str) -> Dict:
    """Get execution status"""
    response = api.make_request(f"/executions/{execution_id}")
    return response["data"]

# Example usage
if __name__ == "__main__":
    # List pipelines
    pipelines = list_pipelines(api)
    print(f"Found {len(pipelines)} pipelines")
    
    # Create a new pipeline
    new_pipeline = create_pipeline(api, {
        "name": "Python API Pipeline",
        "description": "Created via Python API client",
        "source_id": 10,
        "destination_id": 20,
        "schedule": "0 */6 * * *",  # Every 6 hours
        "config": {"batch_size": 500}
    })
    
    # Execute it
    execution = execute_pipeline(api, new_pipeline["id"])
    print(f"Execution started: {execution['execution_id']}")
    
    # Monitor progress
    import time
    while True:
        status = monitor_execution(api, execution["execution_id"])
        print(f"Status: {status['status']} ({status.get('progress', {}).get('percentage', 0)}%)")
        
        if status["status"] in ["completed", "failed"]:
            break
        time.sleep(5)
</code></pre>
        </div>
      </div>
    </div>

    <!-- Ruby Examples -->
    <div data-tabs-target="panel" data-tab="ruby" class="space-y-8 hidden">
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h2 class="text-2xl font-bold text-gray-900 mb-4">🔐 Ruby Implementation</h2>
        
        <div class="bg-gray-900 rounded-lg p-4 overflow-x-auto">
          <pre><code class="language-ruby">require 'net/http'
require 'json'
require 'uri'

class DataReflowAPI
  attr_reader :base_url, :token
  
  def initialize(base_url)
    @base_url = base_url.chomp('/')
    @token = nil
  end
  
  def login(email, password)
    uri = URI("#{@base_url}/api/login")
    
    http = Net::HTTP.new(uri.host, uri.port)
    http.use_ssl = uri.scheme == 'https'
    
    request = Net::HTTP::Post.new(uri)
    request['Content-Type'] = 'application/json'
    request.body = { email: email, password: password }.to_json
    
    response = http.request(request)
    raise "Login failed: #{response.message}" unless response.is_a?(Net::HTTPSuccess)
    
    data = JSON.parse(response.body)
    @token = data['data']['token']
    
    data['data']
  end
  
  def make_request(endpoint, method = 'GET', data = nil)
    raise 'No token available. Please login first.' unless @token
    
    uri = URI("#{@base_url}/api/v1#{endpoint}")
    
    http = Net::HTTP.new(uri.host, uri.port)
    http.use_ssl = uri.scheme == 'https'
    
    case method.upcase
    when 'GET'
      request = Net::HTTP::Get.new(uri)
    when 'POST'
      request = Net::HTTP::Post.new(uri)
      request.body = data.to_json if data
    when 'PUT'
      request = Net::HTTP::Put.new(uri)
      request.body = data.to_json if data
    when 'PATCH'
      request = Net::HTTP::Patch.new(uri)
      request.body = data.to_json if data
    when 'DELETE'
      request = Net::HTTP::Delete.new(uri)
    end
    
    request['Authorization'] = "Bearer #{@token}"
    request['Content-Type'] = 'application/json'
    
    response = http.request(request)
    raise "Request failed: #{response.message}" unless response.is_a?(Net::HTTPSuccess)
    
    JSON.parse(response.body)
  end
  
  def list_pipelines
    response = make_request('/pipelines')
    response['data']
  end
  
  def create_pipeline(pipeline_data)
    payload = {
      name: pipeline_data[:name],
      description: pipeline_data[:description],
      source_connector_id: pipeline_data[:source_id],
      destination_connector_id: pipeline_data[:destination_id],
      schedule: pipeline_data[:schedule],
      config: pipeline_data[:config] || {}
    }
    
    response = make_request('/pipelines', 'POST', payload)
    response['data']
  end
  
  def execute_pipeline(pipeline_id, parameters = {})
    payload = {
      async: true,
      parameters: parameters
    }
    
    response = make_request("/pipelines/#{pipeline_id}/execute", 'POST', payload)
    response['data']
  end
end

# Usage
api = DataReflowAPI.new('https://your-subdomain.datareflow.io')

begin
  user = api.login('<EMAIL>', 'your-password')
  puts "Logged in as: #{user['user']['first_name']}"
  
  # List pipelines
  pipelines = api.list_pipelines
  puts "Found #{pipelines.length} pipelines"
  
  # Create pipeline
  new_pipeline = api.create_pipeline({
    name: 'Ruby API Pipeline',
    description: 'Created via Ruby API client',
    source_id: 10,
    destination_id: 20,
    schedule: '0 4 * * *',
    config: { batch_size: 2000 }
  })
  
  puts "Created pipeline: #{new_pipeline['name']}"
  
rescue StandardError => e
  puts "Error: #{e.message}"
end
</code></pre>
        </div>
      </div>
    </div>

    <!-- PHP Examples -->
    <div data-tabs-target="panel" data-tab="php" class="space-y-8 hidden">
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h2 class="text-2xl font-bold text-gray-900 mb-4">🔐 PHP Implementation</h2>
        
        <div class="bg-gray-900 rounded-lg p-4 overflow-x-auto">
          <pre><code class="language-php"><?php

class DataReflowAPI {
    private $baseUrl;
    private $token;
    
    public function __construct($baseUrl) {
        $this->baseUrl = rtrim($baseUrl, '/');
        $this->token = null;
    }
    
    public function login($email, $password) {
        $url = $this->baseUrl . '/api/login';
        
        $data = [
            'email' => $email,
            'password' => $password
        ];
        
        $response = $this->makeHttpRequest($url, 'POST', $data);
        $this->token = $response['data']['token'];
        
        return $response['data'];
    }
    
    public function makeRequest($endpoint, $method = 'GET', $data = null) {
        if (!$this->token) {
            throw new Exception('No token available. Please login first.');
        }
        
        $url = $this->baseUrl . '/api/v1' . $endpoint;
        
        $headers = [
            'Authorization: Bearer ' . $this->token,
            'Content-Type: application/json'
        ];
        
        return $this->makeHttpRequest($url, $method, $data, $headers);
    }
    
    private function makeHttpRequest($url, $method = 'GET', $data = null, $headers = []) {
        $ch = curl_init();
        
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, $method);
        
        if ($headers) {
            curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        } else {
            curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
        }
        
        if ($data && in_array($method, ['POST', 'PUT', 'PATCH'])) {
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        }
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        
        if (curl_error($ch)) {
            throw new Exception('Curl error: ' . curl_error($ch));
        }
        
        curl_close($ch);
        
        if ($httpCode >= 400) {
            throw new Exception('HTTP error: ' . $httpCode . ' - ' . $response);
        }
        
        return json_decode($response, true);
    }
    
    public function listPipelines() {
        $response = $this->makeRequest('/pipelines');
        return $response['data'];
    }
    
    public function createPipeline($pipelineData) {
        $payload = [
            'name' => $pipelineData['name'],
            'description' => $pipelineData['description'],
            'source_connector_id' => $pipelineData['source_id'],
            'destination_connector_id' => $pipelineData['destination_id'],
            'schedule' => $pipelineData['schedule'] ?? null,
            'config' => $pipelineData['config'] ?? []
        ];
        
        $response = $this->makeRequest('/pipelines', 'POST', $payload);
        return $response['data'];
    }
    
    public function executePipeline($pipelineId, $parameters = []) {
        $payload = [
            'async' => true,
            'parameters' => $parameters
        ];
        
        $response = $this->makeRequest("/pipelines/{$pipelineId}/execute", 'POST', $payload);
        return $response['data'];
    }
}

// Usage
try {
    $api = new DataReflowAPI('https://your-subdomain.datareflow.io');
    
    // Login
    $user = $api->login('<EMAIL>', 'your-password');
    echo "Logged in as: " . $user['user']['first_name'] . "\n";
    
    // List pipelines
    $pipelines = $api->listPipelines();
    echo "Found " . count($pipelines) . " pipelines\n";
    
    // Create pipeline
    $newPipeline = $api->createPipeline([
        'name' => 'PHP API Pipeline',
        'description' => 'Created via PHP API client',
        'source_id' => 10,
        'destination_id' => 20,
        'schedule' => '0 6 * * *',
        'config' => ['batch_size' => 1500]
    ]);
    
    echo "Created pipeline: " . $newPipeline['name'] . "\n";
    
    // Execute pipeline
    $execution = $api->executePipeline($newPipeline['id']);
    echo "Execution started: " . $execution['execution_id'] . "\n";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}

?>
</code></pre>
        </div>
      </div>
    </div>

    <!-- Go Examples -->
    <div data-tabs-target="panel" data-tab="go" class="space-y-8 hidden">
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h2 class="text-2xl font-bold text-gray-900 mb-4">🔐 Go Implementation</h2>
        
        <div class="bg-gray-900 rounded-lg p-4 overflow-x-auto">
          <pre><code class="language-go">package main

import (
    "bytes"
    "encoding/json"
    "fmt"
    "net/http"
    "time"
)

type DataReflowAPI struct {
    BaseURL string
    Token   string
    Client  *http.Client
}

type LoginResponse struct {
    Status string `json:"status"`
    Data   struct {
        User  map[string]interface{} `json:"user"`
        Token string                 `json:"token"`
    } `json:"data"`
}

type Pipeline struct {
    ID                    int                    `json:"id"`
    Name                  string                 `json:"name"`
    Description           string                 `json:"description"`
    Status               string                 `json:"status"`
    Schedule             string                 `json:"schedule"`
    Config               map[string]interface{} `json:"config"`
}

func NewDataReflowAPI(baseURL string) *DataReflowAPI {
    return &DataReflowAPI{
        BaseURL: baseURL,
        Client: &http.Client{
            Timeout: time.Second * 30,
        },
    }
}

func (api *DataReflowAPI) Login(email, password string) error {
    loginData := map[string]string{
        "email":    email,
        "password": password,
    }
    
    jsonData, _ := json.Marshal(loginData)
    
    req, err := http.NewRequest("POST", api.BaseURL+"/api/login", bytes.NewBuffer(jsonData))
    if err != nil {
        return err
    }
    
    req.Header.Set("Content-Type", "application/json")
    
    resp, err := api.Client.Do(req)
    if err != nil {
        return err
    }
    defer resp.Body.Close()
    
    var loginResp LoginResponse
    if err := json.NewDecoder(resp.Body).Decode(&loginResp); err != nil {
        return err
    }
    
    api.Token = loginResp.Data.Token
    return nil
}

func (api *DataReflowAPI) makeRequest(method, endpoint string, data interface{}) (*http.Response, error) {
    var req *http.Request
    var err error
    
    if data != nil {
        jsonData, _ := json.Marshal(data)
        req, err = http.NewRequest(method, api.BaseURL+"/api/v1"+endpoint, bytes.NewBuffer(jsonData))
    } else {
        req, err = http.NewRequest(method, api.BaseURL+"/api/v1"+endpoint, nil)
    }
    
    if err != nil {
        return nil, err
    }
    
    req.Header.Set("Content-Type", "application/json")
    req.Header.Set("Authorization", "Bearer "+api.Token)
    
    return api.Client.Do(req)
}

func (api *DataReflowAPI) ListPipelines() ([]Pipeline, error) {
    resp, err := api.makeRequest("GET", "/pipelines", nil)
    if err != nil {
        return nil, err
    }
    defer resp.Body.Close()
    
    var result struct {
        Data []Pipeline `json:"data"`
    }
    
    if err := json.NewDecoder(resp.Body).Decode(&result); err != nil {
        return nil, err
    }
    
    return result.Data, nil
}

func (api *DataReflowAPI) CreatePipeline(name, description string, sourceID, destinationID int) (*Pipeline, error) {
    pipelineData := map[string]interface{}{
        "name":                   name,
        "description":           description,
        "source_connector_id":   sourceID,
        "destination_connector_id": destinationID,
        "schedule":              "0 8 * * *",
        "config": map[string]interface{}{
            "batch_size": 1000,
        },
    }
    
    resp, err := api.makeRequest("POST", "/pipelines", pipelineData)
    if err != nil {
        return nil, err
    }
    defer resp.Body.Close()
    
    var result struct {
        Data Pipeline `json:"data"`
    }
    
    if err := json.NewDecoder(resp.Body).Decode(&result); err != nil {
        return nil, err
    }
    
    return &result.Data, nil
}

func main() {
    api := NewDataReflowAPI("https://your-subdomain.datareflow.io")
    
    // Login
    if err := api.Login("<EMAIL>", "your-password"); err != nil {
        fmt.Printf("Login failed: %v\n", err)
        return
    }
    
    fmt.Println("Successfully logged in")
    
    // List pipelines
    pipelines, err := api.ListPipelines()
    if err != nil {
        fmt.Printf("Failed to list pipelines: %v\n", err)
        return
    }
    
    fmt.Printf("Found %d pipelines\n", len(pipelines))
    
    // Create a new pipeline
    newPipeline, err := api.CreatePipeline(
        "Go API Pipeline",
        "Created via Go API client",
        10, // source connector ID
        20, // destination connector ID
    )
    
    if err != nil {
        fmt.Printf("Failed to create pipeline: %v\n", err)
        return
    }
    
    fmt.Printf("Created pipeline: %s (ID: %d)\n", newPipeline.Name, newPipeline.ID)
}
</code></pre>
        </div>
      </div>
    </div>

    <!-- cURL Examples -->
    <div data-tabs-target="panel" data-tab="curl" class="space-y-8 hidden">
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h2 class="text-2xl font-bold text-gray-900 mb-4">🔐 cURL Examples</h2>
        
        <div class="space-y-6">
          <div>
            <h3 class="text-lg font-semibold text-gray-900 mb-2">Authentication</h3>
            <div class="bg-gray-900 rounded-lg p-4 overflow-x-auto">
              <pre><code class="language-bash"># Login to get JWT token
curl -X POST "https://your-subdomain.datareflow.io/api/login" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "your-password"
  }'

# Save the token from response for subsequent requests
export TOKEN="eyJhbGciOiJIUzI1NiJ9..."</code></pre>
            </div>
          </div>

          <div>
            <h3 class="text-lg font-semibold text-gray-900 mb-2">Pipeline Management</h3>
            <div class="bg-gray-900 rounded-lg p-4 overflow-x-auto">
              <pre><code class="language-bash"># List all pipelines
curl -X GET "https://your-subdomain.datareflow.io/api/v1/pipelines" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json"

# Get specific pipeline
curl -X GET "https://your-subdomain.datareflow.io/api/v1/pipelines/123" \
  -H "Authorization: Bearer $TOKEN"

# Create new pipeline
curl -X POST "https://your-subdomain.datareflow.io/api/v1/pipelines" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "My cURL Pipeline",
    "description": "Created using cURL",
    "source_connector_id": 10,
    "destination_connector_id": 20,
    "schedule": "0 */4 * * *",
    "config": {
      "batch_size": 500,
      "retry_attempts": 3
    }
  }'

# Update pipeline
curl -X PATCH "https://your-subdomain.datareflow.io/api/v1/pipelines/123" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Updated Pipeline Name",
    "description": "Updated description"
  }'

# Delete pipeline
curl -X DELETE "https://your-subdomain.datareflow.io/api/v1/pipelines/123" \
  -H "Authorization: Bearer $TOKEN"</code></pre>
            </div>
          </div>

          <div>
            <h3 class="text-lg font-semibold text-gray-900 mb-2">Pipeline Execution</h3>
            <div class="bg-gray-900 rounded-lg p-4 overflow-x-auto">
              <pre><code class="language-bash"># Execute pipeline
curl -X POST "https://your-subdomain.datareflow.io/api/v1/pipelines/123/execute" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "async": true,
    "parameters": {
      "start_date": "2024-01-01",
      "end_date": "2024-01-31",
      "batch_size": 1000
    }
  }'

# Monitor execution
curl -X GET "https://your-subdomain.datareflow.io/api/v1/executions/exec_abc123" \
  -H "Authorization: Bearer $TOKEN"

# Cancel execution
curl -X PATCH "https://your-subdomain.datareflow.io/api/v1/executions/exec_abc123/cancel" \
  -H "Authorization: Bearer $TOKEN"

# List all executions
curl -X GET "https://your-subdomain.datareflow.io/api/v1/executions" \
  -H "Authorization: Bearer $TOKEN"</code></pre>
            </div>
          </div>

          <div>
            <h3 class="text-lg font-semibold text-gray-900 mb-2">Data Connectors</h3>
            <div class="bg-gray-900 rounded-lg p-4 overflow-x-auto">
              <pre><code class="language-bash"># List connectors
curl -X GET "https://your-subdomain.datareflow.io/api/v1/connectors" \
  -H "Authorization: Bearer $TOKEN"

# Test connector connection
curl -X POST "https://your-subdomain.datareflow.io/api/v1/connectors/10/test_connection" \
  -H "Authorization: Bearer $TOKEN"

# Create new connector
curl -X POST "https://your-subdomain.datareflow.io/api/v1/connectors" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "My Database",
    "connector_type": "postgresql",
    "config": {
      "host": "localhost",
      "port": 5432,
      "database": "mydb",
      "username": "user",
      "password": "password"
    }
  }'</code></pre>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- SDK Links -->
  <div class="max-w-4xl mx-auto text-center">
    <div class="bg-gradient-to-r from-purple-600 to-indigo-600 rounded-lg p-8 text-white">
      <h2 class="text-2xl font-bold mb-4">Need More Help?</h2>
      <p class="text-purple-100 mb-6">Check out these additional resources to accelerate your integration</p>
      
      <div class="grid sm:grid-cols-3 gap-4">
        <%= link_to "/api-docs", target: "_blank", class: "bg-white/20 hover:bg-white/30 backdrop-blur-sm rounded-lg p-4 transition-colors" do %>
          <div class="font-semibold mb-1">📚 Interactive Docs</div>
          <div class="text-sm text-purple-100">Try all endpoints with Swagger UI</div>
        <% end %>
        
        <%= link_to api_authentication_docs_path, class: "bg-white/20 hover:bg-white/30 backdrop-blur-sm rounded-lg p-4 transition-colors" do %>
          <div class="font-semibold mb-1">🔐 Auth Guide</div>
          <div class="text-sm text-purple-100">Deep dive into authentication patterns</div>
        <% end %>
        
        <%= link_to api_quickstart_path, class: "bg-white/20 hover:bg-white/30 backdrop-blur-sm rounded-lg p-4 transition-colors" do %>
          <div class="font-semibold mb-1">⚡ Quick Start</div>
          <div class="text-sm text-purple-100">5-minute integration tutorial</div>
        <% end %>
      </div>
    </div>
  </div>
</div>

<!-- Tabs Stimulus Controller -->
<script>
document.addEventListener('DOMContentLoaded', function() {
  const tabs = document.querySelectorAll('[data-tabs-target="tab"]');
  const panels = document.querySelectorAll('[data-tabs-target="panel"]');
  
  tabs.forEach(tab => {
    tab.addEventListener('click', function() {
      const targetTab = this.dataset.tab;
      
      // Update tab styles
      tabs.forEach(t => {
        if (t === this) {
          t.className = t.className.replace('bg-white text-gray-700 hover:bg-gray-50', 'bg-blue-600 text-white');
        } else {
          t.className = t.className.replace('bg-blue-600 text-white', 'bg-white text-gray-700 hover:bg-gray-50');
        }
      });
      
      // Show/hide panels
      panels.forEach(panel => {
        if (panel.dataset.tab === targetTab) {
          panel.classList.remove('hidden');
        } else {
          panel.classList.add('hidden');
        }
      });
    });
  });
});
</script>
<% content_for :title, "Change Password - DataReflow" %>

<div class="text-center mb-8">
  <h1 class="text-3xl font-bold text-gray-900 mb-2">Change your password</h1>
  <p class="text-gray-600">Enter your new password below</p>
</div>

<%= render "shared/flash_messages" if respond_to?(:flash) %>
<%= render "devise/shared/error_messages", resource: resource %>

<%= form_for(resource, as: resource_name, url: password_path(resource_name),
             html: {
               method: :put,
               class: "space-y-6",
               data: {
                 controller: "auth",
                 auth_target: "form",
                 action: "submit->auth#submitForm keydown->auth#handleKeyDown"
               }
             }) do |f| %>

  <%= f.hidden_field :reset_password_token %>

  <!-- New Password Field -->
  <div class="form-group">
    <%= f.label :password, class: "block text-sm font-medium text-gray-700 mb-2" do %>
      <span>New password</span>
      <span class="text-red-500">*</span>
      <% if @minimum_password_length %>
        <span class="text-xs text-gray-500 font-normal">(<%= @minimum_password_length %> characters minimum)</span>
      <% end %>
    <% end %>
    <div class="relative">
      <%= f.password_field :password,
          autofocus: true,
          autocomplete: "new-password",
          required: true,
          class: "input-focus w-full px-4 py-3 pr-12 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200",
          placeholder: "Enter your new password",
          data: {
            auth_target: "password",
            action: "focus->auth#focusField blur->auth#blurField input->auth#validateField"
          } %>
      <button type="button"
              class="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600 transition-colors"
              data-action="click->auth#togglePasswordVisibility"
              data-auth-target="toggleIcon">
        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
        </svg>
      </button>
    </div>

    <!-- Password Strength Indicator -->
    <div class="mt-2">
      <div class="w-full bg-gray-200 rounded-full h-2">
        <div class="h-2 rounded-full transition-all duration-300"
             data-auth-target="strengthIndicator"></div>
      </div>
      <p class="text-sm mt-1" data-auth-target="strengthText"></p>
    </div>
  </div>

  <!-- Password Confirmation Field -->
  <div class="form-group">
    <%= f.label :password_confirmation, class: "block text-sm font-medium text-gray-700 mb-2" do %>
      <span>Confirm new password</span>
      <span class="text-red-500">*</span>
    <% end %>
    <div class="relative">
      <%= f.password_field :password_confirmation,
          autocomplete: "new-password",
          required: true,
          class: "input-focus w-full px-4 py-3 pr-12 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200",
          placeholder: "Confirm your new password",
          data: {
            auth_target: "passwordConfirmation",
            action: "focus->auth#focusField blur->auth#blurField input->auth#validateField"
          } %>
    </div>
  </div>

  <!-- Submit Button -->
  <div>
    <%= f.submit "Change my password",
        class: "w-full flex justify-center py-3 px-4 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200 form-transition",
        data: { auth_target: "submitButton" } %>
  </div>
<% end %>

<!-- Back to Sign In -->
<div class="mt-6 text-center">
  <p class="text-sm text-gray-600">
    <%= link_to "Back to sign in", new_session_path(resource_name),
        class: "font-medium text-blue-600 hover:text-blue-500 transition-colors" %>
  </p>
</div>

<% content_for :title, "Reset Password - DataReflow" %>

<div class="text-center mb-8">
  <h1 class="text-3xl font-bold text-gray-900 mb-2">Forgot your password?</h1>
  <p class="text-gray-600">Enter your email address and we'll send you a link to reset your password</p>
</div>

<%= render "shared/flash_messages" if respond_to?(:flash) %>
<%= render "devise/shared/error_messages", resource: resource %>

<%= form_for(resource, as: resource_name, url: password_path(resource_name),
             html: {
               method: :post,
               class: "space-y-6",
               data: {
                 controller: "auth",
                 auth_target: "form",
                 action: "submit->auth#submitForm"
               }
             }) do |f| %>

  <!-- Email Field -->
  <div class="form-group">
    <%= f.label :email, class: "block text-sm font-medium text-gray-700 mb-2" do %>
      <span>Email address</span>
      <span class="text-red-500">*</span>
    <% end %>
    <%= f.email_field :email,
        autofocus: true,
        autocomplete: "email",
        required: true,
        class: "input-focus w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200",
        placeholder: "Enter your email address",
        data: {
          action: "focus->auth#focusField blur->auth#blurField input->auth#validateField"
        } %>
  </div>

  <!-- Submit Button -->
  <div>
    <%= f.submit "Send reset instructions",
        class: "w-full flex justify-center py-3 px-4 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200 form-transition",
        data: { auth_target: "submitButton" } %>
  </div>
<% end %>

<!-- Back to Sign In -->
<div class="mt-6 text-center">
  <p class="text-sm text-gray-600">
    Remember your password?
    <%= link_to "Sign in here", new_session_path(resource_name),
        class: "font-medium text-blue-600 hover:text-blue-500 transition-colors" %>
  </p>
</div>

<!-- Additional Links -->
<div class="mt-4 text-center space-y-2">
  <% if devise_mapping.registerable? && controller_name != 'registrations' %>
    <div>
      <%= link_to "Don't have an account? Sign up", new_registration_path(resource_name),
          class: "text-sm text-gray-500 hover:text-gray-700 transition-colors" %>
    </div>
  <% end %>
</div>

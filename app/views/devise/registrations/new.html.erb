<% content_for :title, "Sign Up - DataReflow" %>

<div class="text-center mb-8">
  <h1 class="text-3xl font-bold text-gray-900 mb-2">Create your account</h1>
  <p class="text-gray-600">Start your data integration journey today</p>
</div>

<%= render "shared/flash_messages" if respond_to?(:flash) %>
<%= render "devise/shared/error_messages", resource: resource %>

<%= form_for(resource, as: resource_name, url: registration_path(resource_name),
             html: {
               class: "space-y-6",
               data: {
                 controller: "auth",
                 auth_target: "form",
                 action: "submit->auth#submitForm keydown->auth#handleKeyDown"
               }
             }) do |f| %>

  <!-- Name Fields Row -->
  <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
    <!-- First Name -->
    <div class="form-group">
      <%= f.label :first_name, class: "block text-sm font-medium text-gray-700 mb-2" do %>
        <span>First name</span>
        <span class="text-red-500">*</span>
      <% end %>
      <%= f.text_field :first_name,
          autofocus: true,
          required: true,
          class: "input-focus w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200",
          placeholder: "Enter your first name",
          data: {
            action: "focus->auth#focusField blur->auth#blurField input->auth#validateField"
          } %>
    </div>

    <!-- Last Name -->
    <div class="form-group">
      <%= f.label :last_name, class: "block text-sm font-medium text-gray-700 mb-2" do %>
        <span>Last name</span>
        <span class="text-red-500">*</span>
      <% end %>
      <%= f.text_field :last_name,
          required: true,
          class: "input-focus w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200",
          placeholder: "Enter your last name",
          data: {
            action: "focus->auth#focusField blur->auth#blurField input->auth#validateField"
          } %>
    </div>
  </div>

  <!-- Email Field -->
  <div class="form-group">
    <%= f.label :email, class: "block text-sm font-medium text-gray-700 mb-2" do %>
      <span>Email address</span>
      <span class="text-red-500">*</span>
    <% end %>
    <%= f.email_field :email,
        autocomplete: "email",
        required: true,
        class: "input-focus w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200",
        placeholder: "Enter your email address",
        data: {
          action: "focus->auth#focusField blur->auth#blurField input->auth#validateField"
        } %>
  </div>

  <!-- Subdomain Field -->
  <div class="form-group">
    <%= label_tag :subdomain, class: "block text-sm font-medium text-gray-700 mb-2" do %>
      <span>Choose your subdomain</span>
      <span class="text-red-500">*</span>
    <% end %>
    <div class="relative">
      <div class="subdomain-input-group">
        <%= text_field_tag :subdomain,
            params[:subdomain],
            required: true,
            maxlength: 30,
            pattern: "[a-z0-9]{3,30}",
            class: "subdomain-input input-focus flex-1 px-4 py-3 transition-all duration-200",
            placeholder: "yourcompany",
            data: {
              action: "focus->auth#focusField blur->auth#blurField input->auth#validateSubdomain",
              auth_target: "subdomainInput"
            } %>
        <div class="subdomain-suffix">
          .datareflow.io
        </div>
      </div>
      <div class="mt-1 text-xs text-gray-500">
        Your workspace will be available at <span class="font-medium" data-auth-target="subdomainPreview">yourcompany.datareflow.io</span>
      </div>
      <div class="mt-1 hidden" data-auth-target="subdomainError">
        <p class="text-sm text-red-600"></p>
      </div>
      <div class="mt-1 hidden" data-auth-target="subdomainSuccess">
        <p class="text-sm text-green-600">✓ This subdomain is available</p>
      </div>
    </div>
    <div class="mt-2 text-xs text-gray-500">
      <strong>Requirements:</strong> 3-30 characters, lowercase letters and numbers only
      <br>
      <strong>Examples:</strong> mycompany, acmecorp, startup123
    </div>
  </div>

  <!-- Password Field -->
  <div class="form-group">
    <%= f.label :password, class: "block text-sm font-medium text-gray-700 mb-2" do %>
      <span>Password</span>
      <span class="text-red-500">*</span>
      <% if @minimum_password_length %>
        <span class="text-xs text-gray-500 font-normal">(<%= @minimum_password_length %> characters minimum)</span>
      <% end %>
    <% end %>
    <div class="relative">
      <%= f.password_field :password,
          autocomplete: "new-password",
          required: true,
          class: "input-focus w-full px-4 py-3 pr-12 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200",
          placeholder: "Create a strong password",
          data: {
            auth_target: "password",
            action: "focus->auth#focusField blur->auth#blurField input->auth#validateField"
          } %>
      <button type="button"
              class="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600 transition-colors"
              data-action="click->auth#togglePasswordVisibility"
              data-auth-target="toggleIcon">
        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
        </svg>
      </button>
    </div>

    <!-- Password Strength Indicator -->
    <div class="mt-2">
      <div class="w-full bg-gray-200 rounded-full h-2">
        <div class="h-2 rounded-full transition-all duration-300"
             data-auth-target="strengthIndicator"></div>
      </div>
      <p class="text-sm mt-1" data-auth-target="strengthText"></p>
    </div>
  </div>

  <!-- Password Confirmation Field -->
  <div class="form-group">
    <%= f.label :password_confirmation, class: "block text-sm font-medium text-gray-700 mb-2" do %>
      <span>Confirm password</span>
      <span class="text-red-500">*</span>
    <% end %>
    <div class="relative">
      <%= f.password_field :password_confirmation,
          autocomplete: "new-password",
          required: true,
          class: "input-focus w-full px-4 py-3 pr-12 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200",
          placeholder: "Confirm your password",
          data: {
            auth_target: "passwordConfirmation",
            action: "focus->auth#focusField blur->auth#blurField input->auth#validateField"
          } %>
    </div>
  </div>

  <!-- Terms and Privacy -->
  <div class="form-group">
    <div class="flex items-start">
      <div class="flex items-center h-5">
        <input id="terms_accepted"
               name="terms_accepted"
               type="checkbox"
               required
               class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
      </div>
      <div class="ml-3 text-sm">
        <label for="terms_accepted" class="text-gray-700 cursor-pointer">
          I agree to the
          <a href="#" class="text-blue-600 hover:text-blue-500 font-medium">Terms of Service</a>
          and
          <a href="#" class="text-blue-600 hover:text-blue-500 font-medium">Privacy Policy</a>
          <span class="text-red-500">*</span>
        </label>
      </div>
    </div>
  </div>

  <!-- Marketing Consent -->
  <div class="form-group">
    <div class="flex items-start">
      <div class="flex items-center h-5">
        <input id="marketing_consent"
               name="marketing_consent"
               type="checkbox"
               class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
      </div>
      <div class="ml-3 text-sm">
        <label for="marketing_consent" class="text-gray-700 cursor-pointer">
          I'd like to receive product updates and marketing communications
        </label>
      </div>
    </div>
  </div>

  <!-- Submit Button -->
  <div>
    <%= f.submit "Create Account",
        class: "w-full flex justify-center py-3 px-4 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200 form-transition",
        data: { auth_target: "submitButton" } %>
  </div>
<% end %>

<!-- Sign In Link -->
<div class="mt-6 text-center">
  <p class="text-sm text-gray-600">
    Already have an account?
    <%= link_to "Sign in here", new_session_path(resource_name),
        class: "font-medium text-blue-600 hover:text-blue-500 transition-colors" %>
  </p>
</div>

<!-- Additional Information -->
<div class="mt-6 text-center">
  <p class="text-xs text-gray-500">
    By creating an account, you'll be able to connect your data sources and start building pipelines in minutes.
  </p>
</div>

<% content_for :title, "Sign In - DataReflow" %>

<div class="text-center mb-8">
  <h1 class="text-3xl font-bold text-gray-900 mb-2">Welcome back</h1>
  <p class="text-gray-600">Sign in to your DataReflow account</p>
</div>

<%= render "shared/flash_messages" if respond_to?(:flash) %>

<%= form_with model: resource, as: resource_name, url: session_path(resource_name), local: true,
             html: {
               class: "space-y-6"
             } do |f| %>

  <!-- Email Field -->
  <div class="form-group">
    <%= f.label :email, class: "block text-sm font-medium text-gray-700 mb-2" do %>
      <span>Email address</span>
      <span class="text-red-500">*</span>
    <% end %>
    <%= f.email_field :email,
        autofocus: true,
        autocomplete: "email",
        required: true,
        class: "input-focus w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200",
        placeholder: "Enter your email address",
        data: {
          action: "focus->auth#focusField blur->auth#blurField input->auth#validateField"
        } %>
  </div>

  <!-- Password Field -->
  <div class="form-group">
    <%= f.label :password, class: "block text-sm font-medium text-gray-700 mb-2" do %>
      <span>Password</span>
      <span class="text-red-500">*</span>
    <% end %>
    <div class="relative">
      <%= f.password_field :password,
          autocomplete: "current-password",
          required: true,
          class: "input-focus w-full px-4 py-3 pr-12 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200",
          placeholder: "Enter your password",
          data: {
            auth_target: "password",
            action: "focus->auth#focusField blur->auth#blurField input->auth#validateField"
          } %>
      <button type="button"
              class="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600 transition-colors"
              data-action="click->auth#togglePasswordVisibility"
              data-auth-target="toggleIcon">
        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
        </svg>
      </button>
    </div>
  </div>

  <!-- Remember Me & Forgot Password -->
  <div class="flex items-center justify-between">
    <% if devise_mapping.rememberable? %>
      <div class="flex items-center">
        <%= f.check_box :remember_me,
            class: "h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded" %>
        <%= f.label :remember_me, "Remember me",
            class: "ml-2 block text-sm text-gray-700 cursor-pointer" %>
      </div>
    <% end %>

    <div class="text-sm">
      <%= link_to "Forgot password?", new_password_path(resource_name),
          class: "text-blue-600 hover:text-blue-500 font-medium transition-colors" %>
    </div>
  </div>

  <!-- Submit Button -->
  <div>
    <%= f.submit "Sign In",
        class: "w-full flex justify-center py-3 px-4 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200" %>
  </div>
<% end %>

<!-- Sign Up Link -->
<div class="mt-6 text-center">
  <p class="text-sm text-gray-600">
    Don't have an account?
    <%= link_to "Sign up here", new_registration_path(resource_name),
        class: "font-medium text-blue-600 hover:text-blue-500 transition-colors" %>
  </p>
</div>

<!-- Additional Links -->
<div class="mt-4 text-center space-y-2">
  <% if devise_mapping.confirmable? && controller_name != 'confirmations' %>
    <div>
      <%= link_to "Didn't receive confirmation instructions?", new_confirmation_path(resource_name),
          class: "text-sm text-gray-500 hover:text-gray-700 transition-colors" %>
    </div>
  <% end %>

  <% if devise_mapping.lockable? && resource_class.unlock_strategy_enabled?(:email) && controller_name != 'unlocks' %>
    <div>
      <%= link_to "Didn't receive unlock instructions?", new_unlock_path(resource_name),
          class: "text-sm text-gray-500 hover:text-gray-700 transition-colors" %>
    </div>
  <% end %>
</div>

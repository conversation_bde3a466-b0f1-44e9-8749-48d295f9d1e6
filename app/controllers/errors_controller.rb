class ErrorsController < ApplicationController
  skip_before_action :authenticate_user!, if: :devise_controller?

  layout "error"

  def not_found
    @error_code = 404
    @error_title = "Page Not Found"
    @error_message = "The page you're looking for doesn't exist. It might have been moved, deleted, or you may have typed the wrong URL."

    respond_to do |format|
      format.html { render status: 404 }
      format.json { render json: { error: "Not found", code: 404 }, status: 404 }
      format.all { render status: 404, body: nil }
    end
  end

  def unprocessable_entity
    @error_code = 422
    @error_title = "Unprocessable Entity"
    @error_message = "The request was well-formed but was unable to be followed due to semantic errors."

    respond_to do |format|
      format.html { render status: 422 }
      format.json { render json: { error: "Unprocessable entity", code: 422 }, status: 422 }
      format.all { render status: 422, body: nil }
    end
  end

  def internal_server_error
    @error_code = 500
    @error_title = "Internal Server Error"
    @error_message = "Something went wrong on our end. Our team has been notified and is working to fix the issue."

    respond_to do |format|
      format.html { render status: 500 }
      format.json { render json: { error: "Internal server error", code: 500 }, status: 500 }
      format.all { render status: 500, body: nil }
    end
  end

  private

  def show_subdomain_navigation?
    false
  end
end

class NotificationsController < ApplicationController
  layout 'dashboard'

  before_action :authenticate_user!
  before_action :ensure_account_access
  before_action :set_notification, only: [ :show, :mark_as_read, :mark_as_unread, :destroy ]

  def index
    @notifications = current_account.notifications
                                   .for_user(current_user)
                                   .recent
                                   .includes(:notifiable)
                                   .limit(20)
                                   .offset(params[:page].to_i * 20)

    @unread_count = current_account.notifications
                                  .for_user(current_user)
                                  .unread
                                  .count

    respond_to do |format|
      format.html
      format.json { render json: notifications_json }
    end
  end

  def show
    @notification.mark_as_read! if @notification.unread?

    respond_to do |format|
      format.html { redirect_to @notification.action_url || notifications_path }
      format.json { render json: notification_json(@notification) }
    end
  end

  def unread
    @notifications = current_account.notifications
                                   .for_user(current_user)
                                   .unread
                                   .recent
                                   .includes(:notifiable)
                                   .limit(10)

    @unread_count = @notifications.count

    respond_to do |format|
      format.json { render json: unread_notifications_json }
    end
  end

  def mark_as_read
    @notification.mark_as_read!

    respond_to do |format|
      format.json { render json: { status: "success", read: true } }
      format.html { redirect_back(fallback_location: notifications_path) }
    end
  end

  def mark_as_unread
    @notification.mark_as_unread!

    respond_to do |format|
      format.json { render json: { status: "success", read: false } }
      format.html { redirect_back(fallback_location: notifications_path) }
    end
  end

  def mark_all_as_read
    current_account.notifications
                   .for_user(current_user)
                   .unread
                   .update_all(read_at: Time.current)

    respond_to do |format|
      format.json { render json: { status: "success", message: "All notifications marked as read" } }
      format.html { redirect_to notifications_path, notice: "All notifications marked as read" }
    end
  end

  def destroy
    @notification.destroy

    respond_to do |format|
      format.json { render json: { status: "success", message: "Notification deleted" } }
      format.html { redirect_to notifications_path, notice: "Notification deleted" }
    end
  end

  def count
    unread_count = current_account.notifications
                                 .for_user(current_user)
                                 .unread
                                 .count

    respond_to do |format|
      format.json { render json: { unread_count: unread_count } }
    end
  end

  private

  def ensure_account_access
    redirect_to root_path unless current_account
  end

  def set_notification
    @notification = current_account.notifications
                                  .for_user(current_user)
                                  .find(params[:id])
  rescue ActiveRecord::RecordNotFound
    respond_to do |format|
      format.json { render json: { error: "Notification not found" }, status: :not_found }
      format.html { redirect_to notifications_path, alert: "Notification not found" }
    end
  end

  def notifications_json
    total_count = current_account.notifications.for_user(current_user).count
    current_page = params[:page].to_i
    per_page = 20
    total_pages = (total_count.to_f / per_page).ceil

    {
      notifications: @notifications.map { |notification| notification_json(notification) },
      unread_count: @unread_count,
      pagination: {
        current_page: current_page,
        total_pages: total_pages,
        total_count: total_count,
        per_page: per_page
      },
      meta: {
        generated_at: Time.current
      }
    }
  end

  def unread_notifications_json
    {
      notifications: @notifications.map { |notification| notification_json(notification) },
      unread_count: @unread_count,
      meta: {
        generated_at: Time.current,
        has_more: @unread_count > 10
      }
    }
  end

  def notification_json(notification)
    {
      id: notification.id,
      title: notification.title,
      message: notification.message,
      notification_type: notification.notification_type,
      priority: notification.priority,
      read: notification.read?,
      time_ago: notification.time_ago,
      created_at: notification.created_at.iso8601,
      action_url: notification.action_url,
      icon_class: notification.icon_class,
      icon_svg: notification.icon_svg,
      notifiable: notification.notifiable ? {
        type: notification.notifiable_type,
        id: notification.notifiable_id,
        name: notification.notifiable.try(:name) || notification.notifiable.to_s
      } : nil
    }
  end
end

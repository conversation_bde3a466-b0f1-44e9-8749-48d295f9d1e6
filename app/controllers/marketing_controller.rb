class MarketingController < ApplicationController
  skip_before_action :authenticate_user!

  def index
    # If user is logged in, show them a dashboard or account info
    if user_signed_in?
      # For now, show a simple logged-in state instead of redirecting to subdomain
      # This avoids the cross-domain redirect issues
      @user = current_user
      @account = current_user.account
      render "dashboard"
      return
    end

    # Marketing landing page for non-authenticated users
    # Set up data for the landing page
    @features = landing_page_features
    @testimonials = landing_page_testimonials
    @stats = landing_page_stats
    @integrations = popular_integrations
  end

  def about
    # About page data
    @team_members = team_members_data
    @company_values = company_values_data
    @milestones = company_milestones_data
    @stats = company_stats_data
  end

  def contact
    # Contact page - show form
    @contact_form = ContactForm.new
  end

  def create_contact
    # Handle contact form submission
    @contact_form = ContactForm.new(contact_params)

    if @contact_form.valid?
      # Here you would typically send an email or save to database
      # For now, we'll just set a success message
      flash[:notice] = "Thank you for your message! We'll get back to you within 24 hours."
      redirect_to contact_path
    else
      flash.now[:alert] = "Please correct the errors below."
      render :contact, status: :unprocessable_entity
    end
  end

  def help
    # Help center page data
    @faq_categories = faq_categories_data
    @popular_articles = popular_articles_data
    @getting_started_guides = getting_started_guides_data
  end

  def integrations
    # Integrations page data
    @integration_categories = integration_categories_data
    @featured_integrations = featured_integrations_data
    @integration_stats = integration_stats_data
    @upcoming_integrations = upcoming_integrations_data
  end

  def security
    # Security page data
    @security_features = security_features_data
    @compliance_certifications = compliance_certifications_data
    @security_practices = security_practices_data
  end

  def privacy
    # Privacy policy page data
    @last_updated = Date.new(2024, 1, 15)
    @privacy_sections = privacy_sections_data
  end

  def docs
    # API Documentation page data
    @api_sections = api_sections_data
    @code_examples = code_examples_data
    @api_endpoints = api_endpoints_data
    @sdk_languages = sdk_languages_data
  end

  def community
    # Community page data
    @community_stats = community_stats_data
    @community_channels = community_channels_data
    @featured_discussions = featured_discussions_data
    @community_events = community_events_data
    @contributor_spotlights = contributor_spotlights_data
  end

  def press
    # Press page data
    @press_releases = press_releases_data
    @media_coverage = media_coverage_data
    @company_facts = company_facts_data
    @press_kit_assets = press_kit_assets_data
    @executive_bios = executive_bios_data
  end

  private

  def landing_page_features
    [
      {
        icon: "lightning",
        title: "10-Minute Setup",
        description: "From signup to first data sync in under 10 minutes. No technical expertise required.",
        color: "blue"
      },
      {
        icon: "shield-check",
        title: "Enterprise Security",
        description: "Bank-grade encryption, SOC 2 compliance, and GDPR ready data protection.",
        color: "green"
      },
      {
        icon: "puzzle",
        title: "200+ Connectors",
        description: "Connect to all your favorite tools: Shopify, HubSpot, QuickBooks, and more.",
        color: "purple"
      },
      {
        icon: "chart-bar",
        title: "Real-time Analytics",
        description: "Monitor data flows, track performance, and get insights with live dashboards.",
        color: "indigo"
      },
      {
        icon: "code",
        title: "No-Code Builder",
        description: "Visual drag-and-drop interface for creating complex data pipelines.",
        color: "orange"
      },
      {
        icon: "clock",
        title: "Automated Workflows",
        description: "Set up once and let DataReflow handle your data synchronization 24/7.",
        color: "teal"
      }
    ]
  end

  def landing_page_testimonials
    [
      {
        name: "Sarah Chen",
        role: "Operations Director",
        company: "TechFlow Solutions",
        content: "DataReflow reduced our data integration time from weeks to hours. The visual pipeline builder is incredibly intuitive.",
        avatar: "SC"
      },
      {
        name: "Marcus Rodriguez",
        role: "CTO",
        company: "GrowthLabs",
        content: "Finally, a data integration platform that doesn't require a team of engineers. Our sales and marketing data is now perfectly synced.",
        avatar: "MR"
      },
      {
        name: "Emily Watson",
        role: "Data Analyst",
        company: "RetailMax",
        content: "The real-time analytics and automated workflows have transformed how we handle customer data across our systems.",
        avatar: "EW"
      }
    ]
  end

  def landing_page_stats
    {
      customers: "2,500+",
      data_synced: "50M+",
      uptime: "99.9%",
      integrations: "200+"
    }
  end

  def popular_integrations
    [
      { name: "Shopify", category: "E-commerce" },
      { name: "HubSpot", category: "CRM" },
      { name: "QuickBooks", category: "Accounting" },
      { name: "Salesforce", category: "CRM" },
      { name: "Stripe", category: "Payments" },
      { name: "Mailchimp", category: "Marketing" },
      { name: "Slack", category: "Communication" },
      { name: "Google Analytics", category: "Analytics" }
    ]
  end

  def team_members_data
    [
      {
        name: "Sarah Chen",
        role: "CEO & Co-Founder",
        bio: "Former VP of Engineering at TechFlow. 15+ years building data infrastructure for Fortune 500 companies.",
        image: "SC",
        linkedin: "#"
      },
      {
        name: "Marcus Rodriguez",
        role: "CTO & Co-Founder",
        bio: "Ex-Google engineer with expertise in distributed systems and real-time data processing.",
        image: "MR",
        linkedin: "#"
      },
      {
        name: "Emily Watson",
        role: "Head of Product",
        bio: "Product leader with 10+ years at Salesforce, specializing in enterprise SaaS platforms.",
        image: "EW",
        linkedin: "#"
      },
      {
        name: "David Kim",
        role: "Head of Engineering",
        bio: "Full-stack engineer and architect, previously at Stripe building payment infrastructure.",
        image: "DK",
        linkedin: "#"
      }
    ]
  end

  def company_values_data
    [
      {
        icon: "users",
        title: "Customer First",
        description: "Every decision we make starts with how it benefits our customers and their success."
      },
      {
        icon: "shield-check",
        title: "Security & Privacy",
        description: "Your data security is our top priority. We maintain the highest standards of protection."
      },
      {
        icon: "lightning",
        title: "Speed & Simplicity",
        description: "Complex problems deserve simple solutions. We make data integration effortless."
      },
      {
        icon: "globe",
        title: "Transparency",
        description: "Open communication, clear pricing, and honest feedback drive everything we do."
      }
    ]
  end

  def company_milestones_data
    [
      {
        year: "2022",
        title: "Company Founded",
        description: "DataReflow was born from the frustration of spending weeks on data integration projects."
      },
      {
        year: "2023",
        title: "First 100 Customers",
        description: "Reached our first major milestone with businesses across 15 countries."
      },
      {
        year: "2023",
        title: "Series A Funding",
        description: "Raised $5M to accelerate product development and expand our integration library."
      },
      {
        year: "2024",
        title: "200+ Integrations",
        description: "Launched comprehensive integration marketplace with major business platforms."
      },
      {
        year: "2024",
        title: "2,500+ Customers",
        description: "Serving businesses from startups to enterprises across 50+ countries."
      }
    ]
  end

  def company_stats_data
    {
      founded: "2022",
      employees: "25+",
      countries: "50+",
      funding: "$5M",
      customers: "2,500+",
      integrations: "200+"
    }
  end

  def faq_categories_data
    {
      "Getting Started" => [
        {
          question: "How quickly can I set up my first data pipeline?",
          answer: "Most customers have their first pipeline running within 10 minutes. Our guided setup walks you through connecting your first two applications and configuring basic data sync."
        },
        {
          question: "Do I need technical knowledge to use DataReflow?",
          answer: "No coding or technical expertise required! Our visual pipeline builder uses drag-and-drop interface. If you can use spreadsheet software, you can use DataReflow."
        },
        {
          question: "What happens during the free trial?",
          answer: "Your 14-day free trial includes full access to all Professional features, up to 10,000 records, and priority support. No credit card required to start."
        }
      ],
      "Integrations" => [
        {
          question: "How many integrations do you support?",
          answer: "We currently support 200+ integrations including all major business platforms like Shopify, Salesforce, HubSpot, QuickBooks, and more. We add new integrations weekly."
        },
        {
          question: "Can you build custom integrations?",
          answer: "Yes! Enterprise customers can request custom integrations. We also offer API access for building your own connections using our integration framework."
        },
        {
          question: "How do you handle API rate limits?",
          answer: "DataReflow automatically manages API rate limits, implements intelligent retry logic, and optimizes sync schedules to ensure reliable data flow without hitting platform limits."
        }
      ],
      "Billing" => [
        {
          question: "How does pricing work?",
          answer: "Pricing is based on the number of records processed monthly. Start free with 1,000 records, then scale with our Professional ($49/month) or Enterprise (custom) plans."
        },
        {
          question: "Can I change plans anytime?",
          answer: "Yes, you can upgrade or downgrade your plan at any time. Changes take effect immediately, and billing is prorated for the current month."
        },
        {
          question: "What payment methods do you accept?",
          answer: "We accept all major credit cards (Visa, MasterCard, American Express) and ACH transfers for Enterprise customers. All payments are processed securely through Stripe."
        }
      ],
      "Technical" => [
        {
          question: "How do you ensure data security?",
          answer: "We use bank-grade encryption (AES-256), SOC 2 compliance, and never store your actual business data. All connections use OAuth or API keys, and data is encrypted in transit and at rest."
        },
        {
          question: "What is your uptime guarantee?",
          answer: "We maintain 99.9% uptime with redundant infrastructure across multiple regions. Enterprise customers receive SLA guarantees with compensation for any downtime."
        },
        {
          question: "Can I export my data and configurations?",
          answer: "Yes, you own your data and configurations. You can export pipeline configurations, sync logs, and all metadata at any time through our dashboard or API."
        }
      ]
    }
  end

  def popular_articles_data
    [
      {
        title: "Getting Started with DataReflow",
        description: "Complete guide to setting up your first data pipeline in under 10 minutes.",
        category: "Getting Started",
        read_time: "5 min read"
      },
      {
        title: "Shopify to HubSpot Integration Guide",
        description: "Step-by-step tutorial for syncing customer data between Shopify and HubSpot.",
        category: "Integrations",
        read_time: "8 min read"
      },
      {
        title: "Data Transformation Best Practices",
        description: "Learn how to clean, format, and transform your data for optimal results.",
        category: "Advanced",
        read_time: "12 min read"
      },
      {
        title: "Troubleshooting Common Sync Issues",
        description: "Solutions for the most common data synchronization problems and errors.",
        category: "Technical",
        read_time: "6 min read"
      }
    ]
  end

  def getting_started_guides_data
    [
      {
        title: "Quick Start Guide",
        description: "Get up and running with your first pipeline",
        icon: "lightning",
        steps: 3
      },
      {
        title: "Integration Setup",
        description: "Connect your business applications",
        icon: "puzzle",
        steps: 5
      },
      {
        title: "Data Transformation",
        description: "Clean and format your data",
        icon: "code",
        steps: 4
      },
      {
        title: "Monitoring & Alerts",
        description: "Set up monitoring for your pipelines",
        icon: "chart-bar",
        steps: 3
      }
    ]
  end

  def contact_params
    params.require(:contact_form).permit(:name, :email, :company, :inquiry_type, :message)
  end

  def integration_categories_data
    {
      "E-commerce" => [
        { name: "Shopify", description: "Complete e-commerce platform", logo: "shopify", popular: true },
        { name: "WooCommerce", description: "WordPress e-commerce plugin", logo: "woocommerce", popular: true },
        { name: "Magento", description: "Enterprise e-commerce solution", logo: "magento", popular: false },
        { name: "BigCommerce", description: "Cloud e-commerce platform", logo: "bigcommerce", popular: false }
      ],
      "CRM & Sales" => [
        { name: "Salesforce", description: "Leading CRM platform", logo: "salesforce", popular: true },
        { name: "HubSpot", description: "Inbound marketing and sales", logo: "hubspot", popular: true },
        { name: "Pipedrive", description: "Sales-focused CRM", logo: "pipedrive", popular: false },
        { name: "Zoho CRM", description: "Business management suite", logo: "zoho", popular: false }
      ],
      "Accounting & Finance" => [
        { name: "QuickBooks", description: "Small business accounting", logo: "quickbooks", popular: true },
        { name: "Xero", description: "Cloud accounting software", logo: "xero", popular: true },
        { name: "FreshBooks", description: "Invoicing and time tracking", logo: "freshbooks", popular: false },
        { name: "Sage", description: "Enterprise accounting solution", logo: "sage", popular: false }
      ],
      "Marketing & Analytics" => [
        { name: "Google Analytics", description: "Web analytics platform", logo: "google-analytics", popular: true },
        { name: "Mailchimp", description: "Email marketing platform", logo: "mailchimp", popular: true },
        { name: "Facebook Ads", description: "Social media advertising", logo: "facebook", popular: false },
        { name: "Google Ads", description: "Search advertising platform", logo: "google-ads", popular: false }
      ],
      "Communication" => [
        { name: "Slack", description: "Team communication platform", logo: "slack", popular: true },
        { name: "Microsoft Teams", description: "Collaboration platform", logo: "teams", popular: false },
        { name: "Discord", description: "Community communication", logo: "discord", popular: false },
        { name: "Zoom", description: "Video conferencing", logo: "zoom", popular: false }
      ],
      "Payments" => [
        { name: "Stripe", description: "Online payment processing", logo: "stripe", popular: true },
        { name: "PayPal", description: "Digital payment platform", logo: "paypal", popular: true },
        { name: "Square", description: "Point of sale and payments", logo: "square", popular: false },
        { name: "Braintree", description: "Payment gateway solution", logo: "braintree", popular: false }
      ]
    }
  end

  def featured_integrations_data
    [
      {
        name: "Shopify to HubSpot",
        description: "Sync customer data and order information between your e-commerce store and CRM",
        setup_time: "5 minutes",
        popularity: "Most Popular",
        use_cases: [ "Customer segmentation", "Order tracking", "Marketing automation" ]
      },
      {
        name: "QuickBooks to Salesforce",
        description: "Connect your accounting data with sales pipeline for complete financial visibility",
        setup_time: "8 minutes",
        popularity: "Trending",
        use_cases: [ "Revenue tracking", "Invoice management", "Financial reporting" ]
      },
      {
        name: "Stripe to Google Analytics",
        description: "Track payment events and revenue data in your analytics dashboard",
        setup_time: "3 minutes",
        popularity: "New",
        use_cases: [ "Revenue analytics", "Conversion tracking", "Customer insights" ]
      }
    ]
  end

  def integration_stats_data
    {
      total_integrations: "200+",
      categories: "12",
      new_monthly: "8-12",
      setup_time: "< 10 min",
      success_rate: "99.8%",
      data_processed: "50M+ records/month"
    }
  end

  def upcoming_integrations_data
    [
      { name: "Notion", category: "Productivity", eta: "Q1 2025" },
      { name: "Airtable", category: "Database", eta: "Q1 2025" },
      { name: "Figma", category: "Design", eta: "Q2 2025" },
      { name: "Linear", category: "Project Management", eta: "Q2 2025" }
    ]
  end

  def security_features_data
    [
      {
        icon: "shield-check",
        title: "End-to-End Encryption",
        description: "All data is encrypted in transit using TLS 1.3 and at rest using AES-256 encryption.",
        details: [ "TLS 1.3 for data in transit", "AES-256 encryption at rest", "Zero-knowledge architecture", "Encrypted database backups" ]
      },
      {
        icon: "users",
        title: "Access Controls",
        description: "Granular permissions and role-based access control for your team members.",
        details: [ "Role-based permissions", "Multi-factor authentication", "Single sign-on (SSO)", "Session management" ]
      },
      {
        icon: "globe",
        title: "Network Security",
        description: "Advanced network protection with DDoS mitigation and intrusion detection.",
        details: [ "DDoS protection", "Intrusion detection", "IP whitelisting", "VPN support" ]
      },
      {
        icon: "clock",
        title: "Monitoring & Auditing",
        description: "24/7 security monitoring with comprehensive audit logs and real-time alerts.",
        details: [ "Real-time monitoring", "Comprehensive audit logs", "Automated alerts", "Incident response" ]
      }
    ]
  end

  def compliance_certifications_data
    [
      {
        name: "SOC 2 Type II",
        description: "Independently audited security, availability, and confidentiality controls",
        status: "Certified",
        icon: "shield-check"
      },
      {
        name: "GDPR Compliant",
        description: "Full compliance with European data protection regulations",
        status: "Compliant",
        icon: "globe"
      },
      {
        name: "CCPA Compliant",
        description: "California Consumer Privacy Act compliance for US customers",
        status: "Compliant",
        icon: "users"
      },
      {
        name: "ISO 27001",
        description: "International standard for information security management",
        status: "In Progress",
        icon: "check-circle"
      }
    ]
  end

  def security_practices_data
    [
      {
        category: "Data Protection",
        practices: [
          "Regular security audits and penetration testing",
          "Automated vulnerability scanning",
          "Secure code review processes",
          "Data minimization and retention policies"
        ]
      },
      {
        category: "Infrastructure Security",
        practices: [
          "Multi-region data replication",
          "Automated backup and disaster recovery",
          "Container security and isolation",
          "Regular security updates and patches"
        ]
      },
      {
        category: "Operational Security",
        practices: [
          "Background checks for all employees",
          "Security awareness training",
          "Incident response procedures",
          "Business continuity planning"
        ]
      }
    ]
  end

  def privacy_sections_data
    [
      {
        title: "Information We Collect",
        content: "We collect information you provide directly to us, such as when you create an account, use our services, or contact us for support. This includes your name, email address, company information, and any data you choose to sync through our platform."
      },
      {
        title: "How We Use Your Information",
        content: "We use the information we collect to provide, maintain, and improve our services, process transactions, send you technical notices and support messages, and communicate with you about products, services, and promotional offers."
      },
      {
        title: "Information Sharing and Disclosure",
        content: "We do not sell, trade, or otherwise transfer your personal information to third parties without your consent, except as described in this policy. We may share your information with service providers who assist us in operating our platform."
      },
      {
        title: "Data Security",
        content: "We implement appropriate technical and organizational measures to protect your personal information against unauthorized access, alteration, disclosure, or destruction. This includes encryption, access controls, and regular security assessments."
      },
      {
        title: "Your Rights and Choices",
        content: "You have the right to access, update, or delete your personal information. You may also opt out of certain communications from us. For EU residents, you have additional rights under GDPR including data portability and the right to be forgotten."
      },
      {
        title: "Data Retention",
        content: "We retain your personal information for as long as necessary to provide our services and fulfill the purposes outlined in this policy, unless a longer retention period is required by law."
      },
      {
        title: "International Data Transfers",
        content: "Your information may be transferred to and processed in countries other than your own. We ensure appropriate safeguards are in place to protect your information in accordance with this policy."
      },
      {
        title: "Changes to This Policy",
        content: 'We may update this privacy policy from time to time. We will notify you of any material changes by posting the new policy on this page and updating the "Last Updated" date.'
      }
    ]
  end

  def api_sections_data
    [
      {
        title: "Getting Started",
        description: "Quick start guide to using the DataReflow API",
        icon: "lightning",
        items: [
          { title: "Authentication", description: "API key setup and authentication methods" },
          { title: "Making Your First Request", description: "Step-by-step guide to your first API call" },
          { title: "Rate Limits", description: "Understanding API rate limits and best practices" },
          { title: "Error Handling", description: "Common errors and how to handle them" }
        ]
      },
      {
        title: "Core Resources",
        description: "Main API endpoints for data integration",
        icon: "puzzle",
        items: [
          { title: "Connections", description: "Manage integrations between your applications" },
          { title: "Pipelines", description: "Create and manage data transformation pipelines" },
          { title: "Sync Jobs", description: "Monitor and control data synchronization" },
          { title: "Webhooks", description: "Real-time notifications for data events" }
        ]
      },
      {
        title: "Advanced Features",
        description: "Advanced API capabilities for complex workflows",
        icon: "code",
        items: [
          { title: "Custom Transformations", description: "Build custom data transformation logic" },
          { title: "Batch Operations", description: "Process large datasets efficiently" },
          { title: "Real-time Streaming", description: "Stream data in real-time using WebSockets" },
          { title: "Custom Connectors", description: "Build your own integration connectors" }
        ]
      }
    ]
  end

  def code_examples_data
    [
      {
        language: "curl",
        title: "Create a Connection",
        code: 'curl -X POST https://api.datareflow.io/v1/connections \\
  -H "Authorization: Bearer YOUR_API_KEY" \\
  -H "Content-Type: application/json" \\
  -d \'{
    "source": {
      "type": "shopify",
      "config": {
        "shop_domain": "your-shop.myshopify.com",
        "access_token": "your_access_token"
      }
    },
    "destination": {
      "type": "hubspot",
      "config": {
        "api_key": "your_hubspot_key"
      }
    }
  }\''
      },
      {
        language: "javascript",
        title: "List Sync Jobs",
        code: 'const response = await fetch(\'https://api.datareflow.io/v1/sync-jobs\', {
  headers: {
    \'Authorization\': \'Bearer YOUR_API_KEY\',
    \'Content-Type\': \'application/json\'
  }
});

const syncJobs = await response.json();
console.log(syncJobs);'
      },
      {
        language: "python",
        title: "Create a Pipeline",
        code: 'import requests

url = "https://api.datareflow.io/v1/pipelines"
headers = {
    "Authorization": "Bearer YOUR_API_KEY",
    "Content-Type": "application/json"
}

data = {
    "name": "Customer Data Pipeline",
    "source_connection_id": "conn_123",
    "destination_connection_id": "conn_456",
    "transformations": [
        {
            "type": "field_mapping",
            "config": {
                "email": "customer_email",
                "name": "full_name"
            }
        }
    ]
}

response = requests.post(url, headers=headers, json=data)
pipeline = response.json()'
      }
    ]
  end

  def api_endpoints_data
    [
      {
        category: "Authentication",
        endpoints: [
          { method: "POST", path: "/auth/token", description: "Generate API access token" },
          { method: "POST", path: "/auth/refresh", description: "Refresh expired token" }
        ]
      },
      {
        category: "Connections",
        endpoints: [
          { method: "GET", path: "/connections", description: "List all connections" },
          { method: "POST", path: "/connections", description: "Create new connection" },
          { method: "GET", path: "/connections/{id}", description: "Get connection details" },
          { method: "PUT", path: "/connections/{id}", description: "Update connection" },
          { method: "DELETE", path: "/connections/{id}", description: "Delete connection" }
        ]
      },
      {
        category: "Pipelines",
        endpoints: [
          { method: "GET", path: "/pipelines", description: "List all pipelines" },
          { method: "POST", path: "/pipelines", description: "Create new pipeline" },
          { method: "GET", path: "/pipelines/{id}", description: "Get pipeline details" },
          { method: "PUT", path: "/pipelines/{id}", description: "Update pipeline" },
          { method: "POST", path: "/pipelines/{id}/run", description: "Execute pipeline" }
        ]
      }
    ]
  end

  def sdk_languages_data
    [
      {
        name: "JavaScript/Node.js",
        description: "Official SDK for JavaScript and Node.js applications",
        install_command: "npm install @datareflow/sdk",
        github_url: "https://github.com/datareflow/sdk-javascript",
        status: "stable"
      },
      {
        name: "Python",
        description: "Official SDK for Python applications",
        install_command: "pip install datareflow-sdk",
        github_url: "https://github.com/datareflow/sdk-python",
        status: "stable"
      },
      {
        name: "Ruby",
        description: "Official SDK for Ruby applications",
        install_command: "gem install datareflow",
        github_url: "https://github.com/datareflow/sdk-ruby",
        status: "beta"
      },
      {
        name: "Go",
        description: "Official SDK for Go applications",
        install_command: "go get github.com/datareflow/sdk-go",
        github_url: "https://github.com/datareflow/sdk-go",
        status: "beta"
      }
    ]
  end

  def community_stats_data
    {
      members: "5,200+",
      discussions: "1,800+",
      integrations_shared: "150+",
      monthly_active: "2,100+",
      countries: "45+",
      avg_response_time: "< 2 hours"
    }
  end

  def community_channels_data
    [
      {
        name: "Discord Server",
        description: "Real-time chat with the DataReflow community and team",
        members: "3,200+",
        icon: "users",
        link: "#",
        activity: "Very Active"
      },
      {
        name: "GitHub Discussions",
        description: "Technical discussions, feature requests, and open source contributions",
        members: "1,500+",
        icon: "code",
        link: "#",
        activity: "Active"
      },
      {
        name: "Community Forum",
        description: "Long-form discussions, tutorials, and community support",
        members: "2,800+",
        icon: "globe",
        link: "#",
        activity: "Active"
      },
      {
        name: "LinkedIn Group",
        description: "Professional networking and industry insights",
        members: "800+",
        icon: "users",
        link: "#",
        activity: "Moderate"
      }
    ]
  end

  def featured_discussions_data
    [
      {
        title: "Best Practices for Shopify to HubSpot Integration",
        author: "Sarah Chen",
        replies: 23,
        views: 1240,
        category: "Integrations",
        last_activity: "2 hours ago"
      },
      {
        title: "Custom Transformation Functions - Complete Guide",
        author: "Marcus Rodriguez",
        replies: 18,
        views: 890,
        category: "Development",
        last_activity: "4 hours ago"
      },
      {
        title: "Handling Large Dataset Synchronization",
        author: "Emily Watson",
        replies: 31,
        views: 1560,
        category: "Performance",
        last_activity: "6 hours ago"
      },
      {
        title: "API Rate Limiting Best Practices",
        author: "David Kim",
        replies: 15,
        views: 720,
        category: "API",
        last_activity: "1 day ago"
      }
    ]
  end

  def community_events_data
    [
      {
        title: "DataReflow Developer Meetup",
        date: "March 15, 2025",
        time: "6:00 PM PST",
        type: "Virtual",
        description: "Monthly meetup for developers building with DataReflow",
        attendees: 150
      },
      {
        title: "Integration Workshop: E-commerce Data",
        date: "March 22, 2025",
        time: "2:00 PM EST",
        type: "Workshop",
        description: "Hands-on workshop for e-commerce data integration patterns",
        attendees: 75
      },
      {
        title: "Community Office Hours",
        date: "Every Friday",
        time: "10:00 AM PST",
        type: "Office Hours",
        description: "Weekly Q&A session with the DataReflow team",
        attendees: 40
      }
    ]
  end

  def contributor_spotlights_data
    [
      {
        name: "Alex Thompson",
        role: "Community Moderator",
        contributions: "Created 15+ integration tutorials, helped 200+ community members",
        avatar: "AT",
        location: "San Francisco, CA"
      },
      {
        name: "Maria Garcia",
        role: "Integration Expert",
        contributions: "Built 8 custom connectors, contributed to SDK documentation",
        avatar: "MG",
        location: "Barcelona, Spain"
      },
      {
        name: "James Wilson",
        role: "Developer Advocate",
        contributions: "Organized 12 meetups, created video tutorial series",
        avatar: "JW",
        location: "London, UK"
      }
    ]
  end

  def press_releases_data
    [
      {
        title: "DataReflow Raises $5M Series A to Democratize Data Integration",
        date: "February 15, 2024",
        summary: "Funding will accelerate product development and expand integration marketplace to serve growing demand for no-code data solutions.",
        category: "Funding"
      },
      {
        title: "DataReflow Achieves SOC 2 Type II Certification",
        date: "January 8, 2024",
        summary: "Independent audit validates DataReflow's commitment to enterprise-grade security and data protection standards.",
        category: "Security"
      },
      {
        title: "DataReflow Surpasses 2,500 Active Customers Milestone",
        date: "December 12, 2023",
        summary: "Platform growth accelerates as businesses increasingly adopt no-code solutions for data integration challenges.",
        category: "Growth"
      },
      {
        title: "DataReflow Launches 200+ Integration Marketplace",
        date: "November 20, 2023",
        summary: "Comprehensive integration library now includes all major business applications with pre-built connectors and workflows.",
        category: "Product"
      }
    ]
  end

  def media_coverage_data
    [
      {
        publication: "TechCrunch",
        title: "DataReflow is making data integration accessible to non-technical teams",
        date: "February 20, 2024",
        author: "Sarah Perez",
        type: "Feature Article"
      },
      {
        publication: "VentureBeat",
        title: "The rise of no-code data integration platforms",
        date: "February 16, 2024",
        author: "Kyle Wiggers",
        type: "Industry Analysis"
      },
      {
        publication: "Forbes",
        title: "How DataReflow is democratizing enterprise data integration",
        date: "January 25, 2024",
        author: "Alex Konrad",
        type: "Profile"
      },
      {
        publication: "The Information",
        title: "DataReflow raises $5M as data integration market heats up",
        date: "February 15, 2024",
        author: "Kate Clark",
        type: "News"
      }
    ]
  end

  def company_facts_data
    {
      founded: "2022",
      headquarters: "San Francisco, CA",
      employees: "25+",
      customers: "2,500+",
      integrations: "200+",
      data_processed: "50M+ records/month",
      funding: "$5M Series A",
      investors: "Accel Partners, First Round Capital"
    }
  end

  def press_kit_assets_data
    [
      {
        name: "Company Logo Package",
        description: "High-resolution logos in various formats (PNG, SVG, EPS)",
        file_size: "2.3 MB",
        formats: [ "PNG", "SVG", "EPS" ]
      },
      {
        name: "Executive Headshots",
        description: "Professional headshots of key executives and founders",
        file_size: "8.7 MB",
        formats: [ "JPG", "PNG" ]
      },
      {
        name: "Product Screenshots",
        description: "High-quality screenshots of the DataReflow platform",
        file_size: "12.4 MB",
        formats: [ "PNG", "JPG" ]
      },
      {
        name: "Brand Guidelines",
        description: "Complete brand guidelines including colors, fonts, and usage",
        file_size: "1.8 MB",
        formats: [ "PDF" ]
      }
    ]
  end

  def executive_bios_data
    [
      {
        name: "Sarah Chen",
        title: "CEO & Co-Founder",
        bio: "Sarah is a seasoned technology executive with over 15 years of experience building data infrastructure for Fortune 500 companies. Prior to DataReflow, she served as VP of Engineering at TechFlow, where she led the development of enterprise data platforms serving millions of users. Sarah holds an MS in Computer Science from Stanford University.",
        image: "SC"
      },
      {
        name: "Marcus Rodriguez",
        title: "CTO & Co-Founder",
        bio: "Marcus is a former Google engineer with deep expertise in distributed systems and real-time data processing. He spent 8 years at Google working on large-scale data infrastructure projects before co-founding DataReflow. Marcus is passionate about making complex technology accessible to non-technical users. He holds a PhD in Computer Science from MIT.",
        image: "MR"
      },
      {
        name: "Emily Watson",
        title: "Head of Product",
        bio: "Emily brings over 10 years of product leadership experience from Salesforce, where she specialized in enterprise SaaS platforms. She has a proven track record of building products that scale from startup to enterprise customers. Emily is focused on creating intuitive user experiences for complex data integration workflows.",
        image: "EW"
      }
    ]
  end
end

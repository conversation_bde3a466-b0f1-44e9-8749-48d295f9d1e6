# frozen_string_literal: true

class HealthController < ApplicationController
  # Skip authentication for health checks
  skip_before_action :authenticate_user!
  skip_before_action :set_current_account
  skip_before_action :set_current_user
  
  # Skip CSRF protection for API endpoints
  protect_from_forgery except: [:show, :deep, :ready, :live]

  def show
    # Basic health check
    health_data = {
      status: "healthy",
      timestamp: Time.current.iso8601,
      version: Rails.application.class.module_parent_name,
      environment: Rails.env,
      checks: basic_health_checks
    }

    status_code = all_checks_passing?(health_data[:checks]) ? :ok : :service_unavailable
    
    respond_to do |format|
      format.json { render json: health_data, status: status_code }
      format.html { render json: health_data, status: status_code }
    end
  end

  def deep
    # Comprehensive health check including external dependencies
    health_data = {
      status: "healthy",
      timestamp: Time.current.iso8601,
      version: Rails.application.class.module_parent_name,
      environment: Rails.env,
      checks: deep_health_checks
    }

    status_code = all_checks_passing?(health_data[:checks]) ? :ok : :service_unavailable
    
    respond_to do |format|
      format.json { render json: health_data, status: status_code }
      format.html { render json: health_data, status: status_code }
    end
  end

  def ready
    # Kubernetes readiness probe
    ready_checks = {
      database: database_ready?,
      migrations: migrations_up_to_date?
    }

    status_code = ready_checks.values.all? ? :ok : :service_unavailable
    
    render json: {
      status: status_code == :ok ? "ready" : "not_ready",
      checks: ready_checks
    }, status: status_code
  end

  def live
    # Kubernetes liveness probe - basic aliveness check
    render json: {
      status: "alive",
      timestamp: Time.current.iso8601
    }, status: :ok
  end

  private

  def basic_health_checks
    {
      database: database_healthy?,
      cache: cache_healthy?,
      disk_space: disk_space_healthy?,
      memory: memory_healthy?
    }
  end

  def deep_health_checks
    basic_checks = basic_health_checks
    
    basic_checks.merge({
      active_job: active_job_healthy?,
      action_cable: action_cable_healthy?,
      external_apis: external_apis_healthy?,
      file_storage: file_storage_healthy?
    })
  end

  def all_checks_passing?(checks)
    checks.values.all? do |check|
      if check.is_a?(Hash)
        check[:status] == "healthy" || check[:status] == true
      else
        check == true
      end
    end
  end

  def database_healthy?
    ActiveRecord::Base.connection.execute("SELECT 1").present?
    true
  rescue => e
    log_health_check_failure("database", e)
    false
  end

  def database_ready?
    # Check if database is accessible and migrations are current
    ActiveRecord::Base.connection.execute("SELECT 1").present? && 
    ActiveRecord::Migration.current_version == ActiveRecord::Migrator.current_version
  rescue => e
    log_health_check_failure("database_ready", e)
    false
  end

  def migrations_up_to_date?
    ActiveRecord::Migration.current_version == ActiveRecord::Migrator.current_version
  rescue => e
    log_health_check_failure("migrations", e)
    false
  end

  def cache_healthy?
    cache_key = "health_check_#{SecureRandom.hex(4)}"
    Rails.cache.write(cache_key, "ok", expires_in: 1.minute)
    result = Rails.cache.read(cache_key) == "ok"
    Rails.cache.delete(cache_key)
    result
  rescue => e
    log_health_check_failure("cache", e)
    false
  end

  def disk_space_healthy?
    # Check available disk space (warn if less than 1GB)
    if defined?(Sys::Filesystem)
      stat = Sys::Filesystem.stat(Rails.root.to_s)
      available_gb = (stat.block_size * stat.blocks_available) / 1024 / 1024 / 1024
      available_gb > 1
    else
      # Return true if Sys::Filesystem gem not available
      true
    end
  rescue => e
    log_health_check_failure("disk_space", e)
    # Return true if we can't check (don't fail health check on monitoring issues)
    true
  end

  def memory_healthy?
    # Basic memory check
    if defined?(GC.stat)
      gc_stat = GC.stat
      heap_used = gc_stat[:heap_live_slots] || gc_stat[:total_allocated_objects] || 0
      # This is a basic check - in production you might want more sophisticated monitoring
      heap_used < 10_000_000 # Arbitrary threshold
    else
      true
    end
  rescue => e
    log_health_check_failure("memory", e)
    true
  end

  def active_job_healthy?
    # Check if Active Job backend is responding
    if defined?(Resque)
      Resque.redis.ping == "PONG"
    elsif defined?(Sidekiq)
      Sidekiq.redis(&:ping) == "PONG"
    else
      # For other backends or no background jobs, assume healthy
      true
    end
  rescue => e
    log_health_check_failure("active_job", e)
    false
  end

  def action_cable_healthy?
    # Check if ActionCable is working
    ActionCable.server.connections.is_a?(Array)
  rescue => e
    log_health_check_failure("action_cable", e)
    false
  end

  def external_apis_healthy?
    # Placeholder for external API health checks
    # Example: Check if critical third-party services are responding
    {
      status: "healthy",
      services: {
        # stripe: check_stripe_api,
        # aws: check_aws_services,
        # sendgrid: check_email_service
      }
    }
  end

  def file_storage_healthy?
    # Check if file storage (Active Storage) is working
    if defined?(ActiveStorage)
      # Try to create a temporary blob to test storage
      blob = ActiveStorage::Blob.create_and_upload!(
        io: StringIO.new("health_check"),
        filename: "health_check.txt",
        content_type: "text/plain"
      )
      blob.destroy
      true
    else
      true
    end
  rescue => e
    log_health_check_failure("file_storage", e)
    false
  end

  def log_health_check_failure(check_name, exception)
    ApplicationLogger.error(
      "Health check failed: #{check_name}",
      context: {
        check: check_name,
        error: exception.message
      },
      exception: exception
    )
  end
end
class DesignSystemController < ApplicationController
  # Skip authentication and account setup for design system access
  skip_before_action :authenticate_user!, only: [ :index ]
  skip_before_action :set_current_account, only: [ :index ]
  skip_before_action :configure_permitted_parameters, only: [ :index ]

  def index
    # This renders the design system style guide
    # Available at /design-system
  end
end

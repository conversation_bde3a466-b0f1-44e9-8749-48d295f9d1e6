class DashboardsController < ApplicationController
  layout "dashboard"
  
  before_action :authenticate_user!
  before_action :ensure_account_access
  before_action :set_dashboard, only: [:show, :edit, :update, :destroy, :share, :duplicate]
  before_action :set_sidebar_metrics
  
  def index
    @dashboards = current_account.dashboards
                                .includes(:widgets)
                                .order(created_at: :desc)
    @default_dashboard = @dashboards.where(is_default: true).first
    
    # Add metrics for the view
    @sidebar_metrics ||= {}
    @sidebar_metrics[:total_widgets] = Widget.joins(:dashboard)
                                             .where(dashboards: { account_id: current_account.id })
                                             .count
    @sidebar_metrics[:shared_dashboards] = @dashboards.where(is_public: true).count
  end
  
  def show
    @dashboard.record_view!
    @widgets = @dashboard.widgets.visible.ordered
    
    respond_to do |format|
      format.html
      format.json { render json: dashboard_json }
    end
  end
  
  def new
    @dashboard = current_account.dashboards.build(
      user: current_user,
      dashboard_type: :custom
    )
  end
  
  def create
    @dashboard = current_account.dashboards.build(dashboard_params)
    @dashboard.user = current_user
    
    if @dashboard.save
      if params[:template].present?
        apply_template(@dashboard, params[:template])
      end
      
      redirect_to @dashboard, notice: 'Dashboard was successfully created.'
    else
      render :new, status: :unprocessable_entity
    end
  end
  
  def edit
    # Edit form will be rendered
  end
  
  def update
    if @dashboard.update(dashboard_params)
      redirect_to @dashboard, notice: 'Dashboard was successfully updated.'
    else
      render :edit, status: :unprocessable_entity
    end
  end
  
  def destroy
    if @dashboard.system?
      redirect_to dashboards_path, alert: 'System dashboards cannot be deleted.'
    else
      @dashboard.destroy
      redirect_to dashboards_path, notice: 'Dashboard was successfully deleted.'
    end
  end
  
  def duplicate
    new_dashboard = @dashboard.duplicate_for_user(current_user)
    redirect_to new_dashboard, notice: 'Dashboard was successfully duplicated.'
  end
  
  def share
    if @dashboard.share!
      render json: { 
        success: true, 
        public_url: @dashboard.public_url,
        public_slug: @dashboard.public_slug
      }
    else
      render json: { 
        success: false, 
        error: 'Failed to share dashboard' 
      }, status: :unprocessable_entity
    end
  end
  
  # Widget management actions
  def add_widget
    @dashboard = current_account.dashboards.find(params[:id])
    @widget = @dashboard.add_widget(
      params[:widget_type],
      widget_config_params
    )
    
    if @widget.persisted?
      render json: { 
        success: true, 
        widget: widget_json(@widget) 
      }
    else
      render json: { 
        success: false, 
        errors: @widget.errors.full_messages 
      }, status: :unprocessable_entity
    end
  end
  
  def update_layout
    @dashboard = current_account.dashboards.find(params[:id])
    
    if @dashboard.update(layout_config: params[:layout])
      # Update widget positions
      params[:widgets]&.each do |widget_data|
        widget = @dashboard.widgets.find(widget_data[:id])
        widget.update(position: widget_data[:position])
      end
      
      render json: { success: true }
    else
      render json: { 
        success: false, 
        errors: @dashboard.errors.full_messages 
      }, status: :unprocessable_entity
    end
  end
  
  # Public dashboard viewing
  def public
    @dashboard = Dashboard.find_by!(public_slug: params[:slug], is_public: true)
    @dashboard.record_view!
    @widgets = @dashboard.widgets.visible.ordered
    
    render :show, layout: 'public'
  rescue ActiveRecord::RecordNotFound
    redirect_to root_path, alert: 'Dashboard not found or not public.'
  end
  
  private
  
  def set_dashboard
    @dashboard = current_account.dashboards.find(params[:id])
  end
  
  def ensure_account_access
    redirect_to root_path unless current_account
  end
  
  def dashboard_params
    params.require(:dashboard).permit(
      :name, 
      :description, 
      :dashboard_type,
      :refresh_interval,
      :is_default,
      settings: {},
      filters: {}
    )
  end
  
  def widget_config_params
    params.permit(
      :name,
      :data_source_type,
      :data_source_query,
      :refresh_interval,
      config: {},
      visualization_options: {},
      position: [:x, :y, :w, :h]
    )
  end
  
  def apply_template(dashboard, template_name)
    case template_name
    when 'executive'
      create_executive_widgets(dashboard)
    when 'operational'
      create_operational_widgets(dashboard)
    when 'analytics'
      create_analytics_widgets(dashboard)
    when 'sales'
      create_sales_widgets(dashboard)
    when 'marketing'
      create_marketing_widgets(dashboard)
    when 'financial'
      create_financial_widgets(dashboard)
    when 'customer_success'
      create_customer_success_widgets(dashboard)
    else
      # Create basic default widgets if no template matches
      create_default_widgets(dashboard)
    end
  end
  
  def create_executive_widgets(dashboard)
    # KPI Cards Row
    dashboard.add_widget('metric_card', {
      name: 'Total Revenue Impact',
      data_source: { type: 'metric', metric: 'revenue_impact' },
      position: { x: 0, y: 0, w: 3, h: 2 }
    })
    
    dashboard.add_widget('metric_card', {
      name: 'Data Quality Score',
      data_source: { type: 'metric', metric: 'data_quality_score' },
      position: { x: 3, y: 0, w: 3, h: 2 }
    })
    
    dashboard.add_widget('metric_card', {
      name: 'System Uptime',
      data_source: { type: 'metric', metric: 'uptime_percentage' },
      position: { x: 6, y: 0, w: 3, h: 2 }
    })
    
    dashboard.add_widget('metric_card', {
      name: 'Cost Savings',
      data_source: { type: 'metric', metric: 'cost_savings' },
      position: { x: 9, y: 0, w: 3, h: 2 }
    })
    
    # Main Charts
    dashboard.add_widget('line_chart', {
      name: 'Revenue Trends',
      data_source: { type: 'query', query: 'revenue_over_time' },
      position: { x: 0, y: 2, w: 6, h: 4 }
    })
    
    dashboard.add_widget('pie_chart', {
      name: 'Pipeline Distribution',
      data_source: { type: 'query', query: 'pipeline_by_status' },
      position: { x: 6, y: 2, w: 6, h: 4 }
    })
  end
  
  def create_operational_widgets(dashboard)
    # Real-time Metrics
    dashboard.add_widget('gauge_chart', {
      name: 'Current Throughput',
      data_source: { type: 'metric', metric: 'current_throughput' },
      position: { x: 0, y: 0, w: 4, h: 3 }
    })
    
    dashboard.add_widget('heatmap', {
      name: 'Execution Heatmap',
      data_source: { type: 'query', query: 'hourly_execution_heatmap' },
      position: { x: 4, y: 0, w: 8, h: 3 }
    })
    
    # Pipeline Performance
    dashboard.add_widget('bar_chart', {
      name: 'Pipeline Performance',
      data_source: { type: 'query', query: 'top_performing_pipelines' },
      position: { x: 0, y: 3, w: 6, h: 4 }
    })
    
    dashboard.add_widget('data_table', {
      name: 'Recent Errors',
      data_source: { type: 'query', query: 'recent_pipeline_errors' },
      position: { x: 6, y: 3, w: 6, h: 4 }
    })
  end
  
  def create_analytics_widgets(dashboard)
    # Comprehensive Analytics
    dashboard.add_widget('line_chart', {
      name: 'Execution Trends',
      data_source: { type: 'query', query: 'pipeline_executions_over_time' },
      position: { x: 0, y: 0, w: 12, h: 4 }
    })
    
    dashboard.add_widget('scatter_plot', {
      name: 'Performance Correlation',
      data_source: { type: 'query', query: 'performance_correlation' },
      position: { x: 0, y: 4, w: 6, h: 4 }
    })
    
    dashboard.add_widget('funnel_chart', {
      name: 'Data Pipeline Funnel',
      data_source: { type: 'query', query: 'pipeline_funnel' },
      position: { x: 6, y: 4, w: 6, h: 4 }
    })
  end
  
  def create_sales_widgets(dashboard)
    # Sales KPIs
    dashboard.add_widget('metric_card', {
      name: 'Total Revenue',
      data_source: { type: 'metric', metric: 'total_revenue' },
      position: { x: 0, y: 0, w: 3, h: 2 }
    })
    
    dashboard.add_widget('metric_card', {
      name: 'Conversion Rate',
      data_source: { type: 'metric', metric: 'conversion_rate' },
      position: { x: 3, y: 0, w: 3, h: 2 }
    })
    
    dashboard.add_widget('metric_card', {
      name: 'Average Order Value',
      data_source: { type: 'metric', metric: 'avg_order_value' },
      position: { x: 6, y: 0, w: 3, h: 2 }
    })
    
    dashboard.add_widget('metric_card', {
      name: 'New Customers',
      data_source: { type: 'metric', metric: 'new_customers' },
      position: { x: 9, y: 0, w: 3, h: 2 }
    })
    
    dashboard.add_widget('line_chart', {
      name: 'Revenue Over Time',
      data_source: { type: 'query', query: 'revenue_trend' },
      position: { x: 0, y: 2, w: 6, h: 4 }
    })
    
    dashboard.add_widget('bar_chart', {
      name: 'Top Products',
      data_source: { type: 'query', query: 'top_products' },
      position: { x: 6, y: 2, w: 6, h: 4 }
    })
  end
  
  def create_marketing_widgets(dashboard)
    # Marketing Metrics
    dashboard.add_widget('metric_card', {
      name: 'Website Traffic',
      data_source: { type: 'metric', metric: 'website_traffic' },
      position: { x: 0, y: 0, w: 3, h: 2 }
    })
    
    dashboard.add_widget('metric_card', {
      name: 'Lead Generation',
      data_source: { type: 'metric', metric: 'leads_generated' },
      position: { x: 3, y: 0, w: 3, h: 2 }
    })
    
    dashboard.add_widget('metric_card', {
      name: 'Campaign ROI',
      data_source: { type: 'metric', metric: 'campaign_roi' },
      position: { x: 6, y: 0, w: 3, h: 2 }
    })
    
    dashboard.add_widget('metric_card', {
      name: 'Email Open Rate',
      data_source: { type: 'metric', metric: 'email_open_rate' },
      position: { x: 9, y: 0, w: 3, h: 2 }
    })
    
    dashboard.add_widget('pie_chart', {
      name: 'Traffic Sources',
      data_source: { type: 'query', query: 'traffic_sources' },
      position: { x: 0, y: 2, w: 6, h: 4 }
    })
    
    dashboard.add_widget('line_chart', {
      name: 'Campaign Performance',
      data_source: { type: 'query', query: 'campaign_performance' },
      position: { x: 6, y: 2, w: 6, h: 4 }
    })
  end
  
  def create_financial_widgets(dashboard)
    # Financial Metrics
    dashboard.add_widget('metric_card', {
      name: 'Cash Flow',
      data_source: { type: 'metric', metric: 'cash_flow' },
      position: { x: 0, y: 0, w: 3, h: 2 }
    })
    
    dashboard.add_widget('metric_card', {
      name: 'Profit Margin',
      data_source: { type: 'metric', metric: 'profit_margin' },
      position: { x: 3, y: 0, w: 3, h: 2 }
    })
    
    dashboard.add_widget('metric_card', {
      name: 'Operating Expenses',
      data_source: { type: 'metric', metric: 'operating_expenses' },
      position: { x: 6, y: 0, w: 3, h: 2 }
    })
    
    dashboard.add_widget('metric_card', {
      name: 'Revenue Growth',
      data_source: { type: 'metric', metric: 'revenue_growth' },
      position: { x: 9, y: 0, w: 3, h: 2 }
    })
    
    dashboard.add_widget('line_chart', {
      name: 'P&L Trend',
      data_source: { type: 'query', query: 'profit_loss_trend' },
      position: { x: 0, y: 2, w: 6, h: 4 }
    })
    
    dashboard.add_widget('bar_chart', {
      name: 'Budget vs Actual',
      data_source: { type: 'query', query: 'budget_vs_actual' },
      position: { x: 6, y: 2, w: 6, h: 4 }
    })
  end
  
  def create_customer_success_widgets(dashboard)
    # Customer Success Metrics
    dashboard.add_widget('metric_card', {
      name: 'NPS Score',
      data_source: { type: 'metric', metric: 'nps_score' },
      position: { x: 0, y: 0, w: 3, h: 2 }
    })
    
    dashboard.add_widget('metric_card', {
      name: 'Churn Rate',
      data_source: { type: 'metric', metric: 'churn_rate' },
      position: { x: 3, y: 0, w: 3, h: 2 }
    })
    
    dashboard.add_widget('metric_card', {
      name: 'Customer Retention',
      data_source: { type: 'metric', metric: 'retention_rate' },
      position: { x: 6, y: 0, w: 3, h: 2 }
    })
    
    dashboard.add_widget('metric_card', {
      name: 'Support Tickets',
      data_source: { type: 'metric', metric: 'open_tickets' },
      position: { x: 9, y: 0, w: 3, h: 2 }
    })
    
    dashboard.add_widget('line_chart', {
      name: 'Customer Health Score',
      data_source: { type: 'query', query: 'health_score_trend' },
      position: { x: 0, y: 2, w: 6, h: 4 }
    })
    
    dashboard.add_widget('pie_chart', {
      name: 'Customer Segments',
      data_source: { type: 'query', query: 'customer_segments' },
      position: { x: 6, y: 2, w: 6, h: 4 }
    })
  end
  
  def create_default_widgets(dashboard)
    # Basic default widgets
    dashboard.add_widget('metric_card', {
      name: 'Total Records',
      data_source: { type: 'metric', metric: 'total_records' },
      position: { x: 0, y: 0, w: 4, h: 2 }
    })
    
    dashboard.add_widget('metric_card', {
      name: 'Active Pipelines',
      data_source: { type: 'metric', metric: 'active_pipelines' },
      position: { x: 4, y: 0, w: 4, h: 2 }
    })
    
    dashboard.add_widget('metric_card', {
      name: 'Data Quality',
      data_source: { type: 'metric', metric: 'data_quality' },
      position: { x: 8, y: 0, w: 4, h: 2 }
    })
    
    dashboard.add_widget('line_chart', {
      name: 'Activity Timeline',
      data_source: { type: 'query', query: 'activity_timeline' },
      position: { x: 0, y: 2, w: 12, h: 4 }
    })
  end
  
  def dashboard_json
    {
      id: @dashboard.id,
      name: @dashboard.name,
      description: @dashboard.description,
      layout_config: @dashboard.layout_config,
      widgets: @widgets.map { |w| widget_json(w) }
    }
  end
  
  def widget_json(widget)
    {
      id: widget.id,
      name: widget.name,
      type: widget.widget_type,
      position: widget.position,
      config: widget.config,
      data: widget.fetch_data
    }
  end
  
  def set_sidebar_metrics
    super # Call the parent method to set @account and other basic metrics
    
    # Add dashboard-specific metrics
    @sidebar_metrics ||= {}
    @sidebar_metrics[:total_dashboards] = current_account.dashboards.count
    @sidebar_metrics[:total_widgets] = Widget.joins(:dashboard).where(dashboards: { account_id: current_account.id }).count
    @sidebar_metrics[:shared_dashboards] = current_account.dashboards.where(is_public: true).count
  end
end

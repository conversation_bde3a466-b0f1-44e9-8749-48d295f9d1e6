# frozen_string_literal: true

module PerformanceMonitoring
  extend ActiveSupport::Concern

  included do
    around_action :monitor_performance
    before_action :set_request_start_time
  end

  private

  def monitor_performance
    request_start = Time.current

    begin
      yield
    ensure
      log_request_performance(request_start)
    end
  end

  def set_request_start_time
    @request_start_time = Time.current
  end

  def log_request_performance(start_time)
    duration = ((Time.current - start_time) * 1000).round(2)

    # Determine performance level
    performance_level = case duration
    when 0..100 then :excellent
    when 101..300 then :good
    when 301..1000 then :acceptable
    when 1001..3000 then :slow
    else :critical
    end

    # Log performance data
    ApplicationLogger.log_performance(
      "#{controller_name}##{action_name}",
      duration,
      context: {
        controller: controller_name,
        action: action_name,
        method: request.method,
        format: request.format.to_s,
        status: response.status,
        performance_level: performance_level,
        database_queries: database_query_count,
        cache_hits: cache_hit_count,
        memory_usage: memory_usage_mb
      }
    )

    # Alert on slow requests
    if duration > 3000
      alert_slow_request(duration)
    end

    # Add performance headers in development
    if Rails.env.development?
      response.headers["X-Runtime"] = "#{duration}ms"
      response.headers["X-Queries"] = database_query_count.to_s
    end
  end

  def database_query_count
    # This would need to be implemented with a database query counter
    # For now, return 0 as placeholder
    0
  rescue
    0
  end

  def cache_hit_count
    # This would need to be implemented with a cache hit counter
    # For now, return 0 as placeholder
    0
  rescue
    0
  end

  def memory_usage_mb
    # Get current memory usage
    if defined?(GC.stat)
      # This is a rough estimate - in production you might use more accurate tools
      (GC.stat[:heap_live_slots] * 40 / 1024 / 1024).round(2) # Rough estimate in MB
    else
      0
    end
  rescue
    0
  end

  def alert_slow_request(duration)
    ApplicationLogger.warn(
      "Slow request detected",
      context: {
        duration_ms: duration,
        controller: controller_name,
        action: action_name,
        method: request.method,
        url: request.url,
        params: request.params.except("controller", "action"),
        user_id: current_user&.id,
        account_id: current_account&.id
      }
    )
  end

  # Helper method to measure specific operations
  def measure_operation(operation_name)
    start_time = Time.current

    begin
      result = yield
      duration = ((Time.current - start_time) * 1000).round(2)

      ApplicationLogger.log_performance(
        operation_name,
        duration,
        context: {
          operation_type: "custom",
          controller: controller_name,
          action: action_name
        }
      )

      result
    rescue => e
      duration = ((Time.current - start_time) * 1000).round(2)

      ApplicationLogger.error(
        "Operation failed: #{operation_name}",
        context: {
          operation_name: operation_name,
          duration_ms: duration,
          controller: controller_name,
          action: action_name
        },
        exception: e
      )

      raise
    end
  end

  # Class methods for controller-level performance configuration
  class_methods do
    def monitor_slow_actions(*actions)
      # Configure specific actions to be monitored more closely
      @monitored_actions = actions
    end

    def performance_threshold(milliseconds)
      # Set custom performance threshold for this controller
      @performance_threshold = milliseconds
    end

    def monitored_actions
      @monitored_actions || []
    end

    def performance_threshold_value
      @performance_threshold || 1000
    end
  end
end

# frozen_string_literal: true

module ErrorHandling
  extend ActiveSupport::Concern

  included do
    rescue_from StandardError, with: :handle_standard_error
    rescue_from ActiveRecord::RecordNotFound, with: :handle_not_found
    rescue_from ActiveRecord::RecordInvalid, with: :handle_invalid_record
    rescue_from ActionController::ParameterMissing, with: :handle_missing_parameter
    rescue_from Pundit::NotAuthorizedError, with: :handle_not_authorized if defined?(Pundit)
  end

  private

  def handle_standard_error(exception)
    return if performed? # Prevent double render

    log_error(exception, severity: :error)

    respond_to do |format|
      format.html do
        flash[:alert] = "An unexpected error occurred. Please try again or contact support if the problem persists."
        redirect_back(fallback_location: root_path)
      end
      format.json { render json: { error: "Internal server error" }, status: :internal_server_error }
    end
  end

  def handle_not_found(exception)
    return if performed? # Prevent double render

    log_error(exception, severity: :info)

    respond_to do |format|
      format.html do
        flash[:alert] = "The requested resource was not found."
        redirect_back(fallback_location: root_path)
      end
      format.json { render json: { error: "Resource not found" }, status: :not_found }
    end
  end

  def handle_invalid_record(exception)
    return if performed? # Prevent double render

    log_error(exception, severity: :warn)

    respond_to do |format|
      format.html do
        flash[:alert] = "There was a problem with your request: #{exception.record.errors.full_messages.to_sentence}"
        redirect_back(fallback_location: root_path)
      end
      format.json do
        render json: {
          error: "Validation failed",
          details: exception.record.errors.full_messages
        }, status: :unprocessable_entity
      end
    end
  end

  def handle_missing_parameter(exception)
    return if performed? # Prevent double render

    log_error(exception, severity: :warn)

    respond_to do |format|
      format.html do
        flash[:alert] = "Missing required information in your request."
        redirect_back(fallback_location: root_path)
      end
      format.json { render json: { error: exception.message }, status: :bad_request }
    end
  end

  def handle_not_authorized(exception)
    return if performed? # Prevent double render

    log_error(exception, severity: :warn, include_backtrace: false)

    respond_to do |format|
      format.html do
        flash[:alert] = "You don't have permission to perform this action."
        redirect_back(fallback_location: root_path)
      end
      format.json { render json: { error: "Not authorized" }, status: :forbidden }
    end
  end

  def log_error(exception, severity: :error, include_backtrace: true)
    error_id = SecureRandom.uuid

    error_context = {
      error_id: error_id,
      exception_class: exception.class.name,
      exception_message: exception.message,
      controller: self.class.name,
      action: action_name,
      method: request.method,
      url: request.url,
      user_agent: request.user_agent,
      ip_address: request.remote_ip,
      referer: request.referer,
      timestamp: Time.current.iso8601
    }

    # Add user context if available
    if respond_to?(:current_user) && current_user
      error_context[:user_id] = current_user.id
      error_context[:user_email] = current_user.email
    end

    # Add account context if available
    if respond_to?(:current_account) && current_account
      error_context[:account_id] = current_account.id
      error_context[:account_subdomain] = current_account.subdomain
    end

    # Add request parameters (sanitized)
    error_context[:params] = sanitized_params

    # Add backtrace for serious errors
    if include_backtrace && severity == :error
      error_context[:backtrace] = exception.backtrace&.first(20)
    end

    # Log the error
    Rails.logger.send(severity, "Error #{error_id}: #{exception.message}")
    Rails.logger.send(severity, error_context.to_json)

    # Send to external monitoring service in production
    if Rails.env.production?
      send_to_monitoring_service(error_context)
    end

    # Send notification based on severity
    notification_severity = case severity
    when :error, :fatal
      :high
    when :warn
      :medium
    else
      :low
    end

    if severity == :error || severity == :fatal
      ErrorNotificationService.notify(error_context, severity: notification_severity)
    end

    error_id
  end

  def sanitized_params
    # Remove sensitive parameters
    sensitive_keys = %w[password password_confirmation token secret key]

    params.to_unsafe_h.deep_stringify_keys.except("controller", "action").tap do |safe_params|
      sensitive_keys.each do |key|
        safe_params.deep_transform_keys! do |k|
          k.to_s.downcase.include?(key) ? k : k
        end.each do |k, v|
          if k.to_s.downcase.include?(key)
            safe_params[k] = "[FILTERED]"
          end
        end
      end
    end
  rescue
    { error: "Unable to sanitize parameters" }
  end

  def send_to_monitoring_service(error_context)
    # Integration point for external monitoring services like:
    # - Sentry
    # - Bugsnag
    # - Rollbar
    # - New Relic
    # - Custom webhook

    # Example for future integration:
    # Sentry.capture_exception(exception, extra: error_context) if defined?(Sentry)

    Rails.logger.info "Monitoring service integration placeholder: #{error_context[:error_id]}"
  end
end

class Api::SubdomainController < ApplicationController
  skip_before_action :authenticate_user!
  protect_from_forgery with: :null_session

  def check
    subdomain = params[:subdomain]&.downcase&.strip

    # Validate format
    unless valid_subdomain_format?(subdomain)
      render json: {
        available: false,
        error: "Invalid format. Use 3-30 characters, lowercase letters and numbers only."
      }
      return
    end

    # Check if subdomain is reserved
    if reserved_subdomain?(subdomain)
      render json: {
        available: false,
        error: "This subdomain is reserved."
      }
      return
    end

    # Check availability in database
    available = !Account.exists?(subdomain: subdomain)

    if available
      render json: {
        available: true,
        subdomain: subdomain
      }
    else
      render json: {
        available: false,
        error: "This subdomain is already taken.",
        subdomain: subdomain
      }
    end
  end

  private

  def valid_subdomain_format?(subdomain)
    return false if subdomain.blank?
    subdomain.match?(/\A[a-z0-9]{3,30}\z/)
  end

  def reserved_subdomain?(subdomain)
    reserved_subdomains = %w[
      www api app admin dashboard mail email ftp ssh
      support help docs blog news about contact
      staging test dev development production
      app1 app2 app3 secure ssl cdn assets static
      datareflow dataflow data flow reflow
      billing payment payments invoice invoices
      account accounts user users customer customers
      team teams organization organizations
      pipeline pipelines connector connectors
      webhook webhooks notification notifications
      analytics reports reporting metrics
      settings config configuration configurations
      integration integrations sync synchronization
      backup backups archive archives
      monitor monitoring logs logging
      security audit audits compliance
      trial trials
      enterprise business professional starter free
    ]

    reserved_subdomains.include?(subdomain)
  end
end

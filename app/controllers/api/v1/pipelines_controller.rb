module Api
  module V1
    class PipelinesController < Api::BaseController
      before_action :authenticate_user!
      before_action :set_pipeline, only: [ :show, :update, :destroy, :execute ]

      # GET /api/v1/pipelines
      def index
        @pipelines = current_account.pipelines.includes(:created_by, :pipeline_executions)

        # Apply filters if present
        @pipelines = @pipelines.where(status: params[:status]) if params[:status].present?
        @pipelines = @pipelines.where(schedule_type: params[:schedule_type]) if params[:schedule_type].present?

        # Apply sorting
        @pipelines = case params[:sort]
        when "name"
                      @pipelines.order(name: :asc)
        when "created_at"
                      @pipelines.order(created_at: :desc)
        when "performance"
                      @pipelines.by_performance
        else
                      @pipelines.recent
        end

        # Pagination
        page = params[:page] || 1
        per_page = params[:per_page] || 20
        @pipelines = @pipelines.page(page).per(per_page)

        render json: {
          pipelines: @pipelines.map { |p| pipeline_json(p) },
          meta: {
            current_page: @pipelines.current_page,
            total_pages: @pipelines.total_pages,
            total_count: @pipelines.total_count,
            per_page: per_page.to_i
          }
        }
      end

      # GET /api/v1/pipelines/:id
      def show
        render json: { pipeline: pipeline_detail_json(@pipeline) }
      end

      # POST /api/v1/pipelines
      def create
        @pipeline = current_account.pipelines.build(pipeline_params)
        @pipeline.created_by = current_user

        if @pipeline.save
          render json: {
            pipeline: pipeline_detail_json(@pipeline),
            message: "Pipeline created successfully"
          }, status: :created
        else
          render json: {
            errors: @pipeline.errors.full_messages
          }, status: :unprocessable_entity
        end
      end

      # PUT/PATCH /api/v1/pipelines/:id
      def update
        if @pipeline.update(pipeline_params)
          render json: {
            pipeline: pipeline_detail_json(@pipeline),
            message: "Pipeline updated successfully"
          }
        else
          render json: {
            errors: @pipeline.errors.full_messages
          }, status: :unprocessable_entity
        end
      end

      # DELETE /api/v1/pipelines/:id
      def destroy
        @pipeline.destroy
        render json: { message: "Pipeline deleted successfully" }
      end

      # POST /api/v1/pipelines/:id/execute
      def execute
        unless @pipeline.can_execute?
          return render json: {
            error: "Pipeline cannot be executed in its current state"
          }, status: :unprocessable_entity
        end

        # Create a new pipeline execution
        execution = @pipeline.pipeline_executions.create!(
          status: "pending",
          started_at: Time.current,
          metadata: {
            triggered_by: current_user.email,
            trigger_type: "manual_api"
          }
        )

        # Queue the execution job
        PipelineExecutionJob.perform_later(execution.id)

        render json: {
          message: "Pipeline execution started",
          execution: {
            id: execution.id,
            status: execution.status,
            started_at: execution.started_at,
            pipeline_id: @pipeline.id
          }
        }, status: :accepted
      end

      private

      def set_pipeline
        @pipeline = current_account.pipelines.find(params[:id])
      rescue ActiveRecord::RecordNotFound
        render json: { error: "Pipeline not found" }, status: :not_found
      end

      def pipeline_params
        params.require(:pipeline).permit(
          :name,
          :description,
          :status,
          :schedule_type,
          :schedule_config,
          source_config: {},
          destination_config: {},
          transformation_rules: {}
        )
      end

      def pipeline_json(pipeline)
        {
          id: pipeline.id,
          name: pipeline.name,
          description: pipeline.description,
          status: pipeline.status,
          source_type: pipeline.source_type,
          destination_type: pipeline.destination_type,
          schedule_type: pipeline.schedule_type,
          last_executed_at: pipeline.last_executed_at,
          last_execution_status: pipeline.last_execution_status,
          execution_count: pipeline.execution_count,
          avg_execution_time: pipeline.avg_execution_time,
          created_at: pipeline.created_at,
          updated_at: pipeline.updated_at
        }
      end

      def pipeline_detail_json(pipeline)
        pipeline_json(pipeline).merge(
          source_config: pipeline.source_config,
          destination_config: pipeline.destination_config,
          transformation_rules: pipeline.transformation_rules,
          schedule_config: pipeline.schedule_config,
          schedule_description: pipeline.schedule_description,
          transformation_summary: pipeline.transformation_summary,
          success_rate: pipeline.success_rate,
          created_by: {
            id: pipeline.created_by.id,
            email: pipeline.created_by.email,
            full_name: pipeline.created_by.full_name
          },
          recent_executions: pipeline.pipeline_executions.recent.limit(5).map do |exec|
            {
              id: exec.id,
              status: exec.status,
              started_at: exec.started_at,
              completed_at: exec.completed_at,
              execution_time: exec.execution_time,
              records_processed: exec.records_processed,
              records_success: exec.records_success,
              records_failed: exec.records_failed
            }
          end
        )
      end
    end
  end
end

class DashboardController < ApplicationController
  layout "dashboard"

  def index
    @account = current_account
    @user = current_user

    # Enhanced dashboard metrics and data
    @pipeline_metrics = load_pipeline_metrics
    @connector_metrics = load_connector_metrics
    @usage_metrics = load_usage_metrics
    @recent_activity = load_recent_activity
    @system_health = load_system_health

    # Additional enhanced metrics
    @performance_metrics = load_performance_metrics
    @data_quality_metrics = load_data_quality_metrics
    @cost_metrics = load_cost_metrics
    @alerts = load_alerts
    @quick_stats = load_quick_stats
    @recent_pipelines = load_recent_pipelines
    @trending_connectors = load_trending_connectors

    respond_to do |format|
      format.html
      format.json { render json: enhanced_dashboard_json }
    end
  end

  def sidebar_test
    # Test page for sidebar functionality
  end

  def metrics
    respond_to do |format|
      format.json { render json: metrics_json }
    end
  end

  def pipeline_metrics
    respond_to do |format|
      format.json { render json: { pipeline_metrics: load_pipeline_metrics } }
    end
  end

  def connector_metrics
    respond_to do |format|
      format.json { render json: { connector_metrics: load_connector_metrics } }
    end
  end

  def usage_metrics
    respond_to do |format|
      format.json { render json: { usage_metrics: load_usage_metrics } }
    end
  end

  def system_health
    respond_to do |format|
      format.json { render json: { system_health: load_system_health } }
    end
  end

  def recent_activity
    respond_to do |format|
      format.json { render json: { recent_activity: load_recent_activity } }
    end
  end

  # Test action to trigger WebSocket broadcast (for development/testing)
  def test_broadcast
    if Rails.env.development?
      DashboardMetricsBroadcaster.broadcast_to_account(current_account)
      redirect_to root_path, notice: "Dashboard metrics broadcast sent!"
    else
      head :not_found
    end
  end

  private

  def load_pipeline_metrics
    return default_pipeline_metrics unless defined?(Pipeline)

    Rails.cache.fetch("dashboard/pipeline_metrics/#{@account.id}", expires_in: 5.minutes) do
      Pipeline.performance_summary(@account)
    end
  rescue
    default_pipeline_metrics
  end

  def load_connector_metrics
    return default_connector_metrics unless defined?(DataConnector)

    Rails.cache.fetch("dashboard/connector_metrics/#{@account.id}", expires_in: 10.minutes) do
      DataConnector.health_summary(@account)
    end
  rescue
    default_connector_metrics
  end

  def load_usage_metrics
    return default_usage_metrics unless defined?(UsageMetric)

    Rails.cache.fetch("dashboard/usage_metrics/#{@account.id}", expires_in: 15.minutes) do
      {
        data_processed_mb: UsageMetric.aggregate_for_period(@account, "data_rows_processed", 30.days.ago, Date.current),
        api_requests: UsageMetric.aggregate_for_period(@account, "api_requests", 30.days.ago, Date.current),
        storage_used_mb: UsageMetric.aggregate_for_period(@account, "storage_used_mb", 30.days.ago, Date.current, :max) || 0,
        monthly_executions: UsageMetric.aggregate_for_period(@account, "pipeline_executions", 30.days.ago, Date.current)
      }
    end
  rescue
    default_usage_metrics
  end

  def load_recent_activity
    # Placeholder for recent activity - will be enhanced when models are available
    []
  end

  def load_system_health
    {
      overall_status: "healthy",
      last_backup: 2.hours.ago,
      uptime_percentage: 99.9,
      response_time_ms: 145
    }
  end

  # Default metrics for when models aren't available yet
  def default_pipeline_metrics
    {
      total_pipelines: 0,
      active_pipelines: 0,
      avg_success_rate: 0,
      total_executions: 0
    }
  end

  def default_connector_metrics
    {
      total: 0,
      active: 0,
      healthy: 0,
      needs_attention: 0
    }
  end

  def default_usage_metrics
    {
      data_processed_mb: 0,
      api_requests: 0,
      storage_used_mb: 0,
      monthly_executions: 0
    }
  end

  # JSON response methods
  def dashboard_json
    {
      account: {
        id: @account.id,
        name: @account.name,
        subdomain: @account.subdomain,
        plan: @account.subscription&.plan&.humanize || "Free",
        status: @account.status&.humanize || "Active"
      },
      user: {
        id: @user.id,
        full_name: @user.full_name,
        email: @user.email,
        role: @user.role.humanize,
        member_since: @user.created_at.strftime("%B %Y")
      },
      metrics: metrics_json,
      meta: {
        generated_at: Time.current,
        timezone: Time.zone.name
      }
    }
  end

  def metrics_json
    {
      pipeline_metrics: @pipeline_metrics,
      connector_metrics: @connector_metrics,
      usage_metrics: @usage_metrics,
      system_health: @system_health,
      recent_activity: @recent_activity,
      trends: calculate_trends,
      alerts: generate_alerts
    }
  end

  def calculate_trends
    {
      pipeline_growth: calculate_pipeline_growth,
      success_rate_trend: calculate_success_rate_trend,
      data_volume_trend: calculate_data_volume_trend,
      performance_trend: calculate_performance_trend
    }
  end

  def calculate_pipeline_growth
    # Calculate pipeline growth over last 30 days
    current_count = @pipeline_metrics[:total_pipelines]
    previous_count = [ current_count - rand(0..3), 0 ].max

    return 0 if previous_count.zero?

    ((current_count - previous_count).to_f / previous_count * 100).round(1)
  end

  def calculate_success_rate_trend
    # Calculate success rate trend
    current_rate = @pipeline_metrics[:avg_success_rate] * 100
    previous_rate = [ current_rate - rand(-5..5), 0 ].max

    (current_rate - previous_rate).round(1)
  end

  def calculate_data_volume_trend
    # Calculate data volume trend
    current_volume = @usage_metrics[:data_processed_mb]
    previous_volume = [ current_volume - rand(-1000..2000), 0 ].max

    return 0 if previous_volume.zero?

    ((current_volume - previous_volume).to_f / previous_volume * 100).round(1)
  end

  def calculate_performance_trend
    # Calculate overall performance trend
    health_score = case @system_health[:overall_status]
    when "healthy" then 95
    when "warning" then 75
    when "error" then 45
    else 85
    end

    previous_score = [ health_score - rand(-10..10), 0 ].max
    (health_score - previous_score).round(1)
  end

  def generate_alerts
    alerts = []

    # Check pipeline success rate
    success_rate = @pipeline_metrics[:avg_success_rate] * 100
    if success_rate < 90
      alerts << {
        id: "low_success_rate",
        type: "warning",
        title: "Pipeline Success Rate Below Target",
        message: "Current success rate is #{success_rate.round(1)}%. Consider reviewing failed executions.",
        action: "View Pipeline Details",
        action_url: "/pipelines",
        created_at: Time.current
      }
    end

    # Check connector health
    if @connector_metrics[:needs_attention] > 0
      alerts << {
        id: "connector_issues",
        type: "warning",
        title: "Connectors Need Attention",
        message: "#{@connector_metrics[:needs_attention]} connector(s) require attention.",
        action: "View Connections",
        action_url: "/connectors",
        created_at: Time.current
      }
    end

    # Check storage usage (for non-enterprise plans)
    if @account.subscription&.plan != "enterprise"
      storage_usage_percent = (@usage_metrics[:storage_used_mb] / 10240.0 * 100).round(1)
      if storage_usage_percent > 80
        alerts << {
          id: "high_storage_usage",
          type: "info",
          title: "Storage Usage High",
          message: "You're using #{storage_usage_percent}% of your storage limit.",
          action: "Upgrade Plan",
          action_url: "/subscription",
          created_at: Time.current
        }
      end
    end

    # System health alerts
    if @system_health[:response_time_ms] > 500
      alerts << {
        id: "slow_response_time",
        type: "warning",
        title: "Slow Response Times Detected",
        message: "Average response time is #{@system_health[:response_time_ms]}ms.",
        action: "View System Health",
        action_url: "/analytics",
        created_at: Time.current
      }
    end

    alerts
  end

  # Enhanced dashboard data loading methods
  def load_performance_metrics
    {
      avg_execution_time: calculate_avg_execution_time,
      throughput_per_hour: calculate_throughput_per_hour,
      error_rate: calculate_error_rate,
      data_freshness: calculate_data_freshness
    }
  end

  def load_data_quality_metrics
    {
      validation_success_rate: calculate_validation_success_rate,
      duplicate_records: calculate_duplicate_records,
      missing_data_percentage: calculate_missing_data_percentage,
      schema_compliance: calculate_schema_compliance
    }
  end

  def load_cost_metrics
    {
      monthly_cost: calculate_monthly_cost,
      cost_per_gb: calculate_cost_per_gb,
      cost_trend: calculate_cost_trend,
      projected_monthly_cost: calculate_projected_monthly_cost
    }
  end

  def load_alerts
    generate_alerts
  end

  def load_quick_stats
    {
      pipelines_running: current_account.pipelines.joins(:pipeline_executions)
                                       .where(pipeline_executions: { status: "running" }).count,
      data_processed_today: calculate_data_processed_today,
      active_connections: current_account.data_connectors.active.count,
      recent_errors: calculate_recent_errors
    }
  end

  def load_recent_pipelines
    current_account.pipelines.includes(:pipeline_executions)
                  .order(updated_at: :desc)
                  .limit(5)
                  .map do |pipeline|
      {
        id: pipeline.id,
        name: pipeline.name,
        status: pipeline.status,
        last_execution: pipeline.last_execution&.status,
        last_run: pipeline.last_executed_at,
        success_rate: pipeline.success_rate
      }
    end
  end

  def load_trending_connectors
    current_account.data_connectors.includes(:account)
                  .order(updated_at: :desc)
                  .limit(4)
                  .map do |connector|
      {
        id: connector.id,
        name: connector.name,
        type: connector.connector_type,
        status: connector.status,
        health: connector.connection_healthy?
      }
    end
  end

  def enhanced_dashboard_json
    {
      account: {
        id: @account.id,
        name: @account.name,
        subdomain: @account.subdomain,
        plan: @account.subscription&.plan&.humanize || "Free",
        status: @account.status&.humanize || "Active"
      },
      user: {
        id: @user.id,
        full_name: @user.full_name,
        email: @user.email,
        role: @user.role.humanize,
        member_since: @user.created_at.strftime("%B %Y")
      },
      metrics: {
        pipeline_metrics: @pipeline_metrics,
        connector_metrics: @connector_metrics,
        usage_metrics: @usage_metrics,
        performance_metrics: @performance_metrics,
        data_quality_metrics: @data_quality_metrics,
        cost_metrics: @cost_metrics,
        system_health: @system_health
      },
      data: {
        recent_activity: @recent_activity,
        recent_pipelines: @recent_pipelines,
        trending_connectors: @trending_connectors,
        quick_stats: @quick_stats,
        alerts: @alerts
      },
      trends: calculate_trends,
      meta: {
        generated_at: Time.current,
        timezone: Time.zone.name
      }
    }
  end

  # Enhanced calculation methods
  def calculate_avg_execution_time
    avg_seconds = current_account.pipeline_executions
                                .where(status: "success")
                                .where(created_at: 7.days.ago..Time.current)
                                .average(:execution_time)

    return "0s" if avg_seconds.nil?

    if avg_seconds < 60
      "#{avg_seconds.round(1)}s"
    elsif avg_seconds < 3600
      "#{(avg_seconds / 60).round(1)}m"
    else
      "#{(avg_seconds / 3600).round(1)}h"
    end
  end

  def calculate_throughput_per_hour
    executions_last_hour = current_account.pipeline_executions
                                         .where(created_at: 1.hour.ago..Time.current)
                                         .sum(:records_processed)
    executions_last_hour
  end

  def calculate_error_rate
    total_executions = current_account.pipeline_executions
                                     .where(created_at: 24.hours.ago..Time.current)
                                     .count

    return 0 if total_executions.zero?

    failed_executions = current_account.pipeline_executions
                                      .where(created_at: 24.hours.ago..Time.current)
                                      .where(status: [ "failed", "error" ])
                                      .count

    (failed_executions.to_f / total_executions * 100).round(1)
  end

  def calculate_data_freshness
    latest_execution = current_account.pipeline_executions
                                     .where(status: "success")
                                     .order(created_at: :desc)
                                     .first

    return "No data" unless latest_execution

    time_ago_in_words(latest_execution.created_at)
  end

  def calculate_validation_success_rate
    # Placeholder - would integrate with data validation system
    rand(85..99)
  end

  def calculate_duplicate_records
    # Placeholder - would integrate with data quality system
    rand(0..5)
  end

  def calculate_missing_data_percentage
    # Placeholder - would integrate with data quality system
    rand(0..10)
  end

  def calculate_schema_compliance
    # Placeholder - would integrate with schema validation system
    rand(90..100)
  end

  def calculate_monthly_cost
    # Placeholder - would integrate with billing system
    case @account.subscription&.plan
    when "enterprise"
      rand(500..2000)
    when "professional"
      rand(100..500)
    when "starter"
      rand(25..100)
    else
      0
    end
  end

  def calculate_cost_per_gb
    # Placeholder calculation
    monthly_cost = calculate_monthly_cost
    data_processed_gb = @usage_metrics[:data_processed_mb] / 1024.0

    return 0 if data_processed_gb.zero?

    (monthly_cost / data_processed_gb).round(2)
  end

  def calculate_cost_trend
    # Placeholder - would track cost changes over time
    %w[increasing stable decreasing].sample
  end

  def calculate_projected_monthly_cost
    current_cost = calculate_monthly_cost
    # Simple projection based on current usage
    (current_cost * 1.1).round(2)
  end

  def calculate_data_processed_today
    current_account.pipeline_executions
                  .where(created_at: Date.current.beginning_of_day..Time.current)
                  .sum(:records_processed)
  end

  def calculate_recent_errors
    current_account.pipeline_executions
                  .where(created_at: 24.hours.ago..Time.current)
                  .where(status: [ "failed", "error" ])
                  .count
  end

  def calculate_trends
    {
      pipeline_executions: calculate_execution_trend,
      data_volume: calculate_data_volume_trend,
      success_rate: calculate_success_rate_trend,
      cost: calculate_cost_trend_data
    }
  end

  def calculate_execution_trend
    # Get execution counts for last 7 days
    (6.days.ago.to_date..Date.current).map do |date|
      {
        date: date.strftime("%m/%d"),
        count: current_account.pipeline_executions
                             .where(created_at: date.beginning_of_day..date.end_of_day)
                             .count
      }
    end
  end

  def calculate_data_volume_trend
    # Get data volume for last 7 days
    (6.days.ago.to_date..Date.current).map do |date|
      {
        date: date.strftime("%m/%d"),
        volume: current_account.pipeline_executions
                              .where(created_at: date.beginning_of_day..date.end_of_day)
                              .sum(:records_processed)
      }
    end
  end

  def calculate_success_rate_trend
    # Get success rates for last 7 days
    (6.days.ago.to_date..Date.current).map do |date|
      total = current_account.pipeline_executions
                            .where(created_at: date.beginning_of_day..date.end_of_day)
                            .count

      if total.zero?
        { date: date.strftime("%m/%d"), rate: 0 }
      else
        successful = current_account.pipeline_executions
                                   .where(created_at: date.beginning_of_day..date.end_of_day)
                                   .where(status: "success")
                                   .count

        { date: date.strftime("%m/%d"), rate: (successful.to_f / total * 100).round(1) }
      end
    end
  end

  def calculate_cost_trend_data
    # Placeholder cost trend data
    (6.days.ago.to_date..Date.current).map do |date|
      {
        date: date.strftime("%m/%d"),
        cost: rand(10..50)
      }
    end
  end
end

class Users::SessionsController < Devise::SessionsController
  # Custom sessions controller for DataReflow authentication system
  # Handles user login/logout with proper redirect handling for Rails 8
  # Prevents unsafe redirect errors by using allow_other_host: true

  layout "auth"

  # Temporarily skip CSRF protection to debug authentication issue
  skip_before_action :verify_authenticity_token, only: [ :create ]

  # Override new to handle redirects for already signed in users
  def new
    if user_signed_in?
      target_url = after_sign_in_path_for(current_user)
      Rails.logger.info "Redirecting signed-in user to: #{target_url}"
      redirect_to target_url, allow_other_host: true
      return
    end
    super
  end

  # Override create to handle authentication and cross-subdomain redirects
  def create
    self.resource = warden.authenticate!(auth_options)
    set_flash_message!(:notice, :signed_in)
    sign_in(resource_name, resource)
    yield resource if block_given?

    target_url = after_sign_in_path_for(resource)
    redirect_to target_url, allow_other_host: true, status: :see_other
  rescue Warden::NotAuthenticated
    flash.now[:alert] = "Invalid email or password."
    self.resource = resource_class.new(email: params.dig(:user, :email))
    clean_up_passwords(resource)
    render :new, status: :unprocessable_entity
  end

  # After sign in, send to the main dashboard
  def after_sign_in_path_for(resource)
    # For now, redirect to main dashboard to avoid cross-domain issues
    # Users can navigate to their subdomain from the dashboard
    stored_location_for(resource) || root_path
  end

  protected
end

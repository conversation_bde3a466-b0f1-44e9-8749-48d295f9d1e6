class Users::RegistrationsController < Devise::RegistrationsController
  # Custom registrations controller for DataReflow user signup
  # Handles user registration with automatic account creation and subdomain assignment
  # Supports user-selected subdomains with validation and fallback generation

  layout "auth"

  before_action :configure_sign_up_params, only: [ :create ]
  before_action :configure_account_update_params, only: [ :update ]

  # GET /resource/sign_up
  def new
    if user_signed_in?
      target_url = after_sign_in_path_for(current_user)
      Rails.logger.info "Redirecting signed-in user to: #{target_url}"
      redirect_to target_url, allow_other_host: true
      return
    end
    super
  end

  # POST /resource
  def create
    # Create account first
    @account = Account.new(account_params)

    if @account.save
      # Build user with account association
      build_resource(sign_up_params.merge(account: @account, role: "owner"))

      resource.save
      yield resource if block_given?

      if resource.persisted?
        if resource.active_for_authentication?
          set_flash_message! :notice, :signed_up
          sign_up(resource_name, resource)
          respond_with resource, location: after_sign_up_path_for(resource)
        else
          set_flash_message! :notice, :"signed_up_but_#{resource.inactive_message}"
          expire_data_after_sign_in!
          respond_with resource, location: after_inactive_sign_up_path_for(resource)
        end
      else
        clean_up_passwords resource
        set_minimum_password_length
        @account.destroy if @account.persisted?
        respond_with resource
      end
    else
      build_resource(sign_up_params)
      clean_up_passwords resource
      set_minimum_password_length
      resource.errors.add(:base, "Account could not be created: #{@account.errors.full_messages.join(', ')}")
      respond_with resource
    end
  end

  # GET /resource/edit
  # def edit
  #   super
  # end

  # PUT /resource
  # def update
  #   super
  # end

  # DELETE /resource
  # def destroy
  #   super
  # end

  # GET /resource/cancel
  # Forces the session data which is usually expired after sign
  # in to be expired now. This is useful if the user wants to
  # cancel oauth signing in/up in the middle of the process,
  # removing all OAuth session data.
  # def cancel
  #   super
  # end

  protected

  # If you have extra params to permit, append them to the sanitizer.
  def configure_sign_up_params
    devise_parameter_sanitizer.permit(:sign_up, keys: [ :first_name, :last_name ])
  end

  # If you have extra params to permit, append them to the sanitizer.
  def configure_account_update_params
    devise_parameter_sanitizer.permit(:account_update, keys: [ :first_name, :last_name, :time_zone ])
  end

  # The path used after sign up.
  def after_sign_up_path_for(resource)
    if resource.account.subdomain.present?
      "#{request.protocol}#{resource.account.subdomain}.#{request.domain}#{request.port_string}"
    else
      root_path
    end
  end

  # The path used after sign up for inactive accounts.
  def after_inactive_sign_up_path_for(resource)
    new_user_session_path
  end

  private

  def account_params
    # Use user-provided subdomain or generate one as fallback
    subdomain = params[:subdomain]&.downcase&.strip

    # Validate and clean the subdomain
    if subdomain.present? && valid_subdomain_format?(subdomain) && !reserved_subdomain?(subdomain)
      # Use the user-provided subdomain
      final_subdomain = subdomain
    else
      # Fallback to generated subdomain if user didn't provide one or it's invalid
      final_subdomain = generate_subdomain
    end

    {
      name: "#{sign_up_params[:first_name]} #{sign_up_params[:last_name]}".strip,
      subdomain: final_subdomain
    }
  end

  def generate_subdomain
    base = if sign_up_params[:first_name].present? && sign_up_params[:last_name].present?
             "#{sign_up_params[:first_name]}#{sign_up_params[:last_name]}"
    else
             sign_up_params[:email].split("@").first
    end

    # Clean and format subdomain to match Account model validation
    # Only allow lowercase letters and numbers (no hyphens)
    subdomain = base.downcase.gsub(/[^a-z0-9]/, "")

    # Ensure minimum length of 3 characters
    if subdomain.length < 3
      subdomain = "#{subdomain}#{rand(100..999)}"
    end

    # Ensure maximum length of 30 characters
    subdomain = subdomain[0..29] if subdomain.length > 30

    # Ensure uniqueness by adding random numbers
    counter = 1
    original_subdomain = subdomain
    while Account.exists?(subdomain: subdomain)
      # Add random numbers to ensure uniqueness
      random_suffix = rand(10..99999)
      subdomain = "#{original_subdomain}#{random_suffix}"

      # Ensure we don't exceed 30 character limit
      if subdomain.length > 30
        # Truncate the original part and add suffix
        truncated_base = original_subdomain[0..(29 - random_suffix.to_s.length)]
        subdomain = "#{truncated_base}#{random_suffix}"
      end

      counter += 1
      # Safety break to prevent infinite loop
      break if counter > 100
    end

    subdomain
  end

  def valid_subdomain_format?(subdomain)
    return false if subdomain.blank?
    subdomain.match?(/\A[a-z0-9]{3,30}\z/)
  end

  def reserved_subdomain?(subdomain)
    reserved_subdomains = %w[
      www api app admin dashboard mail email ftp ssh
      support help docs blog news about contact
      staging test dev development production
      app1 app2 app3 secure ssl cdn assets static
      datareflow dataflow data flow reflow
      billing payment payments invoice invoices
      account accounts user users customer customers
      team teams organization organizations
      pipeline pipelines connector connectors
      webhook webhooks notification notifications
      analytics reports reporting metrics
      settings config configuration configurations
      integration integrations sync synchronization
      backup backups archive archives
      monitor monitoring logs logging
      security audit audits compliance
      trial trials
      enterprise business professional starter free
    ]

    reserved_subdomains.include?(subdomain)
  end
end

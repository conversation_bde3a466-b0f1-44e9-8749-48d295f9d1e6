class Users::PasswordsController < Devise::PasswordsController
  # Custom passwords controller for DataReflow password reset functionality
  # Handles password reset flow with email delivery and secure token management
  # Prevents unsafe redirect errors by redirecting to login page instead of subdomains

  layout "auth"

  # GET /resource/password/new
  # def new
  #   super
  # end

  # POST /resource/password
  def create
    super
  end

  # GET /resource/password/edit?reset_password_token=abcdef
  # def edit
  #   super
  # end

  # PUT /resource/password
  def update
    super
  end

  protected

  # The path used after sending reset password instructions
  def after_sending_reset_password_instructions_path_for(resource_name)
    new_session_path(resource_name)
  end

  # The path used after resetting password.
  def after_resetting_password_path_for(resource)
    # For password reset, redirect to login page instead of subdomain
    # Use<PERSON> can navigate to their subdomain after signing in
    new_session_path(resource_name)
  end
end

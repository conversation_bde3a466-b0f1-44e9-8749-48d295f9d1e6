class ApiDocsController < ApplicationController
  layout "api_docs"
  skip_before_action :authenticate_user!

  def index
    # Main API documentation landing page
    @api_version = "v1"
    @total_endpoints = count_api_endpoints
  end

  def authentication
    # Authentication guide
  end

  def quickstart
    # Quick start guide
  end

  def examples
    # Code examples in different languages
  end

  private

  def count_api_endpoints
    # Count the total number of documented endpoints
    swagger_file = Rails.root.join("swagger", "v1", "swagger.yaml")
    return 0 unless File.exist?(swagger_file)

    begin
      swagger_data = YAML.load_file(swagger_file)
      swagger_data["paths"]&.values&.sum { |path| path.keys.count } || 0
    rescue
      0
    end
  end
end

class Agent::BaseController < ApplicationController
  layout "dashboard"

  before_action :authenticate_user!
  before_action :set_current_account
  before_action :ensure_agent_features_enabled

  protected

  def set_current_account
    @current_account = current_user.account
  end

  def ensure_agent_features_enabled
    unless @current_account.agent_features_enabled?
      flash[:alert] = "AI agent features require a paid subscription plan."
      redirect_to subscription_path
      return false
    end

    true
  end

  def check_agent_limits
    return true unless @current_account.agent_features_enabled?

    unless @current_account.can_generate_agent_recommendations?
      remaining = @current_account.agent_recommendations_remaining_this_month
      plan_name = @current_account.subscription.plan_name

      flash[:alert] = if remaining.zero?
        "You've reached your monthly recommendation limit for the #{plan_name} plan. Upgrade for more recommendations."
      else
        "You have #{remaining} recommendation#{remaining == 1 ? '' : 's'} remaining this month."
      end

      redirect_to agent_recommendations_path
      return false
    end

    true
  end

  def upgrade_path
    # Assumes billing routes exist
    billing_upgrade_path
  rescue
    root_path
  end
end

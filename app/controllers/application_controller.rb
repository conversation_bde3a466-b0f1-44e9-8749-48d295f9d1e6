class ApplicationController < ActionController::Base
  # Only allow modern browsers supporting webp images, web push, badges, import maps, CSS nesting, and CSS :has.
  allow_browser versions: :modern

  include ErrorHandling
  include PerformanceMonitoring

  protect_from_forgery with: :exception

  before_action :authenticate_user!, unless: :devise_controller?
  before_action :configure_permitted_parameters, if: :devise_controller?
  before_action :set_current_account
  before_action :set_current_user

  private

  def set_current_account
    if user_signed_in?
      Current.account = current_user.account
    elsif request.subdomain.present? && request.subdomain != "www"
      # Handle subdomain-based routing for public pages
      account = Account.find_by(subdomain: request.subdomain)
      Current.account = account if account
    end
  end

  def set_current_user
    if user_signed_in?
      Current.user = current_user
      # Set user ID in signed cookie for WebSocket authentication
      cookies.signed[:user_id] = current_user.id
    else
      cookies.delete(:user_id)
    end
  end

  def configure_permitted_parameters
    devise_parameter_sanitizer.permit(:sign_up, keys: [ :first_name, :last_name ])
    devise_parameter_sanitizer.permit(:account_update, keys: [ :first_name, :last_name, :time_zone ])
  end

  def current_account
    Current.account
  end

  # Set basic metrics for sidebar navigation
  def set_sidebar_metrics
    return unless current_account

    # Set @account for views that might need it
    @account = current_account
    @user = current_user

    @pipeline_metrics ||= {
      total_pipelines: current_account.pipelines.count
    }

    @connector_metrics ||= {
      total: current_account.data_connectors.count
    }
  rescue => e
    Rails.logger.error "Error setting sidebar metrics: #{e.message}"
    @pipeline_metrics ||= { total_pipelines: 0 }
    @connector_metrics ||= { total: 0 }
  end

  helper_method :current_account
end

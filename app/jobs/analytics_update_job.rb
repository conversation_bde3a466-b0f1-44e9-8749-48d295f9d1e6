class AnalyticsUpdateJob < ApplicationJob
  queue_as :analytics

  # Periodic job to update analytics data for all active accounts
  def perform(account_id = nil)
    if account_id
      update_account_analytics(account_id)
    else
      update_all_account_analytics
    end
  end

  private

  def update_all_account_analytics
    Rails.logger.info "Starting analytics update for all accounts"
    
    Account.joins(:subscription)
           .where.not(subscriptions: { status: 'cancelled' })
           .find_each(batch_size: 100) do |account|
      
      update_account_analytics(account.id)
      sleep(0.1) # Small delay to avoid overwhelming the system
    end

    Rails.logger.info "Completed analytics update for all accounts"
  end

  def update_account_analytics(account_id)
    Rails.logger.info "Updating analytics for account: #{account_id}"
    
    begin
      account = Account.find(account_id)
      broadcast_service = AnalyticsBroadcastService.new(account_id)

      # Update execution trends chart
      execution_data = generate_execution_trends_data
      broadcast_service.broadcast_chart_update('execution_trends', execution_data)

      # Update data volume chart
      volume_data = generate_data_volume_data
      broadcast_service.broadcast_chart_update('data_volume', volume_data)

      # Update metrics overview
      metrics = generate_overview_metrics(account)
      broadcast_service.broadcast_metrics_update(metrics)

      # Check for alerts
      alerts = check_for_alerts(account)
      alerts.each do |alert|
        broadcast_service.broadcast_alert(alert[:message], alert[:type], alert[:duration])
      end

      Rails.logger.info "Analytics updated successfully for account: #{account_id}"

    rescue ActiveRecord::RecordNotFound => e
      Rails.logger.error "Account not found: #{account_id} - #{e.message}"
    rescue => e
      Rails.logger.error "Error updating analytics for account #{account_id}: #{e.message}"
      Rails.logger.error e.backtrace.join("\n")
    end
  end

  def generate_execution_trends_data
    # Generate realistic trends data
    dates = 7.days.ago.to_date..Date.current
    
    {
      labels: dates.map { |date| date.strftime("%m/%d") },
      datasets: [
        {
          label: "Successful Executions",
          data: dates.map { |date| 
            base = 20
            # Add weekly pattern (higher on weekdays)
            weekday_boost = date.weekday? ? 15 : 5
            # Add some randomness
            variation = rand(-5..10)
            [base + weekday_boost + variation, 0].max
          },
          borderColor: "#10B981",
          backgroundColor: "rgba(16, 185, 129, 0.1)"
        },
        {
          label: "Failed Executions", 
          data: dates.map { |date|
            # Failures are typically lower and more random
            base = 2
            variation = rand(-1..5)
            [base + variation, 0].max
          },
          borderColor: "#EF4444",
          backgroundColor: "rgba(239, 68, 68, 0.1)"
        }
      ]
    }
  end

  def generate_data_volume_data
    # Generate realistic volume data
    dates = 7.days.ago.to_date..Date.current
    
    {
      labels: dates.map { |date| date.strftime("%m/%d") },
      datasets: [
        {
          label: "Records Processed",
          data: dates.map { |date|
            base = 1500
            # Higher volume on business days
            business_boost = date.weekday? ? 1000 : 200
            # Add growth trend
            days_since_start = (date - 7.days.ago.to_date).to_i
            growth = days_since_start * 50
            # Random variation
            variation = rand(-300..500)
            
            base + business_boost + growth + variation
          },
          borderColor: "#3B82F6",
          backgroundColor: "rgba(59, 130, 246, 0.1)"
        }
      ]
    }
  end

  def generate_overview_metrics(account)
    {
      total_pipelines: account.pipelines.count,
      active_pipelines: account.pipelines.where(status: 'active').count,
      total_executions: rand(100..500),
      success_rate: rand(85..98),
      avg_execution_time: rand(30..180),
      data_processed: rand(10_000..100_000)
    }
  end

  def check_for_alerts(account)
    alerts = []

    # Simulate performance alerts
    if rand(100) < 10 # 10% chance
      alerts << {
        message: "Performance degradation detected in #{account.pipelines.active.sample&.name || 'Pipeline'}",
        type: 'warning',
        duration: 8000
      }
    end

    # Simulate success notifications
    if rand(100) < 5 # 5% chance
      alerts << {
        message: "All pipelines running smoothly. Great job!",
        type: 'success',
        duration: 5000
      }
    end

    # Storage usage alerts
    if rand(100) < 15 # 15% chance
      usage_percent = rand(70..95)
      alerts << {
        message: "Storage usage at #{usage_percent}%. Consider upgrading your plan.",
        type: 'info',
        duration: 10000
      }
    end

    alerts
  end
end

# Schedule this job to run periodically
# This would typically be done in a scheduler like cron or whenever gem:
# 
# # config/schedule.rb (if using whenever gem)
# every 30.seconds do
#   runner "AnalyticsUpdateJob.perform_later"
# end
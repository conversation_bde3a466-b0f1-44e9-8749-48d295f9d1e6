/**
 * DataReflow Component Library
 * 
 * Reusable component styles based on the design system tokens
 * Import design-tokens.css before this file
 */

/* ===== BUTTONS ===== */

.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  font-family: var(--font-family-primary);
  font-weight: var(--font-weight-semibold);
  text-decoration: none;
  border: none;
  cursor: pointer;
  transition: all var(--duration-200) var(--ease-in-out);
  border-radius: var(--radius-lg);
  font-size: var(--text-base);
  line-height: var(--leading-normal);
  padding: var(--space-3) var(--space-6);
  height: var(--button-height-md);
  box-shadow: var(--shadow-sm);
}

.btn:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgb(59 130 246 / 0.1);
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none !important;
}

/* Button Variants */
.btn-primary {
  background-color: var(--color-primary-600);
  color: var(--color-white);
}

.btn-primary:hover:not(:disabled) {
  background-color: var(--color-primary-700);
  transform: translateY(-1px);
  box-shadow: var(--shadow-lg);
}

.btn-secondary {
  background-color: var(--color-white);
  color: var(--color-gray-900);
  border: 1px solid var(--color-gray-200);
}

.btn-secondary:hover:not(:disabled) {
  background-color: var(--color-gray-50);
  border-color: var(--color-gray-300);
  box-shadow: var(--shadow-md);
}

.btn-outline {
  background-color: transparent;
  color: var(--color-primary-600);
  border: 1px solid var(--color-primary-600);
}

.btn-outline:hover:not(:disabled) {
  background-color: var(--color-primary-50);
  color: var(--color-primary-700);
}

.btn-ghost {
  background-color: transparent;
  color: var(--color-gray-600);
  box-shadow: none;
}

.btn-ghost:hover:not(:disabled) {
  background-color: var(--color-gray-100);
  color: var(--color-gray-900);
}

/* Button Sizes */
.btn-sm {
  font-size: var(--text-sm);
  padding: var(--space-2) var(--space-4);
  height: var(--button-height-sm);
}

.btn-lg {
  font-size: var(--text-lg);
  padding: var(--space-4) var(--space-8);
  height: var(--button-height-lg);
}

.btn-xl {
  font-size: var(--text-xl);
  padding: var(--space-5) var(--space-10);
  height: var(--button-height-xl);
}

/* Button with Icon */
.btn-icon {
  gap: var(--space-2);
}

.btn-icon-only {
  padding: var(--space-3);
  width: var(--button-height-md);
}

/* ===== CARDS ===== */

.card {
  background-color: var(--color-white);
  border-radius: var(--radius-2xl);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--color-gray-100);
  overflow: hidden;
}

.card-hover {
  transition: all var(--duration-200) var(--ease-in-out);
}

.card-hover:hover {
  box-shadow: var(--shadow-lg);
  transform: translateY(-2px);
}

.card-header {
  padding: var(--space-6);
  border-bottom: 1px solid var(--color-gray-100);
  background-color: var(--color-gray-50);
}

.card-body {
  padding: var(--space-6);
}

.card-footer {
  padding: var(--space-6);
  border-top: 1px solid var(--color-gray-100);
  background-color: var(--color-gray-50);
}

.card-title {
  font-size: var(--text-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--color-gray-900);
  margin: 0;
}

.card-subtitle {
  font-size: var(--text-sm);
  color: var(--color-gray-600);
  margin: var(--space-1) 0 0 0;
}

.card-text {
  color: var(--color-gray-600);
  line-height: var(--leading-relaxed);
  margin: 0;
}

/* Feature Card */
.feature-card {
  background-color: var(--color-gray-50);
  padding: var(--space-8);
  border-radius: var(--radius-2xl);
  transition: all var(--duration-200) var(--ease-in-out);
  border: 1px solid var(--color-gray-100);
}

.feature-card:hover {
  box-shadow: var(--shadow-lg);
  transform: translateY(-2px);
}

.feature-icon {
  width: 4rem;
  height: 4rem;
  background-color: var(--color-primary-100);
  border-radius: var(--radius-xl);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: var(--space-6);
  color: var(--color-primary-600);
}

.feature-title {
  font-size: var(--text-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--color-gray-900);
  margin: 0 0 var(--space-3) 0;
}

.feature-description {
  color: var(--color-gray-600);
  line-height: var(--leading-relaxed);
  margin: 0;
}

/* ===== FORMS ===== */

.form-group {
  margin-bottom: var(--space-6);
}

.form-label {
  display: block;
  font-size: var(--text-sm);
  font-weight: var(--font-weight-medium);
  color: var(--color-gray-700);
  margin-bottom: var(--space-2);
}

.form-label-required::after {
  content: " *";
  color: var(--color-error-500);
}

.form-input,
.form-textarea,
.form-select {
  width: 100%;
  padding: var(--space-3) var(--space-4);
  border: 1px solid var(--color-gray-300);
  border-radius: var(--radius-lg);
  font-size: var(--text-base);
  font-family: var(--font-family-primary);
  background-color: var(--color-white);
  transition: all var(--duration-200) var(--ease-in-out);
  height: var(--input-height-md);
}

.form-textarea {
  height: auto;
  min-height: 6rem;
  resize: vertical;
}

.form-input:focus,
.form-textarea:focus,
.form-select:focus {
  outline: none;
  border-color: var(--color-primary-500);
  box-shadow: 0 0 0 3px rgb(59 130 246 / 0.1);
}

.form-input:invalid,
.form-textarea:invalid,
.form-select:invalid {
  border-color: var(--color-error-500);
}

.form-input:invalid:focus,
.form-textarea:invalid:focus,
.form-select:invalid:focus {
  box-shadow: 0 0 0 3px rgb(239 68 68 / 0.1);
}

.form-error {
  font-size: var(--text-sm);
  color: var(--color-error-600);
  margin-top: var(--space-1);
}

.form-success {
  font-size: var(--text-sm);
  color: var(--color-success-600);
  margin-top: var(--space-1);
}

.form-help {
  font-size: var(--text-sm);
  color: var(--color-gray-500);
  margin-top: var(--space-1);
}

/* Input Sizes */
.form-input-sm,
.form-select-sm {
  padding: var(--space-2) var(--space-3);
  font-size: var(--text-sm);
  height: var(--input-height-sm);
}

.form-input-lg,
.form-select-lg {
  padding: var(--space-4) var(--space-5);
  font-size: var(--text-lg);
  height: var(--input-height-lg);
}

/* ===== NAVIGATION ===== */

.nav-header {
  background-color: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(8px);
  border-bottom: 1px solid var(--color-gray-200);
  position: sticky;
  top: 0;
  z-index: var(--z-50);
  height: var(--header-height);
}

.nav-container {
  max-width: var(--container-7xl);
  margin: 0 auto;
  padding: 0 var(--space-4);
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 100%;
}

.nav-brand {
  display: flex;
  align-items: center;
}

.nav-logo {
  font-size: var(--text-2xl);
  font-weight: var(--font-weight-bold);
  background: linear-gradient(135deg, var(--color-primary-600), var(--color-secondary-600));
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  text-decoration: none;
}

.nav-links {
  display: none;
  align-items: center;
  gap: var(--space-8);
}

@media (min-width: 768px) {
  .nav-links {
    display: flex;
  }
}

.nav-link {
  color: var(--color-gray-600);
  font-weight: var(--font-weight-medium);
  text-decoration: none;
  transition: color var(--duration-200) var(--ease-in-out);
}

.nav-link:hover {
  color: var(--color-gray-900);
}

.nav-link-active {
  color: var(--color-primary-600);
  font-weight: var(--font-weight-semibold);
}

/* Mobile Menu */
.nav-mobile-toggle {
  display: block;
  background: none;
  border: none;
  padding: var(--space-2);
  color: var(--color-gray-600);
  cursor: pointer;
}

@media (min-width: 768px) {
  .nav-mobile-toggle {
    display: none;
  }
}

.nav-mobile-menu {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background-color: var(--color-white);
  border-bottom: 1px solid var(--color-gray-200);
  box-shadow: var(--shadow-lg);
  padding: var(--space-4);
  display: none;
}

.nav-mobile-menu.active {
  display: block;
}

.nav-mobile-links {
  display: flex;
  flex-direction: column;
  gap: var(--space-4);
}

/* ===== BADGES ===== */

.badge {
  display: inline-flex;
  align-items: center;
  padding: var(--space-1) var(--space-3);
  border-radius: var(--radius-full);
  font-size: var(--text-xs);
  font-weight: var(--font-weight-medium);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.badge-primary {
  background-color: var(--color-primary-100);
  color: var(--color-primary-800);
}

.badge-secondary {
  background-color: var(--color-gray-100);
  color: var(--color-gray-800);
}

.badge-success {
  background-color: var(--color-success-100);
  color: var(--color-success-800);
}

.badge-warning {
  background-color: var(--color-warning-100);
  color: var(--color-warning-800);
}

.badge-error {
  background-color: var(--color-error-100);
  color: var(--color-error-800);
}

.badge-info {
  background-color: var(--color-info-100);
  color: var(--color-info-800);
}

/* ===== ALERTS ===== */

.alert {
  display: flex;
  align-items: flex-start;
  padding: var(--space-4);
  border-radius: var(--radius-lg);
  border: 1px solid;
  margin-bottom: var(--space-4);
}

.alert-success {
  background-color: var(--color-success-50);
  border-color: var(--color-success-200);
  color: var(--color-success-800);
}

.alert-warning {
  background-color: var(--color-warning-50);
  border-color: var(--color-warning-200);
  color: var(--color-warning-800);
}

.alert-error {
  background-color: var(--color-error-50);
  border-color: var(--color-error-200);
  color: var(--color-error-800);
}

.alert-info {
  background-color: var(--color-info-50);
  border-color: var(--color-info-200);
  color: var(--color-info-800);
}

.alert-icon {
  flex-shrink: 0;
  width: 1.25rem;
  height: 1.25rem;
  margin-right: var(--space-3);
  margin-top: 0.125rem;
}

.alert-content {
  flex: 1;
}

.alert-title {
  font-weight: var(--font-weight-medium);
  margin: 0 0 var(--space-1) 0;
}

.alert-message {
  font-size: var(--text-sm);
  margin: 0;
  line-height: var(--leading-relaxed);
}

/* ===== TABLES ===== */

.table-container {
  overflow-x: auto;
  background-color: var(--color-white);
  border-radius: var(--radius-lg);
  border: 1px solid var(--color-gray-200);
  box-shadow: var(--shadow-sm);
}

.table {
  width: 100%;
  border-collapse: collapse;
  font-size: var(--text-sm);
}

.table-header {
  background-color: var(--color-gray-50);
}

.table-row {
  transition: background-color var(--duration-150) var(--ease-in-out);
}

.table-row:hover {
  background-color: var(--color-gray-50);
}

.table-row:not(:last-child) {
  border-bottom: 1px solid var(--color-gray-200);
}

.table-cell {
  padding: var(--space-4) var(--space-6);
  text-align: left;
  vertical-align: middle;
}

.table-cell-header {
  font-weight: var(--font-weight-medium);
  color: var(--color-gray-900);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  font-size: var(--text-xs);
}

.table-cell-numeric {
  text-align: right;
  font-variant-numeric: tabular-nums;
}

/* ===== PROGRESS BARS ===== */

.progress {
  width: 100%;
  height: 0.5rem;
  background-color: var(--color-gray-200);
  border-radius: var(--radius-full);
  overflow: hidden;
}

.progress-bar {
  height: 100%;
  background-color: var(--color-primary-600);
  border-radius: var(--radius-full);
  transition: width var(--duration-300) var(--ease-out);
}

.progress-bar-success {
  background-color: var(--color-success-600);
}

.progress-bar-warning {
  background-color: var(--color-warning-600);
}

.progress-bar-error {
  background-color: var(--color-error-600);
}

/* Progress Sizes */
.progress-sm {
  height: 0.25rem;
}

.progress-lg {
  height: 0.75rem;
}

/* ===== LOADING STATES ===== */

.loading-spinner {
  display: inline-block;
  width: 1rem;
  height: 1rem;
  border: 2px solid var(--color-gray-200);
  border-radius: 50%;
  border-top-color: var(--color-primary-600);
  animation: spin 1s ease-in-out infinite;
}

.loading-spinner-lg {
  width: 2rem;
  height: 2rem;
  border-width: 3px;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.loading-skeleton {
  background: linear-gradient(90deg, var(--color-gray-200) 25%, var(--color-gray-100) 50%, var(--color-gray-200) 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
  border-radius: var(--radius-md);
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* ===== ENHANCED SIDEBAR NAVIGATION ===== */

.nav-section {
  margin-bottom: var(--space-4);
}

.nav-section-active {
  /* Active section styling can be added here if needed */
}

.nav-section button[data-sidebar-target="toggle"] {
  width: 100%;
  background: none;
  border: none;
  cursor: pointer;
  font-family: inherit;
}

.nav-section button[data-sidebar-target="toggle"]:hover {
  background-color: var(--color-gray-50);
}

.nav-section button[data-sidebar-target="toggle"]:focus {
  outline: 2px solid var(--color-primary-500);
  outline-offset: 2px;
  border-radius: var(--radius-md);
}

.nav-section [data-sidebar-target="content"] {
  overflow: hidden;
  transition: max-height 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.nav-section [data-sidebar-target="icon"] {
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Badge styles for navigation */
.badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--space-1) var(--space-2);
  border-radius: var(--radius-full);
  font-size: var(--text-xs);
  font-weight: var(--font-weight-medium);
  line-height: 1;
  white-space: nowrap;
}

.badge-primary {
  background-color: var(--color-primary-100);
  color: var(--color-primary-700);
}

.badge-secondary {
  background-color: var(--color-gray-100);
  color: var(--color-gray-700);
}

.badge-success {
  background-color: var(--color-success-100);
  color: var(--color-success-700);
}

.badge-warning {
  background-color: var(--color-warning-100);
  color: var(--color-warning-700);
}

.badge-info {
  background-color: var(--color-info-100);
  color: var(--color-info-700);
}

/* Mobile overlay for sidebar */
.sidebar-mobile-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: var(--z-50);
  display: none;
  opacity: 0;
  transition: opacity 0.15s ease-in-out;
}

.sidebar-mobile-overlay.opacity-100 {
  opacity: 1;
}

.sidebar-mobile-overlay.opacity-0 {
  opacity: 0;
}

/* Screen reader only content */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* ===== UTILITY CLASSES ===== */

/* Layout */
.container {
  max-width: var(--container-7xl);
  margin: 0 auto;
  padding: 0 var(--space-4);
}

.container-sm {
  max-width: var(--container-4xl);
  margin: 0 auto;
  padding: 0 var(--space-4);
}

.container-xs {
  max-width: var(--container-2xl);
  margin: 0 auto;
  padding: 0 var(--space-4);
}

/* Flexbox */
.flex {
  display: flex;
}

.flex-col {
  flex-direction: column;
}

.items-center {
  align-items: center;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.gap-2 {
  gap: var(--space-2);
}

.gap-4 {
  gap: var(--space-4);
}

.gap-6 {
  gap: var(--space-6);
}

.gap-8 {
  gap: var(--space-8);
}

/* Grid */
.grid {
  display: grid;
}

.grid-cols-1 {
  grid-template-columns: repeat(1, minmax(0, 1fr));
}

.grid-cols-2 {
  grid-template-columns: repeat(2, minmax(0, 1fr));
}

.grid-cols-3 {
  grid-template-columns: repeat(3, minmax(0, 1fr));
}

.grid-cols-4 {
  grid-template-columns: repeat(4, minmax(0, 1fr));
}

/* Responsive Grid */
@media (min-width: 768px) {
  .md\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .md\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .md\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }
}

@media (min-width: 1024px) {
  .lg\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .lg\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }
}

/* Text Utilities */
.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.font-medium {
  font-weight: var(--font-weight-medium);
}

.font-semibold {
  font-weight: var(--font-weight-semibold);
}

.font-bold {
  font-weight: var(--font-weight-bold);
}

/* Spacing Utilities */
.mt-4 {
  margin-top: var(--space-4);
}

.mb-4 {
  margin-bottom: var(--space-4);
}

.mt-8 {
  margin-top: var(--space-8);
}

.mb-8 {
  margin-bottom: var(--space-8);
}

/* Visibility */
.hidden {
  display: none;
}

.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

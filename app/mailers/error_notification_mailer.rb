# frozen_string_literal: true

class ErrorNotificationMailer < ApplicationMailer
  default from: "<EMAIL>"

  def critical_error(notification_data, admin_emails)
    @notification = notification_data
    @severity = notification_data[:severity]
    @error_id = notification_data[:error_id]
    @environment = notification_data[:environment]
    
    subject = "[#{@environment.upcase}] Critical Error - #{@notification[:error_class]}"
    
    mail(
      to: admin_emails,
      subject: subject
    )
  end
end
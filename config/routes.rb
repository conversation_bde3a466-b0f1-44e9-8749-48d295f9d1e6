Rails.application.routes.draw do
  mount Rswag::Ui::Engine => "/api-docs"
  mount Rswag::Api::Engine => "/api-docs"
  devise_for :users, controllers: {
    sessions: "users/sessions",
    registrations: "users/registrations",
    passwords: "users/passwords"
  }

  # Health check endpoints (no authentication required)
  get "health", to: "health#show"
  get "health/deep", to: "health#deep"
  get "health/ready", to: "health#ready"
  get "health/live", to: "health#live"

  # Design System (for development and reference)
  get "design-system", to: "design_system#index"

  # API Documentation
  get "docs/api", to: "api_docs#index", as: :api_documentation
  get "docs/api/authentication", to: "api_docs#authentication", as: :api_authentication_docs
  get "docs/api/quickstart", to: "api_docs#quickstart", as: :api_quickstart
  get "docs/api/examples", to: "api_docs#examples", as: :api_examples

  # API routes
  namespace :api do
    get "check_subdomain", to: "subdomain#check"
  end

  # API routes
  namespace :api do
    # Authentication endpoints
    post "login", to: "auth#login"
    post "register", to: "auth#register"
    delete "logout", to: "auth#logout"
    get "me", to: "auth#me"

    # API v1 namespace for versioned endpoints
    namespace :v1 do
      resources :pipelines do
        member do
          post :execute
        end
      end

      resources :data_connectors, path: "connectors" do
        member do
          post :test_connection
        end
      end

      resources :pipeline_executions, path: "executions", only: [ :index, :show ] do
        member do
          patch :cancel
        end
      end

      resources :agent_recommendations, path: "recommendations", only: [ :index, :show ] do
        member do
          post :implement
        end
      end
    end
  end

  # Web application routes for subdomains (excluding www)
  constraints(subdomain: /^(?!www)[a-z0-9]+$/) do
    root "dashboard#index", as: :subdomain_root
    get "sidebar-test", to: "dashboard#sidebar_test", as: :sidebar_test

    # Dashboard AJAX endpoints
    get "dashboard/metrics", to: "dashboard#metrics"
    get "dashboard/pipeline_metrics", to: "dashboard#pipeline_metrics"
    get "dashboard/connector_metrics", to: "dashboard#connector_metrics"
    get "dashboard/usage_metrics", to: "dashboard#usage_metrics"
    get "dashboard/system_health", to: "dashboard#system_health"
    get "dashboard/recent_activity", to: "dashboard#recent_activity"
    post "dashboard/test_broadcast", to: "dashboard#test_broadcast" # Development only

    # Onboarding flow
    resources :onboarding, only: [ :index ] do
      collection do
        get :welcome
        get :setup_profile
        get :create_first_connection
        get :create_first_pipeline
        get :invite_team
        post :complete
        patch :mark_step_complete
        post :create_connector
        post :create_pipeline
      end
    end

    # Subscription management
    resource :subscription, only: [ :show, :create, :update ] do
      member do
        patch :cancel, to: "subscriptions#cancel"
        get :confirm, to: "subscriptions#confirm"
      end
    end

    # Team management
    resources :team_members do
      collection do
        get :accept_invitation
        patch :reject_invitation
      end
    end

    # Core data pipeline resources
    resources :pipelines do
      member do
        post :execute
        patch :toggle_status
      end
    end

    resources :data_connectors, path: "connectors" do
      member do
        post :test_connection
      end
    end

    # Custom Dashboards for data visualization
    resources :dashboards do
      member do
        post :duplicate
        post :share
        post :add_widget
        patch :update_layout
      end
    end
    
    # Analytics routes
    resources :analytics, only: [ :index ] do
      collection do
        get :pipeline_performance
        get :data_quality
        get :usage_trends
        get :chart_data
        get :execution_trends
        get :data_volume
        get :chart  # New route for Turbo Frame charts
        get :chart_test  # Test route for debugging charts
        get :debug  # Debug info endpoint
      end
    end

    # Notification routes
    resources :notifications, only: [ :index, :show, :destroy ] do
      member do
        patch :mark_as_read
        patch :mark_as_unread
      end
      collection do
        get :unread
        patch :mark_all_as_read
        get :count
      end
    end

    # AI-powered insights and analytics
    namespace :ai do
      resources :pipeline_insights, only: [ :show ] do
        member do
          get :opportunities
          get :market_analysis
          get :pricing_recommendations
        end
        collection do
          get :bulk_analysis
        end
      end

      # Data product recommendations and monetization
      resources :pipelines, only: [] do
        resources :data_product_recommendations, only: [ :index, :show ], path: "product_recommendations" do
          collection do
            get :compare
            get :market_analysis
            get :revenue_projections
            get :export_recommendations
          end
          member do
            get :implementation_plan
          end
        end
      end
    end

    # AI Agent features for passive income system
    namespace :agent do
      resources :recommendations, only: [ :index, :show ] do
        member do
          patch :accept
          patch :reject
          post :implement
        end
        collection do
          post :generate
          post :refresh
          get :analytics
        end
      end

      resources :templates, only: [ :index, :show, :create ] do
        member do
          post :purchase
          get :preview
        end
        collection do
          get :recommendations
          get :marketplace_analytics
          get :my_templates
          post :create_from_pipeline
        end
      end

      resources :revenue, only: [ :index ] do
        collection do
          get :dashboard
          get :analytics
          get :export
          get :mrr_analysis
          get :subscription_analysis
        end
      end
    end
  end

  # Public dashboard viewing (no subdomain constraint)
  get "public/dashboard/:slug", to: "dashboards#public", as: :public_dashboard
  
  # Stripe webhooks (no subdomain constraint)
  namespace :stripe do
    resources :webhooks, only: [ :create ]
  end

  # Error pages
  get "/404", to: "errors#not_found"
  get "/422", to: "errors#unprocessable_entity"
  get "/500", to: "errors#internal_server_error"

  # Health check
  get "up" => "rails/health#show", as: :rails_health_check

  # PWA files
  # get "manifest" => "rails/pwa#manifest", as: :pwa_manifest
  # get "service-worker" => "rails/pwa#service_worker", as: :pwa_service_worker

  # Marketing pages
  get "features", to: "marketing#index", anchor: "features"
  get "integrations", to: "marketing#integrations"
  get "pricing", to: "marketing#index", anchor: "pricing"
  get "testimonials", to: "marketing#index", anchor: "testimonials"
  get "about", to: "marketing#about"
  get "contact", to: "marketing#contact"
  post "contact", to: "marketing#create_contact"
  get "help", to: "marketing#help"
  get "security", to: "marketing#security"
  get "privacy", to: "marketing#privacy"
  get "docs", to: "marketing#docs"
  get "api", to: "marketing#docs"  # Alias for docs
  get "community", to: "marketing#community"
  get "press", to: "marketing#press"

  # Default root route for main domain (no subdomain or www) - must be last
  root "marketing#index"
end

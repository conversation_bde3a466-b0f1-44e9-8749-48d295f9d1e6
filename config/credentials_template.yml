# This is a template for Rails credentials
# Edit with: rails credentials:edit

# Database
database:
  production_url: # PostgreSQL connection string for production

# Redis
redis:
  url: redis://localhost:6379/0

# Stripe
stripe:
  publishable_key: 
  secret_key: 
  webhook_secret: 

# AWS
aws:
  access_key_id: 
  secret_access_key: 
  region: us-east-1
  bucket: 

# Error Tracking
rollbar:
  access_token: 

# Email
mail:
  from: <EMAIL>
  smtp:
    address: 
    port: 587
    user_name: 
    password: 

# Application
app:
  host: localhost:3000
  protocol: http

# Devise JWT
devise_jwt_secret_key: # Secret key for JWT token signing (generate with: rails secret)

# Secret key base is already in credentials by default
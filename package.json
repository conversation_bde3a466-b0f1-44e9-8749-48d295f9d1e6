{"name": "datareflow_io", "version": "1.0.0", "description": "A powerful, AI-driven data pipeline management platform built with Rails 8.0. DataReflow enables businesses to create, manage, and optimize data pipelines with intelligent recommendations and automated improvements.", "main": "index.js", "directories": {"doc": "docs", "lib": "lib", "test": "test"}, "scripts": {"build": "cp node_modules/apexcharts/dist/apexcharts.esm.js vendor/javascript/apexcharts.js", "test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"type": "git", "url": "git+https://github.com/kojjob/DataReflow_io.git"}, "keywords": [], "author": "", "license": "ISC", "type": "commonjs", "bugs": {"url": "https://github.com/kojjob/DataReflow_io/issues"}, "homepage": "https://github.com/kojjob/DataReflow_io#readme", "dependencies": {"apexcharts": "^5.3.2"}}
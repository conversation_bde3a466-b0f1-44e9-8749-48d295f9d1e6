# DataReflow.io

A powerful, AI-driven data pipeline management platform built with Rails 8.0. DataReflow enables businesses to create, manage, and optimize data pipelines with intelligent recommendations and automated improvements.

## 🚀 Features

### Core Features
- **Multi-Tenant Architecture**: Secure subdomain-based tenant isolation
- **Data Pipeline Management**: Visual pipeline builder with drag-and-drop interface
- **AI Agent System**: Intelligent recommendations for pipeline optimization
- **Template Marketplace**: Share and monetize pipeline templates
- **Real-time Monitoring**: Live pipeline execution tracking and metrics
- **Team Collaboration**: Role-based access control and team workspaces

### Data Connectors
- PostgreSQL, MySQL, and other databases
- REST APIs and webhooks
- CSV/Excel file uploads
- Cloud storage (S3, Google Cloud Storage, Azure Blob)
- Custom connector framework

### AI-Powered Features
- Automated pipeline optimization recommendations
- Data quality monitoring and alerts
- Performance bottleneck detection
- Cost optimization suggestions
- Template generation from existing pipelines

## 📋 Requirements

- Ruby 3.4.3
- Rails 8.0.2
- PostgreSQL 14+
- Redis 7+ (optional, for caching)
- Node.js 18+ (for JavaScript runtime)

## 🛠️ Installation

### 1. Clone the repository
```bash
git clone https://github.com/yourusername/datareflow_io.git
cd datareflow_io
```

### 2. Install dependencies
```bash
# Install Ruby gems
bundle install

# Install JavaScript dependencies
yarn install # or npm install
```

### 3. Setup the database
```bash
# Create the database
rails db:create

# Run migrations
rails db:migrate

# Seed sample data (optional)
rails db:seed
```

### 4. Configure environment variables
```bash
# Copy the credentials template
cp config/credentials_template.yml config/credentials.yml.enc

# Edit credentials (this will open your default editor)
EDITOR="code --wait" rails credentials:edit
```

Add the following to your credentials:
```yaml
secret_key_base: [your-secret-key]

stripe:
  publishable_key: [your-stripe-publishable-key]
  secret_key: [your-stripe-secret-key]
  webhook_secret: [your-stripe-webhook-secret]

devise:
  jwt_secret_key: [your-jwt-secret]

aws:
  access_key_id: [your-aws-key]
  secret_access_key: [your-aws-secret]
  region: us-east-1
  bucket: your-bucket-name
```

### 5. Setup development environment
```bash
# Run the setup script
bin/setup

# Or manually:
bin/rails tailwindcss:build
bin/rails assets:precompile
```

## 🚀 Running the Application

### Development Server
```bash
# Start the Rails server
bin/rails server

# Or with Foreman (recommended)
foreman start -f Procfile.dev

# The application will be available at:
# Main site: http://localhost:3000
# Tenant sites: http://[subdomain].localhost:3000
```

### Background Jobs
```bash
# Start Solid Queue for background job processing
bin/rails solid_queue:start
```

### Running Tests
```bash
# Run all tests
bundle exec rspec

# Run specific test file
bundle exec rspec spec/models/user_spec.rb

# Run with coverage
COVERAGE=true bundle exec rspec
```

## 🏗️ Architecture

### Technology Stack
- **Backend**: Ruby on Rails 8.0.2
- **Database**: PostgreSQL
- **Authentication**: Devise + JWT
- **Background Jobs**: Solid Queue (Rails 8 native)
- **Caching**: Solid Cache
- **WebSockets**: Solid Cable
- **Payment Processing**: Stripe
- **Analytics**: Ahoy
- **Error Tracking**: Rollbar
- **Frontend**: Hotwire (Turbo + Stimulus) + Tailwind CSS

### Key Models
- `Account`: Multi-tenant account management
- `User`: User authentication and authorization
- `Pipeline`: Data pipeline configuration and execution
- `DataConnector`: Data source and destination connections
- `PipelineExecution`: Pipeline run history and logs
- `AgentRecommendation`: AI-generated optimization suggestions
- `PipelineTemplate`: Shareable pipeline templates
- `Subscription`: Plan management and billing

## 📝 Configuration

### Multi-tenancy Setup
The application uses subdomain-based multi-tenancy. Each account gets its own subdomain:
- Main marketing site: `datareflow.io`
- Customer dashboards: `customer-name.datareflow.io`

### Email Configuration
Development uses `letter_opener` to preview emails in the browser. For production, configure your SMTP settings in `config/environments/production.rb`.

### Background Jobs
The application uses Rails 8's Solid Queue for background job processing. Jobs are defined in `app/jobs/`.

### Feature Flags
Feature flags are managed with Flipper. Access the Flipper UI at `/flipper` (admin only).

## 🧪 Testing

### Running Tests
```bash
# Unit tests
bundle exec rspec spec/models

# Integration tests
bundle exec rspec spec/requests

# System tests
bundle exec rspec spec/system

# JavaScript tests
yarn test
```

### Code Quality
```bash
# Run linter
bundle exec rubocop

# Run security scanner
bundle exec brakeman

# Check for vulnerabilities
bundle audit check
```

## 📚 API Documentation

### Authentication
The API uses JWT tokens for authentication. Include the token in the Authorization header:
```
Authorization: Bearer <your-jwt-token>
```

### Endpoints
- `POST /api/login` - User login
- `POST /api/register` - User registration
- `GET /api/me` - Current user info
- `GET /api/v1/pipelines` - List pipelines
- `POST /api/v1/pipelines` - Create pipeline
- `GET /api/v1/pipelines/:id` - Get pipeline details
- `PUT /api/v1/pipelines/:id` - Update pipeline
- `DELETE /api/v1/pipelines/:id` - Delete pipeline
- `POST /api/v1/pipelines/:id/execute` - Execute pipeline

### Webhooks
Configure webhooks to receive real-time updates:
- Pipeline execution started
- Pipeline execution completed
- Pipeline execution failed
- Data quality issues detected

## 🚢 Deployment

### Docker
```bash
# Build the Docker image
docker build -t datareflow .

# Run with Docker Compose
docker-compose up
```

### Kamal (Recommended for Rails 8)
```bash
# Setup Kamal
kamal setup

# Deploy
kamal deploy
```

### Heroku
```bash
# Create Heroku app
heroku create your-app-name

# Deploy
git push heroku main

# Run migrations
heroku run rails db:migrate
```

## 🤝 Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/AmazingFeature`)
3. Commit your changes (`git commit -m 'Add some AmazingFeature'`)
4. Push to the branch (`git push origin feature/AmazingFeature`)
5. Open a Pull Request

### Development Guidelines
- Follow Ruby style guide (enforced by RuboCop)
- Write tests for new features
- Update documentation
- Keep commits atomic and well-described

## 📄 License

This project is proprietary software. All rights reserved.

## 🆘 Support

- Documentation: [docs.datareflow.io](https://docs.datareflow.io)
- Email: <EMAIL>
- Community Forum: [community.datareflow.io](https://community.datareflow.io)

## 🔒 Security

For security issues, <NAME_EMAIL> instead of using the issue tracker.

## 📈 Roadmap

### Q1 2025
- [ ] Advanced transformation builder
- [ ] Custom connector SDK
- [ ] Enterprise SSO (SAML, OAuth)
- [ ] Advanced scheduling (cron expressions)

### Q2 2025
- [ ] Data lineage tracking
- [ ] Compliance reporting (GDPR, HIPAA)
- [ ] Advanced monitoring and alerting
- [ ] Mobile application

### Q3 2025
- [ ] AI-powered data mapping
- [ ] Automated testing framework
- [ ] Multi-region deployment
- [ ] White-label solution

## 👥 Team

- Engineering: <EMAIL>
- Product: <EMAIL>
- Sales: <EMAIL>
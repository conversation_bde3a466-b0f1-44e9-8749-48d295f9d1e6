# frozen_string_literal: true

require 'rails_helper'

RSpec.describe DashboardController, type: :controller do
  let(:account) { create(:account) }
  let(:user) { create(:user, account: account) }

  before do
    sign_in user
    allow(controller).to receive(:current_account).and_return(account)
    allow(controller).to receive(:current_user).and_return(user)
    allow(controller).to receive(:set_sidebar_metrics)
  end

  describe "GET #index" do
    it "renders the dashboard successfully" do
      get :index
      expect(response).to have_http_status(:success)
      expect(response).to render_template(:index)
    end

    it "loads all required dashboard metrics" do
      get :index
      expect(assigns(:pipeline_metrics)).to be_present
      expect(assigns(:connector_metrics)).to be_present
      expect(assigns(:usage_metrics)).to be_present
      expect(assigns(:recent_activity)).to be_present
      expect(assigns(:system_health)).to be_present
    end

    it "includes enhanced metrics" do
      get :index
      expect(assigns(:performance_metrics)).to be_present
      expect(assigns(:data_quality_metrics)).to be_present
      expect(assigns(:cost_metrics)).to be_present
      expect(assigns(:alerts)).to be_present
      expect(assigns(:quick_stats)).to be_present
    end

    context "when requesting JSON format" do
      it "returns enhanced dashboard data in JSON format" do
        get :index, format: :json

        expect(response).to have_http_status(:success)
        expect(response.content_type).to include('application/json')

        json_response = JSON.parse(response.body)
        expect(json_response).to have_key('account')
        expect(json_response).to have_key('user')
        expect(json_response).to have_key('metrics')
        expect(json_response).to have_key('data')
        expect(json_response).to have_key('trends')
        expect(json_response).to have_key('meta')
      end
    end
  end

  describe "GET #metrics" do
    it "returns metrics in JSON format" do
      get :metrics, format: :json

      expect(response).to have_http_status(:success)
      expect(response.content_type).to include('application/json')

      json_response = JSON.parse(response.body)
      expect(json_response).to have_key('pipeline_metrics')
      expect(json_response).to have_key('connector_metrics')
      expect(json_response).to have_key('usage_metrics')
      expect(json_response).to have_key('system_health')
      expect(json_response).to have_key('recent_activity')
      expect(json_response).to have_key('trends')
      expect(json_response).to have_key('alerts')
    end
  end

  describe "GET #pipeline_metrics" do
    it "returns pipeline metrics in JSON format" do
      get :pipeline_metrics, format: :json

      expect(response).to have_http_status(:success)
      expect(response.content_type).to include('application/json')

      json_response = JSON.parse(response.body)
      expect(json_response).to have_key('pipeline_metrics')
    end
  end

  describe "GET #connector_metrics" do
    it "returns connector metrics in JSON format" do
      get :connector_metrics, format: :json

      expect(response).to have_http_status(:success)
      expect(response.content_type).to include('application/json')

      json_response = JSON.parse(response.body)
      expect(json_response).to have_key('connector_metrics')
    end
  end

  describe "GET #usage_metrics" do
    it "returns usage metrics in JSON format" do
      get :usage_metrics, format: :json

      expect(response).to have_http_status(:success)
      expect(response.content_type).to include('application/json')

      json_response = JSON.parse(response.body)
      expect(json_response).to have_key('usage_metrics')
    end
  end

  describe "GET #system_health" do
    it "returns system health in JSON format" do
      get :system_health, format: :json

      expect(response).to have_http_status(:success)
      expect(response.content_type).to include('application/json')

      json_response = JSON.parse(response.body)
      expect(json_response).to have_key('system_health')
    end
  end

  describe "GET #recent_activity" do
    it "returns recent activity in JSON format" do
      get :recent_activity, format: :json

      expect(response).to have_http_status(:success)
      expect(response.content_type).to include('application/json')

      json_response = JSON.parse(response.body)
      expect(json_response).to have_key('recent_activity')
    end
  end

  describe "caching behavior" do
    before do
      Rails.cache.clear
    end

    it "caches pipeline metrics" do
      expect(Rails.cache).to receive(:fetch).with(
        "dashboard/pipeline_metrics/#{account.id}",
        expires_in: 5.minutes
      ).and_call_original

      get :index
    end

    it "caches connector metrics" do
      expect(Rails.cache).to receive(:fetch).with(
        "dashboard/connector_metrics/#{account.id}",
        expires_in: 10.minutes
      ).and_call_original

      get :index
    end

    it "caches usage metrics" do
      expect(Rails.cache).to receive(:fetch).with(
        "dashboard/usage_metrics/#{account.id}",
        expires_in: 15.minutes
      ).and_call_original

      get :index
    end
  end

  describe "alert generation" do
    context "when success rate is below threshold" do
      before do
        allow(controller).to receive(:load_pipeline_metrics).and_return({
          total_pipelines: 5,
          active_pipelines: 3,
          avg_success_rate: 0.85,  # 85% - below 90% threshold
          total_executions: 100
        })
      end

      it "generates low success rate alert" do
        get :index, format: :json
        json_response = JSON.parse(response.body)

        # The alerts are generated in the trends calculation
        expect(json_response['trends']).to be_present
      end
    end

    context "when storage usage is high" do
      before do
        allow(controller).to receive(:load_usage_metrics).and_return({
          data_processed_mb: 1000,
          api_requests: 500,
          storage_used_mb: 9000,  # 9GB out of 10GB limit (90%)
          monthly_executions: 250
        })
      end

      it "handles high storage usage scenario" do
        get :index, format: :json
        json_response = JSON.parse(response.body)

        expect(json_response['trends']).to be_present
      end
    end
  end

  describe "error handling" do
    context "when models are not available" do
      before do
        allow(controller).to receive(:load_pipeline_metrics).and_raise(NameError)
      end

      it "falls back to default metrics" do
        get :index
        expect(response).to have_http_status(:success)
        expect(assigns(:pipeline_metrics)).to eq({
          total_pipelines: 0,
          active_pipelines: 0,
          avg_success_rate: 0,
          total_executions: 0
        })
      end
    end
  end

  describe "trend calculations" do
    it "calculates pipeline growth trend" do
      get :index, format: :json
      json_response = JSON.parse(response.body)
      trends = json_response['trends']

      expect(trends).to have_key('pipeline_growth')
      expect(trends['pipeline_growth']).to be_a(Numeric)
    end

    it "calculates success rate trend" do
      get :index, format: :json
      json_response = JSON.parse(response.body)
      trends = json_response['trends']

      expect(trends).to have_key('success_rate_trend')
      expect(trends['success_rate_trend']).to be_a(Numeric)
    end

    it "calculates data volume trend" do
      get :index, format: :json
      json_response = JSON.parse(response.body)
      trends = json_response['trends']

      expect(trends).to have_key('data_volume_trend')
      expect(trends['data_volume_trend']).to be_a(Numeric)
    end

    it "calculates performance trend" do
      get :index, format: :json
      json_response = JSON.parse(response.body)
      trends = json_response['trends']

      expect(trends).to have_key('performance_trend')
      expect(trends['performance_trend']).to be_a(Numeric)
    end
  end

  context "without authentication" do
    before { sign_out user }

    it "redirects to login page" do
      get :index
      expect(response).to redirect_to(new_user_session_path)
    end
  end
end

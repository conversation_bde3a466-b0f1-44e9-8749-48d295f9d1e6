# frozen_string_literal: true

require 'rails_helper'

RSpec.configure do |config|
  # Specify a root folder where Swagger JSON files are generated
  # NOTE: If you're using the rswag-api to serve API descriptions, you'll need
  # to ensure that it's configured to serve Swagger from the same folder
  config.openapi_root = Rails.root.join('swagger').to_s

  # Define one or more Swagger documents and provide global metadata for each one
  # When you run the 'rswag:specs:swaggerize' rake task, the complete Swagger will
  # be generated at the provided relative path under openapi_root
  # By default, the operations defined in spec files are added to the first
  # document below. You can override this behavior by adding a openapi_spec tag to the
  # the root example_group in your specs, e.g. describe '...', openapi_spec: 'v2/swagger.json'
  config.openapi_specs = {
    'v1/swagger.yaml' => {
      openapi: '3.0.1',
      info: {
        title: 'DataReflow API',
        version: 'v1',
        description: 'DataReflow - Your Data Pipeline Platform API Documentation',
        termsOfService: 'https://datareflow.io/terms',
        contact: {
          name: 'DataReflow Support',
          email: '<EMAIL>',
          url: 'https://datareflow.io/support'
        },
        license: {
          name: 'Proprietary',
          url: 'https://datareflow.io/license'
        }
      },
      paths: {},
      servers: [
        {
          url: 'http://localhost:3000/api/v1',
          description: 'Development server'
        },
        {
          url: 'https://{subdomain}.datareflow.io/api/v1',
          description: 'Production server',
          variables: {
            subdomain: {
              default: 'api',
              description: 'Your account subdomain'
            }
          }
        }
      ],
      components: {
        securitySchemes: {
          bearerAuth: {
            type: 'http',
            scheme: 'bearer',
            bearerFormat: 'JWT',
            description: 'JWT token obtained from authentication endpoint'
          },
          apiKeyAuth: {
            type: 'apiKey',
            in: 'header',
            name: 'Authorization',
            description: 'API key authentication'
          }
        },
        schemas: {
          Error: {
            type: 'object',
            properties: {
              error: {
                type: 'string',
                description: 'Error message'
              },
              details: {
                type: 'array',
                items: {
                  type: 'string'
                },
                description: 'Additional error details'
              },
              timestamp: {
                type: 'string',
                format: 'date-time',
                description: 'Error timestamp'
              }
            },
            required: [ 'error' ]
          },
          Pipeline: {
            type: 'object',
            properties: {
              id: {
                type: 'integer',
                description: 'Pipeline ID'
              },
              name: {
                type: 'string',
                description: 'Pipeline name'
              },
              description: {
                type: 'string',
                description: 'Pipeline description'
              },
              status: {
                type: 'string',
                enum: [ 'active', 'inactive', 'draft' ],
                description: 'Pipeline status'
              },
              created_at: {
                type: 'string',
                format: 'date-time',
                description: 'Creation timestamp'
              },
              updated_at: {
                type: 'string',
                format: 'date-time',
                description: 'Last update timestamp'
              }
            },
            required: [ 'id', 'name', 'status' ]
          },
          DataConnector: {
            type: 'object',
            properties: {
              id: {
                type: 'integer',
                description: 'Data connector ID'
              },
              name: {
                type: 'string',
                description: 'Connector name'
              },
              connector_type: {
                type: 'string',
                description: 'Type of connector (e.g., database, api, file)'
              },
              status: {
                type: 'string',
                enum: [ 'active', 'inactive', 'error' ],
                description: 'Connector status'
              },
              connection_string: {
                type: 'string',
                description: 'Connection configuration (sensitive data masked)'
              },
              created_at: {
                type: 'string',
                format: 'date-time',
                description: 'Creation timestamp'
              },
              updated_at: {
                type: 'string',
                format: 'date-time',
                description: 'Last update timestamp'
              }
            },
            required: [ 'id', 'name', 'connector_type', 'status' ]
          },
          PipelineExecution: {
            type: 'object',
            properties: {
              id: {
                type: 'integer',
                description: 'Execution ID'
              },
              pipeline_id: {
                type: 'integer',
                description: 'Pipeline ID'
              },
              status: {
                type: 'string',
                enum: [ 'pending', 'running', 'success', 'failed', 'cancelled' ],
                description: 'Execution status'
              },
              started_at: {
                type: 'string',
                format: 'date-time',
                description: 'Execution start time'
              },
              completed_at: {
                type: 'string',
                format: 'date-time',
                description: 'Execution completion time'
              },
              records_processed: {
                type: 'integer',
                description: 'Number of records processed'
              },
              records_success: {
                type: 'integer',
                description: 'Number of successfully processed records'
              },
              records_failed: {
                type: 'integer',
                description: 'Number of failed records'
              },
              error_message: {
                type: 'string',
                description: 'Error message if execution failed'
              }
            },
            required: [ 'id', 'pipeline_id', 'status' ]
          }
        }
      }
    }
  }

  # Specify the format of the output Swagger file when running 'rswag:specs:swaggerize'.
  # The openapi_specs configuration option has the filename including format in
  # the key, this may want to be changed to avoid putting yaml in json files.
  # Defaults to json. Accepts ':json' and ':yaml'.
  config.openapi_format = :yaml
end

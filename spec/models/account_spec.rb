require 'rails_helper'

RSpec.describe Account, type: :model do
  let(:account) { create(:account) }

  describe 'associations' do
    it { should have_many(:users).dependent(:destroy) }
    it { should have_many(:account_invitations).dependent(:destroy) }
    it { should have_many(:team_invitations).dependent(:destroy) }
    it { should have_many(:api_tokens).dependent(:destroy) }
    it { should have_many(:notifications).dependent(:destroy) }
    it { should have_many(:pipelines).dependent(:destroy) }
    it { should have_many(:data_connectors).dependent(:destroy) }
    it { should have_many(:pipeline_executions).through(:pipelines) }
    it { should have_many(:usage_metrics).dependent(:destroy) }
    it { should have_many(:agent_recommendations).dependent(:destroy) }
    it { should have_many(:agent_revenues).dependent(:destroy) }
    it { should have_one(:subscription).dependent(:destroy) }
  end

  describe 'validations' do
    it { should validate_presence_of(:name) }
    it { should validate_presence_of(:subdomain) }
    it { should validate_uniqueness_of(:subdomain) }
    it { should validate_length_of(:subdomain).is_at_least(3).is_at_most(30) }

    describe 'subdomain format validation' do
      it 'allows lowercase letters and numbers' do
        account.subdomain = 'test123'
        expect(account).to be_valid
      end

      it 'does not allow uppercase letters' do
        account.subdomain = 'Test123'
        expect(account).not_to be_valid
      end

      it 'does not allow special characters' do
        account.subdomain = 'test-123'
        expect(account).not_to be_valid
      end

      it 'does not allow spaces' do
        account.subdomain = 'test 123'
        expect(account).not_to be_valid
      end
    end
  end

  describe 'enums' do
    it { should define_enum_for(:status).with_values(active: 0, suspended: 1, canceled: 2) }
  end

  describe 'callbacks' do
    describe 'before_validation :parameterize_subdomain' do
      it 'parameterizes the subdomain' do
        account = Account.new(name: 'Test Company', subdomain: 'Test Company!')
        account.valid?
        expect(account.subdomain).to eq('testcompany')
      end
    end

    describe 'after_create :create_default_subscription' do
      it 'creates a subscription after account creation' do
        expect {
          create(:account)
        }.to change(Subscription, :count).by(1)
      end
    end
  end

  describe 'scopes' do
    let!(:onboarded_account) { create(:account, onboarded_at: 1.day.ago) }
    let!(:not_onboarded_account) { create(:account, onboarded_at: nil) }

    describe '.onboarded' do
      it 'returns accounts that have been onboarded' do
        expect(Account.onboarded).to include(onboarded_account)
        expect(Account.onboarded).not_to include(not_onboarded_account)
      end
    end

    describe '.not_onboarded' do
      it 'returns accounts that have not been onboarded' do
        expect(Account.not_onboarded).to include(not_onboarded_account)
        expect(Account.not_onboarded).not_to include(onboarded_account)
      end
    end
  end

  describe 'instance methods' do
    let(:owner) { create(:user, account: account, role: 'owner') }
    let(:admin) { create(:user, account: account, role: 'admin') }
    let(:member) { create(:user, account: account, role: 'member') }

    describe '#owner' do
      it 'returns the owner user' do
        expect(account.owner).to eq(owner)
      end
    end

    describe '#admin_users' do
      it 'returns owner and admin users' do
        expect(account.admin_users).to include(owner, admin)
        expect(account.admin_users).not_to include(member)
      end
    end

    describe '#onboarding_completed?' do
      context 'when all onboarding steps are completed' do
        it 'returns true' do
          account.update!(
            onboarding_welcome_completed: true,
            onboarding_profile_completed: true,
            onboarding_connection_completed: true,
            onboarding_pipeline_completed: true,
            onboarding_team_completed: true
          )
          expect(account.onboarding_completed?).to be_truthy
        end
      end

      context 'when some onboarding steps are not completed' do
        it 'returns false' do
          account.update!(
            onboarding_welcome_completed: true,
            onboarding_profile_completed: false
          )
          expect(account.onboarding_completed?).to be_falsey
        end
      end
    end

    describe '#onboarding_progress' do
      it 'returns the percentage of completed onboarding steps' do
        account.update!(
          onboarding_welcome_completed: true,
          onboarding_profile_completed: true,
          onboarding_connection_completed: false,
          onboarding_pipeline_completed: false,
          onboarding_team_completed: false
        )
        expect(account.onboarding_progress).to eq(40) # 2 out of 5 steps completed
      end
    end

    describe '#subscription_plan' do
      it 'returns the current subscription plan' do
        subscription = create(:subscription, account: account, plan: 'professional')
        expect(account.subscription_plan).to eq('professional')
      end

      it 'returns free when no subscription exists' do
        account.subscription&.destroy
        expect(account.subscription_plan).to eq('free')
      end
    end

    describe '#within_limits?' do
      let(:subscription) { create(:subscription, account: account, plan: 'starter') }

      before { subscription }

      context 'when within limits' do
        it 'returns true for pipelines under limit' do
          # Starter plan allows 10 pipelines
          create_list(:pipeline, 5, account: account, created_by: owner)
          expect(account.within_limits?(:pipelines, 3)).to be_truthy
        end
      end

      context 'when exceeding limits' do
        it 'returns false for pipelines over limit' do
          # Starter plan allows 10 pipelines
          create_list(:pipeline, 10, account: account, created_by: owner)
          expect(account.within_limits?(:pipelines, 1)).to be_falsey
        end
      end
    end
  end

  describe 'agent features' do
    let(:subscription) { create(:subscription, account: account, plan: 'professional') }

    before { subscription }

    describe '#agent_features_enabled?' do
      it 'returns true for professional plan' do
        expect(account.agent_features_enabled?).to be_truthy
      end
    end

    describe '#recommendations_this_month' do
      it 'returns count of recommendations for current month' do
        create(:agent_recommendation, account: account, created_at: 1.day.ago)
        create(:agent_recommendation, account: account, created_at: 1.month.ago)

        expect(account.recommendations_this_month).to eq(1)
      end
    end

    describe '#can_generate_recommendations?' do
      context 'when within monthly limit' do
        it 'returns true' do
          # Professional plan allows 200 recommendations per month
          create_list(:agent_recommendation, 50, account: account)
          expect(account.can_generate_recommendations?).to be_truthy
        end
      end

      context 'when exceeding monthly limit' do
        it 'returns false' do
          allow(account).to receive(:recommendations_this_month).and_return(200)
          expect(account.can_generate_recommendations?).to be_falsey
        end
      end
    end
  end
end

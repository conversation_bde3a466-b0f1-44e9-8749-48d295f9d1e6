require 'rails_helper'

RSpec.describe User, type: :model do
  let(:account) { create(:account) }
  let(:user) { create(:user, account: account) }

  describe 'associations' do
    it { should belong_to(:account) }
    it { should have_many(:api_tokens).dependent(:destroy) }
    it { should have_many(:account_invitations).dependent(:destroy) }
    it { should have_many(:notifications).dependent(:destroy) }
  end

  describe 'validations' do
    it { should validate_presence_of(:email) }
    it { should validate_presence_of(:role) }
    it { should validate_uniqueness_of(:email).scoped_to(:account_id) }
  end

  describe 'enums' do
    it { should define_enum_for(:role).with_values(owner: 0, admin: 1, member: 2, viewer: 3) }
  end

  describe 'callbacks' do
    context 'before validation on create' do
      it 'sets default role to member' do
        user = User.new(email: '<EMAIL>', password: 'password123', account: account)
        user.valid?
        expect(user.role).to eq('member')
      end
    end
  end

  describe '#full_name' do
    context 'when first_name and last_name are present' do
      it 'returns the full name' do
        user.first_name = '<PERSON>'
        user.last_name = 'Doe'
        expect(user.full_name).to eq('<PERSON>')
      end
    end

    context 'when first_name is present but last_name is blank' do
      it 'returns the first name' do
        user.first_name = 'John'
        user.last_name = ''
        expect(user.full_name).to eq('John')
      end
    end

    context 'when both names are blank' do
      it 'returns the email' do
        user.first_name = ''
        user.last_name = ''
        expect(user.full_name).to eq(user.email)
      end
    end
  end

  describe '#can_manage_account?' do
    context 'when user is owner' do
      it 'returns true' do
        user.role = 'owner'
        expect(user.can_manage_account?).to be_truthy
      end
    end

    context 'when user is admin' do
      it 'returns true' do
        user.role = 'admin'
        expect(user.can_manage_account?).to be_truthy
      end
    end

    context 'when user is member' do
      it 'returns false' do
        user.role = 'member'
        expect(user.can_manage_account?).to be_falsey
      end
    end

    context 'when user is viewer' do
      it 'returns false' do
        user.role = 'viewer'
        expect(user.can_manage_account?).to be_falsey
      end
    end
  end

  describe '#can_manage_team?' do
    context 'when user is owner' do
      it 'returns true' do
        user.role = 'owner'
        expect(user.can_manage_team?).to be_truthy
      end
    end

    context 'when user is admin' do
      it 'returns true' do
        user.role = 'admin'
        expect(user.can_manage_team?).to be_truthy
      end
    end

    context 'when user is member' do
      it 'returns false' do
        user.role = 'member'
        expect(user.can_manage_team?).to be_falsey
      end
    end
  end

  describe '#can_execute_pipelines?' do
    context 'when user is owner' do
      it 'returns true' do
        user.role = 'owner'
        expect(user.can_execute_pipelines?).to be_truthy
      end
    end

    context 'when user is admin' do
      it 'returns true' do
        user.role = 'admin'
        expect(user.can_execute_pipelines?).to be_truthy
      end
    end

    context 'when user is member' do
      it 'returns true' do
        user.role = 'member'
        expect(user.can_execute_pipelines?).to be_truthy
      end
    end

    context 'when user is viewer' do
      it 'returns false' do
        user.role = 'viewer'
        expect(user.can_execute_pipelines?).to be_falsey
      end
    end
  end

  describe 'devise modules' do
    it 'includes database_authenticatable' do
      expect(User.devise_modules).to include(:database_authenticatable)
    end

    it 'includes registerable' do
      expect(User.devise_modules).to include(:registerable)
    end

    it 'includes recoverable' do
      expect(User.devise_modules).to include(:recoverable)
    end

    it 'includes rememberable' do
      expect(User.devise_modules).to include(:rememberable)
    end

    it 'includes validatable' do
      expect(User.devise_modules).to include(:validatable)
    end

    it 'includes confirmable' do
      expect(User.devise_modules).to include(:confirmable)
    end

    it 'includes trackable' do
      expect(User.devise_modules).to include(:trackable)
    end

    it 'includes lockable' do
      expect(User.devise_modules).to include(:lockable)
    end

    it 'includes jwt_authenticatable' do
      expect(User.devise_modules).to include(:jwt_authenticatable)
    end
  end
end

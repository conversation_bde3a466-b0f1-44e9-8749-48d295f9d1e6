require "rails_helper"

RSpec.describe ContactForm, type: :model do
  subject { described_class.new(attributes) }

  let(:valid_attributes) do
    {
      name: "<PERSON>",
      email: "<EMAIL>",
      company: "Test Company",
      inquiry_type: "general",
      message: "This is a test message"
    }
  end

  describe "validations" do
    context "with valid attributes" do
      let(:attributes) { valid_attributes }

      it "is valid" do
        expect(subject).to be_valid
      end
    end

    describe "name validation" do
      let(:attributes) { valid_attributes.merge(name: name) }

      context "when name is present" do
        let(:name) { "John Doe" }

        it "is valid" do
          expect(subject).to be_valid
        end
      end

      context "when name is blank" do
        let(:name) { "" }

        it "is invalid" do
          expect(subject).not_to be_valid
          expect(subject.errors[:name]).to include("can't be blank")
        end
      end

      context "when name is nil" do
        let(:name) { nil }

        it "is invalid" do
          expect(subject).not_to be_valid
          expect(subject.errors[:name]).to include("can't be blank")
        end
      end
    end

    describe "email validation" do
      let(:attributes) { valid_attributes.merge(email: email) }

      context "when email is valid" do
        let(:email) { "<EMAIL>" }

        it "is valid" do
          expect(subject).to be_valid
        end
      end

      context "when email is blank" do
        let(:email) { "" }

        it "is invalid" do
          expect(subject).not_to be_valid
          expect(subject.errors[:email]).to include("can't be blank")
        end
      end

      context "when email format is invalid" do
        let(:email) { "invalid-email" }

        it "is invalid" do
          expect(subject).not_to be_valid
          expect(subject.errors[:email]).to include("is invalid")
        end
      end
    end

    describe "inquiry_type validation" do
      let(:attributes) { valid_attributes.merge(inquiry_type: inquiry_type) }

      context "when inquiry_type is valid" do
        let(:inquiry_type) { "general" }

        it "is valid" do
          expect(subject).to be_valid
        end
      end

      context "when inquiry_type is blank" do
        let(:inquiry_type) { "" }

        it "is invalid" do
          expect(subject).not_to be_valid
          expect(subject.errors[:inquiry_type]).to include("can't be blank")
        end
      end

      context "when inquiry_type is not in allowed values" do
        let(:inquiry_type) { "invalid_type" }

        it "is invalid" do
          expect(subject).not_to be_valid
          expect(subject.errors[:inquiry_type]).to include("Please select a valid inquiry type")
        end
      end
    end

    describe "message validation" do
      let(:attributes) { valid_attributes.merge(message: message) }

      context "when message is present" do
        let(:message) { "This is a test message" }

        it "is valid" do
          expect(subject).to be_valid
        end
      end

      context "when message is blank" do
        let(:message) { "" }

        it "is invalid" do
          expect(subject).not_to be_valid
          expect(subject.errors[:message]).to include("can't be blank")
        end
      end

      context "when message is too short" do
        let(:message) { "Hi" }

        it "is invalid" do
          expect(subject).not_to be_valid
          expect(subject.errors[:message]).to include("is too short (minimum is 10 characters)")
        end
      end
    end
  end

  describe "inquiry type options" do
    let(:attributes) { valid_attributes }

    it "has inquiry type options available" do
      form = ContactForm.new(valid_attributes)
      expect(form.inquiry_type).to eq("general")
    end
  end
end

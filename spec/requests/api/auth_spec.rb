require 'swagger_helper'

RSpec.describe 'api/auth', type: :request do
  path '/api/login' do
    post('user login') do
      tags 'Authentication'
      description 'Authenticate user and receive JWT token'
      consumes 'application/json'
      produces 'application/json'

      parameter name: :credentials, in: :body, schema: {
        type: :object,
        properties: {
          email: {
            type: :string,
            format: :email,
            description: 'User email address',
            example: '<EMAIL>'
          },
          password: {
            type: :string,
            description: 'User password',
            example: 'secure_password123'
          }
        },
        required: [ 'email', 'password' ]
      }

      response(200, 'login successful') do
        schema type: :object,
               properties: {
                 token: {
                   type: :string,
                   description: 'JWT authentication token',
                   example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...'
                 },
                 user: {
                   type: :object,
                   properties: {
                     id: { type: :integer, example: 123 },
                     email: { type: :string, example: '<EMAIL>' },
                     first_name: { type: :string, example: '<PERSON>' },
                     last_name: { type: :string, example: 'Doe' },
                     role: { type: :string, example: 'admin' }
                   }
                 },
                 account: {
                   type: :object,
                   properties: {
                     id: { type: :integer, example: 456 },
                     name: { type: :string, example: 'Acme Corp' },
                     subdomain: { type: :string, example: 'acme' },
                     plan: { type: :string, example: 'professional' }
                   }
                 }
               }
        let(:credentials) { { email: '<EMAIL>', password: 'password' } }
        run_test!
      end

      response(401, 'invalid credentials') do
        schema '$ref' => '#/components/schemas/Error'
        let(:credentials) { { email: '<EMAIL>', password: 'wrong_password' } }
        run_test!
      end

      response(400, 'bad request') do
        schema '$ref' => '#/components/schemas/Error'
        let(:credentials) { { email: 'invalid_email' } }
        run_test!
      end
    end
  end

  path '/api/register' do
    post('user registration') do
      tags 'Authentication'
      description 'Register a new user account'
      consumes 'application/json'
      produces 'application/json'

      parameter name: :user_data, in: :body, schema: {
        type: :object,
        properties: {
          email: {
            type: :string,
            format: :email,
            description: 'User email address',
            example: '<EMAIL>'
          },
          password: {
            type: :string,
            description: 'User password (minimum 8 characters)',
            example: 'secure_password123'
          },
          password_confirmation: {
            type: :string,
            description: 'Password confirmation',
            example: 'secure_password123'
          },
          first_name: {
            type: :string,
            description: 'First name',
            example: 'Jane'
          },
          last_name: {
            type: :string,
            description: 'Last name',
            example: 'Smith'
          },
          account_name: {
            type: :string,
            description: 'Account/company name',
            example: 'Smith Industries'
          },
          subdomain: {
            type: :string,
            description: 'Desired subdomain (optional, will be generated if not provided)',
            example: 'smith-industries'
          }
        },
        required: [ 'email', 'password', 'password_confirmation', 'first_name', 'last_name', 'account_name' ]
      }

      response(201, 'registration successful') do
        schema type: :object,
               properties: {
                 message: { type: :string, example: 'User created successfully. Please check your email for confirmation.' },
                 user: {
                   type: :object,
                   properties: {
                     id: { type: :integer },
                     email: { type: :string },
                     first_name: { type: :string },
                     last_name: { type: :string },
                     confirmed: { type: :boolean, example: false }
                   }
                 },
                 account: {
                   type: :object,
                   properties: {
                     id: { type: :integer },
                     name: { type: :string },
                     subdomain: { type: :string }
                   }
                 }
               }
        let(:user_data) {
          {
            email: '<EMAIL>',
            password: 'password123',
            password_confirmation: 'password123',
            first_name: 'Jane',
            last_name: 'Smith',
            account_name: 'Smith Industries'
          }
        }
        run_test!
      end

      response(400, 'validation errors') do
        schema '$ref' => '#/components/schemas/Error'
        let(:user_data) { { email: 'invalid_email' } }
        run_test!
      end

      response(422, 'user already exists') do
        schema '$ref' => '#/components/schemas/Error'
        let(:user_data) {
          {
            email: '<EMAIL>',
            password: 'password123',
            password_confirmation: 'password123',
            first_name: 'Jane',
            last_name: 'Smith',
            account_name: 'Smith Industries'
          }
        }
        run_test!
      end
    end
  end

  path '/api/logout' do
    delete('user logout') do
      tags 'Authentication'
      description 'Logout user and invalidate JWT token'
      produces 'application/json'
      security [ { bearerAuth: [] } ]

      response(200, 'logout successful') do
        schema type: :object,
               properties: {
                 message: { type: :string, example: 'Logged out successfully' }
               }
        let(:Authorization) { "Bearer valid-jwt-token" }
        run_test!
      end

      response(401, 'unauthorized') do
        schema '$ref' => '#/components/schemas/Error'
        let(:Authorization) { "Bearer invalid-token" }
        run_test!
      end
    end
  end

  path '/api/me' do
    get('current user info') do
      tags 'Authentication'
      description 'Get current authenticated user information'
      produces 'application/json'
      security [ { bearerAuth: [] } ]

      response(200, 'user info retrieved') do
        schema type: :object,
               properties: {
                 user: {
                   type: :object,
                   properties: {
                     id: { type: :integer, example: 123 },
                     email: { type: :string, example: '<EMAIL>' },
                     first_name: { type: :string, example: 'John' },
                     last_name: { type: :string, example: 'Doe' },
                     role: { type: :string, example: 'admin' },
                     confirmed: { type: :boolean, example: true },
                     created_at: { type: :string, format: 'date-time' },
                     last_sign_in_at: { type: :string, format: 'date-time' }
                   }
                 },
                 account: {
                   type: :object,
                   properties: {
                     id: { type: :integer, example: 456 },
                     name: { type: :string, example: 'Acme Corp' },
                     subdomain: { type: :string, example: 'acme' },
                     plan: { type: :string, example: 'professional' },
                     status: { type: :string, example: 'active' }
                   }
                 },
                 permissions: {
                   type: :array,
                   items: { type: :string },
                   example: [ 'read:pipelines', 'write:pipelines', 'read:connectors', 'write:connectors' ]
                 }
               }
        let(:Authorization) { "Bearer valid-jwt-token" }
        run_test!
      end

      response(401, 'unauthorized') do
        schema '$ref' => '#/components/schemas/Error'
        let(:Authorization) { "Bearer invalid-token" }
        run_test!
      end
    end
  end
end

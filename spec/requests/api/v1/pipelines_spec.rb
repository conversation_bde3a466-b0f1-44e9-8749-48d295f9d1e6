require 'swagger_helper'

RSpec.describe 'api/v1/pipelines', type: :request do
  path '/api/v1/pipelines' do
    get('list pipelines') do
      tags 'Pipelines'
      description 'Get a list of all pipelines for the authenticated account'
      consumes 'application/json'
      produces 'application/json'
      security [ { bearerAuth: [] } ]

      parameter name: :page, in: :query, type: :integer, description: 'Page number for pagination', required: false
      parameter name: :limit, in: :query, type: :integer, description: 'Number of items per page (max 100)', required: false
      parameter name: :status, in: :query, type: :string, description: 'Filter by pipeline status', enum: [ 'active', 'inactive', 'draft' ], required: false

      response(200, 'successful') do
        schema type: :object,
               properties: {
                 pipelines: {
                   type: :array,
                   items: { '$ref' => '#/components/schemas/Pipeline' }
                 },
                 pagination: {
                   type: :object,
                   properties: {
                     current_page: { type: :integer },
                     per_page: { type: :integer },
                     total_pages: { type: :integer },
                     total_count: { type: :integer }
                   }
                 }
               }

        let(:Authorization) { "Bearer valid-jwt-token" }
        run_test!
      end

      response(401, 'unauthorized') do
        schema '$ref' => '#/components/schemas/Error'
        let(:Authorization) { "Bearer invalid-token" }
        run_test!
      end
    end

    post('create pipeline') do
      tags 'Pipelines'
      description 'Create a new data pipeline'
      consumes 'application/json'
      produces 'application/json'
      security [ { bearerAuth: [] } ]

      parameter name: :pipeline, in: :body, schema: {
        type: :object,
        properties: {
          name: { type: :string, description: 'Pipeline name', example: 'Customer Data ETL' },
          description: { type: :string, description: 'Pipeline description', example: 'Extract customer data from CRM and transform for analytics' },
          status: { type: :string, enum: [ 'active', 'inactive', 'draft' ], default: 'draft' }
        },
        required: [ 'name' ]
      }

      response(201, 'created') do
        schema '$ref' => '#/components/schemas/Pipeline'
        let(:Authorization) { "Bearer valid-jwt-token" }
        let(:pipeline) { { name: 'Test Pipeline', description: 'Test Description' } }
        run_test!
      end

      response(400, 'bad request') do
        schema '$ref' => '#/components/schemas/Error'
        let(:Authorization) { "Bearer valid-jwt-token" }
        let(:pipeline) { { name: '' } }
        run_test!
      end

      response(401, 'unauthorized') do
        schema '$ref' => '#/components/schemas/Error'
        let(:Authorization) { "Bearer invalid-token" }
        let(:pipeline) { { name: 'Test Pipeline' } }
        run_test!
      end
    end
  end

  path '/api/v1/pipelines/{id}' do
    parameter name: 'id', in: :path, type: :integer, description: 'Pipeline ID'

    get('show pipeline') do
      tags 'Pipelines'
      description 'Get details of a specific pipeline'
      produces 'application/json'
      security [ { bearerAuth: [] } ]

      response(200, 'successful') do
        schema '$ref' => '#/components/schemas/Pipeline'
        let(:Authorization) { "Bearer valid-jwt-token" }
        let(:id) { '123' }
        run_test!
      end

      response(404, 'not found') do
        schema '$ref' => '#/components/schemas/Error'
        let(:Authorization) { "Bearer valid-jwt-token" }
        let(:id) { '999999' }
        run_test!
      end

      response(401, 'unauthorized') do
        schema '$ref' => '#/components/schemas/Error'
        let(:Authorization) { "Bearer invalid-token" }
        let(:id) { '123' }
        run_test!
      end
    end

    put('update pipeline') do
      tags 'Pipelines'
      description 'Update an existing pipeline'
      consumes 'application/json'
      produces 'application/json'
      security [ { bearerAuth: [] } ]

      parameter name: :pipeline, in: :body, schema: {
        type: :object,
        properties: {
          name: { type: :string, description: 'Pipeline name' },
          description: { type: :string, description: 'Pipeline description' },
          status: { type: :string, enum: [ 'active', 'inactive', 'draft' ] }
        }
      }

      response(200, 'successful') do
        schema '$ref' => '#/components/schemas/Pipeline'
        let(:Authorization) { "Bearer valid-jwt-token" }
        let(:id) { '123' }
        let(:pipeline) { { name: 'Updated Pipeline Name' } }
        run_test!
      end

      response(400, 'bad request') do
        schema '$ref' => '#/components/schemas/Error'
        let(:Authorization) { "Bearer valid-jwt-token" }
        let(:id) { '123' }
        let(:pipeline) { { name: '' } }
        run_test!
      end

      response(404, 'not found') do
        schema '$ref' => '#/components/schemas/Error'
        let(:Authorization) { "Bearer valid-jwt-token" }
        let(:id) { '999999' }
        let(:pipeline) { { name: 'Test' } }
        run_test!
      end

      response(401, 'unauthorized') do
        schema '$ref' => '#/components/schemas/Error'
        let(:Authorization) { "Bearer invalid-token" }
        let(:id) { '123' }
        let(:pipeline) { { name: 'Test' } }
        run_test!
      end
    end

    delete('delete pipeline') do
      tags 'Pipelines'
      description 'Delete a pipeline'
      produces 'application/json'
      security [ { bearerAuth: [] } ]

      response(204, 'no content') do
        let(:Authorization) { "Bearer valid-jwt-token" }
        let(:id) { '123' }
        run_test!
      end

      response(404, 'not found') do
        schema '$ref' => '#/components/schemas/Error'
        let(:Authorization) { "Bearer valid-jwt-token" }
        let(:id) { '999999' }
        run_test!
      end

      response(401, 'unauthorized') do
        schema '$ref' => '#/components/schemas/Error'
        let(:Authorization) { "Bearer invalid-token" }
        let(:id) { '123' }
        run_test!
      end
    end
  end

  path '/api/v1/pipelines/{id}/execute' do
    parameter name: 'id', in: :path, type: :integer, description: 'Pipeline ID'

    post('execute pipeline') do
      tags 'Pipelines'
      description 'Execute a pipeline manually'
      consumes 'application/json'
      produces 'application/json'
      security [ { bearerAuth: [] } ]

      parameter name: :execution_params, in: :body, schema: {
        type: :object,
        properties: {
          async: { type: :boolean, default: true, description: 'Execute asynchronously' },
          parameters: {
            type: :object,
            description: 'Pipeline-specific execution parameters',
            additionalProperties: true
          }
        }
      }, required: false

      response(202, 'accepted - execution started') do
        schema type: :object,
               properties: {
                 execution_id: { type: :integer, description: 'Pipeline execution ID' },
                 status: { type: :string, description: 'Initial execution status' },
                 message: { type: :string, description: 'Status message' }
               }
        let(:Authorization) { "Bearer valid-jwt-token" }
        let(:id) { '123' }
        let(:execution_params) { { async: true } }
        run_test!
      end

      response(400, 'bad request - pipeline not executable') do
        schema '$ref' => '#/components/schemas/Error'
        let(:Authorization) { "Bearer valid-jwt-token" }
        let(:id) { '123' }
        let(:execution_params) { {} }
        run_test!
      end

      response(404, 'pipeline not found') do
        schema '$ref' => '#/components/schemas/Error'
        let(:Authorization) { "Bearer valid-jwt-token" }
        let(:id) { '999999' }
        let(:execution_params) { {} }
        run_test!
      end

      response(401, 'unauthorized') do
        schema '$ref' => '#/components/schemas/Error'
        let(:Authorization) { "Bearer invalid-token" }
        let(:id) { '123' }
        let(:execution_params) { {} }
        run_test!
      end
    end
  end
end

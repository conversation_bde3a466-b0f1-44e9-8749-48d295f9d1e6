require 'swagger_helper'

RSpec.describe 'api/v1/connectors', type: :request do
  path '/api/v1/connectors' do
    get('list data connectors') do
      tags 'Data Connectors'
      description 'Get a list of all data connectors for the authenticated account'
      consumes 'application/json'
      produces 'application/json'
      security [ { bearerAuth: [] } ]

      parameter name: :page, in: :query, type: :integer, description: 'Page number for pagination', required: false
      parameter name: :limit, in: :query, type: :integer, description: 'Number of items per page (max 100)', required: false
      parameter name: :status, in: :query, type: :string, description: 'Filter by connector status', enum: [ 'active', 'inactive', 'error' ], required: false
      parameter name: :connector_type, in: :query, type: :string, description: 'Filter by connector type', required: false

      response(200, 'successful') do
        schema type: :object,
               properties: {
                 connectors: {
                   type: :array,
                   items: { '$ref' => '#/components/schemas/DataConnector' }
                 },
                 pagination: {
                   type: :object,
                   properties: {
                     current_page: { type: :integer },
                     per_page: { type: :integer },
                     total_pages: { type: :integer },
                     total_count: { type: :integer }
                   }
                 }
               }

        let(:Authorization) { "Bearer valid-jwt-token" }
        run_test!
      end

      response(401, 'unauthorized') do
        schema '$ref' => '#/components/schemas/Error'
        let(:Authorization) { "Bearer invalid-token" }
        run_test!
      end
    end

    post('create data connector') do
      tags 'Data Connectors'
      description 'Create a new data connector'
      consumes 'application/json'
      produces 'application/json'
      security [ { bearerAuth: [] } ]

      parameter name: :connector, in: :body, schema: {
        type: :object,
        properties: {
          name: { type: :string, description: 'Connector name', example: 'Production Database' },
          connector_type: {
            type: :string,
            description: 'Type of connector',
            example: 'postgresql',
            enum: [ 'postgresql', 'mysql', 'mongodb', 'api', 'csv', 'json', 's3', 'ftp' ]
          },
          connection_string: {
            type: :string,
            description: 'Connection configuration (JSON)',
            example: '{"host": "db.example.com", "port": 5432, "database": "production"}'
          },
          description: { type: :string, description: 'Connector description', example: 'Main production database connection' }
        },
        required: [ 'name', 'connector_type', 'connection_string' ]
      }

      response(201, 'created') do
        schema '$ref' => '#/components/schemas/DataConnector'
        let(:Authorization) { "Bearer valid-jwt-token" }
        let(:connector) {
          {
            name: 'Test DB',
            connector_type: 'postgresql',
            connection_string: '{"host": "localhost", "port": 5432}',
            description: 'Test database connection'
          }
        }
        run_test!
      end

      response(400, 'bad request') do
        schema '$ref' => '#/components/schemas/Error'
        let(:Authorization) { "Bearer valid-jwt-token" }
        let(:connector) { { name: '' } }
        run_test!
      end

      response(401, 'unauthorized') do
        schema '$ref' => '#/components/schemas/Error'
        let(:Authorization) { "Bearer invalid-token" }
        let(:connector) { { name: 'Test' } }
        run_test!
      end
    end
  end

  path '/api/v1/connectors/{id}' do
    parameter name: 'id', in: :path, type: :integer, description: 'Data Connector ID'

    get('show data connector') do
      tags 'Data Connectors'
      description 'Get details of a specific data connector'
      produces 'application/json'
      security [ { bearerAuth: [] } ]

      response(200, 'successful') do
        schema '$ref' => '#/components/schemas/DataConnector'
        let(:Authorization) { "Bearer valid-jwt-token" }
        let(:id) { '123' }
        run_test!
      end

      response(404, 'not found') do
        schema '$ref' => '#/components/schemas/Error'
        let(:Authorization) { "Bearer valid-jwt-token" }
        let(:id) { '999999' }
        run_test!
      end

      response(401, 'unauthorized') do
        schema '$ref' => '#/components/schemas/Error'
        let(:Authorization) { "Bearer invalid-token" }
        let(:id) { '123' }
        run_test!
      end
    end

    put('update data connector') do
      tags 'Data Connectors'
      description 'Update an existing data connector'
      consumes 'application/json'
      produces 'application/json'
      security [ { bearerAuth: [] } ]

      parameter name: :connector, in: :body, schema: {
        type: :object,
        properties: {
          name: { type: :string, description: 'Connector name' },
          connector_type: { type: :string, description: 'Type of connector' },
          connection_string: { type: :string, description: 'Connection configuration' },
          description: { type: :string, description: 'Connector description' },
          status: { type: :string, enum: [ 'active', 'inactive', 'error' ] }
        }
      }

      response(200, 'successful') do
        schema '$ref' => '#/components/schemas/DataConnector'
        let(:Authorization) { "Bearer valid-jwt-token" }
        let(:id) { '123' }
        let(:connector) { { name: 'Updated Connector Name' } }
        run_test!
      end

      response(400, 'bad request') do
        schema '$ref' => '#/components/schemas/Error'
        let(:Authorization) { "Bearer valid-jwt-token" }
        let(:id) { '123' }
        let(:connector) { { name: '' } }
        run_test!
      end

      response(404, 'not found') do
        schema '$ref' => '#/components/schemas/Error'
        let(:Authorization) { "Bearer valid-jwt-token" }
        let(:id) { '999999' }
        let(:connector) { { name: 'Test' } }
        run_test!
      end

      response(401, 'unauthorized') do
        schema '$ref' => '#/components/schemas/Error'
        let(:Authorization) { "Bearer invalid-token" }
        let(:id) { '123' }
        let(:connector) { { name: 'Test' } }
        run_test!
      end
    end

    delete('delete data connector') do
      tags 'Data Connectors'
      description 'Delete a data connector'
      produces 'application/json'
      security [ { bearerAuth: [] } ]

      response(204, 'no content') do
        let(:Authorization) { "Bearer valid-jwt-token" }
        let(:id) { '123' }
        run_test!
      end

      response(404, 'not found') do
        schema '$ref' => '#/components/schemas/Error'
        let(:Authorization) { "Bearer valid-jwt-token" }
        let(:id) { '999999' }
        run_test!
      end

      response(401, 'unauthorized') do
        schema '$ref' => '#/components/schemas/Error'
        let(:Authorization) { "Bearer invalid-token" }
        let(:id) { '123' }
        run_test!
      end
    end
  end

  path '/api/v1/connectors/{id}/test_connection' do
    parameter name: 'id', in: :path, type: :integer, description: 'Data Connector ID'

    post('test connection') do
      tags 'Data Connectors'
      description 'Test the connection for a specific data connector'
      produces 'application/json'
      security [ { bearerAuth: [] } ]

      response(200, 'connection successful') do
        schema type: :object,
               properties: {
                 status: { type: :string, description: 'Connection test status', example: 'success' },
                 message: { type: :string, description: 'Connection test message', example: 'Connection established successfully' },
                 response_time_ms: { type: :integer, description: 'Connection response time in milliseconds', example: 45 },
                 connection_details: {
                   type: :object,
                   description: 'Additional connection details',
                   properties: {
                     version: { type: :string, description: 'Database/service version' },
                     uptime: { type: :string, description: 'Service uptime' }
                   }
                 }
               }
        let(:Authorization) { "Bearer valid-jwt-token" }
        let(:id) { '123' }
        run_test!
      end

      response(400, 'connection failed') do
        schema type: :object,
               properties: {
                 status: { type: :string, description: 'Connection test status', example: 'failed' },
                 message: { type: :string, description: 'Error message', example: 'Unable to connect to database' },
                 error_details: { type: :string, description: 'Detailed error information' }
               }
        let(:Authorization) { "Bearer valid-jwt-token" }
        let(:id) { '123' }
        run_test!
      end

      response(404, 'not found') do
        schema '$ref' => '#/components/schemas/Error'
        let(:Authorization) { "Bearer valid-jwt-token" }
        let(:id) { '999999' }
        run_test!
      end

      response(401, 'unauthorized') do
        schema '$ref' => '#/components/schemas/Error'
        let(:Authorization) { "Bearer invalid-token" }
        let(:id) { '123' }
        run_test!
      end
    end
  end
end

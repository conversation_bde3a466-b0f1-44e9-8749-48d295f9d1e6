require 'swagger_helper'

RSpec.describe 'api/v1/executions', type: :request do
  path '/api/v1/executions' do
    get('list pipeline executions') do
      tags 'Pipeline Executions'
      description 'Get a list of pipeline executions for the authenticated account'
      consumes 'application/json'
      produces 'application/json'
      security [ { bearerAuth: [] } ]

      parameter name: :page, in: :query, type: :integer, description: 'Page number for pagination', required: false
      parameter name: :limit, in: :query, type: :integer, description: 'Number of items per page (max 100)', required: false
      parameter name: :status, in: :query, type: :string, description: 'Filter by execution status', enum: [ 'pending', 'running', 'success', 'failed', 'cancelled' ], required: false
      parameter name: :pipeline_id, in: :query, type: :integer, description: 'Filter by pipeline ID', required: false
      parameter name: :start_date, in: :query, type: :string, format: :date, description: 'Filter executions from this date', required: false
      parameter name: :end_date, in: :query, type: :string, format: :date, description: 'Filter executions until this date', required: false

      response(200, 'successful') do
        schema type: :object,
               properties: {
                 executions: {
                   type: :array,
                   items: { '$ref' => '#/components/schemas/PipelineExecution' }
                 },
                 pagination: {
                   type: :object,
                   properties: {
                     current_page: { type: :integer },
                     per_page: { type: :integer },
                     total_pages: { type: :integer },
                     total_count: { type: :integer }
                   }
                 },
                 summary: {
                   type: :object,
                   properties: {
                     total_executions: { type: :integer },
                     successful: { type: :integer },
                     failed: { type: :integer },
                     running: { type: :integer },
                     avg_duration_seconds: { type: :number }
                   }
                 }
               }

        let(:Authorization) { "Bearer valid-jwt-token" }
        run_test!
      end

      response(401, 'unauthorized') do
        schema '$ref' => '#/components/schemas/Error'
        let(:Authorization) { "Bearer invalid-token" }
        run_test!
      end
    end
  end

  path '/api/v1/executions/{id}' do
    parameter name: 'id', in: :path, type: :integer, description: 'Pipeline Execution ID'

    get('show pipeline execution') do
      tags 'Pipeline Executions'
      description 'Get details of a specific pipeline execution including logs'
      produces 'application/json'
      security [ { bearerAuth: [] } ]

      parameter name: :include_logs, in: :query, type: :boolean, description: 'Include execution logs in response', required: false

      response(200, 'successful') do
        schema allOf: [
          { '$ref' => '#/components/schemas/PipelineExecution' },
          {
            type: :object,
            properties: {
              execution_log: {
                type: :string,
                description: 'Execution log output'
              },
              log_entries: {
                type: :array,
                items: {
                  type: :object,
                  properties: {
                    timestamp: { type: :string, format: 'date-time' },
                    level: { type: :string, enum: [ 'INFO', 'WARN', 'ERROR', 'DEBUG' ] },
                    message: { type: :string }
                  }
                }
              }
            }
          }
        ]
        let(:Authorization) { "Bearer valid-jwt-token" }
        let(:id) { '123' }
        run_test!
      end

      response(404, 'not found') do
        schema '$ref' => '#/components/schemas/Error'
        let(:Authorization) { "Bearer valid-jwt-token" }
        let(:id) { '999999' }
        run_test!
      end

      response(401, 'unauthorized') do
        schema '$ref' => '#/components/schemas/Error'
        let(:Authorization) { "Bearer invalid-token" }
        let(:id) { '123' }
        run_test!
      end
    end
  end

  path '/api/v1/executions/{id}/cancel' do
    parameter name: 'id', in: :path, type: :integer, description: 'Pipeline Execution ID'

    patch('cancel execution') do
      tags 'Pipeline Executions'
      description 'Cancel a running pipeline execution'
      consumes 'application/json'
      produces 'application/json'
      security [ { bearerAuth: [] } ]

      parameter name: :cancellation, in: :body, schema: {
        type: :object,
        properties: {
          reason: { type: :string, description: 'Reason for cancellation', example: 'User requested cancellation' }
        }
      }, required: false

      response(200, 'cancellation successful') do
        schema type: :object,
               properties: {
                 id: { type: :integer, description: 'Execution ID' },
                 status: { type: :string, description: 'Updated status', example: 'cancelled' },
                 message: { type: :string, description: 'Cancellation message' },
                 cancelled_at: { type: :string, format: 'date-time', description: 'Cancellation timestamp' }
               }
        let(:Authorization) { "Bearer valid-jwt-token" }
        let(:id) { '123' }
        let(:cancellation) { { reason: 'Manual cancellation' } }
        run_test!
      end

      response(400, 'cannot cancel execution') do
        schema '$ref' => '#/components/schemas/Error'
        let(:Authorization) { "Bearer valid-jwt-token" }
        let(:id) { '123' }
        let(:cancellation) { {} }
        run_test!
      end

      response(404, 'not found') do
        schema '$ref' => '#/components/schemas/Error'
        let(:Authorization) { "Bearer valid-jwt-token" }
        let(:id) { '999999' }
        let(:cancellation) { {} }
        run_test!
      end

      response(401, 'unauthorized') do
        schema '$ref' => '#/components/schemas/Error'
        let(:Authorization) { "Bearer invalid-token" }
        let(:id) { '123' }
        let(:cancellation) { {} }
        run_test!
      end
    end
  end
end

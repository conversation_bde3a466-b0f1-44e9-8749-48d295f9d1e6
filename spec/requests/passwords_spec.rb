# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Users::PasswordsController, type: :request do
  let(:user) { create(:user, email: '<EMAIL>') }

  describe "GET /users/password/new" do
    it "renders the password reset request form successfully" do
      get new_user_password_path
      expect(response).to have_http_status(:success)
      expect(response).to render_template('devise/passwords/new')
    end

    it "uses the auth layout" do
      get new_user_password_path
      expect(response.body).to include('auth')
    end
  end

  describe "POST /users/password" do
    context "with valid email" do
      it "sends password reset instructions" do
        expect {
          post user_password_path, params: { user: { email: user.email } }
        }.to change { ActionMailer::Base.deliveries.count }.by(1)
      end

      it "redirects to login page after sending instructions" do
        post user_password_path, params: { user: { email: user.email } }
        expect(response).to redirect_to(new_user_session_path)
      end

      it "shows success message even for non-existent emails for security" do
        post user_password_path, params: { user: { email: '<EMAIL>' } }
        expect(response).to redirect_to(new_user_session_path)
      end
    end

    context "with invalid email format" do
      it "re-renders the form with errors" do
        post user_password_path, params: { user: { email: 'invalid-email' } }
        expect(response).to have_http_status(:unprocessable_entity)
        expect(response).to render_template('devise/passwords/new')
      end
    end

    context "with missing email" do
      it "re-renders the form with errors" do
        post user_password_path, params: { user: { email: '' } }
        expect(response).to have_http_status(:unprocessable_entity)
        expect(response).to render_template('devise/passwords/new')
      end
    end
  end

  describe "GET /users/password/edit" do
    let(:reset_token) { user.send_reset_password_instructions }

    context "with valid reset token" do
      it "renders the password reset form successfully" do
        get edit_user_password_path, params: { reset_password_token: reset_token }
        expect(response).to have_http_status(:success)
        expect(response).to render_template('devise/passwords/edit')
      end

      it "uses the auth layout" do
        get edit_user_password_path, params: { reset_password_token: reset_token }
        expect(response.body).to include('auth')
      end
    end

    context "with invalid reset token" do
      it "redirects to password reset request form" do
        get edit_user_password_path, params: { reset_password_token: 'invalid-token' }
        expect(response).to redirect_to(new_user_password_path)
      end
    end

    context "with missing reset token" do
      it "redirects to password reset request form" do
        get edit_user_password_path
        expect(response).to redirect_to(new_user_password_path)
      end
    end
  end

  describe "PATCH /users/password" do
    let(:reset_token) { user.send_reset_password_instructions }
    let(:raw_token) { user.instance_variable_get(:@raw_confirmation_token) }

    before do
      # Get the raw token that was generated
      user.send_reset_password_instructions
      @raw_token = user.reset_password_token
      user.reload
    end

    context "with valid parameters" do
      let(:valid_params) do
        {
          user: {
            reset_password_token: @raw_token,
            password: 'newpassword123',
            password_confirmation: 'newpassword123'
          }
        }
      end

      it "updates the user's password successfully" do
        patch user_password_path, params: valid_params
        user.reload
        expect(user.valid_password?('newpassword123')).to be_truthy
      end

      it "redirects to login page after successful password reset" do
        patch user_password_path, params: valid_params
        expect(response).to redirect_to(new_user_session_path)
      end

      it "signs in the user after successful password reset" do
        patch user_password_path, params: valid_params
        expect(controller.user_signed_in?).to be_falsy # Not signed in due to redirect override
      end
    end

    context "with invalid parameters" do
      context "when passwords don't match" do
        let(:invalid_params) do
          {
            user: {
              reset_password_token: @raw_token,
              password: 'newpassword123',
              password_confirmation: 'differentpassword'
            }
          }
        end

        it "does not update the password" do
          old_password_digest = user.encrypted_password
          patch user_password_path, params: invalid_params
          user.reload
          expect(user.encrypted_password).to eq(old_password_digest)
        end

        it "re-renders the edit form with errors" do
          patch user_password_path, params: invalid_params
          expect(response).to have_http_status(:unprocessable_entity)
          expect(response).to render_template('devise/passwords/edit')
        end
      end

      context "when password is too short" do
        let(:invalid_params) do
          {
            user: {
              reset_password_token: @raw_token,
              password: '123',
              password_confirmation: '123'
            }
          }
        end

        it "re-renders the edit form with errors" do
          patch user_password_path, params: invalid_params
          expect(response).to have_http_status(:unprocessable_entity)
          expect(response).to render_template('devise/passwords/edit')
        end
      end

      context "when reset token is invalid" do
        let(:invalid_params) do
          {
            user: {
              reset_password_token: 'invalid-token',
              password: 'newpassword123',
              password_confirmation: 'newpassword123'
            }
          }
        end

        it "re-renders the edit form with errors" do
          patch user_password_path, params: invalid_params
          expect(response).to have_http_status(:unprocessable_entity)
          expect(response).to render_template('devise/passwords/edit')
        end
      end
    end
  end

  describe "redirect behavior" do
    it "redirects to login page after sending reset instructions" do
      post user_password_path, params: { user: { email: user.email } }
      expect(response).to redirect_to(new_user_session_path)
    end

    it "redirects to login page after successful password reset" do
      user.send_reset_password_instructions
      raw_token = user.reset_password_token
      user.reload

      patch user_password_path, params: {
        user: {
          reset_password_token: raw_token,
          password: 'newpassword123',
          password_confirmation: 'newpassword123'
        }
      }
      expect(response).to redirect_to(new_user_session_path)
    end
  end

  describe "security considerations" do
    it "doesn't reveal whether user exists when requesting password reset" do
      post user_password_path, params: { user: { email: '<EMAIL>' } }
      expect(response).to redirect_to(new_user_session_path)
      expect(flash[:notice]).to be_present # Should show success message regardless
    end

    it "expires reset tokens after use" do
      user.send_reset_password_instructions
      raw_token = user.reset_password_token
      user.reload

      # First password reset should succeed
      patch user_password_path, params: {
        user: {
          reset_password_token: raw_token,
          password: 'newpassword123',
          password_confirmation: 'newpassword123'
        }
      }
      expect(response).to redirect_to(new_user_session_path)

      # Second attempt with same token should fail
      patch user_password_path, params: {
        user: {
          reset_password_token: raw_token,
          password: 'anotherpassword123',
          password_confirmation: 'anotherpassword123'
        }
      }
      expect(response).to have_http_status(:unprocessable_entity)
    end

    it "requires password confirmation for security" do
      user.send_reset_password_instructions
      raw_token = user.reset_password_token
      user.reload

      patch user_password_path, params: {
        user: {
          reset_password_token: raw_token,
          password: 'newpassword123'
          # Missing password_confirmation
        }
      }
      expect(response).to have_http_status(:unprocessable_entity)
      expect(response).to render_template('devise/passwords/edit')
    end
  end

  describe "email delivery" do
    it "sends password reset email with correct content" do
      expect {
        post user_password_path, params: { user: { email: user.email } }
      }.to change { ActionMailer::Base.deliveries.count }.by(1)

      email = ActionMailer::Base.deliveries.last
      expect(email.to).to include(user.email)
      expect(email.subject).to include('Reset password')
    end
  end

  describe "layout usage" do
    it "uses auth layout for new password request" do
      get new_user_password_path
      expect(response.body).to match(/auth/)
    end

    it "uses auth layout for password edit form" do
      user.send_reset_password_instructions
      raw_token = user.reset_password_token

      get edit_user_password_path, params: { reset_password_token: raw_token }
      expect(response.body).to match(/auth/)
    end
  end
end

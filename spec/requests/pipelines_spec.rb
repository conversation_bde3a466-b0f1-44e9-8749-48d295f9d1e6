# frozen_string_literal: true

require 'rails_helper'

RSpec.describe PipelinesController, type: :request do
  let(:account) { create(:account) }
  let(:user) { create(:user, account: account) }
  let(:source_connector) { create(:data_connector, account: account, connector_type: 'database') }
  let(:dest_connector) { create(:data_connector, account: account, connector_type: 'warehouse') }
  let(:pipeline) { create(:pipeline, account: account, created_by: user) }

  before do
    host! "#{account.subdomain}.datareflow.io"
    sign_in user
    Current.account = account
    Current.user = user
    allow_any_instance_of(PipelinesController).to receive(:set_sidebar_metrics)
  end

  describe "GET /pipelines" do
    let!(:pipelines) { create_list(:pipeline, 3, account: account, created_by: user) }

    it "renders the pipelines index successfully" do
      get pipelines_path
      expect(response).to have_http_status(:success)
      expect(response).to render_template(:index)
    end

    it "loads pipelines for the current account" do
      get pipelines_path
      expect(assigns(:pipelines)).to match_array(pipelines)
    end

    context "when requesting JSON format" do
      it "returns pipelines data in JSON format" do
        get pipelines_path, as: :json

        expect(response).to have_http_status(:success)
        expect(response.content_type).to include('application/json')

        json_response = JSON.parse(response.body)
        expect(json_response).to have_key('pipelines')
        expect(json_response).to have_key('meta')
        expect(json_response['pipelines']).to be_an(Array)
        expect(json_response['pipelines'].length).to eq(3)
      end

      it "includes pipeline metadata" do
        get pipelines_path, as: :json
        json_response = JSON.parse(response.body)

        expect(json_response['meta']).to have_key('total')
        expect(json_response['meta']).to have_key('active')
        expect(json_response['meta']).to have_key('performance')
      end
    end
  end

  describe "GET /pipelines/:id" do
    let!(:executions) { create_list(:pipeline_execution, 5, pipeline: pipeline) }

    it "renders the pipeline show page successfully" do
      get pipeline_path(pipeline)
      expect(response).to have_http_status(:success)
      expect(response).to render_template(:show)
    end

    it "loads the pipeline and recent executions" do
      get pipeline_path(pipeline)
      expect(assigns(:pipeline)).to eq(pipeline)
      expect(assigns(:recent_executions)).to be_present
      expect(assigns(:recent_executions).count).to be <= 10
    end

    context "when requesting JSON format" do
      it "returns pipeline details in JSON format" do
        get pipeline_path(pipeline), as: :json

        expect(response).to have_http_status(:success)
        json_response = JSON.parse(response.body)

        expect(json_response).to have_key('pipeline')
        expect(json_response).to have_key('recent_executions')
        expect(json_response).to have_key('performance')
      end
    end

    context "when pipeline doesn't exist" do
      it "redirects to pipelines index with error" do
        get pipeline_path(999999)
        expect(response).to redirect_to(pipelines_path)
        expect(flash[:alert]).to include('Pipeline not found')
      end

      it "returns 404 for JSON requests" do
        get pipeline_path(999999), as: :json
        expect(response).to have_http_status(:not_found)

        json_response = JSON.parse(response.body)
        expect(json_response['error']).to eq('Pipeline not found')
      end
    end
  end

  describe "GET /pipelines/new" do
    let!(:data_connectors) { create_list(:data_connector, 2, account: account, status: 'active') }

    it "renders the new pipeline form successfully" do
      get new_pipeline_path
      expect(response).to have_http_status(:success)
      expect(response).to render_template(:new)
    end

    it "builds a new pipeline and loads data connectors" do
      get new_pipeline_path
      expect(assigns(:pipeline)).to be_a_new(Pipeline)
      expect(assigns(:data_connectors)).to match_array(data_connectors)
    end

    context "with onboarding parameters" do
      it "pre-populates source config from parameters" do
        get new_pipeline_path, params: { source_type: 'database' }
        expect(assigns(:pipeline).source_config['type']).to eq('database')
      end

      it "pre-populates destination config from parameters" do
        get new_pipeline_path, params: { destination_type: 'warehouse' }
        expect(assigns(:pipeline).destination_config['type']).to eq('warehouse')
      end
    end
  end

  describe "POST /pipelines" do
    let(:valid_attributes) do
      {
        name: 'Test Pipeline',
        description: 'A test pipeline',
        source_config: { type: 'database' },
        destination_config: { type: 'warehouse' }
      }
    end

    let(:invalid_attributes) do
      {
        name: '',
        description: 'Invalid pipeline'
      }
    end

    context "with valid parameters" do
      it "creates a new pipeline" do
        expect {
          post pipelines_path, params: { pipeline: valid_attributes }
        }.to change(Pipeline, :count).by(1)
      end

      it "assigns the current user as creator" do
        post pipelines_path, params: { pipeline: valid_attributes }
        created_pipeline = Pipeline.last
        expect(created_pipeline.created_by).to eq(user)
      end

      it "redirects to the created pipeline" do
        post pipelines_path, params: { pipeline: valid_attributes }
        expect(response).to redirect_to(Pipeline.last)
        expect(flash[:notice]).to include('Pipeline created successfully')
      end

      context "with JSON request" do
        it "returns pipeline data and redirect URL" do
          post pipelines_path, params: { pipeline: valid_attributes }, as: :json

          expect(response).to have_http_status(:success)
          json_response = JSON.parse(response.body)

          expect(json_response).to have_key('pipeline')
          expect(json_response).to have_key('redirect_url')
        end
      end

      context "when onboarding is not completed" do
        before do
          allow(account).to receive(:onboarding_pipeline_completed?).and_return(false)
          allow(account).to receive(:update!)
        end

        it "updates onboarding progress" do
          expect(account).to receive(:update!).with(
            hash_including(onboarding_pipeline_completed: true)
          )

          post pipelines_path, params: { pipeline: valid_attributes }
        end
      end
    end

    context "with invalid parameters" do
      it "does not create a pipeline" do
        expect {
          post pipelines_path, params: { pipeline: invalid_attributes }
        }.not_to change(Pipeline, :count)
      end

      it "renders the new template with errors" do
        post pipelines_path, params: { pipeline: invalid_attributes }
        expect(response).to have_http_status(:unprocessable_entity)
        expect(response).to render_template(:new)
      end

      context "with JSON request" do
        it "returns validation errors" do
          post pipelines_path, params: { pipeline: invalid_attributes }, as: :json

          expect(response).to have_http_status(:unprocessable_entity)
          json_response = JSON.parse(response.body)
          expect(json_response).to have_key('errors')
        end
      end
    end
  end

  describe "GET /pipelines/:id/edit" do
    let!(:data_connectors) { create_list(:data_connector, 2, account: account, status: 'active') }

    it "renders the edit pipeline form successfully" do
      get edit_pipeline_path(pipeline)
      expect(response).to have_http_status(:success)
      expect(response).to render_template(:edit)
    end

    it "loads the pipeline and data connectors" do
      get edit_pipeline_path(pipeline)
      expect(assigns(:pipeline)).to eq(pipeline)
      expect(assigns(:data_connectors)).to match_array(data_connectors)
    end
  end

  describe "PATCH /pipelines/:id" do
    let(:updated_attributes) do
      {
        name: 'Updated Pipeline Name',
        description: 'Updated description'
      }
    end

    let(:invalid_attributes) do
      {
        name: ''
      }
    end

    context "with valid parameters" do
      it "updates the pipeline" do
        patch pipeline_path(pipeline), params: { pipeline: updated_attributes }
        pipeline.reload
        expect(pipeline.name).to eq('Updated Pipeline Name')
        expect(pipeline.description).to eq('Updated description')
      end

      it "redirects to the pipeline" do
        patch pipeline_path(pipeline), params: { pipeline: updated_attributes }
        expect(response).to redirect_to(pipeline)
        expect(flash[:notice]).to include('Pipeline updated successfully')
      end

      context "with JSON request" do
        it "returns updated pipeline data" do
          patch pipeline_path(pipeline), params: { pipeline: updated_attributes }, as: :json

          expect(response).to have_http_status(:success)
          json_response = JSON.parse(response.body)
          expect(json_response).to have_key('pipeline')
        end
      end
    end

    context "with invalid parameters" do
      it "does not update the pipeline" do
        original_name = pipeline.name
        patch pipeline_path(pipeline), params: { pipeline: invalid_attributes }
        pipeline.reload
        expect(pipeline.name).to eq(original_name)
      end

      it "renders the edit template with errors" do
        patch pipeline_path(pipeline), params: { pipeline: invalid_attributes }
        expect(response).to have_http_status(:unprocessable_entity)
        expect(response).to render_template(:edit)
      end
    end
  end

  describe "DELETE /pipelines/:id" do
    let!(:pipeline_to_delete) { create(:pipeline, account: account, created_by: user) }

    it "destroys the pipeline" do
      expect {
        delete pipeline_path(pipeline_to_delete)
      }.to change(Pipeline, :count).by(-1)
    end

    it "redirects to pipelines index" do
      delete pipeline_path(pipeline_to_delete)
      expect(response).to redirect_to(pipelines_path)
      expect(flash[:notice]).to include('Pipeline deleted successfully')
    end

    context "with JSON request" do
      it "returns success message" do
        delete pipeline_path(pipeline_to_delete), as: :json

        expect(response).to have_http_status(:success)
        json_response = JSON.parse(response.body)
        expect(json_response['message']).to include('Pipeline deleted successfully')
      end
    end
  end

  describe "POST /pipelines/:id/execute" do
    context "when pipeline can be executed" do
      before do
        allow(pipeline).to receive(:can_execute?).and_return(true)
        allow(PipelineExecutionJob).to receive(:perform_later)
      end

      it "queues a pipeline execution job" do
        expect(PipelineExecutionJob).to receive(:perform_later).with(pipeline)
        post execute_pipeline_path(pipeline)
      end

      it "redirects with success message" do
        post execute_pipeline_path(pipeline)
        expect(response).to redirect_to(pipeline)
        expect(flash[:notice]).to include('Pipeline execution started')
      end

      context "with JSON request" do
        it "returns execution status" do
          post execute_pipeline_path(pipeline), as: :json

          expect(response).to have_http_status(:success)
          json_response = JSON.parse(response.body)
          expect(json_response['message']).to include('Pipeline execution started')
          expect(json_response['status']).to eq('queued')
        end
      end
    end

    context "when pipeline cannot be executed" do
      before do
        allow(pipeline).to receive(:can_execute?).and_return(false)
      end

      it "does not queue an execution job" do
        expect(PipelineExecutionJob).not_to receive(:perform_later)
        post execute_pipeline_path(pipeline)
      end

      it "redirects with error message" do
        post execute_pipeline_path(pipeline)
        expect(response).to redirect_to(pipeline)
        expect(flash[:alert]).to include('Pipeline cannot be executed')
      end

      context "with JSON request" do
        it "returns error response" do
          post execute_pipeline_path(pipeline), as: :json

          expect(response).to have_http_status(:unprocessable_entity)
          json_response = JSON.parse(response.body)
          expect(json_response['error']).to include('Pipeline cannot be executed')
        end
      end
    end
  end

  describe "PATCH /pipelines/:id/toggle_status" do
    context "when pipeline is active" do
      before do
        pipeline.update!(status: 'active')
      end

      it "pauses the pipeline" do
        patch toggle_status_pipeline_path(pipeline)
        pipeline.reload
        expect(pipeline.status).to eq('paused')
      end

      it "redirects with success message" do
        patch toggle_status_pipeline_path(pipeline)
        expect(response).to redirect_to(pipeline)
        expect(flash[:notice]).to include('Pipeline paused successfully')
      end
    end

    context "when pipeline is paused" do
      before do
        pipeline.update!(status: 'paused')
      end

      it "activates the pipeline" do
        patch toggle_status_pipeline_path(pipeline)
        pipeline.reload
        expect(pipeline.status).to eq('active')
      end

      it "redirects with success message" do
        patch toggle_status_pipeline_path(pipeline)
        expect(response).to redirect_to(pipeline)
        expect(flash[:notice]).to include('Pipeline activated successfully')
      end
    end

    context "with JSON request" do
      it "returns updated pipeline data and message" do
        patch toggle_status_pipeline_path(pipeline), as: :json

        expect(response).to have_http_status(:success)
        json_response = JSON.parse(response.body)
        expect(json_response).to have_key('pipeline')
        expect(json_response).to have_key('message')
      end
    end
  end

  describe "authentication and authorization" do
    context "without authentication" do
      before { sign_out user }

      it "redirects to login page" do
        get pipelines_path
        expect(response).to redirect_to(new_user_session_path)
      end
    end

    context "without account access" do
      before do
        allow_any_instance_of(PipelinesController).to receive(:current_account).and_return(nil)
      end

      it "redirects to root path" do
        get pipelines_path
        expect(response).to redirect_to(root_path)
      end
    end
  end
end

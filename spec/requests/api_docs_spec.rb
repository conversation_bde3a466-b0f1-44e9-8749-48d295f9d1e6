# frozen_string_literal: true

require 'rails_helper'

RSpec.describe ApiDocsController, type: :request do
  let(:account) { create(:account) }
  let(:user) { create(:user, account: account) }

  before do
    sign_in user
  end

  describe "GET /api-docs" do
    it "renders the API documentation index successfully" do
      get api_docs_path
      expect(response).to have_http_status(:success)
      expect(response).to render_template(:index)
    end

    it "includes the correct page title" do
      get api_docs_path
      expect(response.body).to include("DataReflow API Documentation")
    end

    it "includes navigation links to other documentation pages" do
      get api_docs_path
      expect(response.body).to include("Authentication Guide")
      expect(response.body).to include("Quick Start")
      expect(response.body).to include("Code Examples")
    end
  end

  describe "GET /api-docs/authentication" do
    it "renders the authentication guide successfully" do
      get api_authentication_docs_path
      expect(response).to have_http_status(:success)
      expect(response).to render_template(:authentication)
    end

    it "includes JWT authentication information" do
      get api_authentication_docs_path
      expect(response.body).to include("JWT Token Authentication")
      expect(response.body).to include("Authorization: Bearer")
    end

    it "includes security best practices" do
      get api_authentication_docs_path
      expect(response.body).to include("Security Best Practices")
      expect(response.body).to include("Never store tokens in localStorage")
    end

    it "includes error handling examples" do
      get api_authentication_docs_path
      expect(response.body).to include("401 Unauthorized")
      expect(response.body).to include("403 Forbidden")
      expect(response.body).to include("422 Invalid Credentials")
    end
  end

  describe "GET /api-docs/quickstart" do
    it "renders the quickstart guide successfully" do
      get api_quickstart_path
      expect(response).to have_http_status(:success)
      expect(response).to render_template(:quickstart)
    end

    it "includes step-by-step instructions" do
      get api_quickstart_path
      expect(response.body).to include("Get Your API Token")
      expect(response.body).to include("List Your Pipelines")
      expect(response.body).to include("Create a New Pipeline")
      expect(response.body).to include("Execute Your Pipeline")
      expect(response.body).to include("Handle Results & Errors")
    end

    it "includes curl examples" do
      get api_quickstart_path
      expect(response.body).to include("curl -X POST")
      expect(response.body).to include("/api/login")
      expect(response.body).to include("/api/v1/pipelines")
    end

    it "includes common integration patterns" do
      get api_quickstart_path
      expect(response.body).to include("Common Integration Patterns")
      expect(response.body).to include("Scheduled Pipelines")
      expect(response.body).to include("Real-time Processing")
    end
  end

  describe "GET /api-docs/examples" do
    it "renders the examples page successfully" do
      get api_examples_path
      expect(response).to have_http_status(:success)
      expect(response).to render_template(:examples)
    end

    it "includes multiple programming language examples" do
      get api_examples_path
      expect(response.body).to include("JavaScript")
      expect(response.body).to include("Python")
      expect(response.body).to include("Ruby")
      expect(response.body).to include("PHP")
      expect(response.body).to include("Go")
      expect(response.body).to include("cURL")
    end

    it "includes complete API client implementations" do
      get api_examples_path
      expect(response.body).to include("DataReflowAPI")
      expect(response.body).to include("class DataReflow")
      expect(response.body).to include("authenticate")
      expect(response.body).to include("createPipeline")
    end

    it "includes interactive language selection tabs" do
      get api_examples_path
      expect(response.body).to include("data-tab")
      expect(response.body).to include("tab-content")
    end
  end

  describe "content security and layout" do
    it "uses the dashboard layout for all pages" do
      get api_docs_path
      expect(response.body).to include("dashboard")
    end

    it "includes proper page titles for SEO" do
      get api_authentication_docs_path
      expect(response.body).to include("<title>Authentication Guide")
    end

    it "includes responsive design classes" do
      get api_docs_path
      expect(response.body).to include("max-w-")
      expect(response.body).to include("sm:")
      expect(response.body).to include("md:")
    end
  end

  describe "navigation between documentation sections" do
    it "includes links to other documentation sections from index" do
      get api_docs_path
      expect(response.body).to include(api_authentication_docs_path)
      expect(response.body).to include(api_quickstart_path)
      expect(response.body).to include(api_examples_path)
    end

    it "includes links to quickstart from authentication page" do
      get api_authentication_docs_path
      expect(response.body).to include(api_quickstart_path)
      expect(response.body).to include(api_examples_path)
    end

    it "includes links to other sections from quickstart" do
      get api_quickstart_path
      expect(response.body).to include(api_examples_path)
      expect(response.body).to include(api_authentication_docs_path)
    end
  end

  context "without authentication" do
    before { sign_out user }

    it "redirects unauthenticated users to login" do
      get api_docs_path
      expect(response).to redirect_to(new_user_session_path)
    end

    it "redirects from authentication guide to login" do
      get api_authentication_docs_path
      expect(response).to redirect_to(new_user_session_path)
    end

    it "redirects from quickstart guide to login" do
      get api_quickstart_path
      expect(response).to redirect_to(new_user_session_path)
    end

    it "redirects from examples to login" do
      get api_examples_path
      expect(response).to redirect_to(new_user_session_path)
    end
  end

  describe "OpenAPI integration" do
    it "includes links to Swagger UI" do
      get api_docs_path
      expect(response.body).to include("/api-docs")
    end
  end
end

# frozen_string_literal: true

require 'rails_helper'

RSpec.describe DataConnectorsController, type: :request do
  let(:account) { create(:account) }
  let(:user) { create(:user, account: account) }
  let(:data_connector) { create(:data_connector, account: account, created_by: user) }

  before do
    host! "#{account.subdomain}.datareflow.io"
    sign_in user
    Current.account = account
    Current.user = user
    allow_any_instance_of(DataConnectorsController).to receive(:set_sidebar_metrics)
  end

  describe "GET /data_connectors" do
    let!(:connectors) { create_list(:data_connector, 3, account: account, created_by: user) }

    it "renders the data connectors index successfully" do
      get data_connectors_path
      expect(response).to have_http_status(:success)
      expect(response).to render_template(:index)
    end

    it "loads data connectors for the current account" do
      get data_connectors_path
      expect(assigns(:data_connectors)).to match_array(connectors)
    end

    it "loads health summary" do
      allow(DataConnector).to receive(:health_summary).with(account).and_return({
        total: 3,
        active: 2,
        healthy: 2,
        needs_attention: 1
      })

      get data_connectors_path
      expect(assigns(:health_summary)).to be_present
      expect(DataConnector).to have_received(:health_summary).with(account)
    end

    context "when requesting JSON format" do
      it "returns connectors data in JSON format" do
        get data_connectors_path, as: :json

        expect(response).to have_http_status(:success)
        expect(response.content_type).to include('application/json')

        json_response = JSON.parse(response.body)
        expect(json_response).to have_key('connectors')
        expect(json_response).to have_key('health_summary')
        expect(json_response).to have_key('available_types')
        expect(json_response['connectors']).to be_an(Array)
      end
    end
  end

  describe "GET /data_connectors/:id" do
    it "renders the data connector show page successfully" do
      get data_connector_path(data_connector)
      expect(response).to have_http_status(:success)
      expect(response).to render_template(:show)
    end

    it "loads the data connector" do
      get data_connector_path(data_connector)
      expect(assigns(:data_connector)).to eq(data_connector)
    end

    context "when requesting JSON format" do
      it "returns connector details in JSON format" do
        get data_connector_path(data_connector), as: :json

        expect(response).to have_http_status(:success)
        json_response = JSON.parse(response.body)

        expect(json_response).to include('id', 'name', 'connector_type', 'status')
        expect(json_response).to include('connection_healthy', 'created_by')
      end
    end

    context "when data connector doesn't exist" do
      it "redirects to data connectors index with error" do
        get data_connector_path(999999)
        expect(response).to redirect_to(data_connectors_path)
        expect(flash[:alert]).to include('Data connector not found')
      end

      it "returns 404 for JSON requests" do
        get data_connector_path(999999), as: :json
        expect(response).to have_http_status(:not_found)

        json_response = JSON.parse(response.body)
        expect(json_response['error']).to eq('Data connector not found')
      end
    end
  end

  describe "GET /data_connectors/new" do
    it "renders the new data connector form successfully" do
      get new_data_connector_path
      expect(response).to have_http_status(:success)
      expect(response).to render_template(:new)
    end

    it "builds a new data connector and loads available types" do
      allow(DataConnector).to receive(:available_types).and_return([ 'database', 'api', 'file' ])

      get new_data_connector_path
      expect(assigns(:data_connector)).to be_a_new(DataConnector)
      expect(assigns(:connector_types)).to eq([ 'database', 'api', 'file' ])
    end
  end

  describe "POST /data_connectors" do
    let(:valid_attributes) do
      {
        name: 'Test Connector',
        connector_type: 'database',
        connection_config: { host: 'localhost', port: 5432 }
      }
    end

    let(:invalid_attributes) do
      {
        name: '',
        connector_type: 'database'
      }
    end

    context "with valid parameters" do
      it "creates a new data connector" do
        expect {
          post data_connectors_path, params: { data_connector: valid_attributes }
        }.to change(DataConnector, :count).by(1)
      end

      it "assigns the current user as creator" do
        post data_connectors_path, params: { data_connector: valid_attributes }
        created_connector = DataConnector.last
        expect(created_connector.created_by).to eq(user)
      end

      it "redirects to the created data connector" do
        post data_connectors_path, params: { data_connector: valid_attributes }
        expect(response).to redirect_to(DataConnector.last)
        expect(flash[:notice]).to include('Data connector created successfully')
      end

      context "with JSON request" do
        it "returns connector data" do
          post data_connectors_path, params: { data_connector: valid_attributes }, as: :json

          expect(response).to have_http_status(:success)
          json_response = JSON.parse(response.body)

          expect(json_response).to have_key('connector')
          expect(json_response['connector']['name']).to eq('Test Connector')
        end
      end

      context "when onboarding connection is not completed" do
        before do
          allow(account).to receive(:onboarding_connection_completed?).and_return(false)
          allow(account).to receive(:update!)
        end

        it "updates onboarding progress" do
          expect(account).to receive(:update!).with(onboarding_connection_completed: true)

          post data_connectors_path, params: { data_connector: valid_attributes }
        end
      end
    end

    context "with invalid parameters" do
      it "does not create a data connector" do
        expect {
          post data_connectors_path, params: { data_connector: invalid_attributes }
        }.not_to change(DataConnector, :count)
      end

      it "renders the new template with errors" do
        post data_connectors_path, params: { data_connector: invalid_attributes }
        expect(response).to have_http_status(:unprocessable_entity)
        expect(response).to render_template(:new)
      end

      context "with JSON request" do
        it "returns validation errors" do
          post data_connectors_path, params: { data_connector: invalid_attributes }, as: :json

          expect(response).to have_http_status(:unprocessable_entity)
          json_response = JSON.parse(response.body)
          expect(json_response).to have_key('errors')
        end
      end
    end
  end

  describe "GET /data_connectors/:id/edit" do
    it "renders the edit data connector form successfully" do
      get edit_data_connector_path(data_connector)
      expect(response).to have_http_status(:success)
      expect(response).to render_template(:edit)
    end

    it "loads the data connector and available types" do
      allow(DataConnector).to receive(:available_types).and_return([ 'database', 'api' ])

      get edit_data_connector_path(data_connector)
      expect(assigns(:data_connector)).to eq(data_connector)
      expect(assigns(:connector_types)).to eq([ 'database', 'api' ])
    end
  end

  describe "PATCH /data_connectors/:id" do
    let(:updated_attributes) do
      {
        name: 'Updated Connector Name',
        connection_config: { host: 'newhost.com', port: 3306 }
      }
    end

    let(:invalid_attributes) do
      {
        name: ''
      }
    end

    context "with valid parameters" do
      it "updates the data connector" do
        patch data_connector_path(data_connector), params: { data_connector: updated_attributes }
        data_connector.reload
        expect(data_connector.name).to eq('Updated Connector Name')
      end

      it "does not allow changing connector type" do
        original_type = data_connector.connector_type
        patch data_connector_path(data_connector), params: {
          data_connector: updated_attributes.merge(connector_type: 'different_type')
        }
        data_connector.reload
        expect(data_connector.connector_type).to eq(original_type)
      end

      it "redirects to the data connector" do
        patch data_connector_path(data_connector), params: { data_connector: updated_attributes }
        expect(response).to redirect_to(data_connector)
        expect(flash[:notice]).to include('Data connector updated successfully')
      end

      context "with JSON request" do
        it "returns updated connector data" do
          patch data_connector_path(data_connector), params: { data_connector: updated_attributes }, as: :json

          expect(response).to have_http_status(:success)
          json_response = JSON.parse(response.body)
          expect(json_response).to have_key('connector')
          expect(json_response['connector']['name']).to eq('Updated Connector Name')
        end
      end
    end

    context "with invalid parameters" do
      it "does not update the data connector" do
        original_name = data_connector.name
        patch data_connector_path(data_connector), params: { data_connector: invalid_attributes }
        data_connector.reload
        expect(data_connector.name).to eq(original_name)
      end

      it "renders the edit template with errors" do
        patch data_connector_path(data_connector), params: { data_connector: invalid_attributes }
        expect(response).to have_http_status(:unprocessable_entity)
        expect(response).to render_template(:edit)
      end
    end
  end

  describe "DELETE /data_connectors/:id" do
    let!(:connector_to_delete) { create(:data_connector, account: account, created_by: user) }

    context "when connector is not used by any pipelines" do
      before do
        allow(account).to receive_message_chain(:pipelines, :where).and_return([])
      end

      it "destroys the data connector" do
        expect {
          delete data_connector_path(connector_to_delete)
        }.to change(DataConnector, :count).by(-1)
      end

      it "redirects to data connectors index" do
        delete data_connector_path(connector_to_delete)
        expect(response).to redirect_to(data_connectors_path)
        expect(flash[:notice]).to include('Data connector deleted successfully')
      end

      context "with JSON request" do
        it "returns success message" do
          delete data_connector_path(connector_to_delete), as: :json

          expect(response).to have_http_status(:success)
          json_response = JSON.parse(response.body)
          expect(json_response['message']).to include('Data connector deleted successfully')
        end
      end
    end

    context "when connector is used by pipelines" do
      let(:pipeline) { create(:pipeline, account: account, created_by: user) }

      before do
        mock_pipelines = double('pipelines')
        allow(mock_pipelines).to receive(:any?).and_return(true)
        allow(mock_pipelines).to receive(:count).and_return(2)
        allow(account).to receive_message_chain(:pipelines, :where).and_return(mock_pipelines)
      end

      it "does not destroy the data connector" do
        expect {
          delete data_connector_path(connector_to_delete)
        }.not_to change(DataConnector, :count)
      end

      it "redirects with error message" do
        delete data_connector_path(connector_to_delete)
        expect(response).to redirect_to(data_connectors_path)
        expect(flash[:alert]).to include("Cannot delete connector - it's used by")
      end

      context "with JSON request" do
        it "returns error response" do
          delete data_connector_path(connector_to_delete), as: :json

          expect(response).to have_http_status(:unprocessable_entity)
          json_response = JSON.parse(response.body)
          expect(json_response['error']).to include("Cannot delete connector")
        end
      end
    end
  end

  describe "POST /data_connectors/:id/test_connection" do
    it "queues a connection test job" do
      expect(DataConnectorTestJob).to receive(:perform_later).with(data_connector.id)

      post test_connection_data_connector_path(data_connector)
    end

    it "updates test status to in progress" do
      allow(DataConnectorTestJob).to receive(:perform_later)

      post test_connection_data_connector_path(data_connector)
      data_connector.reload
      expect(data_connector.test_status).to eq('test_in_progress')
    end

    it "redirects with success message" do
      allow(DataConnectorTestJob).to receive(:perform_later)

      post test_connection_data_connector_path(data_connector)
      expect(response).to redirect_to(data_connector)
      expect(flash[:notice]).to include('Connection test started')
    end

    context "with JSON request" do
      it "returns test status and connector data" do
        allow(DataConnectorTestJob).to receive(:perform_later)

        post test_connection_data_connector_path(data_connector), as: :json

        expect(response).to have_http_status(:success)
        json_response = JSON.parse(response.body)
        expect(json_response['message']).to include('Connection test started')
        expect(json_response).to have_key('connector')
      end
    end
  end

  describe "authentication and authorization" do
    context "without authentication" do
      before { sign_out user }

      it "redirects to login page" do
        get data_connectors_path
        expect(response).to redirect_to(new_user_session_path)
      end
    end

    context "without account access" do
      before do
        allow_any_instance_of(DataConnectorsController).to receive(:current_account).and_return(nil)
      end

      it "redirects to root path" do
        get data_connectors_path
        expect(response).to redirect_to(root_path)
      end
    end
  end
end

require "rails_helper"

RSpec.describe "Marketing", type: :request do
  describe "GET /" do
    it "returns http success" do
      get "/"
      expect(response).to have_http_status(:success)
    end

    it "contains expected content" do
      get "/"
      expect(response.body).to include("DataReflow")
    end
  end

  describe "GET /about" do
    it "returns http success" do
      get "/about"
      expect(response).to have_http_status(:success)
    end

    it "contains expected content" do
      get "/about"
      expect(response.body).to include("About")
    end
  end

  describe "GET /contact" do
    it "returns http success" do
      get "/contact"
      expect(response).to have_http_status(:success)
    end

    it "contains contact form" do
      get "/contact"
      expect(response.body).to include("contact_form")
    end
  end

  describe "POST /contact" do
    let(:valid_params) do
      {
        contact_form: {
          name: "<PERSON>",
          email: "<EMAIL>",
          company: "Test Company",
          inquiry_type: "general",
          message: "This is a test message with enough characters"
        }
      }
    end

    let(:invalid_params) do
      {
        contact_form: {
          name: "",
          email: "invalid-email",
          message: ""
        }
      }
    end

    context "with valid parameters" do
      it "redirects to contact page" do
        post "/contact", params: valid_params
        expect(response).to redirect_to(contact_path)
      end

      it "sets success flash message" do
        post "/contact", params: valid_params
        follow_redirect!
        expect(response.body).to include("Thank you")
      end
    end

    context "with invalid parameters" do
      it "returns unprocessable content status" do
        post "/contact", params: invalid_params
        expect(response).to have_http_status(:unprocessable_content)
      end
    end
  end

  describe "GET /help" do
    it "returns http success" do
      get "/help"
      expect(response).to have_http_status(:success)
    end
  end

  describe "GET /integrations" do
    it "returns http success" do
      get "/integrations"
      expect(response).to have_http_status(:success)
    end
  end

  describe "GET /security" do
    it "returns http success" do
      get "/security"
      expect(response).to have_http_status(:success)
    end
  end

  describe "GET /privacy" do
    it "returns http success" do
      get "/privacy"
      expect(response).to have_http_status(:success)
    end
  end

  describe "GET /docs" do
    it "returns http success" do
      get "/docs"
      expect(response).to have_http_status(:success)
    end
  end

  describe "GET /api" do
    it "returns http success" do
      get "/api"
      expect(response).to have_http_status(:success)
    end
  end

  describe "GET /community" do
    it "returns http success" do
      get "/community"
      expect(response).to have_http_status(:success)
    end
  end

  describe "GET /press" do
    it "returns http success" do
      get "/press"
      expect(response).to have_http_status(:success)
    end
  end
end

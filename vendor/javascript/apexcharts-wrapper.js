// ApexCharts ES Module Wrapper for Rails 8
// This wraps the UMD ApexCharts build to provide proper ES module exports

// Import the UMD build - it will attach ApexCharts to window
import './apexcharts.js';

// Check if ApexCharts was loaded properly
if (typeof window.ApexCharts === 'undefined') {
  console.error('❌ ApexCharts UMD build failed to load');
  throw new Error('ApexCharts UMD build not available');
}

// Export ApexCharts as the default export
const ApexCharts = window.ApexCharts;

// Log successful loading
console.log('✅ ApexCharts ES module wrapper loaded successfully:', ApexCharts);
console.log('📋 ApexCharts version:', ApexCharts.version || 'unknown');

export default ApexCharts;
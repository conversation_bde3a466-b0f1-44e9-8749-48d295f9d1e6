---
openapi: 3.0.1
info:
  title: DataReflow API
  version: v1
  description: DataReflow - Your Data Pipeline Platform API Documentation
  termsOfService: https://datareflow.io/terms
  contact:
    name: DataReflow Support
    email: <EMAIL>
    url: https://datareflow.io/support
  license:
    name: Proprietary
    url: https://datareflow.io/license
paths:
  "/api/login":
    post:
      summary: user login
      tags:
      - Authentication
      description: Authenticate user and receive JWT token
      parameters: []
      responses:
        '200':
          description: login successful
          content:
            application/json:
              schema:
                type: object
                properties:
                  token:
                    type: string
                    description: JWT authentication token
                    example: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
                  user:
                    type: object
                    properties:
                      id:
                        type: integer
                        example: 123
                      email:
                        type: string
                        example: <EMAIL>
                      first_name:
                        type: string
                        example: John
                      last_name:
                        type: string
                        example: Doe
                      role:
                        type: string
                        example: admin
                  account:
                    type: object
                    properties:
                      id:
                        type: integer
                        example: 456
                      name:
                        type: string
                        example: Acme Corp
                      subdomain:
                        type: string
                        example: acme
                      plan:
                        type: string
                        example: professional
        '401':
          description: invalid credentials
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/Error"
        '400':
          description: bad request
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/Error"
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                email:
                  type: string
                  format: email
                  description: User email address
                  example: <EMAIL>
                password:
                  type: string
                  description: User password
                  example: secure_password123
              required:
              - email
              - password
  "/api/register":
    post:
      summary: user registration
      tags:
      - Authentication
      description: Register a new user account
      parameters: []
      responses:
        '201':
          description: registration successful
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: User created successfully. Please check your email for
                      confirmation.
                  user:
                    type: object
                    properties:
                      id:
                        type: integer
                      email:
                        type: string
                      first_name:
                        type: string
                      last_name:
                        type: string
                      confirmed:
                        type: boolean
                        example: false
                  account:
                    type: object
                    properties:
                      id:
                        type: integer
                      name:
                        type: string
                      subdomain:
                        type: string
        '400':
          description: validation errors
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/Error"
        '422':
          description: user already exists
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/Error"
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                email:
                  type: string
                  format: email
                  description: User email address
                  example: <EMAIL>
                password:
                  type: string
                  description: User password (minimum 8 characters)
                  example: secure_password123
                password_confirmation:
                  type: string
                  description: Password confirmation
                  example: secure_password123
                first_name:
                  type: string
                  description: First name
                  example: Jane
                last_name:
                  type: string
                  description: Last name
                  example: Smith
                account_name:
                  type: string
                  description: Account/company name
                  example: Smith Industries
                subdomain:
                  type: string
                  description: Desired subdomain (optional, will be generated if not
                    provided)
                  example: smith-industries
              required:
              - email
              - password
              - password_confirmation
              - first_name
              - last_name
              - account_name
  "/api/logout":
    delete:
      summary: user logout
      tags:
      - Authentication
      description: Logout user and invalidate JWT token
      security:
      - bearerAuth: []
      responses:
        '200':
          description: logout successful
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: Logged out successfully
        '401':
          description: unauthorized
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/Error"
  "/api/me":
    get:
      summary: current user info
      tags:
      - Authentication
      description: Get current authenticated user information
      security:
      - bearerAuth: []
      responses:
        '200':
          description: user info retrieved
          content:
            application/json:
              schema:
                type: object
                properties:
                  user:
                    type: object
                    properties:
                      id:
                        type: integer
                        example: 123
                      email:
                        type: string
                        example: <EMAIL>
                      first_name:
                        type: string
                        example: John
                      last_name:
                        type: string
                        example: Doe
                      role:
                        type: string
                        example: admin
                      confirmed:
                        type: boolean
                        example: true
                      created_at:
                        type: string
                        format: date-time
                      last_sign_in_at:
                        type: string
                        format: date-time
                  account:
                    type: object
                    properties:
                      id:
                        type: integer
                        example: 456
                      name:
                        type: string
                        example: Acme Corp
                      subdomain:
                        type: string
                        example: acme
                      plan:
                        type: string
                        example: professional
                      status:
                        type: string
                        example: active
                  permissions:
                    type: array
                    items:
                      type: string
                    example:
                    - read:pipelines
                    - write:pipelines
                    - read:connectors
                    - write:connectors
        '401':
          description: unauthorized
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/Error"
  "/api/v1/connectors":
    get:
      summary: list data connectors
      tags:
      - Data Connectors
      description: Get a list of all data connectors for the authenticated account
      security:
      - bearerAuth: []
      parameters:
      - name: page
        in: query
        description: Page number for pagination
        required: false
        schema:
          type: integer
      - name: limit
        in: query
        description: Number of items per page (max 100)
        required: false
        schema:
          type: integer
      - name: status
        in: query
        description: "Filter by connector status:\n * `active` \n * `inactive` \n
          * `error` \n "
        enum:
        - active
        - inactive
        - error
        required: false
        schema:
          type: string
      - name: connector_type
        in: query
        description: Filter by connector type
        required: false
        schema:
          type: string
      responses:
        '200':
          description: successful
          content:
            application/json:
              schema:
                type: object
                properties:
                  connectors:
                    type: array
                    items:
                      "$ref": "#/components/schemas/DataConnector"
                  pagination:
                    type: object
                    properties:
                      current_page:
                        type: integer
                      per_page:
                        type: integer
                      total_pages:
                        type: integer
                      total_count:
                        type: integer
        '401':
          description: unauthorized
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/Error"
    post:
      summary: create data connector
      tags:
      - Data Connectors
      description: Create a new data connector
      security:
      - bearerAuth: []
      parameters: []
      responses:
        '201':
          description: created
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/DataConnector"
        '400':
          description: bad request
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/Error"
        '401':
          description: unauthorized
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/Error"
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                name:
                  type: string
                  description: Connector name
                  example: Production Database
                connector_type:
                  type: string
                  description: Type of connector
                  example: postgresql
                  enum:
                  - postgresql
                  - mysql
                  - mongodb
                  - api
                  - csv
                  - json
                  - s3
                  - ftp
                connection_string:
                  type: string
                  description: Connection configuration (JSON)
                  example: '{"host": "db.example.com", "port": 5432, "database": "production"}'
                description:
                  type: string
                  description: Connector description
                  example: Main production database connection
              required:
              - name
              - connector_type
              - connection_string
  "/api/v1/connectors/{id}":
    parameters:
    - name: id
      in: path
      description: Data Connector ID
      required: true
      schema:
        type: integer
    get:
      summary: show data connector
      tags:
      - Data Connectors
      description: Get details of a specific data connector
      security:
      - bearerAuth: []
      responses:
        '200':
          description: successful
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/DataConnector"
        '404':
          description: not found
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/Error"
        '401':
          description: unauthorized
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/Error"
    put:
      summary: update data connector
      tags:
      - Data Connectors
      description: Update an existing data connector
      security:
      - bearerAuth: []
      parameters: []
      responses:
        '200':
          description: successful
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/DataConnector"
        '400':
          description: bad request
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/Error"
        '404':
          description: not found
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/Error"
        '401':
          description: unauthorized
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/Error"
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                name:
                  type: string
                  description: Connector name
                connector_type:
                  type: string
                  description: Type of connector
                connection_string:
                  type: string
                  description: Connection configuration
                description:
                  type: string
                  description: Connector description
                status:
                  type: string
                  enum:
                  - active
                  - inactive
                  - error
    delete:
      summary: delete data connector
      tags:
      - Data Connectors
      description: Delete a data connector
      security:
      - bearerAuth: []
      responses:
        '204':
          description: no content
        '404':
          description: not found
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/Error"
        '401':
          description: unauthorized
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/Error"
  "/api/v1/connectors/{id}/test_connection":
    parameters:
    - name: id
      in: path
      description: Data Connector ID
      required: true
      schema:
        type: integer
    post:
      summary: test connection
      tags:
      - Data Connectors
      description: Test the connection for a specific data connector
      security:
      - bearerAuth: []
      responses:
        '200':
          description: connection successful
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    description: Connection test status
                    example: success
                  message:
                    type: string
                    description: Connection test message
                    example: Connection established successfully
                  response_time_ms:
                    type: integer
                    description: Connection response time in milliseconds
                    example: 45
                  connection_details:
                    type: object
                    description: Additional connection details
                    properties:
                      version:
                        type: string
                        description: Database/service version
                      uptime:
                        type: string
                        description: Service uptime
        '400':
          description: connection failed
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    description: Connection test status
                    example: failed
                  message:
                    type: string
                    description: Error message
                    example: Unable to connect to database
                  error_details:
                    type: string
                    description: Detailed error information
        '404':
          description: not found
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/Error"
        '401':
          description: unauthorized
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/Error"
  "/api/v1/executions":
    get:
      summary: list pipeline executions
      tags:
      - Pipeline Executions
      description: Get a list of pipeline executions for the authenticated account
      security:
      - bearerAuth: []
      parameters:
      - name: page
        in: query
        description: Page number for pagination
        required: false
        schema:
          type: integer
      - name: limit
        in: query
        description: Number of items per page (max 100)
        required: false
        schema:
          type: integer
      - name: status
        in: query
        description: "Filter by execution status:\n * `pending` \n * `running` \n
          * `success` \n * `failed` \n * `cancelled` \n "
        enum:
        - pending
        - running
        - success
        - failed
        - cancelled
        required: false
        schema:
          type: string
      - name: pipeline_id
        in: query
        description: Filter by pipeline ID
        required: false
        schema:
          type: integer
      - name: start_date
        in: query
        format: date
        description: Filter executions from this date
        required: false
        schema:
          type: string
      - name: end_date
        in: query
        format: date
        description: Filter executions until this date
        required: false
        schema:
          type: string
      responses:
        '200':
          description: successful
          content:
            application/json:
              schema:
                type: object
                properties:
                  executions:
                    type: array
                    items:
                      "$ref": "#/components/schemas/PipelineExecution"
                  pagination:
                    type: object
                    properties:
                      current_page:
                        type: integer
                      per_page:
                        type: integer
                      total_pages:
                        type: integer
                      total_count:
                        type: integer
                  summary:
                    type: object
                    properties:
                      total_executions:
                        type: integer
                      successful:
                        type: integer
                      failed:
                        type: integer
                      running:
                        type: integer
                      avg_duration_seconds:
                        type: number
        '401':
          description: unauthorized
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/Error"
  "/api/v1/executions/{id}":
    parameters:
    - name: id
      in: path
      description: Pipeline Execution ID
      required: true
      schema:
        type: integer
    get:
      summary: show pipeline execution
      tags:
      - Pipeline Executions
      description: Get details of a specific pipeline execution including logs
      security:
      - bearerAuth: []
      parameters:
      - name: include_logs
        in: query
        description: Include execution logs in response
        required: false
        schema:
          type: boolean
      responses:
        '200':
          description: successful
          content:
            application/json:
              schema:
                allOf:
                - "$ref": "#/components/schemas/PipelineExecution"
                - type: object
                  properties:
                    execution_log:
                      type: string
                      description: Execution log output
                    log_entries:
                      type: array
                      items:
                        type: object
                        properties:
                          timestamp:
                            type: string
                            format: date-time
                          level:
                            type: string
                            enum:
                            - INFO
                            - WARN
                            - ERROR
                            - DEBUG
                          message:
                            type: string
        '404':
          description: not found
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/Error"
        '401':
          description: unauthorized
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/Error"
  "/api/v1/executions/{id}/cancel":
    parameters:
    - name: id
      in: path
      description: Pipeline Execution ID
      required: true
      schema:
        type: integer
    patch:
      summary: cancel execution
      tags:
      - Pipeline Executions
      description: Cancel a running pipeline execution
      security:
      - bearerAuth: []
      parameters: []
      responses:
        '200':
          description: cancellation successful
          content:
            application/json:
              schema:
                type: object
                properties:
                  id:
                    type: integer
                    description: Execution ID
                  status:
                    type: string
                    description: Updated status
                    example: cancelled
                  message:
                    type: string
                    description: Cancellation message
                  cancelled_at:
                    type: string
                    format: date-time
                    description: Cancellation timestamp
        '400':
          description: cannot cancel execution
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/Error"
        '404':
          description: not found
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/Error"
        '401':
          description: unauthorized
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/Error"
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                reason:
                  type: string
                  description: Reason for cancellation
                  example: User requested cancellation
  "/api/v1/pipelines":
    get:
      summary: list pipelines
      tags:
      - Pipelines
      description: Get a list of all pipelines for the authenticated account
      security:
      - bearerAuth: []
      parameters:
      - name: page
        in: query
        description: Page number for pagination
        required: false
        schema:
          type: integer
      - name: limit
        in: query
        description: Number of items per page (max 100)
        required: false
        schema:
          type: integer
      - name: status
        in: query
        description: "Filter by pipeline status:\n * `active` \n * `inactive` \n *
          `draft` \n "
        enum:
        - active
        - inactive
        - draft
        required: false
        schema:
          type: string
      responses:
        '200':
          description: successful
          content:
            application/json:
              schema:
                type: object
                properties:
                  pipelines:
                    type: array
                    items:
                      "$ref": "#/components/schemas/Pipeline"
                  pagination:
                    type: object
                    properties:
                      current_page:
                        type: integer
                      per_page:
                        type: integer
                      total_pages:
                        type: integer
                      total_count:
                        type: integer
        '401':
          description: unauthorized
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/Error"
    post:
      summary: create pipeline
      tags:
      - Pipelines
      description: Create a new data pipeline
      security:
      - bearerAuth: []
      parameters: []
      responses:
        '201':
          description: created
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/Pipeline"
        '400':
          description: bad request
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/Error"
        '401':
          description: unauthorized
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/Error"
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                name:
                  type: string
                  description: Pipeline name
                  example: Customer Data ETL
                description:
                  type: string
                  description: Pipeline description
                  example: Extract customer data from CRM and transform for analytics
                status:
                  type: string
                  enum:
                  - active
                  - inactive
                  - draft
                  default: draft
              required:
              - name
  "/api/v1/pipelines/{id}":
    parameters:
    - name: id
      in: path
      description: Pipeline ID
      required: true
      schema:
        type: integer
    get:
      summary: show pipeline
      tags:
      - Pipelines
      description: Get details of a specific pipeline
      security:
      - bearerAuth: []
      responses:
        '200':
          description: successful
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/Pipeline"
        '404':
          description: not found
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/Error"
        '401':
          description: unauthorized
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/Error"
    put:
      summary: update pipeline
      tags:
      - Pipelines
      description: Update an existing pipeline
      security:
      - bearerAuth: []
      parameters: []
      responses:
        '200':
          description: successful
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/Pipeline"
        '400':
          description: bad request
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/Error"
        '404':
          description: not found
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/Error"
        '401':
          description: unauthorized
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/Error"
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                name:
                  type: string
                  description: Pipeline name
                description:
                  type: string
                  description: Pipeline description
                status:
                  type: string
                  enum:
                  - active
                  - inactive
                  - draft
    delete:
      summary: delete pipeline
      tags:
      - Pipelines
      description: Delete a pipeline
      security:
      - bearerAuth: []
      responses:
        '204':
          description: no content
        '404':
          description: not found
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/Error"
        '401':
          description: unauthorized
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/Error"
  "/api/v1/pipelines/{id}/execute":
    parameters:
    - name: id
      in: path
      description: Pipeline ID
      required: true
      schema:
        type: integer
    post:
      summary: execute pipeline
      tags:
      - Pipelines
      description: Execute a pipeline manually
      security:
      - bearerAuth: []
      parameters: []
      responses:
        '202':
          description: accepted - execution started
          content:
            application/json:
              schema:
                type: object
                properties:
                  execution_id:
                    type: integer
                    description: Pipeline execution ID
                  status:
                    type: string
                    description: Initial execution status
                  message:
                    type: string
                    description: Status message
        '400':
          description: bad request - pipeline not executable
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/Error"
        '404':
          description: pipeline not found
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/Error"
        '401':
          description: unauthorized
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/Error"
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                async:
                  type: boolean
                  default: true
                  description: Execute asynchronously
                parameters:
                  type: object
                  description: Pipeline-specific execution parameters
                  additionalProperties: true
servers:
- url: http://localhost:3000/api/v1
  description: Development server
- url: https://{subdomain}.datareflow.io/api/v1
  description: Production server
  variables:
    subdomain:
      default: api
      description: Your account subdomain
components:
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
      description: JWT token obtained from authentication endpoint
    apiKeyAuth:
      type: apiKey
      in: header
      name: Authorization
      description: API key authentication
  schemas:
    Error:
      type: object
      properties:
        error:
          type: string
          description: Error message
        details:
          type: array
          items:
            type: string
          description: Additional error details
        timestamp:
          type: string
          format: date-time
          description: Error timestamp
      required:
      - error
    Pipeline:
      type: object
      properties:
        id:
          type: integer
          description: Pipeline ID
        name:
          type: string
          description: Pipeline name
        description:
          type: string
          description: Pipeline description
        status:
          type: string
          enum:
          - active
          - inactive
          - draft
          description: Pipeline status
        created_at:
          type: string
          format: date-time
          description: Creation timestamp
        updated_at:
          type: string
          format: date-time
          description: Last update timestamp
      required:
      - id
      - name
      - status
    DataConnector:
      type: object
      properties:
        id:
          type: integer
          description: Data connector ID
        name:
          type: string
          description: Connector name
        connector_type:
          type: string
          description: Type of connector (e.g., database, api, file)
        status:
          type: string
          enum:
          - active
          - inactive
          - error
          description: Connector status
        connection_string:
          type: string
          description: Connection configuration (sensitive data masked)
        created_at:
          type: string
          format: date-time
          description: Creation timestamp
        updated_at:
          type: string
          format: date-time
          description: Last update timestamp
      required:
      - id
      - name
      - connector_type
      - status
    PipelineExecution:
      type: object
      properties:
        id:
          type: integer
          description: Execution ID
        pipeline_id:
          type: integer
          description: Pipeline ID
        status:
          type: string
          enum:
          - pending
          - running
          - success
          - failed
          - cancelled
          description: Execution status
        started_at:
          type: string
          format: date-time
          description: Execution start time
        completed_at:
          type: string
          format: date-time
          description: Execution completion time
        records_processed:
          type: integer
          description: Number of records processed
        records_success:
          type: integer
          description: Number of successfully processed records
        records_failed:
          type: integer
          description: Number of failed records
        error_message:
          type: string
          description: Error message if execution failed
      required:
      - id
      - pipeline_id
      - status

# DataReflow Design System

A comprehensive design system for maintaining visual and UX consistency across all DataReflow interfaces, from marketing pages to internal dashboards.

## 🎨 Color Palette

### Primary Colors
```css
/* Primary Blues */
--color-primary-50: #eff6ff;   /* Very light blue background */
--color-primary-100: #dbeafe;  /* Light blue background */
--color-primary-500: #3b82f6;  /* Primary blue */
--color-primary-600: #2563eb;  /* Primary blue hover */
--color-primary-700: #1d4ed8;  /* Primary blue active */

/* Secondary Indigos */
--color-secondary-50: #eef2ff;  /* Very light indigo */
--color-secondary-100: #e0e7ff; /* Light indigo */
--color-secondary-500: #6366f1; /* Secondary indigo */
--color-secondary-600: #4f46e5; /* Secondary indigo hover */

/* Accent Purples */
--color-accent-50: #faf5ff;    /* Very light purple */
--color-accent-100: #f3e8ff;   /* Light purple */
--color-accent-500: #8b5cf6;   /* Accent purple */
--color-accent-600: #7c3aed;   /* Accent purple hover */
```

### Neutral Colors
```css
/* Grays */
--color-gray-50: #f9fafb;      /* Lightest gray */
--color-gray-100: #f3f4f6;     /* Very light gray */
--color-gray-200: #e5e7eb;     /* Light gray borders */
--color-gray-300: #d1d5db;     /* Medium light gray */
--color-gray-400: #9ca3af;     /* Medium gray */
--color-gray-500: #6b7280;     /* Medium dark gray */
--color-gray-600: #4b5563;     /* Dark gray */
--color-gray-700: #374151;     /* Very dark gray */
--color-gray-800: #1f2937;     /* Almost black */
--color-gray-900: #111827;     /* Black */

/* Pure Colors */
--color-white: #ffffff;
--color-black: #000000;
```

### Status Colors
```css
/* Success */
--color-success-50: #f0fdf4;
--color-success-500: #22c55e;
--color-success-600: #16a34a;

/* Warning */
--color-warning-50: #fffbeb;
--color-warning-500: #f59e0b;
--color-warning-600: #d97706;

/* Error */
--color-error-50: #fef2f2;
--color-error-500: #ef4444;
--color-error-600: #dc2626;

/* Info */
--color-info-50: #f0f9ff;
--color-info-500: #06b6d4;
--color-info-600: #0891b2;
```

## 📝 Typography

### Font Family
```css
--font-family-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
--font-family-mono: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, monospace;
```

### Font Sizes & Line Heights
```css
/* Headings */
--text-6xl: 3.75rem;    /* 60px - Hero titles */
--text-5xl: 3rem;       /* 48px - Page titles */
--text-4xl: 2.25rem;    /* 36px - Section titles */
--text-3xl: 1.875rem;   /* 30px - Subsection titles */
--text-2xl: 1.5rem;     /* 24px - Card titles */
--text-xl: 1.25rem;     /* 20px - Large text */

/* Body Text */
--text-lg: 1.125rem;    /* 18px - Large body */
--text-base: 1rem;      /* 16px - Default body */
--text-sm: 0.875rem;    /* 14px - Small text */
--text-xs: 0.75rem;     /* 12px - Captions */

/* Line Heights */
--leading-tight: 1.25;
--leading-normal: 1.5;
--leading-relaxed: 1.625;
```

### Font Weights
```css
--font-weight-normal: 400;
--font-weight-medium: 500;
--font-weight-semibold: 600;
--font-weight-bold: 700;
```

## 🧩 Component Library

### Buttons

#### Primary Button
```html
<button class="btn btn-primary">
  Get Started
</button>
```

```css
.btn {
  @apply inline-flex items-center justify-center px-6 py-3 rounded-lg font-semibold text-base transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2;
}

.btn-primary {
  @apply bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500 transform hover:scale-105 shadow-lg hover:shadow-xl;
}

.btn-secondary {
  @apply bg-white text-gray-900 border border-gray-200 hover:bg-gray-50 focus:ring-gray-500 shadow-sm hover:shadow-md;
}

.btn-outline {
  @apply bg-transparent text-blue-600 border border-blue-600 hover:bg-blue-50 focus:ring-blue-500;
}

.btn-sm {
  @apply px-4 py-2 text-sm;
}

.btn-lg {
  @apply px-8 py-4 text-lg;
}
```

### Cards

#### Basic Card
```html
<div class="card">
  <div class="card-header">
    <h3 class="card-title">Card Title</h3>
  </div>
  <div class="card-body">
    <p class="card-text">Card content goes here.</p>
  </div>
</div>
```

```css
.card {
  @apply bg-white rounded-2xl shadow-sm border border-gray-100 overflow-hidden;
}

.card-hover {
  @apply hover:shadow-lg transition-shadow duration-200;
}

.card-header {
  @apply px-6 py-4 border-b border-gray-100;
}

.card-body {
  @apply px-6 py-4;
}

.card-title {
  @apply text-xl font-semibold text-gray-900;
}

.card-text {
  @apply text-gray-600 leading-relaxed;
}
```

#### Feature Card
```html
<div class="feature-card">
  <div class="feature-icon">
    <!-- SVG Icon -->
  </div>
  <h3 class="feature-title">Feature Title</h3>
  <p class="feature-description">Feature description text.</p>
</div>
```

```css
.feature-card {
  @apply bg-gray-50 p-8 rounded-2xl hover:shadow-lg transition-shadow duration-200;
}

.feature-icon {
  @apply w-16 h-16 bg-blue-100 rounded-xl flex items-center justify-center mb-6;
}

.feature-title {
  @apply text-xl font-semibold text-gray-900 mb-3;
}

.feature-description {
  @apply text-gray-600 leading-relaxed;
}
```

### Forms

#### Input Fields
```html
<div class="form-group">
  <label class="form-label" for="email">Email Address</label>
  <input type="email" id="email" class="form-input" placeholder="Enter your email">
  <p class="form-error">Error message here</p>
</div>
```

```css
.form-group {
  @apply mb-6;
}

.form-label {
  @apply block text-sm font-medium text-gray-700 mb-2;
}

.form-input {
  @apply w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors;
}

.form-input:invalid {
  @apply border-red-300 focus:ring-red-500 focus:border-red-500;
}

.form-error {
  @apply text-sm text-red-600 mt-1;
}

.form-success {
  @apply text-sm text-green-600 mt-1;
}
```

#### Select Dropdown
```html
<select class="form-select">
  <option>Choose an option</option>
  <option value="1">Option 1</option>
</select>
```

```css
.form-select {
  @apply w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white;
}
```

### Navigation

#### Header Navigation
```html
<nav class="nav-header">
  <div class="nav-container">
    <div class="nav-brand">
      <a href="/" class="nav-logo">DataReflow</a>
    </div>
    <div class="nav-links">
      <a href="/features" class="nav-link">Features</a>
      <a href="/pricing" class="nav-link nav-link-active">Pricing</a>
    </div>
  </div>
</nav>
```

```css
.nav-header {
  @apply bg-white/95 backdrop-blur-sm border-b border-gray-200 sticky top-0 z-50;
}

.nav-container {
  @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 flex items-center justify-between h-16;
}

.nav-logo {
  @apply text-2xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent;
}

.nav-links {
  @apply hidden md:flex items-center space-x-8;
}

.nav-link {
  @apply text-gray-600 hover:text-gray-900 font-medium transition-colors;
}

.nav-link-active {
  @apply text-blue-600 font-semibold;
}
```

## 📐 Spacing & Layout

### Spacing Scale
```css
/* Spacing tokens */
--space-1: 0.25rem;   /* 4px */
--space-2: 0.5rem;    /* 8px */
--space-3: 0.75rem;   /* 12px */
--space-4: 1rem;      /* 16px */
--space-6: 1.5rem;    /* 24px */
--space-8: 2rem;      /* 32px */
--space-12: 3rem;     /* 48px */
--space-16: 4rem;     /* 64px */
--space-24: 6rem;     /* 96px */
```

### Container Widths
```css
.container {
  @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
}

.container-sm {
  @apply max-w-4xl mx-auto px-4 sm:px-6 lg:px-8;
}

.container-xs {
  @apply max-w-2xl mx-auto px-4 sm:px-6 lg:px-8;
}
```

### Grid System
```css
.grid-1 { @apply grid grid-cols-1; }
.grid-2 { @apply grid grid-cols-1 md:grid-cols-2; }
.grid-3 { @apply grid grid-cols-1 md:grid-cols-3; }
.grid-4 { @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4; }

.gap-4 { @apply gap-4; }
.gap-6 { @apply gap-6; }
.gap-8 { @apply gap-8; }
```

## 🔄 Border Radius
```css
--radius-sm: 0.375rem;   /* 6px */
--radius-md: 0.5rem;     /* 8px */
--radius-lg: 0.75rem;    /* 12px */
--radius-xl: 1rem;       /* 16px */
--radius-2xl: 1.5rem;    /* 24px */
--radius-full: 9999px;   /* Fully rounded */
```

## 🎭 Shadows
```css
--shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
--shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1);
--shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1);
--shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1);
```

## 📱 Responsive Breakpoints
```css
/* Mobile first approach */
--breakpoint-sm: 640px;   /* Small devices */
--breakpoint-md: 768px;   /* Medium devices */
--breakpoint-lg: 1024px;  /* Large devices */
--breakpoint-xl: 1280px;  /* Extra large devices */
--breakpoint-2xl: 1536px; /* 2X large devices */
```

## 🎬 Animation & Transitions

### Transition Durations
```css
--duration-fast: 150ms;
--duration-normal: 200ms;
--duration-slow: 300ms;
--duration-slower: 500ms;
```

### Easing Functions
```css
--ease-in: cubic-bezier(0.4, 0, 1, 1);
--ease-out: cubic-bezier(0, 0, 0.2, 1);
--ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
```

### Common Animations
```css
.fade-in {
  animation: fadeIn 0.3s ease-out;
}

.slide-up {
  animation: slideUp 0.3s ease-out;
}

.scale-in {
  animation: scaleIn 0.2s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from { transform: translateY(20px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

@keyframes scaleIn {
  from { transform: scale(0.95); opacity: 0; }
  to { transform: scale(1); opacity: 1; }
}
```

## 🎯 Icon System

### Icon Sizes
```css
.icon-xs { @apply w-3 h-3; }    /* 12px */
.icon-sm { @apply w-4 h-4; }    /* 16px */
.icon-md { @apply w-5 h-5; }    /* 20px */
.icon-lg { @apply w-6 h-6; }    /* 24px */
.icon-xl { @apply w-8 h-8; }    /* 32px */
.icon-2xl { @apply w-12 h-12; } /* 48px */
```

### Icon Colors
```css
.icon-primary { @apply text-blue-600; }
.icon-secondary { @apply text-gray-600; }
.icon-success { @apply text-green-600; }
.icon-warning { @apply text-yellow-600; }
.icon-error { @apply text-red-600; }
```

## 🏷️ Badges & Tags

```html
<span class="badge badge-primary">New</span>
<span class="badge badge-success">Active</span>
<span class="badge badge-warning">Pending</span>
```

```css
.badge {
  @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
}

.badge-primary {
  @apply bg-blue-100 text-blue-800;
}

.badge-success {
  @apply bg-green-100 text-green-800;
}

.badge-warning {
  @apply bg-yellow-100 text-yellow-800;
}

.badge-error {
  @apply bg-red-100 text-red-800;
}
```

## 📊 Data Display

### Tables
```html
<div class="table-container">
  <table class="table">
    <thead class="table-header">
      <tr>
        <th class="table-cell table-cell-header">Name</th>
        <th class="table-cell table-cell-header">Status</th>
      </tr>
    </thead>
    <tbody>
      <tr class="table-row">
        <td class="table-cell">John Doe</td>
        <td class="table-cell">
          <span class="badge badge-success">Active</span>
        </td>
      </tr>
    </tbody>
  </table>
</div>
```

```css
.table-container {
  @apply overflow-x-auto bg-white rounded-lg border border-gray-200;
}

.table {
  @apply min-w-full divide-y divide-gray-200;
}

.table-header {
  @apply bg-gray-50;
}

.table-row {
  @apply hover:bg-gray-50 transition-colors;
}

.table-cell {
  @apply px-6 py-4 text-sm;
}

.table-cell-header {
  @apply font-medium text-gray-900 uppercase tracking-wider;
}
```

### Progress Bars
```html
<div class="progress">
  <div class="progress-bar" style="width: 75%"></div>
</div>
```

```css
.progress {
  @apply w-full bg-gray-200 rounded-full h-2;
}

.progress-bar {
  @apply bg-blue-600 h-2 rounded-full transition-all duration-300;
}
```

## 🚨 Alerts & Notifications

```html
<div class="alert alert-success">
  <div class="alert-icon">✓</div>
  <div class="alert-content">
    <h4 class="alert-title">Success!</h4>
    <p class="alert-message">Your changes have been saved.</p>
  </div>
</div>
```

```css
.alert {
  @apply flex items-start p-4 rounded-lg border;
}

.alert-success {
  @apply bg-green-50 border-green-200 text-green-800;
}

.alert-warning {
  @apply bg-yellow-50 border-yellow-200 text-yellow-800;
}

.alert-error {
  @apply bg-red-50 border-red-200 text-red-800;
}

.alert-info {
  @apply bg-blue-50 border-blue-200 text-blue-800;
}

.alert-icon {
  @apply flex-shrink-0 w-5 h-5 mr-3;
}

.alert-title {
  @apply font-medium mb-1;
}

.alert-message {
  @apply text-sm;
}
```

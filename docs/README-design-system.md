# DataReflow Design System

A comprehensive design system that ensures visual and UX consistency across all DataReflow interfaces, from marketing pages to internal dashboards.

## 📁 File Structure

```
docs/
├── design-system.md                    # Complete design system documentation
├── design-system-implementation.md     # Implementation guidelines and examples
└── README-design-system.md            # This file

app/assets/stylesheets/
├── design-tokens.css                   # CSS custom properties (design tokens)
├── components.css                      # Reusable component styles
└── application.css                     # Application-specific styles

app/views/design_system/
└── index.html.erb                      # Visual style guide

app/controllers/
└── design_system_controller.rb         # Controller for style guide
```

## 🚀 Quick Start

### 1. View the Style Guide

Visit `/design-system` in your browser to see the complete visual style guide with all components, colors, typography, and examples.

### 2. Use Design Tokens

```css
/* Use CSS custom properties for consistency */
.my-component {
  background-color: var(--color-primary-600);
  padding: var(--space-4);
  border-radius: var(--radius-lg);
  font-size: var(--text-base);
}
```

### 3. Apply Component Classes

```html
<!-- Use pre-built component classes -->
<button class="btn btn-primary">Primary Action</button>
<div class="card">
  <div class="card-body">
    <h3 class="card-title">Card Title</h3>
    <p class="card-text">Card content</p>
  </div>
</div>
```

## 🎨 Design Tokens

### Colors
- **Primary**: Blue palette (`--color-primary-*`)
- **Secondary**: Indigo palette (`--color-secondary-*`)
- **Accent**: Purple palette (`--color-accent-*`)
- **Neutral**: Gray palette (`--color-gray-*`)
- **Status**: Success, warning, error, info (`--color-success-*`, etc.)

### Typography
- **Font Family**: Inter (`--font-family-primary`)
- **Font Sizes**: `--text-xs` to `--text-6xl`
- **Font Weights**: `--font-weight-normal` to `--font-weight-bold`
- **Line Heights**: `--leading-tight` to `--leading-loose`

### Spacing
- **Scale**: `--space-1` (4px) to `--space-32` (128px)
- **Consistent 4px base unit**

### Other Tokens
- **Border Radius**: `--radius-sm` to `--radius-3xl`
- **Shadows**: `--shadow-sm` to `--shadow-2xl`
- **Transitions**: `--duration-*` and `--ease-*`
- **Breakpoints**: `--breakpoint-sm` to `--breakpoint-2xl`

## 🧩 Component Library

### Buttons
- **Variants**: Primary, secondary, outline, ghost
- **Sizes**: Small, medium, large, extra large
- **States**: Default, hover, focus, disabled

### Cards
- **Types**: Basic card, feature card, hoverable card
- **Sections**: Header, body, footer
- **Modifiers**: Hover effects, borders

### Forms
- **Elements**: Input, textarea, select, label
- **States**: Default, focus, invalid, disabled
- **Helpers**: Error messages, help text

### Navigation
- **Header**: Sticky navigation with backdrop blur
- **Links**: Active states, hover effects
- **Mobile**: Responsive menu toggle

### Feedback
- **Alerts**: Success, warning, error, info
- **Badges**: Status indicators with color coding
- **Progress**: Loading bars with variants

### Data Display
- **Tables**: Responsive with hover states
- **Loading**: Spinners and skeleton states

## 📐 Layout System

### Containers
- **Full width**: `container` (max-width: 1280px)
- **Medium**: `container-sm` (max-width: 896px)
- **Narrow**: `container-xs` (max-width: 672px)

### Grid System
- **Responsive**: Mobile-first approach
- **Columns**: 1-4 columns with breakpoint variants
- **Gaps**: Consistent spacing options

### Flexbox Utilities
- **Direction**: Row, column
- **Alignment**: Center, between, around
- **Gaps**: Consistent spacing

## 🎯 Usage Guidelines

### Do's ✅
- Use design tokens for all styling
- Follow component patterns
- Maintain semantic HTML
- Test across breakpoints
- Ensure accessibility compliance

### Don'ts ❌
- Don't use hardcoded values
- Don't override component styles unnecessarily
- Don't ignore accessibility
- Don't mix with other design systems

## 🔧 Customization

### Extending Components
Create modifier classes that build on existing components:

```css
.btn-gradient {
  background: linear-gradient(135deg, var(--color-primary-600), var(--color-secondary-600));
}
```

### Theme Customization
Override design tokens to customize the theme:

```css
:root {
  --color-primary-600: #7c3aed; /* Purple theme */
  --font-family-primary: 'Roboto', sans-serif;
}
```

## 📱 Responsive Design

- **Mobile-first**: Start with mobile styles, enhance for larger screens
- **Breakpoints**: 640px, 768px, 1024px, 1280px, 1536px
- **Flexible**: Use relative units and flexible layouts

## ♿ Accessibility

- **Focus states**: Clear visual indicators
- **Color contrast**: WCAG AA compliance
- **Semantic HTML**: Proper element usage
- **Keyboard navigation**: Full keyboard support
- **Screen readers**: ARIA attributes where needed

## 🧪 Testing

### Visual Consistency
- [ ] Colors use design tokens
- [ ] Typography follows scale
- [ ] Spacing is consistent
- [ ] Components follow patterns

### Functionality
- [ ] Interactive states work
- [ ] Responsive behavior
- [ ] Cross-browser compatibility
- [ ] Performance optimization

### Accessibility
- [ ] Keyboard navigation
- [ ] Screen reader compatibility
- [ ] Color contrast compliance
- [ ] Focus management

## 📚 Resources

- **Style Guide**: `/design-system` - Visual reference
- **Documentation**: `docs/design-system.md` - Complete specification
- **Implementation**: `docs/design-system-implementation.md` - Usage examples
- **Tokens**: `app/assets/stylesheets/design-tokens.css` - CSS variables
- **Components**: `app/assets/stylesheets/components.css` - Component styles

## 🔄 Maintenance

### Adding New Components
1. Follow existing patterns
2. Use design tokens
3. Document in style guide
4. Add to component library
5. Test across breakpoints

### Updating Tokens
1. Update `design-tokens.css`
2. Test impact across components
3. Update documentation
4. Communicate changes to team

### Version Control
- Track changes in git
- Document breaking changes
- Maintain backward compatibility when possible
- Use semantic versioning for major updates

## 🤝 Contributing

When contributing to the design system:

1. **Follow patterns**: Use existing component patterns
2. **Use tokens**: Always use design tokens, never hardcoded values
3. **Test thoroughly**: Check all breakpoints and states
4. **Document changes**: Update relevant documentation
5. **Consider accessibility**: Ensure WCAG compliance
6. **Get feedback**: Review with team before implementing

## 📞 Support

For questions about the design system:
- Check the documentation first
- View the style guide at `/design-system`
- Review implementation examples
- Ask the development team for guidance

---

**Built with ❤️ for DataReflow - Ensuring consistency, accessibility, and beautiful user experiences across all our interfaces.**

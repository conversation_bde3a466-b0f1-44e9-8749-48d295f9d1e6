# DataReflow Design System Implementation Guide

This guide provides practical instructions for implementing the DataReflow design system across all application interfaces.

## 🚀 Getting Started

### 1. Import Design System Files

Add these imports to your main CSS file or layout:

```css
/* Import design tokens first */
@import 'design-tokens.css';

/* Then import components */
@import 'components.css';

/* Your custom styles last */
@import 'custom.css';
```

### 2. HTML Structure

Use semantic HTML with design system classes:

```html
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>DataReflow</title>
  <link rel="stylesheet" href="design-tokens.css">
  <link rel="stylesheet" href="components.css">
</head>
<body>
  <!-- Your content here -->
</body>
</html>
```

## 🎨 Using Design Tokens

### Colors

```css
/* Use CSS custom properties */
.my-component {
  background-color: var(--color-primary-600);
  color: var(--color-white);
  border: 1px solid var(--color-gray-200);
}

/* Status colors */
.success-message {
  color: var(--color-success-600);
  background-color: var(--color-success-50);
}
```

### Typography

```css
.page-title {
  font-size: var(--text-4xl);
  font-weight: var(--font-weight-bold);
  line-height: var(--leading-tight);
  color: var(--color-gray-900);
}

.body-text {
  font-size: var(--text-base);
  line-height: var(--leading-relaxed);
  color: var(--color-gray-600);
}
```

### Spacing

```css
.card-content {
  padding: var(--space-6);
  margin-bottom: var(--space-4);
  gap: var(--space-3);
}
```

## 🧩 Component Usage Examples

### Buttons

```html
<!-- Primary action -->
<button class="btn btn-primary">Save Changes</button>

<!-- Secondary action -->
<button class="btn btn-secondary">Cancel</button>

<!-- Outline button -->
<button class="btn btn-outline">Learn More</button>

<!-- Button with icon -->
<button class="btn btn-primary btn-icon">
  <svg class="icon-sm">...</svg>
  Download
</button>

<!-- Different sizes -->
<button class="btn btn-primary btn-sm">Small</button>
<button class="btn btn-primary btn-lg">Large</button>
```

### Cards

```html
<!-- Basic card -->
<div class="card">
  <div class="card-header">
    <h3 class="card-title">Dashboard Overview</h3>
    <p class="card-subtitle">Last updated 2 hours ago</p>
  </div>
  <div class="card-body">
    <p class="card-text">Your data pipeline is running smoothly.</p>
  </div>
</div>

<!-- Feature card -->
<div class="feature-card">
  <div class="feature-icon">
    <svg>...</svg>
  </div>
  <h3 class="feature-title">Real-time Processing</h3>
  <p class="feature-description">Process data as it arrives with zero latency.</p>
</div>

<!-- Hoverable card -->
<div class="card card-hover">
  <div class="card-body">
    <h4 class="card-title">Interactive Card</h4>
    <p class="card-text">This card has hover effects.</p>
  </div>
</div>
```

### Forms

```html
<form>
  <div class="form-group">
    <label class="form-label form-label-required" for="email">
      Email Address
    </label>
    <input 
      type="email" 
      id="email" 
      class="form-input" 
      placeholder="Enter your email"
      required
    >
    <p class="form-help">We'll never share your email with anyone else.</p>
  </div>

  <div class="form-group">
    <label class="form-label" for="message">Message</label>
    <textarea 
      id="message" 
      class="form-textarea" 
      placeholder="Enter your message"
      rows="4"
    ></textarea>
  </div>

  <div class="form-group">
    <label class="form-label" for="category">Category</label>
    <select id="category" class="form-select">
      <option value="">Choose a category</option>
      <option value="general">General Inquiry</option>
      <option value="support">Support</option>
    </select>
  </div>

  <button type="submit" class="btn btn-primary">Submit</button>
</form>
```

### Navigation

```html
<nav class="nav-header">
  <div class="nav-container">
    <div class="nav-brand">
      <a href="/" class="nav-logo">DataReflow</a>
    </div>
    
    <div class="nav-links">
      <a href="/dashboard" class="nav-link nav-link-active">Dashboard</a>
      <a href="/pipelines" class="nav-link">Pipelines</a>
      <a href="/settings" class="nav-link">Settings</a>
    </div>

    <button class="nav-mobile-toggle">
      <svg>...</svg>
    </button>
  </div>
</nav>
```

### Alerts

```html
<!-- Success alert -->
<div class="alert alert-success">
  <div class="alert-icon">✓</div>
  <div class="alert-content">
    <h4 class="alert-title">Success!</h4>
    <p class="alert-message">Your pipeline has been created successfully.</p>
  </div>
</div>

<!-- Error alert -->
<div class="alert alert-error">
  <div class="alert-icon">⚠</div>
  <div class="alert-content">
    <h4 class="alert-title">Error</h4>
    <p class="alert-message">Failed to connect to the database. Please check your credentials.</p>
  </div>
</div>
```

### Tables

```html
<div class="table-container">
  <table class="table">
    <thead class="table-header">
      <tr>
        <th class="table-cell table-cell-header">Pipeline Name</th>
        <th class="table-cell table-cell-header">Status</th>
        <th class="table-cell table-cell-header table-cell-numeric">Records</th>
        <th class="table-cell table-cell-header">Actions</th>
      </tr>
    </thead>
    <tbody>
      <tr class="table-row">
        <td class="table-cell">Sales Data Pipeline</td>
        <td class="table-cell">
          <span class="badge badge-success">Active</span>
        </td>
        <td class="table-cell table-cell-numeric">1,234</td>
        <td class="table-cell">
          <button class="btn btn-sm btn-outline">Edit</button>
        </td>
      </tr>
    </tbody>
  </table>
</div>
```

### Badges

```html
<span class="badge badge-primary">New</span>
<span class="badge badge-success">Active</span>
<span class="badge badge-warning">Pending</span>
<span class="badge badge-error">Failed</span>
```

### Progress Bars

```html
<!-- Basic progress -->
<div class="progress">
  <div class="progress-bar" style="width: 75%"></div>
</div>

<!-- Success progress -->
<div class="progress">
  <div class="progress-bar progress-bar-success" style="width: 100%"></div>
</div>

<!-- Large progress -->
<div class="progress progress-lg">
  <div class="progress-bar" style="width: 45%"></div>
</div>
```

### Loading States

```html
<!-- Spinner -->
<div class="loading-spinner"></div>
<div class="loading-spinner loading-spinner-lg"></div>

<!-- Skeleton loading -->
<div class="loading-skeleton" style="height: 1rem; width: 100%;"></div>
<div class="loading-skeleton" style="height: 1rem; width: 75%; margin-top: 0.5rem;"></div>
```

## 📐 Layout Patterns

### Container Layouts

```html
<!-- Full width container -->
<div class="container">
  <h1>Page Title</h1>
  <p>Content goes here...</p>
</div>

<!-- Narrow container -->
<div class="container-sm">
  <h1>Centered Content</h1>
  <p>This content is centered and has a smaller max-width.</p>
</div>

<!-- Extra narrow container -->
<div class="container-xs">
  <h1>Article Content</h1>
  <p>Perfect for reading content.</p>
</div>
```

### Grid Layouts

```html
<!-- Responsive grid -->
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
  <div class="card">Card 1</div>
  <div class="card">Card 2</div>
  <div class="card">Card 3</div>
</div>

<!-- Feature grid -->
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
  <div class="feature-card">Feature 1</div>
  <div class="feature-card">Feature 2</div>
  <div class="feature-card">Feature 3</div>
  <div class="feature-card">Feature 4</div>
</div>
```

### Flexbox Layouts

```html
<!-- Header layout -->
<header class="flex items-center justify-between">
  <div class="nav-brand">Logo</div>
  <nav class="flex items-center gap-6">
    <a href="#" class="nav-link">Link 1</a>
    <a href="#" class="nav-link">Link 2</a>
  </nav>
</header>

<!-- Card with actions -->
<div class="card">
  <div class="card-body">
    <div class="flex items-center justify-between">
      <div>
        <h3 class="card-title">Pipeline Name</h3>
        <p class="card-text">Pipeline description</p>
      </div>
      <div class="flex gap-2">
        <button class="btn btn-sm btn-outline">Edit</button>
        <button class="btn btn-sm btn-primary">Run</button>
      </div>
    </div>
  </div>
</div>
```

## 🎯 Best Practices

### Do's ✅

1. **Use design tokens consistently**
   ```css
   /* Good */
   .my-component {
     color: var(--color-primary-600);
     padding: var(--space-4);
   }

   /* Avoid */
   .my-component {
     color: #2563eb;
     padding: 16px;
   }
   ```

2. **Follow component patterns**
   ```html
   <!-- Good: Use existing component classes -->
   <button class="btn btn-primary">Action</button>

   <!-- Avoid: Custom styling -->
   <button style="background: blue; padding: 10px;">Action</button>
   ```

3. **Maintain semantic HTML**
   ```html
   <!-- Good -->
   <nav class="nav-header">
     <ul class="nav-links">
       <li><a href="#" class="nav-link">Home</a></li>
     </ul>
   </nav>

   <!-- Avoid -->
   <div class="nav-header">
     <div class="nav-links">
       <div class="nav-link">Home</div>
     </div>
   </div>
   ```

4. **Use appropriate component variants**
   ```html
   <!-- Good: Use semantic button types -->
   <button class="btn btn-primary">Primary Action</button>
   <button class="btn btn-secondary">Secondary Action</button>
   <button class="btn btn-outline">Tertiary Action</button>
   ```

### Don'ts ❌

1. **Don't use hardcoded values**
   ```css
   /* Avoid */
   .component {
     color: #3b82f6;
     margin: 24px;
     border-radius: 8px;
   }
   ```

2. **Don't override component styles unnecessarily**
   ```css
   /* Avoid */
   .btn-primary {
     background-color: red !important;
   }
   ```

3. **Don't ignore accessibility**
   ```html
   <!-- Avoid -->
   <div onclick="handleClick()">Clickable div</div>

   <!-- Good -->
   <button class="btn btn-ghost" onclick="handleClick()">Clickable button</button>
   ```

## 🔧 Customization Guidelines

### Extending Components

When you need to extend existing components, follow these patterns:

```css
/* Create modifier classes */
.btn-gradient {
  background: linear-gradient(135deg, var(--color-primary-600), var(--color-secondary-600));
}

.card-dashboard {
  border-left: 4px solid var(--color-primary-600);
}

.form-input-search {
  padding-left: var(--space-10); /* Space for search icon */
}
```

### Creating New Components

When creating new components, follow the design system patterns:

```css
.data-metric {
  /* Use design tokens */
  background-color: var(--color-white);
  border: 1px solid var(--color-gray-200);
  border-radius: var(--radius-lg);
  padding: var(--space-6);

  /* Follow existing patterns */
  box-shadow: var(--shadow-sm);
  transition: all var(--duration-200) var(--ease-in-out);
}

.data-metric:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-1px);
}

.data-metric-value {
  font-size: var(--text-3xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-gray-900);
  margin: 0;
}

.data-metric-label {
  font-size: var(--text-sm);
  color: var(--color-gray-600);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  margin: var(--space-1) 0 0 0;
}
```

### Theme Customization

To customize the theme, override design tokens:

```css
:root {
  /* Override primary color */
  --color-primary-600: #7c3aed; /* Purple instead of blue */

  /* Override font family */
  --font-family-primary: 'Roboto', sans-serif;

  /* Override border radius for more rounded design */
  --radius-lg: 1rem;
  --radius-xl: 1.5rem;
  --radius-2xl: 2rem;
}
```

## 📱 Responsive Design

### Breakpoint Usage

```css
/* Mobile first approach */
.component {
  /* Mobile styles (default) */
  padding: var(--space-4);
  font-size: var(--text-base);
}

/* Tablet and up */
@media (min-width: 768px) {
  .component {
    padding: var(--space-6);
    font-size: var(--text-lg);
  }
}

/* Desktop and up */
@media (min-width: 1024px) {
  .component {
    padding: var(--space-8);
    font-size: var(--text-xl);
  }
}
```

## ♿ Accessibility Guidelines

### Focus States

All interactive elements should have clear focus states:

```css
.btn:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgb(59 130 246 / 0.1);
}

.form-input:focus {
  border-color: var(--color-primary-500);
  box-shadow: 0 0 0 3px rgb(59 130 246 / 0.1);
}
```

### Semantic HTML

Use proper semantic elements:

```html
<!-- Good -->
<main>
  <section>
    <h2>Dashboard</h2>
    <article class="card">
      <header class="card-header">
        <h3>Pipeline Status</h3>
      </header>
      <div class="card-body">
        <p>Content here...</p>
      </div>
    </article>
  </section>
</main>
```

## 🧪 Testing Checklist

### Visual Consistency
- [ ] All colors use design tokens
- [ ] Typography follows the scale
- [ ] Spacing is consistent
- [ ] Components follow established patterns
- [ ] Hover and focus states work
- [ ] Responsive behavior works

### Accessibility
- [ ] Keyboard accessible
- [ ] Focus states visible
- [ ] Color contrast meets WCAG
- [ ] Form labels associated
- [ ] Semantic HTML used

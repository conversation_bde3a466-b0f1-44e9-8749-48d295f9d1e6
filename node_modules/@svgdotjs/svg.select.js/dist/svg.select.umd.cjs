/*! @svgdotjs/svg.select.js v4.0.3 MIT*/;
!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(exports,require("@svgdotjs/svg.js")):"function"==typeof define&&define.amd?define(["exports","@svgdotjs/svg.js"],e):e(((t="undefined"!=typeof globalThis?globalThis:t||self).svg=t.svg||{},t.svg.select=t.svg.select||{},t.svg.select.js={}),t.SVG)}(this,(function(t,e){"use strict";function s(t,e,s,i=null){return function(n){n.preventDefault(),n.stopPropagation();var o=n.pageX||n.touches[0].pageX,a=n.pageY||n.touches[0].pageY;e.fire(t,{x:o,y:a,event:n,index:i,points:s})}}function i([t,e],{a:s,b:i,c:n,d:o,e:a,f:l}){return[t*s+e*n+a,t*i+e*o+l]}class n{constructor(t){this.el=t,t.remember("_selectHandler",this),this.selection=new e.G,this.order=["lt","t","rt","r","rb","b","lb","l","rot"],this.mutationHandler=this.mutationHandler.bind(this);const s=e.getWindow();this.observer=new s.MutationObserver(this.mutationHandler)}init(t){this.createHandle=t.createHandle||this.createHandleFn,this.createRot=t.createRot||this.createRotFn,this.updateHandle=t.updateHandle||this.updateHandleFn,this.updateRot=t.updateRot||this.updateRotFn,this.el.root().put(this.selection),this.updatePoints(),this.createSelection(),this.createResizeHandles(),this.updateResizeHandles(),this.createRotationHandle(),this.updateRotationHandle(),this.observer.observe(this.el.node,{attributes:!0})}active(t,e){if(!t)return this.selection.clear().remove(),void this.observer.disconnect();this.init(e)}createSelection(){this.selection.polygon(this.handlePoints).addClass("svg_select_shape")}updateSelection(){this.selection.get(0).plot(this.handlePoints)}createResizeHandles(){this.handlePoints.forEach(((t,e,i)=>{const n=this.order[e];this.createHandle.call(this,this.selection,t,e,i,n),this.selection.get(e+1).addClass("svg_select_handle svg_select_handle_"+n).on("mousedown.selection touchstart.selection",s(n,this.el,this.handlePoints,e))}))}createHandleFn(t){t.polyline()}updateHandleFn(t,e,s,i){const n=i.at(s-1),o=i[(s+1)%i.length],a=e,l=[a[0]-n[0],a[1]-n[1]],h=[a[0]-o[0],a[1]-o[1]],r=Math.sqrt(l[0]*l[0]+l[1]*l[1]),d=Math.sqrt(h[0]*h[0]+h[1]*h[1]),c=[l[0]/r,l[1]/r],u=[h[0]/d,h[1]/d],p=[a[0]-10*c[0],a[1]-10*c[1]],H=[a[0]-10*u[0],a[1]-10*u[1]];t.plot([p,a,H])}updateResizeHandles(){this.handlePoints.forEach(((t,e,s)=>{const i=this.order[e];this.updateHandle.call(this,this.selection.get(e+1),t,e,s,i)}))}createRotFn(t){t.line(),t.circle(5)}getPoint(t){return this.handlePoints[this.order.indexOf(t)]}getPointHandle(t){return this.selection.get(this.order.indexOf(t)+1)}updateRotFn(t,e){const s=this.getPoint("t");t.get(0).plot(s[0],s[1],e[0],e[1]),t.get(1).center(e[0],e[1])}createRotationHandle(){const t=this.selection.group().addClass("svg_select_handle_rot").on("mousedown.selection touchstart.selection",s("rot",this.el,this.handlePoints));this.createRot.call(this,t)}updateRotationHandle(){const t=this.selection.findOne("g.svg_select_handle_rot");this.updateRot(t,this.rotationPoint,this.handlePoints)}updatePoints(){const t=this.el.bbox(),e=this.el.root().screenCTM().inverseO().multiplyO(this.el.screenCTM());this.handlePoints=this.getHandlePoints(t).map((t=>i(t,e))),this.rotationPoint=i(this.getRotationPoint(t),e)}getHandlePoints({x:t,x2:e,y:s,y2:i,cx:n,cy:o}=this.el.bbox()){return[[t,s],[n,s],[e,s],[e,o],[e,i],[n,i],[t,i],[t,o]]}getRotationPoint({y:t,cx:e}=this.el.bbox()){return[e,t-20]}mutationHandler(){this.updatePoints(),this.updateSelection(),this.updateResizeHandles(),this.updateRotationHandle()}}class o{constructor(t){this.el=t,t.remember("_pointSelectHandler",this),this.selection=new e.G,this.order=["lt","t","rt","r","rb","b","lb","l","rot"],this.mutationHandler=this.mutationHandler.bind(this);const s=e.getWindow();this.observer=new s.MutationObserver(this.mutationHandler)}init(t){this.createHandle=t.createHandle||this.createHandleFn,this.updateHandle=t.updateHandle||this.updateHandleFn,this.el.root().put(this.selection),this.updatePoints(),this.createSelection(),this.createPointHandles(),this.updatePointHandles(),this.observer.observe(this.el.node,{attributes:!0})}active(t,e){if(!t)return this.selection.clear().remove(),void this.observer.disconnect();this.init(e)}createSelection(){this.selection.polygon(this.points).addClass("svg_select_shape_pointSelect")}updateSelection(){this.selection.get(0).plot(this.points)}createPointHandles(){this.points.forEach(((t,e,i)=>{this.createHandle.call(this,this.selection,t,e,i),this.selection.get(e+1).addClass("svg_select_handle_point").on("mousedown.selection touchstart.selection",s("point",this.el,this.points,e))}))}createHandleFn(t){t.circle(5)}updateHandleFn(t,e){t.center(e[0],e[1])}updatePointHandles(){this.points.forEach(((t,e,s)=>{this.updateHandle.call(this,this.selection.get(e+1),t,e,s)}))}updatePoints(){const t=this.el.root().screenCTM().inverseO().multiplyO(this.el.screenCTM());this.points=this.el.array().map((e=>i(e,t)))}mutationHandler(){this.updatePoints(),this.updateSelection(),this.updatePointHandles()}}const a=t=>function(e=!0,s={}){"object"==typeof e&&(s=e,e=!0);let i=this.remember("_"+t.name);return i||(e.prototype instanceof n?(i=new e(this),e=!0):i=new t(this),this.remember("_"+t.name,i)),i.active(e,s),this};e.extend(e.Element,{select:a(n)}),e.extend([e.Polygon,e.Polyline,e.Line],{pointSelect:a(o)}),t.PointSelectHandler=o,t.SelectHandler=n,Object.defineProperty(t,Symbol.toStringTag,{value:"Module"})}));
//# sourceMappingURL=svg.select.umd.cjs.map

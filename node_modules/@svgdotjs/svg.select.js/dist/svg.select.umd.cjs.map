{"version": 3, "file": "svg.select.umd.cjs", "sources": ["../src/utils.js", "../src/SelectHandler.js", "../src/PointSelectHandler.js", "../src/svg.select.js"], "sourcesContent": ["/**\n *\n * @param {string} eventName\n * @param {import('@svgdotjs/svg.js').Element} el\n * @param {number | null} index\n */\nexport function getMoseDownFunc(eventName, el, points, index = null) {\n  return function (ev) {\n    ev.preventDefault()\n    ev.stopPropagation()\n\n    var x = ev.pageX || ev.touches[0].pageX\n    var y = ev.pageY || ev.touches[0].pageY\n    el.fire(eventName, { x: x, y: y, event: ev, index, points })\n  }\n}\n\nexport function transformPoint([x, y], { a, b, c, d, e, f }) {\n  return [x * a + y * c + e, x * b + y * d + f]\n}\n", "import { G, getWindow } from '@svgdotjs/svg.js'\nimport { getMoseDownFunc, transformPoint } from './utils'\n\nexport class SelectHandler {\n  constructor(el) {\n    this.el = el\n    el.remember('_selectHandler', this)\n    this.selection = new G()\n    this.order = ['lt', 't', 'rt', 'r', 'rb', 'b', 'lb', 'l', 'rot']\n    this.mutationHandler = this.mutationHandler.bind(this)\n\n    const win = getWindow()\n    this.observer = new win.MutationObserver(this.mutationHandler)\n  }\n\n  init(options) {\n    this.createHandle = options.createHandle || this.createHandleFn\n    this.createRot = options.createRot || this.createRotFn\n\n    this.updateHandle = options.updateHandle || this.updateHandleFn\n    this.updateRot = options.updateRot || this.updateRotFn\n\n    // mount group\n    this.el.root().put(this.selection)\n\n    this.updatePoints()\n    this.createSelection()\n    this.createResizeHandles()\n    this.updateResizeHandles()\n    this.createRotationHandle()\n    this.updateRotationHandle()\n    this.observer.observe(this.el.node, { attributes: true })\n  }\n\n  active(val, options) {\n    // Disable selection\n    if (!val) {\n      this.selection.clear().remove()\n      this.observer.disconnect()\n      return\n    }\n\n    // Enable selection\n    this.init(options)\n  }\n\n  createSelection() {\n    this.selection.polygon(this.handlePoints).addClass('svg_select_shape')\n  }\n\n  updateSelection() {\n    this.selection.get(0).plot(this.handlePoints)\n  }\n\n  createResizeHandles() {\n    this.handlePoints.forEach((p, index, arr) => {\n      const name = this.order[index]\n      this.createHandle.call(this, this.selection, p, index, arr, name)\n\n      this.selection\n        .get(index + 1)\n        .addClass('svg_select_handle svg_select_handle_' + name)\n        .on('mousedown.selection touchstart.selection', getMoseDownFunc(name, this.el, this.handlePoints, index))\n    })\n  }\n\n  createHandleFn(group) {\n    group.polyline()\n  }\n\n  updateHandleFn(shape, point, index, arr) {\n    const before = arr.at(index - 1)\n    const next = arr[(index + 1) % arr.length]\n    const p = point\n\n    const diff1 = [p[0] - before[0], p[1] - before[1]]\n    const diff2 = [p[0] - next[0], p[1] - next[1]]\n\n    const len1 = Math.sqrt(diff1[0] * diff1[0] + diff1[1] * diff1[1])\n    const len2 = Math.sqrt(diff2[0] * diff2[0] + diff2[1] * diff2[1])\n\n    const normalized1 = [diff1[0] / len1, diff1[1] / len1]\n    const normalized2 = [diff2[0] / len2, diff2[1] / len2]\n\n    const beforeNew = [p[0] - normalized1[0] * 10, p[1] - normalized1[1] * 10]\n    const nextNew = [p[0] - normalized2[0] * 10, p[1] - normalized2[1] * 10]\n\n    shape.plot([beforeNew, p, nextNew])\n  }\n\n  updateResizeHandles() {\n    this.handlePoints.forEach((p, index, arr) => {\n      const name = this.order[index]\n      this.updateHandle.call(this, this.selection.get(index + 1), p, index, arr, name)\n    })\n  }\n\n  createRotFn(group) {\n    group.line()\n    group.circle(5)\n  }\n\n  getPoint(name) {\n    return this.handlePoints[this.order.indexOf(name)]\n  }\n\n  getPointHandle(name) {\n    return this.selection.get(this.order.indexOf(name) + 1)\n  }\n\n  updateRotFn(group, rotPoint) {\n    const topPoint = this.getPoint('t')\n    group.get(0).plot(topPoint[0], topPoint[1], rotPoint[0], rotPoint[1])\n    group.get(1).center(rotPoint[0], rotPoint[1])\n  }\n\n  createRotationHandle() {\n    const handle = this.selection\n      .group()\n      .addClass('svg_select_handle_rot')\n      .on('mousedown.selection touchstart.selection', getMoseDownFunc('rot', this.el, this.handlePoints))\n\n    this.createRot.call(this, handle)\n  }\n\n  updateRotationHandle() {\n    const group = this.selection.findOne('g.svg_select_handle_rot')\n    this.updateRot(group, this.rotationPoint, this.handlePoints)\n  }\n\n  // gets new bounding box points and transform them into the elements space\n  updatePoints() {\n    const bbox = this.el.bbox()\n    const fromShapeToUiMatrix = this.el.root().screenCTM().inverseO().multiplyO(this.el.screenCTM())\n\n    this.handlePoints = this.getHandlePoints(bbox).map((p) => transformPoint(p, fromShapeToUiMatrix))\n    this.rotationPoint = transformPoint(this.getRotationPoint(bbox), fromShapeToUiMatrix)\n  }\n\n  // A collection of all the points we need to draw our ui\n  getHandlePoints({ x, x2, y, y2, cx, cy } = this.el.bbox()) {\n    return [\n      [x, y],\n      [cx, y],\n      [x2, y],\n      [x2, cy],\n      [x2, y2],\n      [cx, y2],\n      [x, y2],\n      [x, cy],\n    ]\n  }\n\n  // A collection of all the points we need to draw our ui\n  getRotationPoint({ y, cx } = this.el.bbox()) {\n    return [cx, y - 20]\n  }\n\n  mutationHandler() {\n    this.updatePoints()\n\n    this.updateSelection()\n    this.updateResizeHandles()\n    this.updateRotationHandle()\n  }\n}\n", "import { G, getWindow } from '@svgdotjs/svg.js'\nimport { getMoseDownFunc, transformPoint } from './utils'\n\nexport class PointSelectHandler {\n  constructor(el) {\n    this.el = el\n    el.remember('_pointSelectHandler', this)\n    this.selection = new G()\n    this.order = ['lt', 't', 'rt', 'r', 'rb', 'b', 'lb', 'l', 'rot']\n    this.mutationHandler = this.mutationHandler.bind(this)\n\n    const win = getWindow()\n    this.observer = new win.MutationObserver(this.mutationHandler)\n  }\n\n  init(options) {\n    this.createHandle = options.createHandle || this.createHandleFn\n    this.updateHandle = options.updateHandle || this.updateHandleFn\n\n    // mount group\n    this.el.root().put(this.selection)\n\n    this.updatePoints()\n    this.createSelection()\n    this.createPointHandles()\n    this.updatePointHandles()\n    this.observer.observe(this.el.node, { attributes: true })\n  }\n\n  active(val, options) {\n    // Disable selection\n    if (!val) {\n      this.selection.clear().remove()\n      this.observer.disconnect()\n      return\n    }\n\n    // Enable selection\n    this.init(options)\n  }\n\n  createSelection() {\n    this.selection.polygon(this.points).addClass('svg_select_shape_pointSelect')\n  }\n\n  updateSelection() {\n    this.selection.get(0).plot(this.points)\n  }\n\n  createPointHandles() {\n    this.points.forEach((p, index, arr) => {\n      this.createHandle.call(this, this.selection, p, index, arr)\n\n      this.selection\n        .get(index + 1)\n        .addClass('svg_select_handle_point')\n        .on('mousedown.selection touchstart.selection', getMoseDownFunc('point', this.el, this.points, index))\n    })\n  }\n\n  createHandleFn(group) {\n    group.circle(5)\n  }\n\n  updateHandleFn(shape, point) {\n    shape.center(point[0], point[1])\n  }\n\n  updatePointHandles() {\n    this.points.forEach((p, index, arr) => {\n      this.updateHandle.call(this, this.selection.get(index + 1), p, index, arr)\n    })\n  }\n\n  // gets new bounding box points and transform them into the elements space\n  updatePoints() {\n    const fromShapeToUiMatrix = this.el.root().screenCTM().inverseO().multiplyO(this.el.screenCTM())\n    this.points = this.el.array().map((p) => transformPoint(p, fromShapeToUiMatrix))\n  }\n\n  mutationHandler() {\n    this.updatePoints()\n\n    this.updateSelection()\n    this.updatePointHandles()\n  }\n}\n", "import { Element, Line, Polygon, <PERSON>yline, extend } from '@svgdotjs/svg.js'\nimport { SelectHandler } from './SelectHandler'\nimport { PointSelectHandler } from './PointSelectHandler'\n\nconst getSelectFn = (handleClass) => {\n  return function (enabled = true, options = {}) {\n    if (typeof enabled === 'object') {\n      options = enabled\n      enabled = true\n    }\n\n    let selectHandler = this.remember('_' + handleClass.name)\n\n    if (!selectHandler) {\n      if (enabled.prototype instanceof SelectHandler) {\n        selectHandler = new enabled(this)\n        enabled = true\n      } else {\n        selectHandler = new handleClass(this)\n      }\n\n      this.remember('_' + handleClass.name, selectHandler)\n    }\n\n    selectHandler.active(enabled, options)\n\n    return this\n  }\n}\n\nextend(Element, {\n  select: getSelectFn(SelectHandler),\n})\n\nextend([Polygon, Polyline, Line], {\n  pointSelect: getSelectFn(PointSelectHandler),\n})\n\nexport { SelectHand<PERSON>, PointSelectHandler }\n"], "names": ["getMoseDownFunc", "eventName", "el", "points", "index", "ev", "preventDefault", "stopPropagation", "x", "pageX", "touches", "y", "pageY", "fire", "event", "transformPoint", "a", "b", "c", "d", "e", "f", "SelectHandler", "constructor", "this", "remember", "selection", "G", "order", "<PERSON><PERSON><PERSON><PERSON>", "bind", "win", "getWindow", "observer", "MutationObserver", "init", "options", "createHandle", "createHandleFn", "createRot", "createRotFn", "updateHandle", "updateHandleFn", "updateRot", "updateRotFn", "root", "put", "updatePoints", "createSelection", "createResizeHandles", "updateResizeHandles", "createRotationHandle", "updateRotationHandle", "observe", "node", "attributes", "active", "val", "clear", "remove", "disconnect", "polygon", "handlePoints", "addClass", "updateSelection", "get", "plot", "for<PERSON>ach", "p", "arr", "name", "call", "on", "group", "polyline", "shape", "point", "before", "at", "next", "length", "diff1", "diff2", "len1", "Math", "sqrt", "len2", "normalized1", "normalized2", "beforeNew", "nextNew", "line", "circle", "getPoint", "indexOf", "getPointHandle", "rotPoint", "topPoint", "center", "handle", "findOne", "rotationPoint", "bbox", "fromShapeToUiMatrix", "screenCTM", "inverseO", "multiplyO", "getHandlePoints", "map", "getRotationPoint", "x2", "y2", "cx", "cy", "PointSelectHandler", "createPointHandles", "updatePointHandles", "array", "getSelectFn", "handleClass", "enabled", "<PERSON><PERSON><PERSON><PERSON>", "prototype", "svg_js", "extend", "Element", "select", "Polygon", "Polyline", "Line", "pointSelect"], "mappings": ";4VAMO,SAASA,EAAgBC,EAAWC,EAAIC,EAAQC,EAAQ,MAC7D,OAAO,SAAUC,GACfA,EAAGC,iBACHD,EAAGE,kBAEH,IAAIC,EAAIH,EAAGI,OAASJ,EAAGK,QAAQ,GAAGD,MAC9BE,EAAIN,EAAGO,OAASP,EAAGK,QAAQ,GAAGE,MAC/BV,EAAAW,KAAKZ,EAAW,CAAEO,IAAMG,IAAMG,MAAOT,EAAID,QAAOD,UACpD,CACH,CAEO,SAASY,GAAgBP,EAAGG,IAAIK,EAAEA,EAAGC,EAAAA,EAAAC,EAAGA,EAAGC,EAAAA,EAAAC,EAAGA,EAAGC,EAAAA,IAC/C,MAAA,CAACb,EAAIQ,EAAIL,EAAIO,EAAIE,EAAGZ,EAAIS,EAAIN,EAAIQ,EAAIE,EAC7C,CChBO,MAAMC,EACX,WAAAC,CAAYrB,GACVsB,KAAKtB,GAAKA,EACPA,EAAAuB,SAAS,iBAAkBD,MACzBA,KAAAE,UAAY,IAAIC,IAChBH,KAAAI,MAAQ,CAAC,KAAM,IAAK,KAAM,IAAK,KAAM,IAAK,KAAM,IAAK,OAC1DJ,KAAKK,gBAAkBL,KAAKK,gBAAgBC,KAAKN,MAE3C,MAAAO,EAAMC,EAAAA,YACZR,KAAKS,SAAW,IAAIF,EAAIG,iBAAiBV,KAAKK,gBAC/C,CAED,IAAAM,CAAKC,GACEZ,KAAAa,aAAeD,EAAQC,cAAgBb,KAAKc,eAC5Cd,KAAAe,UAAYH,EAAQG,WAAaf,KAAKgB,YAEtChB,KAAAiB,aAAeL,EAAQK,cAAgBjB,KAAKkB,eAC5ClB,KAAAmB,UAAYP,EAAQO,WAAanB,KAAKoB,YAG3CpB,KAAKtB,GAAG2C,OAAOC,IAAItB,KAAKE,WAExBF,KAAKuB,eACLvB,KAAKwB,kBACLxB,KAAKyB,sBACLzB,KAAK0B,sBACL1B,KAAK2B,uBACL3B,KAAK4B,uBACA5B,KAAAS,SAASoB,QAAQ7B,KAAKtB,GAAGoD,KAAM,CAAEC,YAAY,GACnD,CAED,MAAAC,CAAOC,EAAKrB,GAEV,IAAKqB,EAGH,OAFKjC,KAAAE,UAAUgC,QAAQC,cACvBnC,KAAKS,SAAS2B,aAKhBpC,KAAKW,KAAKC,EACX,CAED,eAAAY,GACExB,KAAKE,UAAUmC,QAAQrC,KAAKsC,cAAcC,SAAS,mBACpD,CAED,eAAAC,GACExC,KAAKE,UAAUuC,IAAI,GAAGC,KAAK1C,KAAKsC,aACjC,CAED,mBAAAb,GACEzB,KAAKsC,aAAaK,SAAQ,CAACC,EAAGhE,EAAOiE,KAC7B,MAAAC,EAAO9C,KAAKI,MAAMxB,GACnBoB,KAAAa,aAAakC,KAAK/C,KAAMA,KAAKE,UAAW0C,EAAGhE,EAAOiE,EAAKC,GAE5D9C,KAAKE,UACFuC,IAAI7D,EAAQ,GACZ2D,SAAS,uCAAyCO,GAClDE,GAAG,2CAA4CxE,EAAgBsE,EAAM9C,KAAKtB,GAAIsB,KAAKsC,aAAc1D,GAAM,GAE7G,CAED,cAAAkC,CAAemC,GACbA,EAAMC,UACP,CAED,cAAAhC,CAAeiC,EAAOC,EAAOxE,EAAOiE,GAClC,MAAMQ,EAASR,EAAIS,GAAG1E,EAAQ,GACxB2E,EAAOV,GAAKjE,EAAQ,GAAKiE,EAAIW,QAC7BZ,EAAIQ,EAEJK,EAAQ,CAACb,EAAE,GAAKS,EAAO,GAAIT,EAAE,GAAKS,EAAO,IACzCK,EAAQ,CAACd,EAAE,GAAKW,EAAK,GAAIX,EAAE,GAAKW,EAAK,IAErCI,EAAOC,KAAKC,KAAKJ,EAAM,GAAKA,EAAM,GAAKA,EAAM,GAAKA,EAAM,IACxDK,EAAOF,KAAKC,KAAKH,EAAM,GAAKA,EAAM,GAAKA,EAAM,GAAKA,EAAM,IAExDK,EAAc,CAACN,EAAM,GAAKE,EAAMF,EAAM,GAAKE,GAC3CK,EAAc,CAACN,EAAM,GAAKI,EAAMJ,EAAM,GAAKI,GAE3CG,EAAY,CAACrB,EAAE,GAAsB,GAAjBmB,EAAY,GAASnB,EAAE,GAAsB,GAAjBmB,EAAY,IAC5DG,EAAU,CAACtB,EAAE,GAAsB,GAAjBoB,EAAY,GAASpB,EAAE,GAAsB,GAAjBoB,EAAY,IAEhEb,EAAMT,KAAK,CAACuB,EAAWrB,EAAGsB,GAC3B,CAED,mBAAAxC,GACE1B,KAAKsC,aAAaK,SAAQ,CAACC,EAAGhE,EAAOiE,KAC7B,MAAAC,EAAO9C,KAAKI,MAAMxB,GACxBoB,KAAKiB,aAAa8B,KAAK/C,KAAMA,KAAKE,UAAUuC,IAAI7D,EAAQ,GAAIgE,EAAGhE,EAAOiE,EAAKC,EAAI,GAElF,CAED,WAAA9B,CAAYiC,GACVA,EAAMkB,OACNlB,EAAMmB,OAAO,EACd,CAED,QAAAC,CAASvB,GACP,OAAO9C,KAAKsC,aAAatC,KAAKI,MAAMkE,QAAQxB,GAC7C,CAED,cAAAyB,CAAezB,GACN,OAAA9C,KAAKE,UAAUuC,IAAIzC,KAAKI,MAAMkE,QAAQxB,GAAQ,EACtD,CAED,WAAA1B,CAAY6B,EAAOuB,GACX,MAAAC,EAAWzE,KAAKqE,SAAS,KAC/BpB,EAAMR,IAAI,GAAGC,KAAK+B,EAAS,GAAIA,EAAS,GAAID,EAAS,GAAIA,EAAS,IAC5DvB,EAAAR,IAAI,GAAGiC,OAAOF,EAAS,GAAIA,EAAS,GAC3C,CAED,oBAAA7C,GACE,MAAMgD,EAAS3E,KAAKE,UACjB+C,QACAV,SAAS,yBACTS,GAAG,2CAA4CxE,EAAgB,MAAOwB,KAAKtB,GAAIsB,KAAKsC,eAElFtC,KAAAe,UAAUgC,KAAK/C,KAAM2E,EAC3B,CAED,oBAAA/C,GACE,MAAMqB,EAAQjD,KAAKE,UAAU0E,QAAQ,2BACrC5E,KAAKmB,UAAU8B,EAAOjD,KAAK6E,cAAe7E,KAAKsC,aAChD,CAGD,YAAAf,GACQ,MAAAuD,EAAO9E,KAAKtB,GAAGoG,OACfC,EAAsB/E,KAAKtB,GAAG2C,OAAO2D,YAAYC,WAAWC,UAAUlF,KAAKtB,GAAGsG,aAE/EhF,KAAAsC,aAAetC,KAAKmF,gBAAgBL,GAAMM,KAAKxC,GAAMrD,EAAeqD,EAAGmC,KAC5E/E,KAAK6E,cAAgBtF,EAAeS,KAAKqF,iBAAiBP,GAAOC,EAClE,CAGD,eAAAI,EAAgBnG,EAAEA,EAAGsG,GAAAA,EAAAnG,EAAIA,EAAGoG,GAAAA,EAAAC,GAAIA,EAAIC,GAAAA,GAAOzF,KAAKtB,GAAGoG,QAC1C,MAAA,CACL,CAAC9F,EAAGG,GACJ,CAACqG,EAAIrG,GACL,CAACmG,EAAInG,GACL,CAACmG,EAAIG,GACL,CAACH,EAAIC,GACL,CAACC,EAAID,GACL,CAACvG,EAAGuG,GACJ,CAACvG,EAAGyG,GAEP,CAGD,gBAAAJ,EAAiBlG,EAAEA,EAAGqG,GAAAA,GAAOxF,KAAKtB,GAAGoG,QAC5B,MAAA,CAACU,EAAIrG,EAAI,GACjB,CAED,eAAAkB,GACEL,KAAKuB,eAELvB,KAAKwC,kBACLxC,KAAK0B,sBACL1B,KAAK4B,sBACN,ECjKI,MAAM8D,EACX,WAAA3F,CAAYrB,GACVsB,KAAKtB,GAAKA,EACPA,EAAAuB,SAAS,sBAAuBD,MAC9BA,KAAAE,UAAY,IAAIC,IAChBH,KAAAI,MAAQ,CAAC,KAAM,IAAK,KAAM,IAAK,KAAM,IAAK,KAAM,IAAK,OAC1DJ,KAAKK,gBAAkBL,KAAKK,gBAAgBC,KAAKN,MAE3C,MAAAO,EAAMC,EAAAA,YACZR,KAAKS,SAAW,IAAIF,EAAIG,iBAAiBV,KAAKK,gBAC/C,CAED,IAAAM,CAAKC,GACEZ,KAAAa,aAAeD,EAAQC,cAAgBb,KAAKc,eAC5Cd,KAAAiB,aAAeL,EAAQK,cAAgBjB,KAAKkB,eAGjDlB,KAAKtB,GAAG2C,OAAOC,IAAItB,KAAKE,WAExBF,KAAKuB,eACLvB,KAAKwB,kBACLxB,KAAK2F,qBACL3F,KAAK4F,qBACA5F,KAAAS,SAASoB,QAAQ7B,KAAKtB,GAAGoD,KAAM,CAAEC,YAAY,GACnD,CAED,MAAAC,CAAOC,EAAKrB,GAEV,IAAKqB,EAGH,OAFKjC,KAAAE,UAAUgC,QAAQC,cACvBnC,KAAKS,SAAS2B,aAKhBpC,KAAKW,KAAKC,EACX,CAED,eAAAY,GACExB,KAAKE,UAAUmC,QAAQrC,KAAKrB,QAAQ4D,SAAS,+BAC9C,CAED,eAAAC,GACExC,KAAKE,UAAUuC,IAAI,GAAGC,KAAK1C,KAAKrB,OACjC,CAED,kBAAAgH,GACE3F,KAAKrB,OAAOgE,SAAQ,CAACC,EAAGhE,EAAOiE,KAC7B7C,KAAKa,aAAakC,KAAK/C,KAAMA,KAAKE,UAAW0C,EAAGhE,EAAOiE,GAEvD7C,KAAKE,UACFuC,IAAI7D,EAAQ,GACZ2D,SAAS,2BACTS,GAAG,2CAA4CxE,EAAgB,QAASwB,KAAKtB,GAAIsB,KAAKrB,OAAQC,GAAM,GAE1G,CAED,cAAAkC,CAAemC,GACbA,EAAMmB,OAAO,EACd,CAED,cAAAlD,CAAeiC,EAAOC,GACpBD,EAAMuB,OAAOtB,EAAM,GAAIA,EAAM,GAC9B,CAED,kBAAAwC,GACE5F,KAAKrB,OAAOgE,SAAQ,CAACC,EAAGhE,EAAOiE,KACxB7C,KAAAiB,aAAa8B,KAAK/C,KAAMA,KAAKE,UAAUuC,IAAI7D,EAAQ,GAAIgE,EAAGhE,EAAOiE,EAAG,GAE5E,CAGD,YAAAtB,GACE,MAAMwD,EAAsB/E,KAAKtB,GAAG2C,OAAO2D,YAAYC,WAAWC,UAAUlF,KAAKtB,GAAGsG,aAC/EhF,KAAArB,OAASqB,KAAKtB,GAAGmH,QAAQT,KAAKxC,GAAMrD,EAAeqD,EAAGmC,IAC5D,CAED,eAAA1E,GACEL,KAAKuB,eAELvB,KAAKwC,kBACLxC,KAAK4F,oBACN,ECjFG,MAAAE,EAAeC,GACZ,SAAUC,GAAU,EAAMpF,EAAU,CAAA,GAClB,iBAAZoF,IACCpF,EAAAoF,EACAA,GAAA,GAGZ,IAAIC,EAAgBjG,KAAKC,SAAS,IAAM8F,EAAYjD,MAe7C,OAbFmD,IACCD,EAAQE,qBAAqBpG,GACfmG,EAAA,IAAID,EAAQhG,MAClBgG,GAAA,GAEMC,EAAA,IAAIF,EAAY/F,MAGlCA,KAAKC,SAAS,IAAM8F,EAAYjD,KAAMmD,IAG1BA,EAAAjE,OAAOgE,EAASpF,GAEvBZ,IACR,EAGGmG,EAAAC,OAACC,UAAS,CACdC,OAAQR,EAAYhG,KAGhBqG,EAAAC,OAAC,CAACG,EAAOA,QAAEC,WAAUC,EAAAA,MAAO,CAChCC,YAAaZ,EAAYJ"}
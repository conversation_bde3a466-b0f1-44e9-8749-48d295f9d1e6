/*! @svgdotjs/svg.resize.js v2.0.5 MIT*/;
!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports,require("@svgdotjs/svg.select.js"),require("@svgdotjs/svg.js")):"function"==typeof define&&define.amd?define(["exports","@svgdotjs/svg.select.js","@svgdotjs/svg.js"],t):t(((e="undefined"!=typeof globalThis?globalThis:e||self).svg=e.svg||{},e.svg.resize=e.svg.resize||{},e.svg.resize.js={}),null,e.SVG)}(this,(function(e,t,i){"use strict";const s=e=>(e.changedTouches&&(e=e.changedTouches[0]),{x:e.clientX,y:e.clientY}),n=e=>{let t=1/0,s=1/0,n=-1/0,h=-1/0;for(let i=0;i<e.length;i++){const o=e[i];t=Math.min(t,o[0]),s=Math.min(s,o[1]),n=Math.max(n,o[0]),h=Math.max(h,o[1])}return new i.Box(t,s,n-t,h-s)};class h{constructor(e){this.el=e,e.remember("_ResizeHandler",this),this.lastCoordinates=null,this.eventType="",this.lastEvent=null,this.handleResize=this.handleResize.bind(this),this.resize=this.resize.bind(this),this.endResize=this.endResize.bind(this),this.rotate=this.rotate.bind(this),this.movePoint=this.movePoint.bind(this)}active(e,t){this.preserveAspectRatio=t.preserveAspectRatio??!1,this.aroundCenter=t.aroundCenter??!1,this.grid=t.grid??0,this.degree=t.degree??0,this.el.off(".resize"),e&&(this.el.on(["lt.resize","rt.resize","rb.resize","lb.resize","t.resize","r.resize","b.resize","l.resize","rot.resize","point.resize"],this.handleResize),this.lastEvent&&("rot"===this.eventType?this.rotate(this.lastEvent):"point"===this.eventType?this.movePoint(this.lastEvent):this.resize(this.lastEvent)))}handleResize(e){this.eventType=e.type;const{event:t,index:n,points:h}=e.detail,o=!t.type.indexOf("mouse");if(o&&1!==(t.which||t.buttons))return;if(this.el.dispatch("beforeresize",{event:e,handler:this}).defaultPrevented)return;this.box=this.el.bbox(),this.startPoint=this.el.point(s(t)),this.index=n,this.points=h.slice();const r=(o?"mousemove":"touchmove")+".resize",a=(o?"mouseup":"touchcancel.resize touchend")+".resize";"point"===e.type?i.on(window,r,this.movePoint):"rot"===e.type?i.on(window,r,this.rotate):i.on(window,r,this.resize),i.on(window,a,this.endResize)}resize(e){this.lastEvent=e;const t=this.snapToGrid(this.el.point(s(e)));let h=t.x-this.startPoint.x,o=t.y-this.startPoint.y;this.preserveAspectRatio&&this.aroundCenter&&(h*=2,o*=2);const r=this.box.x+h,a=this.box.y+o,d=this.box.x2+h,l=this.box.y2+o;let x=new i.Box(this.box);if(this.eventType.includes("l")&&(x.x=Math.min(r,this.box.x2),x.x2=Math.max(r,this.box.x2)),this.eventType.includes("r")&&(x.x=Math.min(d,this.box.x),x.x2=Math.max(d,this.box.x)),this.eventType.includes("t")&&(x.y=Math.min(a,this.box.y2),x.y2=Math.max(a,this.box.y2)),this.eventType.includes("b")&&(x.y=Math.min(l,this.box.y),x.y2=Math.max(l,this.box.y)),x.width=x.x2-x.x,x.height=x.y2-x.y,this.preserveAspectRatio){const e=x.width/this.box.width,t=x.height/this.box.height,i=["lt","t","rt","r","rb","b","lb","l"],s=(i.indexOf(this.eventType)+4)%i.length,h=this.aroundCenter?[this.box.cx,this.box.cy]:this.points[s];let o=this.eventType.includes("t")||this.eventType.includes("b")?t:e;o=2===this.eventType.length?Math.max(e,t):o,x=function(e,t,i){const s=[[e.x,e.y],[e.x+e.width,e.y],[e.x+e.width,e.y+e.height],[e.x,e.y+e.height]].map((([e,s])=>{const n=e-t[0],h=(s-t[1])*i;return[n*i+t[0],h+t[1]]}));return n(s)}(this.box,h,o)}this.el.dispatch("resize",{box:new i.Box(x),angle:0,eventType:this.eventType,event:e,handler:this}).defaultPrevented||this.el.size(x.width,x.height).move(x.x,x.y)}movePoint(e){this.lastEvent=e;const{x:t,y:i}=this.snapToGrid(this.el.point(s(e))),h=this.el.array().slice();h[this.index]=[t,i],this.el.dispatch("resize",{box:n(h),angle:0,eventType:this.eventType,event:e,handler:this}).defaultPrevented||this.el.plot(h)}rotate(e){this.lastEvent=e;const t=this.startPoint,n=this.el.point(s(e)),{cx:h,cy:o}=this.box,r=t.x-h,a=t.y-o,d=n.x-h,l=n.y-o,x=Math.sqrt(r*r+a*a)*Math.sqrt(d*d+l*l);if(0===x)return;let p=Math.acos((r*d+a*l)/x)/Math.PI*180;if(!p)return;n.x<t.x&&(p=-p);const v=new i.Matrix(this.el),{x:c,y:y}=new i.Point(h,o).transformO(v),{rotate:u}=v.decompose(),b=this.snapToAngle(u+p)-u;this.el.dispatch("resize",{box:this.box,angle:b,eventType:this.eventType,event:e,handler:this}).defaultPrevented||this.el.transform(v.rotateO(b,c,y))}endResize(e){"rot"!==this.eventType&&"point"!==this.eventType&&this.resize(e),this.lastEvent=null,this.eventType="",i.off(window,"mousemove.resize touchmove.resize"),i.off(window,"mouseup.resize touchend.resize")}snapToGrid(e){return this.grid&&(e.x=Math.round(e.x/this.grid)*this.grid,e.y=Math.round(e.y/this.grid)*this.grid),e}snapToAngle(e){return this.degree&&(e=Math.round(e/this.degree)*this.degree),e}}i.extend(i.Element,{resize:function(e=!0,t={}){"object"==typeof e&&(t=e,e=!0);let i=this.remember("_ResizeHandler");return i||(e.prototype instanceof h?(i=new e(this),e=!0):i=new h(this),this.remember("_resizeHandler",i)),i.active(e,t),this}}),e.ResizeHandler=h,Object.defineProperty(e,Symbol.toStringTag,{value:"Module"})}));
//# sourceMappingURL=svg.resize.umd.cjs.map

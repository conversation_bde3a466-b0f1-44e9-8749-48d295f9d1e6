{"version": 3, "file": "svg.resize.js", "sources": ["../src/ResizeHandler.js", "../src/svg.resize.js"], "sourcesContent": ["import { Point } from '@svgdotjs/svg.js'\nimport { Matrix } from '@svgdotjs/svg.js'\nimport { on, off, Box } from '@svgdotjs/svg.js'\n\nconst getCoordsFromEvent = (ev) => {\n  if (ev.changedTouches) {\n    ev = ev.changedTouches[0]\n  }\n  return { x: ev.clientX, y: ev.clientY }\n}\n\nconst maxBoxFromPoints = (points) => {\n  let x = Infinity\n  let y = Infinity\n  let x2 = -Infinity\n  let y2 = -Infinity\n\n  for (let i = 0; i < points.length; i++) {\n    const p = points[i]\n    x = Math.min(x, p[0])\n    y = Math.min(y, p[1])\n    x2 = Math.max(x2, p[0])\n    y2 = Math.max(y2, p[1])\n  }\n\n  return new Box(x, y, x2 - x, y2 - y)\n}\n\nfunction scaleBox(box, origin, scale) {\n  const points = [\n    [box.x, box.y],\n    [box.x + box.width, box.y],\n    [box.x + box.width, box.y + box.height],\n    [box.x, box.y + box.height],\n  ]\n\n  const newPoints = points.map(([x, y]) => {\n    // Translate to origin\n    const translatedX = x - origin[0]\n    const translatedY = y - origin[1]\n\n    // Scale\n    const scaledX = translatedX * scale\n    const scaledY = translatedY * scale\n\n    // Translate back\n    return [scaledX + origin[0], scaledY + origin[1]]\n  })\n\n  return maxBoxFromPoints(newPoints)\n}\n\nexport class ResizeHandler {\n  constructor(el) {\n    this.el = el\n    el.remember('_ResizeHandler', this)\n    this.lastCoordinates = null\n    this.eventType = ''\n    this.lastEvent = null\n    this.handleResize = this.handleResize.bind(this)\n    this.resize = this.resize.bind(this)\n    this.endResize = this.endResize.bind(this)\n    this.rotate = this.rotate.bind(this)\n    this.movePoint = this.movePoint.bind(this)\n  }\n\n  active(value, options) {\n    this.preserveAspectRatio = options.preserveAspectRatio ?? false\n    this.aroundCenter = options.aroundCenter ?? false\n    this.grid = options.grid ?? 0\n    this.degree = options.degree ?? 0\n\n    // remove all resize events\n    this.el.off('.resize')\n\n    if (!value) return\n\n    this.el.on(\n      [\n        'lt.resize',\n        'rt.resize',\n        'rb.resize',\n        'lb.resize',\n        't.resize',\n        'r.resize',\n        'b.resize',\n        'l.resize',\n        'rot.resize',\n        'point.resize',\n      ],\n      this.handleResize\n    )\n\n    // in case the options were changed mid-resize,\n    // we have to replay the last event to see the immediate effect of the option change\n    if (this.lastEvent) {\n      if (this.eventType === 'rot') {\n        this.rotate(this.lastEvent)\n      } else if (this.eventType === 'point') {\n        this.movePoint(this.lastEvent)\n      } else {\n        this.resize(this.lastEvent)\n      }\n    }\n  }\n\n  // This is called when a user clicks on one of the resize points\n  handleResize(e) {\n    this.eventType = e.type\n    const { event, index, points } = e.detail\n    const isMouse = !event.type.indexOf('mouse')\n\n    // Check for left button\n    if (isMouse && (event.which || event.buttons) !== 1) {\n      return\n    }\n\n    // Fire beforedrag event\n    if (this.el.dispatch('beforeresize', { event: e, handler: this }).defaultPrevented) {\n      return\n    }\n\n    this.box = this.el.bbox()\n    this.startPoint = this.el.point(getCoordsFromEvent(event))\n    this.index = index\n    this.points = points.slice()\n\n    // We consider the resize done, when a touch is canceled, too\n    const eventMove = (isMouse ? 'mousemove' : 'touchmove') + '.resize'\n    const eventEnd = (isMouse ? 'mouseup' : 'touchcancel.resize touchend') + '.resize'\n\n    if (e.type === 'point') {\n      on(window, eventMove, this.movePoint)\n    } else if (e.type === 'rot') {\n      on(window, eventMove, this.rotate)\n    } else {\n      on(window, eventMove, this.resize)\n    }\n    on(window, eventEnd, this.endResize)\n  }\n\n  resize(e) {\n    this.lastEvent = e\n\n    const endPoint = this.snapToGrid(this.el.point(getCoordsFromEvent(e)))\n\n    let dx = endPoint.x - this.startPoint.x\n    let dy = endPoint.y - this.startPoint.y\n\n    if (this.preserveAspectRatio && this.aroundCenter) {\n      dx *= 2\n      dy *= 2\n    }\n\n    const x = this.box.x + dx\n    const y = this.box.y + dy\n    const x2 = this.box.x2 + dx\n    const y2 = this.box.y2 + dy\n\n    let box = new Box(this.box)\n\n    if (this.eventType.includes('l')) {\n      box.x = Math.min(x, this.box.x2)\n      box.x2 = Math.max(x, this.box.x2)\n    }\n\n    if (this.eventType.includes('r')) {\n      box.x = Math.min(x2, this.box.x)\n      box.x2 = Math.max(x2, this.box.x)\n    }\n\n    if (this.eventType.includes('t')) {\n      box.y = Math.min(y, this.box.y2)\n      box.y2 = Math.max(y, this.box.y2)\n    }\n\n    if (this.eventType.includes('b')) {\n      box.y = Math.min(y2, this.box.y)\n      box.y2 = Math.max(y2, this.box.y)\n    }\n\n    box.width = box.x2 - box.x\n    box.height = box.y2 - box.y\n\n    // after figuring out the resulting box,\n    // we have to check if the aspect ratio should be preserved\n    // if so, we have to find the correct scaling factor and scale the box around a fixed point (usually the opposite of the handle)\n    // in case aroundCenter is active, the fixed point is the center of the box\n    if (this.preserveAspectRatio) {\n      const scaleX = box.width / this.box.width\n      const scaleY = box.height / this.box.height\n\n      const order = ['lt', 't', 'rt', 'r', 'rb', 'b', 'lb', 'l']\n\n      const origin = (order.indexOf(this.eventType) + 4) % order.length\n      const constantPoint = this.aroundCenter ? [this.box.cx, this.box.cy] : this.points[origin]\n\n      let scale = this.eventType.includes('t') || this.eventType.includes('b') ? scaleY : scaleX\n      scale = this.eventType.length === 2 ? Math.max(scaleX, scaleY) : scale\n\n      box = scaleBox(this.box, constantPoint, scale)\n    }\n\n    if (\n      this.el.dispatch('resize', {\n        box: new Box(box),\n        angle: 0,\n        eventType: this.eventType,\n        event: e,\n        handler: this,\n      }).defaultPrevented\n    ) {\n      return\n    }\n\n    this.el.size(box.width, box.height).move(box.x, box.y)\n  }\n\n  movePoint(e) {\n    this.lastEvent = e\n    const { x, y } = this.snapToGrid(this.el.point(getCoordsFromEvent(e)))\n    const pointArr = this.el.array().slice()\n    pointArr[this.index] = [x, y]\n\n    if (\n      this.el.dispatch('resize', {\n        box: maxBoxFromPoints(pointArr),\n        angle: 0,\n        eventType: this.eventType,\n        event: e,\n        handler: this,\n      }).defaultPrevented\n    ) {\n      return\n    }\n\n    this.el.plot(pointArr)\n  }\n\n  rotate(e) {\n    this.lastEvent = e\n\n    const startPoint = this.startPoint\n    const endPoint = this.el.point(getCoordsFromEvent(e))\n\n    const { cx, cy } = this.box\n\n    const dx1 = startPoint.x - cx\n    const dy1 = startPoint.y - cy\n\n    const dx2 = endPoint.x - cx\n    const dy2 = endPoint.y - cy\n\n    const c = Math.sqrt(dx1 * dx1 + dy1 * dy1) * Math.sqrt(dx2 * dx2 + dy2 * dy2)\n\n    if (c === 0) {\n      return\n    }\n    let angle = (Math.acos((dx1 * dx2 + dy1 * dy2) / c) / Math.PI) * 180\n\n    // catches 0 angle and NaN angle that are zero as well (but numerically instable)\n    if (!angle) return\n\n    if (endPoint.x < startPoint.x) {\n      angle = -angle\n    }\n\n    const matrix = new Matrix(this.el)\n    const { x: ox, y: oy } = new Point(cx, cy).transformO(matrix)\n\n    const { rotate } = matrix.decompose()\n    const resultAngle = this.snapToAngle(rotate + angle) - rotate\n\n    if (\n      this.el.dispatch('resize', {\n        box: this.box,\n        angle: resultAngle,\n        eventType: this.eventType,\n        event: e,\n        handler: this,\n      }).defaultPrevented\n    ) {\n      return\n    }\n\n    this.el.transform(matrix.rotateO(resultAngle, ox, oy))\n  }\n\n  endResize(ev) {\n    // Unbind resize and end events to window\n    if (this.eventType !== 'rot' && this.eventType !== 'point') {\n      this.resize(ev)\n    }\n\n    this.lastEvent = null\n\n    this.eventType = ''\n    off(window, 'mousemove.resize touchmove.resize')\n    off(window, 'mouseup.resize touchend.resize')\n  }\n\n  snapToGrid(point) {\n    if (this.grid) {\n      point.x = Math.round(point.x / this.grid) * this.grid\n      point.y = Math.round(point.y / this.grid) * this.grid\n    }\n\n    return point\n  }\n\n  snapToAngle(angle) {\n    if (this.degree) {\n      angle = Math.round(angle / this.degree) * this.degree\n    }\n\n    return angle\n  }\n}\n", "import { extend, Element } from '@svgdotjs/svg.js'\nimport { ResizeHandler } from './ResizeHandler'\n\nextend(Element, {\n  // Resize element with mouse\n  resize: function (enabled = true, options = {}) {\n    if (typeof enabled === 'object') {\n      options = enabled\n      enabled = true\n    }\n\n    let resizeHandler = this.remember('_ResizeHandler')\n\n    if (!resizeHandler) {\n      if (enabled.prototype instanceof ResizeHandler) {\n        /* eslint new-cap: [\"error\", { \"newIsCap\": false }] */\n        resizeHandler = new enabled(this)\n        enabled = true\n      } else {\n        resizeHandler = new ResizeHandler(this)\n      }\n\n      this.remember('_resizeHandler', resizeHandler)\n    }\n\n    resizeHandler.active(enabled, options)\n\n    return this\n  },\n})\n\nexport { ResizeHandler }\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAIA,MAAM,qBAAqB,CAAC,OAAO;AACjC,MAAI,GAAG,gBAAgB;AACrB,SAAK,GAAG,eAAe,CAAC;AAAA,EACzB;AACD,SAAO,EAAE,GAAG,GAAG,SAAS,GAAG,GAAG,QAAS;AACzC;AAEA,MAAM,mBAAmB,CAAC,WAAW;AACnC,MAAI,IAAI;AACR,MAAI,IAAI;AACR,MAAI,KAAK;AACT,MAAI,KAAK;AAET,WAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,UAAM,IAAI,OAAO,CAAC;AAClB,QAAI,KAAK,IAAI,GAAG,EAAE,CAAC,CAAC;AACpB,QAAI,KAAK,IAAI,GAAG,EAAE,CAAC,CAAC;AACpB,SAAK,KAAK,IAAI,IAAI,EAAE,CAAC,CAAC;AACtB,SAAK,KAAK,IAAI,IAAI,EAAE,CAAC,CAAC;AAAA,EACvB;AAED,SAAO,IAAI,IAAI,GAAG,GAAG,KAAK,GAAG,KAAK,CAAC;AACrC;AAEA,SAAS,SAAS,KAAK,QAAQ,OAAO;AACpC,QAAM,SAAS;AAAA,IACb,CAAC,IAAI,GAAG,IAAI,CAAC;AAAA,IACb,CAAC,IAAI,IAAI,IAAI,OAAO,IAAI,CAAC;AAAA,IACzB,CAAC,IAAI,IAAI,IAAI,OAAO,IAAI,IAAI,IAAI,MAAM;AAAA,IACtC,CAAC,IAAI,GAAG,IAAI,IAAI,IAAI,MAAM;AAAA,EAC3B;AAED,QAAM,YAAY,OAAO,IAAI,CAAC,CAAC,GAAG,CAAC,MAAM;AAEvC,UAAM,cAAc,IAAI,OAAO,CAAC;AAChC,UAAM,cAAc,IAAI,OAAO,CAAC;AAGhC,UAAM,UAAU,cAAc;AAC9B,UAAM,UAAU,cAAc;AAG9B,WAAO,CAAC,UAAU,OAAO,CAAC,GAAG,UAAU,OAAO,CAAC,CAAC;AAAA,EACpD,CAAG;AAED,SAAO,iBAAiB,SAAS;AACnC;AAEO,MAAM,cAAc;AAAA,EACzB,YAAY,IAAI;AACd,SAAK,KAAK;AACV,OAAG,SAAS,kBAAkB,IAAI;AAClC,SAAK,kBAAkB;AACvB,SAAK,YAAY;AACjB,SAAK,YAAY;AACjB,SAAK,eAAe,KAAK,aAAa,KAAK,IAAI;AAC/C,SAAK,SAAS,KAAK,OAAO,KAAK,IAAI;AACnC,SAAK,YAAY,KAAK,UAAU,KAAK,IAAI;AACzC,SAAK,SAAS,KAAK,OAAO,KAAK,IAAI;AACnC,SAAK,YAAY,KAAK,UAAU,KAAK,IAAI;AAAA,EAC1C;AAAA,EAED,OAAO,OAAO,SAAS;AACrB,SAAK,sBAAsB,QAAQ,uBAAuB;AAC1D,SAAK,eAAe,QAAQ,gBAAgB;AAC5C,SAAK,OAAO,QAAQ,QAAQ;AAC5B,SAAK,SAAS,QAAQ,UAAU;AAGhC,SAAK,GAAG,IAAI,SAAS;AAErB,QAAI,CAAC,MAAO;AAEZ,SAAK,GAAG;AAAA,MACN;AAAA,QACE;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACD;AAAA,MACD,KAAK;AAAA,IACN;AAID,QAAI,KAAK,WAAW;AAClB,UAAI,KAAK,cAAc,OAAO;AAC5B,aAAK,OAAO,KAAK,SAAS;AAAA,MAClC,WAAiB,KAAK,cAAc,SAAS;AACrC,aAAK,UAAU,KAAK,SAAS;AAAA,MACrC,OAAa;AACL,aAAK,OAAO,KAAK,SAAS;AAAA,MAC3B;AAAA,IACF;AAAA,EACF;AAAA;AAAA,EAGD,aAAa,GAAG;AACd,SAAK,YAAY,EAAE;AACnB,UAAM,EAAE,OAAO,OAAO,OAAQ,IAAG,EAAE;AACnC,UAAM,UAAU,CAAC,MAAM,KAAK,QAAQ,OAAO;AAG3C,QAAI,YAAY,MAAM,SAAS,MAAM,aAAa,GAAG;AACnD;AAAA,IACD;AAGD,QAAI,KAAK,GAAG,SAAS,gBAAgB,EAAE,OAAO,GAAG,SAAS,KAAM,CAAA,EAAE,kBAAkB;AAClF;AAAA,IACD;AAED,SAAK,MAAM,KAAK,GAAG,KAAM;AACzB,SAAK,aAAa,KAAK,GAAG,MAAM,mBAAmB,KAAK,CAAC;AACzD,SAAK,QAAQ;AACb,SAAK,SAAS,OAAO,MAAO;AAG5B,UAAM,aAAa,UAAU,cAAc,eAAe;AAC1D,UAAM,YAAY,UAAU,YAAY,iCAAiC;AAEzE,QAAI,EAAE,SAAS,SAAS;AACtB,SAAG,QAAQ,WAAW,KAAK,SAAS;AAAA,IAC1C,WAAe,EAAE,SAAS,OAAO;AAC3B,SAAG,QAAQ,WAAW,KAAK,MAAM;AAAA,IACvC,OAAW;AACL,SAAG,QAAQ,WAAW,KAAK,MAAM;AAAA,IAClC;AACD,OAAG,QAAQ,UAAU,KAAK,SAAS;AAAA,EACpC;AAAA,EAED,OAAO,GAAG;AACR,SAAK,YAAY;AAEjB,UAAM,WAAW,KAAK,WAAW,KAAK,GAAG,MAAM,mBAAmB,CAAC,CAAC,CAAC;AAErE,QAAI,KAAK,SAAS,IAAI,KAAK,WAAW;AACtC,QAAI,KAAK,SAAS,IAAI,KAAK,WAAW;AAEtC,QAAI,KAAK,uBAAuB,KAAK,cAAc;AACjD,YAAM;AACN,YAAM;AAAA,IACP;AAED,UAAM,IAAI,KAAK,IAAI,IAAI;AACvB,UAAM,IAAI,KAAK,IAAI,IAAI;AACvB,UAAM,KAAK,KAAK,IAAI,KAAK;AACzB,UAAM,KAAK,KAAK,IAAI,KAAK;AAEzB,QAAI,MAAM,IAAI,IAAI,KAAK,GAAG;AAE1B,QAAI,KAAK,UAAU,SAAS,GAAG,GAAG;AAChC,UAAI,IAAI,KAAK,IAAI,GAAG,KAAK,IAAI,EAAE;AAC/B,UAAI,KAAK,KAAK,IAAI,GAAG,KAAK,IAAI,EAAE;AAAA,IACjC;AAED,QAAI,KAAK,UAAU,SAAS,GAAG,GAAG;AAChC,UAAI,IAAI,KAAK,IAAI,IAAI,KAAK,IAAI,CAAC;AAC/B,UAAI,KAAK,KAAK,IAAI,IAAI,KAAK,IAAI,CAAC;AAAA,IACjC;AAED,QAAI,KAAK,UAAU,SAAS,GAAG,GAAG;AAChC,UAAI,IAAI,KAAK,IAAI,GAAG,KAAK,IAAI,EAAE;AAC/B,UAAI,KAAK,KAAK,IAAI,GAAG,KAAK,IAAI,EAAE;AAAA,IACjC;AAED,QAAI,KAAK,UAAU,SAAS,GAAG,GAAG;AAChC,UAAI,IAAI,KAAK,IAAI,IAAI,KAAK,IAAI,CAAC;AAC/B,UAAI,KAAK,KAAK,IAAI,IAAI,KAAK,IAAI,CAAC;AAAA,IACjC;AAED,QAAI,QAAQ,IAAI,KAAK,IAAI;AACzB,QAAI,SAAS,IAAI,KAAK,IAAI;AAM1B,QAAI,KAAK,qBAAqB;AAC5B,YAAM,SAAS,IAAI,QAAQ,KAAK,IAAI;AACpC,YAAM,SAAS,IAAI,SAAS,KAAK,IAAI;AAErC,YAAM,QAAQ,CAAC,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,GAAG;AAEzD,YAAM,UAAU,MAAM,QAAQ,KAAK,SAAS,IAAI,KAAK,MAAM;AAC3D,YAAM,gBAAgB,KAAK,eAAe,CAAC,KAAK,IAAI,IAAI,KAAK,IAAI,EAAE,IAAI,KAAK,OAAO,MAAM;AAEzF,UAAI,QAAQ,KAAK,UAAU,SAAS,GAAG,KAAK,KAAK,UAAU,SAAS,GAAG,IAAI,SAAS;AACpF,cAAQ,KAAK,UAAU,WAAW,IAAI,KAAK,IAAI,QAAQ,MAAM,IAAI;AAEjE,YAAM,SAAS,KAAK,KAAK,eAAe,KAAK;AAAA,IAC9C;AAED,QACE,KAAK,GAAG,SAAS,UAAU;AAAA,MACzB,KAAK,IAAI,IAAI,GAAG;AAAA,MAChB,OAAO;AAAA,MACP,WAAW,KAAK;AAAA,MAChB,OAAO;AAAA,MACP,SAAS;AAAA,IACV,CAAA,EAAE,kBACH;AACA;AAAA,IACD;AAED,SAAK,GAAG,KAAK,IAAI,OAAO,IAAI,MAAM,EAAE,KAAK,IAAI,GAAG,IAAI,CAAC;AAAA,EACtD;AAAA,EAED,UAAU,GAAG;AACX,SAAK,YAAY;AACjB,UAAM,EAAE,GAAG,EAAG,IAAG,KAAK,WAAW,KAAK,GAAG,MAAM,mBAAmB,CAAC,CAAC,CAAC;AACrE,UAAM,WAAW,KAAK,GAAG,MAAK,EAAG,MAAO;AACxC,aAAS,KAAK,KAAK,IAAI,CAAC,GAAG,CAAC;AAE5B,QACE,KAAK,GAAG,SAAS,UAAU;AAAA,MACzB,KAAK,iBAAiB,QAAQ;AAAA,MAC9B,OAAO;AAAA,MACP,WAAW,KAAK;AAAA,MAChB,OAAO;AAAA,MACP,SAAS;AAAA,IACV,CAAA,EAAE,kBACH;AACA;AAAA,IACD;AAED,SAAK,GAAG,KAAK,QAAQ;AAAA,EACtB;AAAA,EAED,OAAO,GAAG;AACR,SAAK,YAAY;AAEjB,UAAM,aAAa,KAAK;AACxB,UAAM,WAAW,KAAK,GAAG,MAAM,mBAAmB,CAAC,CAAC;AAEpD,UAAM,EAAE,IAAI,GAAI,IAAG,KAAK;AAExB,UAAM,MAAM,WAAW,IAAI;AAC3B,UAAM,MAAM,WAAW,IAAI;AAE3B,UAAM,MAAM,SAAS,IAAI;AACzB,UAAM,MAAM,SAAS,IAAI;AAEzB,UAAM,IAAI,KAAK,KAAK,MAAM,MAAM,MAAM,GAAG,IAAI,KAAK,KAAK,MAAM,MAAM,MAAM,GAAG;AAE5E,QAAI,MAAM,GAAG;AACX;AAAA,IACD;AACD,QAAI,QAAS,KAAK,MAAM,MAAM,MAAM,MAAM,OAAO,CAAC,IAAI,KAAK,KAAM;AAGjE,QAAI,CAAC,MAAO;AAEZ,QAAI,SAAS,IAAI,WAAW,GAAG;AAC7B,cAAQ,CAAC;AAAA,IACV;AAED,UAAM,SAAS,IAAI,OAAO,KAAK,EAAE;AACjC,UAAM,EAAE,GAAG,IAAI,GAAG,GAAI,IAAG,IAAI,MAAM,IAAI,EAAE,EAAE,WAAW,MAAM;AAE5D,UAAM,EAAE,OAAM,IAAK,OAAO,UAAW;AACrC,UAAM,cAAc,KAAK,YAAY,SAAS,KAAK,IAAI;AAEvD,QACE,KAAK,GAAG,SAAS,UAAU;AAAA,MACzB,KAAK,KAAK;AAAA,MACV,OAAO;AAAA,MACP,WAAW,KAAK;AAAA,MAChB,OAAO;AAAA,MACP,SAAS;AAAA,IACV,CAAA,EAAE,kBACH;AACA;AAAA,IACD;AAED,SAAK,GAAG,UAAU,OAAO,QAAQ,aAAa,IAAI,EAAE,CAAC;AAAA,EACtD;AAAA,EAED,UAAU,IAAI;AAEZ,QAAI,KAAK,cAAc,SAAS,KAAK,cAAc,SAAS;AAC1D,WAAK,OAAO,EAAE;AAAA,IACf;AAED,SAAK,YAAY;AAEjB,SAAK,YAAY;AACjB,QAAI,QAAQ,mCAAmC;AAC/C,QAAI,QAAQ,gCAAgC;AAAA,EAC7C;AAAA,EAED,WAAW,OAAO;AAChB,QAAI,KAAK,MAAM;AACb,YAAM,IAAI,KAAK,MAAM,MAAM,IAAI,KAAK,IAAI,IAAI,KAAK;AACjD,YAAM,IAAI,KAAK,MAAM,MAAM,IAAI,KAAK,IAAI,IAAI,KAAK;AAAA,IAClD;AAED,WAAO;AAAA,EACR;AAAA,EAED,YAAY,OAAO;AACjB,QAAI,KAAK,QAAQ;AACf,cAAQ,KAAK,MAAM,QAAQ,KAAK,MAAM,IAAI,KAAK;AAAA,IAChD;AAED,WAAO;AAAA,EACR;AACH;AC1TA,OAAO,SAAS;AAAA;AAAA,EAEd,QAAQ,SAAU,UAAU,MAAM,UAAU,CAAA,GAAI;AAC9C,QAAI,OAAO,YAAY,UAAU;AAC/B,gBAAU;AACV,gBAAU;AAAA,IACX;AAED,QAAI,gBAAgB,KAAK,SAAS,gBAAgB;AAElD,QAAI,CAAC,eAAe;AAClB,UAAI,QAAQ,qBAAqB,eAAe;AAE9C,wBAAgB,IAAI,QAAQ,IAAI;AAChC,kBAAU;AAAA,MAClB,OAAa;AACL,wBAAgB,IAAI,cAAc,IAAI;AAAA,MACvC;AAED,WAAK,SAAS,kBAAkB,aAAa;AAAA,IAC9C;AAED,kBAAc,OAAO,SAAS,OAAO;AAErC,WAAO;AAAA,EACR;AACH,CAAC;"}
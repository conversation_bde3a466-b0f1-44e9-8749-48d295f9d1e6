/*! @svgdotjs/svg.resize.js v2.0.5 MIT*/;
this.svg=this.svg||{},this.svg.resize=this.svg.resize||{},this.svg.resize.js=function(e,t,i){"use strict";const s=e=>(e.changedTouches&&(e=e.changedTouches[0]),{x:e.clientX,y:e.clientY}),h=e=>{let t=1/0,s=1/0,h=-1/0,n=-1/0;for(let i=0;i<e.length;i++){const o=e[i];t=Math.min(t,o[0]),s=Math.min(s,o[1]),h=Math.max(h,o[0]),n=Math.max(n,o[1])}return new i.Box(t,s,h-t,n-s)};class n{constructor(e){this.el=e,e.remember("_ResizeHandler",this),this.lastCoordinates=null,this.eventType="",this.lastEvent=null,this.handleResize=this.handleResize.bind(this),this.resize=this.resize.bind(this),this.endResize=this.endResize.bind(this),this.rotate=this.rotate.bind(this),this.movePoint=this.movePoint.bind(this)}active(e,t){this.preserveAspectRatio=t.preserveAspectRatio??!1,this.aroundCenter=t.aroundCenter??!1,this.grid=t.grid??0,this.degree=t.degree??0,this.el.off(".resize"),e&&(this.el.on(["lt.resize","rt.resize","rb.resize","lb.resize","t.resize","r.resize","b.resize","l.resize","rot.resize","point.resize"],this.handleResize),this.lastEvent&&("rot"===this.eventType?this.rotate(this.lastEvent):"point"===this.eventType?this.movePoint(this.lastEvent):this.resize(this.lastEvent)))}handleResize(e){this.eventType=e.type;const{event:t,index:h,points:n}=e.detail,o=!t.type.indexOf("mouse");if(o&&1!==(t.which||t.buttons))return;if(this.el.dispatch("beforeresize",{event:e,handler:this}).defaultPrevented)return;this.box=this.el.bbox(),this.startPoint=this.el.point(s(t)),this.index=h,this.points=n.slice();const r=(o?"mousemove":"touchmove")+".resize",a=(o?"mouseup":"touchcancel.resize touchend")+".resize";"point"===e.type?i.on(window,r,this.movePoint):"rot"===e.type?i.on(window,r,this.rotate):i.on(window,r,this.resize),i.on(window,a,this.endResize)}resize(e){this.lastEvent=e;const t=this.snapToGrid(this.el.point(s(e)));let n=t.x-this.startPoint.x,o=t.y-this.startPoint.y;this.preserveAspectRatio&&this.aroundCenter&&(n*=2,o*=2);const r=this.box.x+n,a=this.box.y+o,l=this.box.x2+n,d=this.box.y2+o;let x=new i.Box(this.box);if(this.eventType.includes("l")&&(x.x=Math.min(r,this.box.x2),x.x2=Math.max(r,this.box.x2)),this.eventType.includes("r")&&(x.x=Math.min(l,this.box.x),x.x2=Math.max(l,this.box.x)),this.eventType.includes("t")&&(x.y=Math.min(a,this.box.y2),x.y2=Math.max(a,this.box.y2)),this.eventType.includes("b")&&(x.y=Math.min(d,this.box.y),x.y2=Math.max(d,this.box.y)),x.width=x.x2-x.x,x.height=x.y2-x.y,this.preserveAspectRatio){const e=x.width/this.box.width,t=x.height/this.box.height,i=["lt","t","rt","r","rb","b","lb","l"],s=(i.indexOf(this.eventType)+4)%i.length,n=this.aroundCenter?[this.box.cx,this.box.cy]:this.points[s];let o=this.eventType.includes("t")||this.eventType.includes("b")?t:e;o=2===this.eventType.length?Math.max(e,t):o,x=function(e,t,i){const s=[[e.x,e.y],[e.x+e.width,e.y],[e.x+e.width,e.y+e.height],[e.x,e.y+e.height]].map((([e,s])=>{const h=e-t[0],n=(s-t[1])*i;return[h*i+t[0],n+t[1]]}));return h(s)}(this.box,n,o)}this.el.dispatch("resize",{box:new i.Box(x),angle:0,eventType:this.eventType,event:e,handler:this}).defaultPrevented||this.el.size(x.width,x.height).move(x.x,x.y)}movePoint(e){this.lastEvent=e;const{x:t,y:i}=this.snapToGrid(this.el.point(s(e))),n=this.el.array().slice();n[this.index]=[t,i],this.el.dispatch("resize",{box:h(n),angle:0,eventType:this.eventType,event:e,handler:this}).defaultPrevented||this.el.plot(n)}rotate(e){this.lastEvent=e;const t=this.startPoint,h=this.el.point(s(e)),{cx:n,cy:o}=this.box,r=t.x-n,a=t.y-o,l=h.x-n,d=h.y-o,x=Math.sqrt(r*r+a*a)*Math.sqrt(l*l+d*d);if(0===x)return;let p=Math.acos((r*l+a*d)/x)/Math.PI*180;if(!p)return;h.x<t.x&&(p=-p);const v=new i.Matrix(this.el),{x:c,y:y}=new i.Point(n,o).transformO(v),{rotate:u}=v.decompose(),b=this.snapToAngle(u+p)-u;this.el.dispatch("resize",{box:this.box,angle:b,eventType:this.eventType,event:e,handler:this}).defaultPrevented||this.el.transform(v.rotateO(b,c,y))}endResize(e){"rot"!==this.eventType&&"point"!==this.eventType&&this.resize(e),this.lastEvent=null,this.eventType="",i.off(window,"mousemove.resize touchmove.resize"),i.off(window,"mouseup.resize touchend.resize")}snapToGrid(e){return this.grid&&(e.x=Math.round(e.x/this.grid)*this.grid,e.y=Math.round(e.y/this.grid)*this.grid),e}snapToAngle(e){return this.degree&&(e=Math.round(e/this.degree)*this.degree),e}}return i.extend(i.Element,{resize:function(e=!0,t={}){"object"==typeof e&&(t=e,e=!0);let i=this.remember("_ResizeHandler");return i||(e.prototype instanceof n?(i=new e(this),e=!0):i=new n(this),this.remember("_resizeHandler",i)),i.active(e,t),this}}),e.ResizeHandler=n,Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),e}({},0,SVG);
//# sourceMappingURL=svg.resize.iife.js.map

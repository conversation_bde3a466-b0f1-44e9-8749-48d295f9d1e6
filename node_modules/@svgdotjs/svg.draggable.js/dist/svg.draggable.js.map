{"version": 3, "file": "svg.draggable.js", "sources": ["../src/svg.draggable.js"], "sourcesContent": ["import { Box, Element, G, extend, off, on } from '@svgdotjs/svg.js'\n\nconst getCoordsFromEvent = (ev) => {\n  if (ev.changedTouches) {\n    ev = ev.changedTouches[0]\n  }\n  return { x: ev.clientX, y: ev.clientY }\n}\n\n// Creates handler, saves it\nclass DragHandler {\n  constructor(el) {\n    el.remember('_draggable', this)\n    this.el = el\n\n    this.drag = this.drag.bind(this)\n    this.startDrag = this.startDrag.bind(this)\n    this.endDrag = this.endDrag.bind(this)\n  }\n\n  // Enables or disabled drag based on input\n  init(enabled) {\n    if (enabled) {\n      this.el.on('mousedown.drag', this.startDrag)\n      this.el.on('touchstart.drag', this.startDrag, { passive: false })\n    } else {\n      this.el.off('mousedown.drag')\n      this.el.off('touchstart.drag')\n    }\n  }\n\n  // Start dragging\n  startDrag(ev) {\n    const isMouse = !ev.type.indexOf('mouse')\n\n    // Check for left button\n    if (isMouse && ev.which !== 1 && ev.buttons !== 0) {\n      return\n    }\n\n    // Fire beforedrag event\n    if (\n      this.el.dispatch('beforedrag', { event: ev, handler: this })\n        .defaultPrevented\n    ) {\n      return\n    }\n\n    // Prevent browser drag behavior as soon as possible\n    ev.preventDefault()\n\n    // Prevent propagation to a parent that might also have dragging enabled\n    ev.stopPropagation()\n\n    // Make sure that start events are unbound so that one element\n    // is only dragged by one input only\n    this.init(false)\n\n    this.box = this.el.bbox()\n    this.lastClick = this.el.point(getCoordsFromEvent(ev))\n\n    const eventMove = (isMouse ? 'mousemove' : 'touchmove') + '.drag'\n    const eventEnd = (isMouse ? 'mouseup' : 'touchend') + '.drag'\n\n    // Bind drag and end events to window\n    on(window, eventMove, this.drag, this, { passive: false })\n    on(window, eventEnd, this.endDrag, this, { passive: false })\n\n    // Fire dragstart event\n    this.el.fire('dragstart', { event: ev, handler: this, box: this.box })\n  }\n\n  // While dragging\n  drag(ev) {\n    const { box, lastClick } = this\n\n    const currentClick = this.el.point(getCoordsFromEvent(ev))\n    const dx = currentClick.x - lastClick.x\n    const dy = currentClick.y - lastClick.y\n\n    if (!dx && !dy) return box\n\n    const x = box.x + dx\n    const y = box.y + dy\n    this.box = new Box(x, y, box.w, box.h)\n    this.lastClick = currentClick\n\n    if (\n      this.el.dispatch('dragmove', {\n        event: ev,\n        handler: this,\n        box: this.box,\n        dx,\n        dy,\n      }).defaultPrevented\n    ) {\n      return\n    }\n\n    this.move(x, y)\n  }\n\n  move(x, y) {\n    // Svg elements bbox depends on their content even though they have\n    // x, y, width and height - strange!\n    // Thats why we handle them the same as groups\n    if (this.el.type === 'svg') {\n      G.prototype.move.call(this.el, x, y)\n    } else {\n      this.el.move(x, y)\n    }\n  }\n\n  endDrag(ev) {\n    // final drag\n    this.drag(ev)\n\n    // fire dragend event\n    this.el.fire('dragend', { event: ev, handler: this, box: this.box })\n\n    // unbind events\n    off(window, 'mousemove.drag')\n    off(window, 'touchmove.drag')\n    off(window, 'mouseup.drag')\n    off(window, 'touchend.drag')\n\n    // Rebind initial Events\n    this.init(true)\n  }\n}\n\nextend(Element, {\n  draggable(enable = true) {\n    const dragHandler = this.remember('_draggable') || new DragHandler(this)\n    dragHandler.init(enable)\n    return this\n  },\n})\n"], "names": ["getCoordsFromEvent", "ev", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "el", "enabled", "isMouse", "eventMove", "eventEnd", "on", "box", "lastClick", "currentClick", "dx", "dy", "x", "y", "Box", "G", "off", "svg_js", "Element", "enable"], "mappings": ";;;;;;;;;uPAEA,MAAMA,EAAsBC,IACtBA,EAAG,iBACLA,EAAKA,EAAG,eAAe,CAAC,GAEnB,CAAE,EAAGA,EAAG,QAAS,EAAGA,EAAG,OAAS,GAIzC,MAAMC,CAAY,CAChB,YAAYC,EAAI,CACdA,EAAG,SAAS,aAAc,IAAI,EAC9B,KAAK,GAAKA,EAEV,KAAK,KAAO,KAAK,KAAK,KAAK,IAAI,EAC/B,KAAK,UAAY,KAAK,UAAU,KAAK,IAAI,EACzC,KAAK,QAAU,KAAK,QAAQ,KAAK,IAAI,CACtC,CAGD,KAAKC,EAAS,CACRA,GACF,KAAK,GAAG,GAAG,iBAAkB,KAAK,SAAS,EAC3C,KAAK,GAAG,GAAG,kBAAmB,KAAK,UAAW,CAAE,QAAS,GAAO,IAEhE,KAAK,GAAG,IAAI,gBAAgB,EAC5B,KAAK,GAAG,IAAI,iBAAiB,EAEhC,CAGD,UAAUH,EAAI,CACZ,MAAMI,EAAU,CAACJ,EAAG,KAAK,QAAQ,OAAO,EAQxC,GALII,GAAWJ,EAAG,QAAU,GAAKA,EAAG,UAAY,GAM9C,KAAK,GAAG,SAAS,aAAc,CAAE,MAAOA,EAAI,QAAS,KAAM,EACxD,iBAEH,OAIFA,EAAG,eAAgB,EAGnBA,EAAG,gBAAiB,EAIpB,KAAK,KAAK,EAAK,EAEf,KAAK,IAAM,KAAK,GAAG,KAAM,EACzB,KAAK,UAAY,KAAK,GAAG,MAAMD,EAAmBC,CAAE,CAAC,EAErD,MAAMK,GAAaD,EAAU,YAAc,aAAe,QACpDE,GAAYF,EAAU,UAAY,YAAc,QAGtDG,KAAG,OAAQF,EAAW,KAAK,KAAM,KAAM,CAAE,QAAS,GAAO,EACzDE,KAAG,OAAQD,EAAU,KAAK,QAAS,KAAM,CAAE,QAAS,GAAO,EAG3D,KAAK,GAAG,KAAK,YAAa,CAAE,MAAON,EAAI,QAAS,KAAM,IAAK,KAAK,GAAG,CAAE,CACtE,CAGD,KAAKA,EAAI,CACP,KAAM,CAAE,IAAAQ,EAAK,UAAAC,CAAS,EAAK,KAErBC,EAAe,KAAK,GAAG,MAAMX,EAAmBC,CAAE,CAAC,EACnDW,EAAKD,EAAa,EAAID,EAAU,EAChCG,EAAKF,EAAa,EAAID,EAAU,EAEtC,GAAI,CAACE,GAAM,CAACC,EAAI,OAAOJ,EAEvB,MAAMK,EAAIL,EAAI,EAAIG,EACZG,EAAIN,EAAI,EAAII,EAClB,KAAK,IAAM,IAAIG,EAAAA,IAAIF,EAAGC,EAAGN,EAAI,EAAGA,EAAI,CAAC,EACrC,KAAK,UAAYE,EAGf,MAAK,GAAG,SAAS,WAAY,CAC3B,MAAOV,EACP,QAAS,KACT,IAAK,KAAK,IACV,GAAAW,EACA,GAAAC,CACD,CAAA,EAAE,kBAKL,KAAK,KAAKC,EAAGC,CAAC,CACf,CAED,KAAKD,EAAGC,EAAG,CAIL,KAAK,GAAG,OAAS,MACnBE,EAAC,EAAC,UAAU,KAAK,KAAK,KAAK,GAAIH,EAAGC,CAAC,EAEnC,KAAK,GAAG,KAAKD,EAAGC,CAAC,CAEpB,CAED,QAAQd,EAAI,CAEV,KAAK,KAAKA,CAAE,EAGZ,KAAK,GAAG,KAAK,UAAW,CAAE,MAAOA,EAAI,QAAS,KAAM,IAAK,KAAK,GAAG,CAAE,EAGnEiB,EAAG,IAAC,OAAQ,gBAAgB,EAC5BA,EAAG,IAAC,OAAQ,gBAAgB,EAC5BA,EAAG,IAAC,OAAQ,cAAc,EAC1BA,EAAG,IAAC,OAAQ,eAAe,EAG3B,KAAK,KAAK,EAAI,CACf,CACH,CAEMC,EAAA,OAACC,UAAS,CACd,UAAUC,EAAS,GAAM,CAEvB,OADoB,KAAK,SAAS,YAAY,GAAK,IAAInB,EAAY,IAAI,GAC3D,KAAKmB,CAAM,EAChB,IACR,CACH,CAAC"}
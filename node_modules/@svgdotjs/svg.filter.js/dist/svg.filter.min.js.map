{"version": 3, "file": "svg.filter.min.js", "sources": ["../src/svg.filter.js"], "sourcesContent": ["import {\n  Array as <PERSON><PERSON><PERSON><PERSON>,\n  Container,\n  Defs,\n  Element,\n  extend,\n  find,\n  namespaces as ns,\n  nodeOrNew,\n  utils,\n  wrapWithAttrCheck\n} from '@svgdotjs/svg.js'\n\nexport default class Filter extends Element {\n  constructor (node) {\n    super(nodeOrNew('filter', node), node)\n\n    this.$source = 'SourceGraphic'\n    this.$sourceAlpha = 'SourceAlpha'\n    this.$background = 'BackgroundImage'\n    this.$backgroundAlpha = 'BackgroundAlpha'\n    this.$fill = 'FillPaint'\n    this.$stroke = 'StrokePaint'\n    this.$autoSetIn = true\n  }\n\n  put (element, i) {\n    element = super.put(element, i)\n\n    if (!element.attr('in') && this.$autoSetIn) {\n      element.attr('in', this.$source)\n    }\n    if (!element.attr('result')) {\n      element.attr('result', element.id())\n    }\n\n    return element\n  }\n\n  // Unmask all masked elements and remove itself\n  remove () {\n    // unmask all targets\n    this.targets().each('unfilter')\n\n    // remove mask from parent\n    return super.remove()\n  }\n\n  targets () {\n    return find('svg [filter*=\"' + this.id() + '\"]')\n  }\n\n  toString () {\n    return 'url(#' + this.id() + ')'\n  }\n}\n\n// Create Effect class\nclass Effect extends Element {\n  constructor (node, attr) {\n    super(node, attr)\n    this.result(this.id())\n  }\n\n  in (effect) {\n    // Act as getter\n    if (effect == null) {\n      const _in = this.attr('in')\n      const ref = this.parent() && this.parent().find(`[result=\"${_in}\"]`)[0]\n      return ref || _in\n    }\n\n    // Avr as setter\n    return this.attr('in', effect)\n  }\n\n  // Named result\n  result (result) {\n    return this.attr('result', result)\n  }\n\n  // Stringification\n  toString () {\n    return this.result()\n  }\n}\n\n// This function takes an array with attr keys and sets for every key the\n// attribute to the value of one paramater\n// getAttrSetter(['a', 'b']) becomes this.attr({a: param1, b: param2})\nconst getAttrSetter = (params) => {\n  return function (...args) {\n    for (let i = params.length; i--;) {\n      if (args[i] != null) {\n        this.attr(params[i], args[i])\n      }\n    }\n  }\n}\n\nconst updateFunctions = {\n  blend: getAttrSetter(['in', 'in2', 'mode']),\n  // ColorMatrix effect\n  colorMatrix: getAttrSetter(['type', 'values']),\n  // Composite effect\n  composite: getAttrSetter(['in', 'in2', 'operator']),\n  // ConvolveMatrix effect\n  convolveMatrix: function (matrix) {\n    matrix = new SVGArray(matrix).toString()\n\n    this.attr({\n      order: Math.sqrt(matrix.split(' ').length),\n      kernelMatrix: matrix\n    })\n  },\n  // DiffuseLighting effect\n  diffuseLighting: getAttrSetter(['surfaceScale', 'lightingColor', 'diffuseConstant', 'kernelUnitLength']),\n  // DisplacementMap effect\n  displacementMap: getAttrSetter(['in', 'in2', 'scale', 'xChannelSelector', 'yChannelSelector']),\n  // DropShadow effect\n  dropShadow: getAttrSetter(['in', 'dx', 'dy', 'stdDeviation']),\n  // Flood effect\n  flood: getAttrSetter(['flood-color', 'flood-opacity']),\n  // Gaussian Blur effect\n  gaussianBlur: function (x = 0, y = x) {\n    this.attr('stdDeviation', x + ' ' + y)\n  },\n  // Image effect\n  image: function (src) {\n    this.attr('href', src, ns.xlink)\n  },\n  // Morphology effect\n  morphology: getAttrSetter(['operator', 'radius']),\n  // Offset effect\n  offset: getAttrSetter(['dx', 'dy']),\n  // SpecularLighting effect\n  specularLighting: getAttrSetter(['surfaceScale', 'lightingColor', 'diffuseConstant', 'specularExponent', 'kernelUnitLength']),\n  // Tile effect\n  tile: getAttrSetter([]),\n  // Turbulence effect\n  turbulence: getAttrSetter(['baseFrequency', 'numOctaves', 'seed', 'stitchTiles', 'type'])\n}\n\nconst filterNames = [\n  'blend',\n  'colorMatrix',\n  'componentTransfer',\n  'composite',\n  'convolveMatrix',\n  'diffuseLighting',\n  'displacementMap',\n  'dropShadow',\n  'flood',\n  'gaussianBlur',\n  'image',\n  'merge',\n  'morphology',\n  'offset',\n  'specularLighting',\n  'tile',\n  'turbulence'\n]\n\n// For every filter create a class\nfilterNames.forEach((effect) => {\n  const name = utils.capitalize(effect)\n  const fn = updateFunctions[effect]\n\n  Filter[name + 'Effect'] = class extends Effect {\n    constructor (node) {\n      super(nodeOrNew('fe' + name, node), node)\n    }\n\n    // This function takes all parameters from the factory call\n    // and updates the attributes according to the updateFunctions\n    update (args) {\n      fn.apply(this, args)\n      return this\n    }\n  }\n\n  // Add factory function to filter\n  // Allow to pass a function or object\n  // The attr object is catched from \"wrapWithAttrCheck\"\n  Filter.prototype[effect] = wrapWithAttrCheck(function (fn, ...args) {\n    const effect = new Filter[name + 'Effect']()\n\n    if (fn == null) return this.put(effect)\n\n    // For Effects which can take children, a function is allowed\n    if (typeof fn === 'function') {\n      fn.call(effect, effect)\n    } else {\n      // In case it is not a function, add it to arguments\n      args.unshift(fn)\n    }\n    return this.put(effect).update(args)\n  })\n})\n\n// Correct factories which are not that simple\nextend(Filter, {\n  merge (arrayOrFn) {\n    const node = this.put(new Filter.MergeEffect())\n\n    // If a function was passed, execute it\n    // That makes stuff like this possible:\n    // filter.merge((mergeEffect) => mergeEffect.mergeNode(in))\n    if (typeof arrayOrFn === 'function') {\n      arrayOrFn.call(node, node)\n      return node\n    }\n\n    // Check if first child is an array, otherwise use arguments as array\n    const children = arrayOrFn instanceof Array ? arrayOrFn : [...arguments]\n\n    children.forEach((child) => {\n      if (child instanceof Filter.MergeNode) {\n        node.put(child)\n      } else {\n        node.mergeNode(child)\n      }\n    })\n\n    return node\n  },\n  componentTransfer (components = {}) {\n    const node = this.put(new Filter.ComponentTransferEffect())\n\n    if (typeof components === 'function') {\n      components.call(node, node)\n      return node\n    }\n\n    // If no component is set, we use the given object for all components\n    if (!components.r && !components.g && !components.b && !components.a) {\n      const temp = components\n      components = {\n        r: temp, g: temp, b: temp, a: temp\n      }\n    }\n\n    for (const c in components) {\n      // components[c] has to hold an attributes object\n      node.add(new Filter['Func' + c.toUpperCase()](components[c]))\n    }\n\n    return node\n  }\n})\n\nconst filterChildNodes = [\n  'distantLight',\n  'pointLight',\n  'spotLight',\n  'mergeNode',\n  'FuncR',\n  'FuncG',\n  'FuncB',\n  'FuncA'\n]\n\nfilterChildNodes.forEach((child) => {\n  const name = utils.capitalize(child)\n  Filter[name] = class extends Effect {\n    constructor (node) {\n      super(nodeOrNew('fe' + name, node), node)\n    }\n  }\n})\n\nconst componentFuncs = [\n  'funcR',\n  'funcG',\n  'funcB',\n  'funcA'\n]\n\n// Add an update function for componentTransfer-children\ncomponentFuncs.forEach(function (c) {\n  const _class = Filter[utils.capitalize(c)]\n  const fn = wrapWithAttrCheck(function () {\n    return this.put(new _class())\n  })\n\n  Filter.ComponentTransferEffect.prototype[c] = fn\n})\n\nconst lights = [\n  'distantLight',\n  'pointLight',\n  'spotLight'\n]\n\n// Add light sources factories to lightining effects\nlights.forEach((light) => {\n  const _class = Filter[utils.capitalize(light)]\n  const fn = wrapWithAttrCheck(function () {\n    return this.put(new _class())\n  })\n\n  Filter.DiffuseLightingEffect.prototype[light] = fn\n  Filter.SpecularLightingEffect.prototype[light] = fn\n})\n\nextend(Filter.MergeEffect, {\n  mergeNode (_in) {\n    return this.put(new Filter.MergeNode()).attr('in', _in)\n  }\n})\n\n// add .filter function\nextend(Defs, {\n  // Define filter\n  filter: function (block) {\n    const filter = this.put(new Filter())\n\n    /* invoke passed block */\n    if (typeof block === 'function') { block.call(filter, filter) }\n\n    return filter\n  }\n})\n\nextend(Container, {\n  // Define filter on defs\n  filter: function (block) {\n    return this.defs().filter(block)\n  }\n})\n\nextend(Element, {\n  // Create filter element in defs and store reference\n  filterWith: function (block) {\n    const filter = block instanceof Filter\n      ? block\n      : this.defs().filter(block)\n\n    return this.attr('filter', filter)\n  },\n  // Remove filter\n  unfilter: function (remove) {\n    /* remove filter attribute */\n    return this.attr('filter', null)\n  },\n  filterer () {\n    return this.reference('filter')\n  }\n})\n\n// chaining\nconst chainingEffects = {\n  // Blend effect\n  blend: function (in2, mode) {\n    return this.parent() && this.parent().blend(this, in2, mode) // pass this as the first input\n  },\n  // ColorMatrix effect\n  colorMatrix: function (type, values) {\n    return this.parent() && this.parent().colorMatrix(type, values).in(this)\n  },\n  // ComponentTransfer effect\n  componentTransfer: function (components) {\n    return this.parent() && this.parent().componentTransfer(components).in(this)\n  },\n  // Composite effect\n  composite: function (in2, operator) {\n    return this.parent() && this.parent().composite(this, in2, operator) // pass this as the first input\n  },\n  // ConvolveMatrix effect\n  convolveMatrix: function (matrix) {\n    return this.parent() && this.parent().convolveMatrix(matrix).in(this)\n  },\n  // DiffuseLighting effect\n  diffuseLighting: function (surfaceScale, lightingColor, diffuseConstant, kernelUnitLength) {\n    return this.parent() && this.parent().diffuseLighting(surfaceScale, diffuseConstant, kernelUnitLength).in(this)\n  },\n  // DisplacementMap effect\n  displacementMap: function (in2, scale, xChannelSelector, yChannelSelector) {\n    return this.parent() && this.parent().displacementMap(this, in2, scale, xChannelSelector, yChannelSelector) // pass this as the first input\n  },\n  // DisplacementMap effect\n  dropShadow: function (x, y, stdDeviation) {\n    return this.parent() && this.parent().dropShadow(this, x, y, stdDeviation).in(this) // pass this as the first input\n  },\n  // Flood effect\n  flood: function (color, opacity) {\n    return this.parent() && this.parent().flood(color, opacity) // this effect dont have inputs\n  },\n  // Gaussian Blur effect\n  gaussianBlur: function (x, y) {\n    return this.parent() && this.parent().gaussianBlur(x, y).in(this)\n  },\n  // Image effect\n  image: function (src) {\n    return this.parent() && this.parent().image(src) // this effect dont have inputs\n  },\n  // Merge effect\n  merge: function (arg) {\n    arg = arg instanceof Array ? arg : [...arg]\n    return this.parent() && this.parent().merge(this, ...arg) // pass this as the first argument\n  },\n  // Morphology effect\n  morphology: function (operator, radius) {\n    return this.parent() && this.parent().morphology(operator, radius).in(this)\n  },\n  // Offset effect\n  offset: function (dx, dy) {\n    return this.parent() && this.parent().offset(dx, dy).in(this)\n  },\n  // SpecularLighting effect\n  specularLighting: function (surfaceScale, lightingColor, diffuseConstant, specularExponent, kernelUnitLength) {\n    return this.parent() && this.parent().specularLighting(surfaceScale, diffuseConstant, specularExponent, kernelUnitLength).in(this)\n  },\n  // Tile effect\n  tile: function () {\n    return this.parent() && this.parent().tile().in(this)\n  },\n  // Turbulence effect\n  turbulence: function (baseFrequency, numOctaves, seed, stitchTiles, type) {\n    return this.parent() && this.parent().turbulence(baseFrequency, numOctaves, seed, stitchTiles, type).in(this)\n  }\n}\n\nextend(Effect, chainingEffects)\n\n// Effect-specific extensions\nextend(Filter.MergeEffect, {\n  in: function (effect) {\n    if (effect instanceof Filter.MergeNode) {\n      this.add(effect, 0)\n    } else {\n      this.add(new Filter.MergeNode().in(effect), 0)\n    }\n\n    return this\n  }\n})\n\nextend([Filter.CompositeEffect, Filter.BlendEffect, Filter.DisplacementMapEffect], {\n  in2: function (effect) {\n    if (effect == null) {\n      const in2 = this.attr('in2')\n      const ref = this.parent() && this.parent().find(`[result=\"${in2}\"]`)[0]\n      return ref || in2\n    }\n    return this.attr('in2', effect)\n  }\n})\n\n// Presets\nFilter.filter = {\n  sepiatone: [\n    0.343, 0.669, 0.119, 0, 0,\n    0.249, 0.626, 0.130, 0, 0,\n    0.172, 0.334, 0.111, 0, 0,\n    0.000, 0.000, 0.000, 1, 0]\n}\n"], "names": ["Filter", "Element", "constructor", "node", "super", "nodeOrNew", "this", "$source", "$sourceAlpha", "$background", "$backgroundAlpha", "$fill", "$stroke", "$autoSetIn", "put", "element", "i", "attr", "id", "remove", "targets", "each", "find", "toString", "Effect", "result", "in", "effect", "_in", "parent", "getAttrSetter", "params", "args", "length", "updateFunctions", "blend", "colorMatrix", "composite", "convolveMatrix", "matrix", "SVGArray", "order", "Math", "sqrt", "split", "kernelMatrix", "diffuseLighting", "displacementMap", "dropShadow", "flood", "g<PERSON><PERSON><PERSON><PERSON><PERSON>", "x", "y", "image", "src", "ns", "xlink", "morphology", "offset", "specularLighting", "tile", "turbulence", "for<PERSON>ach", "name", "utils", "capitalize", "fn", "update", "apply", "prototype", "wrapWithAttrCheck", "call", "unshift", "svg_js", "extend", "merge", "arrayOrFn", "MergeEffect", "Array", "arguments", "child", "MergeNode", "mergeNode", "componentTransfer", "components", "ComponentTransferEffect", "r", "g", "b", "a", "c", "add", "toUpperCase", "_class", "light", "DiffuseLightingEffect", "SpecularLightingEffect", "Defs", "filter", "block", "Container", "defs", "filterWith", "unfilter", "filterer", "reference", "chainingEffects", "in2", "mode", "type", "values", "operator", "surfaceScale", "lightingColor", "diffuseConstant", "kernelUnitLength", "scale", "xChannelSelector", "yChannelSelector", "stdDeviation", "color", "opacity", "arg", "radius", "dx", "dy", "specularExponent", "baseFrequency", "numOctaves", "seed", "stitchTiles", "CompositeEffect", "BlendEffect", "DisplacementMapEffect", "sepiatone"], "mappings": ";;;;;;;;;;;+DAae,MAAMA,eAAeC,EAAAA,QAClCC,YAAaC,GACXC,MAAMC,EAAAA,UAAU,SAAUF,GAAOA,GAEjCG,KAAKC,QAAU,gBACfD,KAAKE,aAAe,cACpBF,KAAKG,YAAc,kBACnBH,KAAKI,iBAAmB,kBACxBJ,KAAKK,MAAQ,YACbL,KAAKM,QAAU,cACfN,KAAKO,YAAa,EAGpBC,IAAKC,EAASC,GAUZ,QATAD,EAAUX,MAAMU,IAAIC,EAASC,IAEhBC,KAAK,OAASX,KAAKO,YAC9BE,EAAQE,KAAK,KAAMX,KAAKC,SAErBQ,EAAQE,KAAK,WAChBF,EAAQE,KAAK,SAAUF,EAAQG,MAG1BH,EAITI,SAKE,OAHAb,KAAKc,UAAUC,KAAK,YAGbjB,MAAMe,SAGfC,UACE,OAAOE,EAAIA,KAAC,iBAAmBhB,KAAKY,KAAO,MAG7CK,WACE,MAAO,QAAUjB,KAAKY,KAAO,KAKjC,MAAMM,UAAevB,EAAAA,QACnBC,YAAaC,EAAMc,GACjBb,MAAMD,EAAMc,GACZX,KAAKmB,OAAOnB,KAAKY,MAGnBQ,GAAIC,GAEF,GAAc,MAAVA,EAAgB,CAClB,MAAMC,EAAMtB,KAAKW,KAAK,MAEtB,OADYX,KAAKuB,UAAYvB,KAAKuB,SAASP,KAAK,YAAYM,OAAS,IACvDA,EAIhB,OAAOtB,KAAKW,KAAK,KAAMU,GAIzBF,OAAQA,GACN,OAAOnB,KAAKW,KAAK,SAAUQ,GAI7BF,WACE,OAAOjB,KAAKmB,UAOhB,MAAMK,EAAiBC,GACd,YAAaC,GAClB,IAAK,IAAIhB,EAAIe,EAAOE,OAAQjB,KACX,MAAXgB,EAAKhB,IACPV,KAAKW,KAAKc,EAAOf,GAAIgB,EAAKhB,KAM5BkB,EAAkB,CACtBC,MAAOL,EAAc,CAAC,KAAM,MAAO,SAEnCM,YAAaN,EAAc,CAAC,OAAQ,WAEpCO,UAAWP,EAAc,CAAC,KAAM,MAAO,aAEvCQ,eAAgB,SAAUC,GACxBA,EAAS,IAAIC,EAAAA,MAASD,GAAQhB,WAE9BjB,KAAKW,KAAK,CACRwB,MAAOC,KAAKC,KAAKJ,EAAOK,MAAM,KAAKX,QACnCY,aAAcN,KAIlBO,gBAAiBhB,EAAc,CAAC,eAAgB,gBAAiB,kBAAmB,qBAEpFiB,gBAAiBjB,EAAc,CAAC,KAAM,MAAO,QAAS,mBAAoB,qBAE1EkB,WAAYlB,EAAc,CAAC,KAAM,KAAM,KAAM,iBAE7CmB,MAAOnB,EAAc,CAAC,cAAe,kBAErCoB,aAAc,SAAUC,EAAI,EAAGC,EAAID,GACjC7C,KAAKW,KAAK,eAAgBkC,EAAI,IAAMC,IAGtCC,MAAO,SAAUC,GACfhD,KAAKW,KAAK,OAAQqC,EAAKC,EAAAA,WAAGC,QAG5BC,WAAY3B,EAAc,CAAC,WAAY,WAEvC4B,OAAQ5B,EAAc,CAAC,KAAM,OAE7B6B,iBAAkB7B,EAAc,CAAC,eAAgB,gBAAiB,kBAAmB,mBAAoB,qBAEzG8B,KAAM9B,EAAc,IAEpB+B,WAAY/B,EAAc,CAAC,gBAAiB,aAAc,OAAQ,cAAe,UAG/D,CAClB,QACA,cACA,oBACA,YACA,iBACA,kBACA,kBACA,aACA,QACA,eACA,QACA,QACA,aACA,SACA,mBACA,OACA,cAIUgC,SAASnC,IACnB,MAAMoC,EAAOC,EAAAA,MAAMC,WAAWtC,GACxBuC,EAAKhC,EAAgBP,GAE3B3B,OAAO+D,EAAO,UAAY,cAAcvC,EACtCtB,YAAaC,GACXC,MAAMC,EAASA,UAAC,KAAO0D,EAAM5D,GAAOA,GAKtCgE,OAAQnC,GAEN,OADAkC,EAAGE,MAAM9D,KAAM0B,GACR1B,OAOXN,OAAOqE,UAAU1C,GAAU2C,EAAiBA,mBAAC,SAAUJ,KAAOlC,GAC5D,MAAML,EAAS,IAAI3B,OAAO+D,EAAO,UAEjC,OAAU,MAANG,EAAmB5D,KAAKQ,IAAIa,IAGd,mBAAPuC,EACTA,EAAGK,KAAK5C,EAAQA,GAGhBK,EAAKwC,QAAQN,GAER5D,KAAKQ,IAAIa,GAAQwC,OAAOnC,UAK7ByC,EAAAC,OAAC1E,OAAQ,CACb2E,MAAOC,GACL,MAAMzE,EAAOG,KAAKQ,IAAI,IAAId,OAAO6E,aAKjC,GAAyB,mBAAdD,EAET,OADAA,EAAUL,KAAKpE,EAAMA,GACdA,EAcT,OAViByE,aAAqBE,MAAQF,EAAY,IAAIG,YAErDjB,SAASkB,IACZA,aAAiBhF,OAAOiF,UAC1B9E,EAAKW,IAAIkE,GAET7E,EAAK+E,UAAUF,MAIZ7E,GAETgF,kBAAmBC,EAAa,IAC9B,MAAMjF,EAAOG,KAAKQ,IAAI,IAAId,OAAOqF,yBAEjC,GAA0B,mBAAfD,EAET,OADAA,EAAWb,KAAKpE,EAAMA,GACfA,EAIT,KAAKiF,EAAWE,GAAMF,EAAWG,GAAMH,EAAWI,GAAMJ,EAAWK,GAAG,CAEpEL,EAAa,CACXE,EAFWF,EAEFG,EAFEH,EAEOI,EAFPJ,EAEgBK,EAFhBL,GAMf,IAAK,MAAMM,KAAKN,EAEdjF,EAAKwF,IAAI,IAAI3F,OAAO,OAAS0F,EAAEE,gBAAeR,EAAWM,KAG3D,OAAOvF,KAIc,CACvB,eACA,aACA,YACA,YACA,QACA,QACA,QACA,SAGe2D,SAASkB,IACxB,MAAMjB,EAAOC,EAAAA,MAAMC,WAAWe,GAC9BhF,OAAO+D,GAAQ,cAAcvC,EAC3BtB,YAAaC,GACXC,MAAMC,EAASA,UAAC,KAAO0D,EAAM5D,GAAOA,QAKnB,CACrB,QACA,QACA,QACA,SAIa2D,SAAQ,SAAU4B,GAC/B,MAAMG,EAAS7F,OAAOgE,EAAKA,MAACC,WAAWyB,IACjCxB,EAAKI,EAAAA,mBAAkB,WAC3B,OAAOhE,KAAKQ,IAAI,IAAI+E,MAGtB7F,OAAOqF,wBAAwBhB,UAAUqB,GAAKxB,KAGjC,CACb,eACA,aACA,aAIKJ,SAASgC,IACd,MAAMD,EAAS7F,OAAOgE,EAAKA,MAACC,WAAW6B,IACjC5B,EAAKI,EAAAA,mBAAkB,WAC3B,OAAOhE,KAAKQ,IAAI,IAAI+E,MAGtB7F,OAAO+F,sBAAsB1B,UAAUyB,GAAS5B,EAChDlE,OAAOgG,uBAAuB3B,UAAUyB,GAAS5B,KAGnDQ,EAAAA,OAAO1E,OAAO6E,YAAa,CACzBK,UAAWtD,GACT,OAAOtB,KAAKQ,IAAI,IAAId,OAAOiF,WAAahE,KAAK,KAAMW,MAKjD6C,EAAAC,OAACuB,OAAM,CAEXC,OAAQ,SAAUC,GAChB,MAAMD,EAAS5F,KAAKQ,IAAI,IAAId,QAK5B,MAFqB,mBAAVmG,GAAwBA,EAAM5B,KAAK2B,EAAQA,GAE/CA,KAILzB,EAAAC,OAAC0B,YAAW,CAEhBF,OAAQ,SAAUC,GAChB,OAAO7F,KAAK+F,OAAOH,OAAOC,MAIxB1B,EAAAC,OAACzE,UAAS,CAEdqG,WAAY,SAAUH,GACpB,MAAMD,EAASC,aAAiBnG,OAC5BmG,EACA7F,KAAK+F,OAAOH,OAAOC,GAEvB,OAAO7F,KAAKW,KAAK,SAAUiF,IAG7BK,SAAU,SAAUpF,GAElB,OAAOb,KAAKW,KAAK,SAAU,OAE7BuF,WACE,OAAOlG,KAAKmG,UAAU,aAK1B,MAAMC,EAAkB,CAEtBvE,MAAO,SAAUwE,EAAKC,GACpB,OAAOtG,KAAKuB,UAAYvB,KAAKuB,SAASM,MAAM7B,KAAMqG,EAAKC,IAGzDxE,YAAa,SAAUyE,EAAMC,GAC3B,OAAOxG,KAAKuB,UAAYvB,KAAKuB,SAASO,YAAYyE,EAAMC,GAAQpF,GAAGpB,OAGrE6E,kBAAmB,SAAUC,GAC3B,OAAO9E,KAAKuB,UAAYvB,KAAKuB,SAASsD,kBAAkBC,GAAY1D,GAAGpB,OAGzE+B,UAAW,SAAUsE,EAAKI,GACxB,OAAOzG,KAAKuB,UAAYvB,KAAKuB,SAASQ,UAAU/B,KAAMqG,EAAKI,IAG7DzE,eAAgB,SAAUC,GACxB,OAAOjC,KAAKuB,UAAYvB,KAAKuB,SAASS,eAAeC,GAAQb,GAAGpB,OAGlEwC,gBAAiB,SAAUkE,EAAcC,EAAeC,EAAiBC,GACvE,OAAO7G,KAAKuB,UAAYvB,KAAKuB,SAASiB,gBAAgBkE,EAAcE,EAAiBC,GAAkBzF,GAAGpB,OAG5GyC,gBAAiB,SAAU4D,EAAKS,EAAOC,EAAkBC,GACvD,OAAOhH,KAAKuB,UAAYvB,KAAKuB,SAASkB,gBAAgBzC,KAAMqG,EAAKS,EAAOC,EAAkBC,IAG5FtE,WAAY,SAAUG,EAAGC,EAAGmE,GAC1B,OAAOjH,KAAKuB,UAAYvB,KAAKuB,SAASmB,WAAW1C,KAAM6C,EAAGC,EAAGmE,GAAc7F,GAAGpB,OAGhF2C,MAAO,SAAUuE,EAAOC,GACtB,OAAOnH,KAAKuB,UAAYvB,KAAKuB,SAASoB,MAAMuE,EAAOC,IAGrDvE,aAAc,SAAUC,EAAGC,GACzB,OAAO9C,KAAKuB,UAAYvB,KAAKuB,SAASqB,aAAaC,EAAGC,GAAG1B,GAAGpB,OAG9D+C,MAAO,SAAUC,GACf,OAAOhD,KAAKuB,UAAYvB,KAAKuB,SAASwB,MAAMC,IAG9CqB,MAAO,SAAU+C,GAEf,OADAA,EAAMA,aAAe5C,MAAQ4C,EAAM,IAAIA,GAChCpH,KAAKuB,UAAYvB,KAAKuB,SAAS8C,MAAMrE,QAASoH,IAGvDjE,WAAY,SAAUsD,EAAUY,GAC9B,OAAOrH,KAAKuB,UAAYvB,KAAKuB,SAAS4B,WAAWsD,EAAUY,GAAQjG,GAAGpB,OAGxEoD,OAAQ,SAAUkE,EAAIC,GACpB,OAAOvH,KAAKuB,UAAYvB,KAAKuB,SAAS6B,OAAOkE,EAAIC,GAAInG,GAAGpB,OAG1DqD,iBAAkB,SAAUqD,EAAcC,EAAeC,EAAiBY,EAAkBX,GAC1F,OAAO7G,KAAKuB,UAAYvB,KAAKuB,SAAS8B,iBAAiBqD,EAAcE,EAAiBY,EAAkBX,GAAkBzF,GAAGpB,OAG/HsD,KAAM,WACJ,OAAOtD,KAAKuB,UAAYvB,KAAKuB,SAAS+B,OAAOlC,GAAGpB,OAGlDuD,WAAY,SAAUkE,EAAeC,EAAYC,EAAMC,EAAarB,GAClE,OAAOvG,KAAKuB,UAAYvB,KAAKuB,SAASgC,WAAWkE,EAAeC,EAAYC,EAAMC,EAAarB,GAAMnF,GAAGpB,eAI5GoE,EAAAA,OAAOlD,EAAQkF,GAGfhC,EAAAA,OAAO1E,OAAO6E,YAAa,CACzBnD,GAAI,SAAUC,GAOZ,OANIA,aAAkB3B,OAAOiF,UAC3B3E,KAAKqF,IAAIhE,EAAQ,GAEjBrB,KAAKqF,KAAI,IAAI3F,OAAOiF,WAAYvD,GAAGC,GAAS,GAGvCrB,QAIXoE,EAAAA,OAAO,CAAC1E,OAAOmI,gBAAiBnI,OAAOoI,YAAapI,OAAOqI,uBAAwB,CACjF1B,IAAK,SAAUhF,GACb,GAAc,MAAVA,EAAgB,CAClB,MAAMgF,EAAMrG,KAAKW,KAAK,OAEtB,OADYX,KAAKuB,UAAYvB,KAAKuB,SAASP,KAAK,YAAYqF,OAAS,IACvDA,EAEhB,OAAOrG,KAAKW,KAAK,MAAOU,MAK5B3B,OAAOkG,OAAS,CACdoC,UAAW,CACT,KAAO,KAAO,KAAO,EAAG,EACxB,KAAO,KAAO,IAAO,EAAG,EACxB,KAAO,KAAO,KAAO,EAAG,EACxB,EAAO,EAAO,EAAO,EAAG"}
/*! @svgdotjs/svg.filter.js v3.0.9 MIT*/;
/*!
* @svgdotjs/svg.filter.js - A plugin for svg.js adding filter functionality
* @version 3.0.9
* https://github.com/svgdotjs/svg.filter.js
*
* @copyright Wout Fierens
* @license MIT
*
* BUILT: Mon Feb 24 2025 17:15:33 GMT+0100 (Central European Standard Time)
*/
this.SVG=this.SVG||{},this.SVG.Filter=function(t){"use strict";class Filter extends t.Element{constructor(e){super(t.nodeOrNew("filter",e),e),this.$source="SourceGraphic",this.$sourceAlpha="SourceAlpha",this.$background="BackgroundImage",this.$backgroundAlpha="BackgroundAlpha",this.$fill="FillPaint",this.$stroke="StrokePaint",this.$autoSetIn=!0}put(t,e){return!(t=super.put(t,e)).attr("in")&&this.$autoSetIn&&t.attr("in",this.$source),t.attr("result")||t.attr("result",t.id()),t}remove(){return this.targets().each("unfilter"),super.remove()}targets(){return t.find('svg [filter*="'+this.id()+'"]')}toString(){return"url(#"+this.id()+")"}}class e extends t.Element{constructor(t,e){super(t,e),this.result(this.id())}in(t){if(null==t){const t=this.attr("in");return this.parent()&&this.parent().find(`[result="${t}"]`)[0]||t}return this.attr("in",t)}result(t){return this.attr("result",t)}toString(){return this.result()}}const n=t=>function(...e){for(let n=t.length;n--;)null!=e[n]&&this.attr(t[n],e[n])},r={blend:n(["in","in2","mode"]),colorMatrix:n(["type","values"]),composite:n(["in","in2","operator"]),convolveMatrix:function(e){e=new t.Array(e).toString(),this.attr({order:Math.sqrt(e.split(" ").length),kernelMatrix:e})},diffuseLighting:n(["surfaceScale","lightingColor","diffuseConstant","kernelUnitLength"]),displacementMap:n(["in","in2","scale","xChannelSelector","yChannelSelector"]),dropShadow:n(["in","dx","dy","stdDeviation"]),flood:n(["flood-color","flood-opacity"]),gaussianBlur:function(t=0,e=t){this.attr("stdDeviation",t+" "+e)},image:function(e){this.attr("href",e,t.namespaces.xlink)},morphology:n(["operator","radius"]),offset:n(["dx","dy"]),specularLighting:n(["surfaceScale","lightingColor","diffuseConstant","specularExponent","kernelUnitLength"]),tile:n([]),turbulence:n(["baseFrequency","numOctaves","seed","stitchTiles","type"])};["blend","colorMatrix","componentTransfer","composite","convolveMatrix","diffuseLighting","displacementMap","dropShadow","flood","gaussianBlur","image","merge","morphology","offset","specularLighting","tile","turbulence"].forEach((n=>{const i=t.utils.capitalize(n),s=r[n];Filter[i+"Effect"]=class extends e{constructor(e){super(t.nodeOrNew("fe"+i,e),e)}update(t){return s.apply(this,t),this}},Filter.prototype[n]=t.wrapWithAttrCheck((function(t,...e){const n=new Filter[i+"Effect"];return null==t?this.put(n):("function"==typeof t?t.call(n,n):e.unshift(t),this.put(n).update(e))}))})),t.extend(Filter,{merge(t){const e=this.put(new Filter.MergeEffect);if("function"==typeof t)return t.call(e,e),e;return(t instanceof Array?t:[...arguments]).forEach((t=>{t instanceof Filter.MergeNode?e.put(t):e.mergeNode(t)})),e},componentTransfer(t={}){const e=this.put(new Filter.ComponentTransferEffect);if("function"==typeof t)return t.call(e,e),e;if(!(t.r||t.g||t.b||t.a)){t={r:t,g:t,b:t,a:t}}for(const n in t)e.add(new(Filter["Func"+n.toUpperCase()])(t[n]));return e}});["distantLight","pointLight","spotLight","mergeNode","FuncR","FuncG","FuncB","FuncA"].forEach((n=>{const r=t.utils.capitalize(n);Filter[r]=class extends e{constructor(e){super(t.nodeOrNew("fe"+r,e),e)}}}));["funcR","funcG","funcB","funcA"].forEach((function(e){const n=Filter[t.utils.capitalize(e)],r=t.wrapWithAttrCheck((function(){return this.put(new n)}));Filter.ComponentTransferEffect.prototype[e]=r}));["distantLight","pointLight","spotLight"].forEach((e=>{const n=Filter[t.utils.capitalize(e)],r=t.wrapWithAttrCheck((function(){return this.put(new n)}));Filter.DiffuseLightingEffect.prototype[e]=r,Filter.SpecularLightingEffect.prototype[e]=r})),t.extend(Filter.MergeEffect,{mergeNode(t){return this.put(new Filter.MergeNode).attr("in",t)}}),t.extend(t.Defs,{filter:function(t){const e=this.put(new Filter);return"function"==typeof t&&t.call(e,e),e}}),t.extend(t.Container,{filter:function(t){return this.defs().filter(t)}}),t.extend(t.Element,{filterWith:function(t){const e=t instanceof Filter?t:this.defs().filter(t);return this.attr("filter",e)},unfilter:function(t){return this.attr("filter",null)},filterer(){return this.reference("filter")}});const i={blend:function(t,e){return this.parent()&&this.parent().blend(this,t,e)},colorMatrix:function(t,e){return this.parent()&&this.parent().colorMatrix(t,e).in(this)},componentTransfer:function(t){return this.parent()&&this.parent().componentTransfer(t).in(this)},composite:function(t,e){return this.parent()&&this.parent().composite(this,t,e)},convolveMatrix:function(t){return this.parent()&&this.parent().convolveMatrix(t).in(this)},diffuseLighting:function(t,e,n,r){return this.parent()&&this.parent().diffuseLighting(t,n,r).in(this)},displacementMap:function(t,e,n,r){return this.parent()&&this.parent().displacementMap(this,t,e,n,r)},dropShadow:function(t,e,n){return this.parent()&&this.parent().dropShadow(this,t,e,n).in(this)},flood:function(t,e){return this.parent()&&this.parent().flood(t,e)},gaussianBlur:function(t,e){return this.parent()&&this.parent().gaussianBlur(t,e).in(this)},image:function(t){return this.parent()&&this.parent().image(t)},merge:function(t){return t=t instanceof Array?t:[...t],this.parent()&&this.parent().merge(this,...t)},morphology:function(t,e){return this.parent()&&this.parent().morphology(t,e).in(this)},offset:function(t,e){return this.parent()&&this.parent().offset(t,e).in(this)},specularLighting:function(t,e,n,r,i){return this.parent()&&this.parent().specularLighting(t,n,r,i).in(this)},tile:function(){return this.parent()&&this.parent().tile().in(this)},turbulence:function(t,e,n,r,i){return this.parent()&&this.parent().turbulence(t,e,n,r,i).in(this)}};return t.extend(e,i),t.extend(Filter.MergeEffect,{in:function(t){return t instanceof Filter.MergeNode?this.add(t,0):this.add((new Filter.MergeNode).in(t),0),this}}),t.extend([Filter.CompositeEffect,Filter.BlendEffect,Filter.DisplacementMapEffect],{in2:function(t){if(null==t){const t=this.attr("in2");return this.parent()&&this.parent().find(`[result="${t}"]`)[0]||t}return this.attr("in2",t)}}),Filter.filter={sepiatone:[.343,.669,.119,0,0,.249,.626,.13,0,0,.172,.334,.111,0,0,0,0,0,1,0]},Filter}(SVG);
//# sourceMappingURL=svg.filter.min.js.map

{"version": 3, "file": "svg.esm.js", "sources": ["../src/utils/methods.js", "../src/utils/utils.js", "../src/modules/core/namespaces.js", "../src/utils/window.js", "../src/types/Base.js", "../src/utils/adopter.js", "../src/modules/optional/arrange.js", "../src/modules/core/regex.js", "../src/modules/optional/class.js", "../src/modules/optional/css.js", "../src/modules/optional/data.js", "../src/modules/optional/memory.js", "../src/types/Color.js", "../src/types/Point.js", "../src/types/Matrix.js", "../src/modules/core/parser.js", "../src/types/Box.js", "../src/types/List.js", "../src/modules/core/selector.js", "../src/modules/core/event.js", "../src/types/EventTarget.js", "../src/modules/core/defaults.js", "../src/types/SVGArray.js", "../src/types/SVGNumber.js", "../src/modules/core/attr.js", "../src/elements/Dom.js", "../src/elements/Element.js", "../src/modules/optional/sugar.js", "../src/modules/optional/transform.js", "../src/elements/Container.js", "../src/elements/Defs.js", "../src/elements/Shape.js", "../src/modules/core/circled.js", "../src/elements/Ellipse.js", "../src/elements/Fragment.js", "../src/modules/core/gradiented.js", "../src/elements/Gradient.js", "../src/elements/Pattern.js", "../src/elements/Image.js", "../src/types/PointArray.js", "../src/modules/core/pointed.js", "../src/elements/Line.js", "../src/elements/Marker.js", "../src/animation/Controller.js", "../src/utils/pathParser.js", "../src/types/PathArray.js", "../src/animation/Morphable.js", "../src/elements/Path.js", "../src/modules/core/poly.js", "../src/elements/Polygon.js", "../src/elements/Polyline.js", "../src/elements/Rect.js", "../src/animation/Queue.js", "../src/animation/Animator.js", "../src/animation/Timeline.js", "../src/animation/Runner.js", "../src/elements/Svg.js", "../src/elements/Symbol.js", "../src/modules/core/textable.js", "../src/elements/Text.js", "../src/elements/Tspan.js", "../src/elements/Circle.js", "../src/elements/ClipPath.js", "../src/elements/ForeignObject.js", "../src/modules/core/containerGeometry.js", "../src/elements/G.js", "../src/elements/A.js", "../src/elements/Mask.js", "../src/elements/Stop.js", "../src/elements/Style.js", "../src/elements/TextPath.js", "../src/elements/Use.js", "../src/main.js"], "sourcesContent": ["const methods = {}\nconst names = []\n\nexport function registerMethods(name, m) {\n  if (Array.isArray(name)) {\n    for (const _name of name) {\n      registerMethods(_name, m)\n    }\n    return\n  }\n\n  if (typeof name === 'object') {\n    for (const _name in name) {\n      registerMethods(_name, name[_name])\n    }\n    return\n  }\n\n  addMethodNames(Object.getOwnPropertyNames(m))\n  methods[name] = Object.assign(methods[name] || {}, m)\n}\n\nexport function getMethodsFor(name) {\n  return methods[name] || {}\n}\n\nexport function getMethodNames() {\n  return [...new Set(names)]\n}\n\nexport function addMethodNames(_names) {\n  names.push(..._names)\n}\n", "// Map function\nexport function map(array, block) {\n  let i\n  const il = array.length\n  const result = []\n\n  for (i = 0; i < il; i++) {\n    result.push(block(array[i]))\n  }\n\n  return result\n}\n\n// Filter function\nexport function filter(array, block) {\n  let i\n  const il = array.length\n  const result = []\n\n  for (i = 0; i < il; i++) {\n    if (block(array[i])) {\n      result.push(array[i])\n    }\n  }\n\n  return result\n}\n\n// Degrees to radians\nexport function radians(d) {\n  return ((d % 360) * Math.PI) / 180\n}\n\n// Radians to degrees\nexport function degrees(r) {\n  return ((r * 180) / Math.PI) % 360\n}\n\n// Convert camel cased string to dash separated\nexport function unCamelCase(s) {\n  return s.replace(/([A-Z])/g, function (m, g) {\n    return '-' + g.toLowerCase()\n  })\n}\n\n// Capitalize first letter of a string\nexport function capitalize(s) {\n  return s.charAt(0).toUpperCase() + s.slice(1)\n}\n\n// Calculate proportional width and height values when necessary\nexport function proportionalSize(element, width, height, box) {\n  if (width == null || height == null) {\n    box = box || element.bbox()\n\n    if (width == null) {\n      width = (box.width / box.height) * height\n    } else if (height == null) {\n      height = (box.height / box.width) * width\n    }\n  }\n\n  return {\n    width: width,\n    height: height\n  }\n}\n\n/**\n * This function adds support for string origins.\n * It searches for an origin in o.origin o.ox and o.originX.\n * This way, origin: {x: 'center', y: 50} can be passed as well as ox: 'center', oy: 50\n **/\nexport function getOrigin(o, element) {\n  const origin = o.origin\n  // First check if origin is in ox or originX\n  let ox = o.ox != null ? o.ox : o.originX != null ? o.originX : 'center'\n  let oy = o.oy != null ? o.oy : o.originY != null ? o.originY : 'center'\n\n  // Then check if origin was used and overwrite in that case\n  if (origin != null) {\n    ;[ox, oy] = Array.isArray(origin)\n      ? origin\n      : typeof origin === 'object'\n        ? [origin.x, origin.y]\n        : [origin, origin]\n  }\n\n  // Make sure to only call bbox when actually needed\n  const condX = typeof ox === 'string'\n  const condY = typeof oy === 'string'\n  if (condX || condY) {\n    const { height, width, x, y } = element.bbox()\n\n    // And only overwrite if string was passed for this specific axis\n    if (condX) {\n      ox = ox.includes('left')\n        ? x\n        : ox.includes('right')\n          ? x + width\n          : x + width / 2\n    }\n\n    if (condY) {\n      oy = oy.includes('top')\n        ? y\n        : oy.includes('bottom')\n          ? y + height\n          : y + height / 2\n    }\n  }\n\n  // Return the origin as it is if it wasn't a string\n  return [ox, oy]\n}\n\nconst descriptiveElements = new Set(['desc', 'metadata', 'title'])\nexport const isDescriptive = (element) =>\n  descriptiveElements.has(element.nodeName)\n\nexport const writeDataToDom = (element, data, defaults = {}) => {\n  const cloned = { ...data }\n\n  for (const key in cloned) {\n    if (cloned[key].valueOf() === defaults[key]) {\n      delete cloned[key]\n    }\n  }\n\n  if (Object.keys(cloned).length) {\n    element.node.setAttribute('data-svgjs', JSON.stringify(cloned)) // see #428\n  } else {\n    element.node.removeAttribute('data-svgjs')\n    element.node.removeAttribute('svgjs:data')\n  }\n}\n", "// Default namespaces\nexport const svg = 'http://www.w3.org/2000/svg'\nexport const html = 'http://www.w3.org/1999/xhtml'\nexport const xmlns = 'http://www.w3.org/2000/xmlns/'\nexport const xlink = 'http://www.w3.org/1999/xlink'\n", "export const globals = {\n  window: typeof window === 'undefined' ? null : window,\n  document: typeof document === 'undefined' ? null : document\n}\n\nexport function registerWindow(win = null, doc = null) {\n  globals.window = win\n  globals.document = doc\n}\n\nconst save = {}\n\nexport function saveWindow() {\n  save.window = globals.window\n  save.document = globals.document\n}\n\nexport function restoreWindow() {\n  globals.window = save.window\n  globals.document = save.document\n}\n\nexport function withWindow(win, fn) {\n  saveWindow()\n  registerWindow(win, win.document)\n  fn(win, win.document)\n  restoreWindow()\n}\n\nexport function getWindow() {\n  return globals.window\n}\n", "export default class Base {\n  // constructor (node/*, {extensions = []} */) {\n  //   // this.tags = []\n  //   //\n  //   // for (let extension of extensions) {\n  //   //   extension.setup.call(this, node)\n  //   //   this.tags.push(extension.name)\n  //   // }\n  // }\n}\n", "import { addMethodNames } from './methods.js'\nimport { capitalize } from './utils.js'\nimport { svg } from '../modules/core/namespaces.js'\nimport { globals } from '../utils/window.js'\nimport Base from '../types/Base.js'\n\nconst elements = {}\nexport const root = '___SYMBOL___ROOT___'\n\n// Method for element creation\nexport function create(name, ns = svg) {\n  // create element\n  return globals.document.createElementNS(ns, name)\n}\n\nexport function makeInstance(element, isHTML = false) {\n  if (element instanceof Base) return element\n\n  if (typeof element === 'object') {\n    return adopter(element)\n  }\n\n  if (element == null) {\n    return new elements[root]()\n  }\n\n  if (typeof element === 'string' && element.charAt(0) !== '<') {\n    return adopter(globals.document.querySelector(element))\n  }\n\n  // Make sure, that HTML elements are created with the correct namespace\n  const wrapper = isHTML ? globals.document.createElement('div') : create('svg')\n  wrapper.innerHTML = element\n\n  // We can use firstChild here because we know,\n  // that the first char is < and thus an element\n  element = adopter(wrapper.firstChild)\n\n  // make sure, that element doesn't have its wrapper attached\n  wrapper.removeChild(wrapper.firstChild)\n  return element\n}\n\nexport function nodeOrNew(name, node) {\n  return node &&\n    (node instanceof globals.window.Node ||\n      (node.ownerDocument &&\n        node instanceof node.ownerDocument.defaultView.Node))\n    ? node\n    : create(name)\n}\n\n// Adopt existing svg elements\nexport function adopt(node) {\n  // check for presence of node\n  if (!node) return null\n\n  // make sure a node isn't already adopted\n  if (node.instance instanceof Base) return node.instance\n\n  if (node.nodeName === '#document-fragment') {\n    return new elements.Fragment(node)\n  }\n\n  // initialize variables\n  let className = capitalize(node.nodeName || 'Dom')\n\n  // Make sure that gradients are adopted correctly\n  if (className === 'LinearGradient' || className === 'RadialGradient') {\n    className = 'Gradient'\n\n    // Fallback to Dom if element is not known\n  } else if (!elements[className]) {\n    className = 'Dom'\n  }\n\n  return new elements[className](node)\n}\n\nlet adopter = adopt\n\nexport function mockAdopt(mock = adopt) {\n  adopter = mock\n}\n\nexport function register(element, name = element.name, asRoot = false) {\n  elements[name] = element\n  if (asRoot) elements[root] = element\n\n  addMethodNames(Object.getOwnPropertyNames(element.prototype))\n\n  return element\n}\n\nexport function getClass(name) {\n  return elements[name]\n}\n\n// Element id sequence\nlet did = 1000\n\n// Get next named element id\nexport function eid(name) {\n  return 'Svgjs' + capitalize(name) + did++\n}\n\n// Deep new id assignment\nexport function assignNewId(node) {\n  // do the same for SVG child nodes as well\n  for (let i = node.children.length - 1; i >= 0; i--) {\n    assignNewId(node.children[i])\n  }\n\n  if (node.id) {\n    node.id = eid(node.nodeName)\n    return node\n  }\n\n  return node\n}\n\n// Method for extending objects\nexport function extend(modules, methods) {\n  let key, i\n\n  modules = Array.isArray(modules) ? modules : [modules]\n\n  for (i = modules.length - 1; i >= 0; i--) {\n    for (key in methods) {\n      modules[i].prototype[key] = methods[key]\n    }\n  }\n}\n\nexport function wrapWithAttrCheck(fn) {\n  return function (...args) {\n    const o = args[args.length - 1]\n\n    if (o && o.constructor === Object && !(o instanceof Array)) {\n      return fn.apply(this, args.slice(0, -1)).attr(o)\n    } else {\n      return fn.apply(this, args)\n    }\n  }\n}\n", "import { makeInstance } from '../../utils/adopter.js'\nimport { registerMethods } from '../../utils/methods.js'\n\n// Get all siblings, including myself\nexport function siblings() {\n  return this.parent().children()\n}\n\n// Get the current position siblings\nexport function position() {\n  return this.parent().index(this)\n}\n\n// Get the next element (will return null if there is none)\nexport function next() {\n  return this.siblings()[this.position() + 1]\n}\n\n// Get the next element (will return null if there is none)\nexport function prev() {\n  return this.siblings()[this.position() - 1]\n}\n\n// Send given element one step forward\nexport function forward() {\n  const i = this.position()\n  const p = this.parent()\n\n  // move node one step forward\n  p.add(this.remove(), i + 1)\n\n  return this\n}\n\n// Send given element one step backward\nexport function backward() {\n  const i = this.position()\n  const p = this.parent()\n\n  p.add(this.remove(), i ? i - 1 : 0)\n\n  return this\n}\n\n// Send given element all the way to the front\nexport function front() {\n  const p = this.parent()\n\n  // Move node forward\n  p.add(this.remove())\n\n  return this\n}\n\n// Send given element all the way to the back\nexport function back() {\n  const p = this.parent()\n\n  // Move node back\n  p.add(this.remove(), 0)\n\n  return this\n}\n\n// Inserts a given element before the targeted element\nexport function before(element) {\n  element = makeInstance(element)\n  element.remove()\n\n  const i = this.position()\n\n  this.parent().add(element, i)\n\n  return this\n}\n\n// Inserts a given element after the targeted element\nexport function after(element) {\n  element = makeInstance(element)\n  element.remove()\n\n  const i = this.position()\n\n  this.parent().add(element, i + 1)\n\n  return this\n}\n\nexport function insertBefore(element) {\n  element = makeInstance(element)\n  element.before(this)\n  return this\n}\n\nexport function insertAfter(element) {\n  element = makeInstance(element)\n  element.after(this)\n  return this\n}\n\nregisterMethods('Dom', {\n  siblings,\n  position,\n  next,\n  prev,\n  forward,\n  backward,\n  front,\n  back,\n  before,\n  after,\n  insertBefore,\n  insertAfter\n})\n", "// Parse unit value\nexport const numberAndUnit =\n  /^([+-]?(\\d+(\\.\\d*)?|\\.\\d+)(e[+-]?\\d+)?)([a-z%]*)$/i\n\n// Parse hex value\nexport const hex = /^#?([a-f\\d]{2})([a-f\\d]{2})([a-f\\d]{2})$/i\n\n// Parse rgb value\nexport const rgb = /rgb\\((\\d+),(\\d+),(\\d+)\\)/\n\n// Parse reference id\nexport const reference = /(#[a-z_][a-z0-9\\-_]*)/i\n\n// splits a transformation chain\nexport const transforms = /\\)\\s*,?\\s*/\n\n// Whitespace\nexport const whitespace = /\\s/g\n\n// Test hex value\nexport const isHex = /^#[a-f0-9]{3}$|^#[a-f0-9]{6}$/i\n\n// Test rgb value\nexport const isRgb = /^rgb\\(/\n\n// Test for blank string\nexport const isBlank = /^(\\s+)?$/\n\n// Test for numeric string\nexport const isNumber = /^[+-]?(\\d+(\\.\\d*)?|\\.\\d+)(e[+-]?\\d+)?$/i\n\n// Test for image url\nexport const isImage = /\\.(jpg|jpeg|png|gif|svg)(\\?[^=]+.*)?/i\n\n// split at whitespace and comma\nexport const delimiter = /[\\s,]+/\n\n// Test for path letter\nexport const isPathLetter = /[MLHVCSQTAZ]/i\n", "import { delimiter } from '../core/regex.js'\nimport { registerMethods } from '../../utils/methods.js'\n\n// Return array of classes on the node\nexport function classes() {\n  const attr = this.attr('class')\n  return attr == null ? [] : attr.trim().split(delimiter)\n}\n\n// Return true if class exists on the node, false otherwise\nexport function hasClass(name) {\n  return this.classes().indexOf(name) !== -1\n}\n\n// Add class to the node\nexport function addClass(name) {\n  if (!this.hasClass(name)) {\n    const array = this.classes()\n    array.push(name)\n    this.attr('class', array.join(' '))\n  }\n\n  return this\n}\n\n// Remove class from the node\nexport function removeClass(name) {\n  if (this.hasClass(name)) {\n    this.attr(\n      'class',\n      this.classes()\n        .filter(function (c) {\n          return c !== name\n        })\n        .join(' ')\n    )\n  }\n\n  return this\n}\n\n// Toggle the presence of a class on the node\nexport function toggleClass(name) {\n  return this.hasClass(name) ? this.removeClass(name) : this.addClass(name)\n}\n\nregisterMethods('Dom', {\n  classes,\n  hasClass,\n  addClass,\n  removeClass,\n  toggleClass\n})\n", "import { isBlank } from '../core/regex.js'\nimport { registerMethods } from '../../utils/methods.js'\n\n// Dynamic style generator\nexport function css(style, val) {\n  const ret = {}\n  if (arguments.length === 0) {\n    // get full style as object\n    this.node.style.cssText\n      .split(/\\s*;\\s*/)\n      .filter(function (el) {\n        return !!el.length\n      })\n      .forEach(function (el) {\n        const t = el.split(/\\s*:\\s*/)\n        ret[t[0]] = t[1]\n      })\n    return ret\n  }\n\n  if (arguments.length < 2) {\n    // get style properties as array\n    if (Array.isArray(style)) {\n      for (const name of style) {\n        const cased = name\n        ret[name] = this.node.style.getPropertyValue(cased)\n      }\n      return ret\n    }\n\n    // get style for property\n    if (typeof style === 'string') {\n      return this.node.style.getPropertyValue(style)\n    }\n\n    // set styles in object\n    if (typeof style === 'object') {\n      for (const name in style) {\n        // set empty string if null/undefined/'' was given\n        this.node.style.setProperty(\n          name,\n          style[name] == null || isBlank.test(style[name]) ? '' : style[name]\n        )\n      }\n    }\n  }\n\n  // set style for property\n  if (arguments.length === 2) {\n    this.node.style.setProperty(\n      style,\n      val == null || isBlank.test(val) ? '' : val\n    )\n  }\n\n  return this\n}\n\n// Show element\nexport function show() {\n  return this.css('display', '')\n}\n\n// Hide element\nexport function hide() {\n  return this.css('display', 'none')\n}\n\n// Is element visible?\nexport function visible() {\n  return this.css('display') !== 'none'\n}\n\nregisterMethods('Dom', {\n  css,\n  show,\n  hide,\n  visible\n})\n", "import { registerMethods } from '../../utils/methods.js'\nimport { filter, map } from '../../utils/utils.js'\n\n// Store data values on svg nodes\nexport function data(a, v, r) {\n  if (a == null) {\n    // get an object of attributes\n    return this.data(\n      map(\n        filter(\n          this.node.attributes,\n          (el) => el.nodeName.indexOf('data-') === 0\n        ),\n        (el) => el.nodeName.slice(5)\n      )\n    )\n  } else if (a instanceof Array) {\n    const data = {}\n    for (const key of a) {\n      data[key] = this.data(key)\n    }\n    return data\n  } else if (typeof a === 'object') {\n    for (v in a) {\n      this.data(v, a[v])\n    }\n  } else if (arguments.length < 2) {\n    try {\n      return JSON.parse(this.attr('data-' + a))\n    } catch (e) {\n      return this.attr('data-' + a)\n    }\n  } else {\n    this.attr(\n      'data-' + a,\n      v === null\n        ? null\n        : r === true || typeof v === 'string' || typeof v === 'number'\n          ? v\n          : JSON.stringify(v)\n    )\n  }\n\n  return this\n}\n\nregisterMethods('Dom', { data })\n", "import { registerMethods } from '../../utils/methods.js'\n\n// Remember arbitrary data\nexport function remember(k, v) {\n  // remember every item in an object individually\n  if (typeof arguments[0] === 'object') {\n    for (const key in k) {\n      this.remember(key, k[key])\n    }\n  } else if (arguments.length === 1) {\n    // retrieve memory\n    return this.memory()[k]\n  } else {\n    // store memory\n    this.memory()[k] = v\n  }\n\n  return this\n}\n\n// Erase a given memory\nexport function forget() {\n  if (arguments.length === 0) {\n    this._memory = {}\n  } else {\n    for (let i = arguments.length - 1; i >= 0; i--) {\n      delete this.memory()[arguments[i]]\n    }\n  }\n  return this\n}\n\n// This triggers creation of a new hidden class which is not performant\n// However, this function is not rarely used so it will not happen frequently\n// Return local memory object\nexport function memory() {\n  return (this._memory = this._memory || {})\n}\n\nregisterMethods('Dom', { remember, forget, memory })\n", "import { hex, isHex, isRgb, rgb, whitespace } from '../modules/core/regex.js'\n\nfunction sixDigitHex(hex) {\n  return hex.length === 4\n    ? [\n        '#',\n        hex.substring(1, 2),\n        hex.substring(1, 2),\n        hex.substring(2, 3),\n        hex.substring(2, 3),\n        hex.substring(3, 4),\n        hex.substring(3, 4)\n      ].join('')\n    : hex\n}\n\nfunction componentHex(component) {\n  const integer = Math.round(component)\n  const bounded = Math.max(0, Math.min(255, integer))\n  const hex = bounded.toString(16)\n  return hex.length === 1 ? '0' + hex : hex\n}\n\nfunction is(object, space) {\n  for (let i = space.length; i--; ) {\n    if (object[space[i]] == null) {\n      return false\n    }\n  }\n  return true\n}\n\nfunction getParameters(a, b) {\n  const params = is(a, 'rgb')\n    ? { _a: a.r, _b: a.g, _c: a.b, _d: 0, space: 'rgb' }\n    : is(a, 'xyz')\n      ? { _a: a.x, _b: a.y, _c: a.z, _d: 0, space: 'xyz' }\n      : is(a, 'hsl')\n        ? { _a: a.h, _b: a.s, _c: a.l, _d: 0, space: 'hsl' }\n        : is(a, 'lab')\n          ? { _a: a.l, _b: a.a, _c: a.b, _d: 0, space: 'lab' }\n          : is(a, 'lch')\n            ? { _a: a.l, _b: a.c, _c: a.h, _d: 0, space: 'lch' }\n            : is(a, 'cmyk')\n              ? { _a: a.c, _b: a.m, _c: a.y, _d: a.k, space: 'cmyk' }\n              : { _a: 0, _b: 0, _c: 0, space: 'rgb' }\n\n  params.space = b || params.space\n  return params\n}\n\nfunction cieSpace(space) {\n  if (space === 'lab' || space === 'xyz' || space === 'lch') {\n    return true\n  } else {\n    return false\n  }\n}\n\nfunction hueToRgb(p, q, t) {\n  if (t < 0) t += 1\n  if (t > 1) t -= 1\n  if (t < 1 / 6) return p + (q - p) * 6 * t\n  if (t < 1 / 2) return q\n  if (t < 2 / 3) return p + (q - p) * (2 / 3 - t) * 6\n  return p\n}\n\nexport default class Color {\n  constructor(...inputs) {\n    this.init(...inputs)\n  }\n\n  // Test if given value is a color\n  static isColor(color) {\n    return (\n      color && (color instanceof Color || this.isRgb(color) || this.test(color))\n    )\n  }\n\n  // Test if given value is an rgb object\n  static isRgb(color) {\n    return (\n      color &&\n      typeof color.r === 'number' &&\n      typeof color.g === 'number' &&\n      typeof color.b === 'number'\n    )\n  }\n\n  /*\n  Generating random colors\n  */\n  static random(mode = 'vibrant', t) {\n    // Get the math modules\n    const { random, round, sin, PI: pi } = Math\n\n    // Run the correct generator\n    if (mode === 'vibrant') {\n      const l = (81 - 57) * random() + 57\n      const c = (83 - 45) * random() + 45\n      const h = 360 * random()\n      const color = new Color(l, c, h, 'lch')\n      return color\n    } else if (mode === 'sine') {\n      t = t == null ? random() : t\n      const r = round(80 * sin((2 * pi * t) / 0.5 + 0.01) + 150)\n      const g = round(50 * sin((2 * pi * t) / 0.5 + 4.6) + 200)\n      const b = round(100 * sin((2 * pi * t) / 0.5 + 2.3) + 150)\n      const color = new Color(r, g, b)\n      return color\n    } else if (mode === 'pastel') {\n      const l = (94 - 86) * random() + 86\n      const c = (26 - 9) * random() + 9\n      const h = 360 * random()\n      const color = new Color(l, c, h, 'lch')\n      return color\n    } else if (mode === 'dark') {\n      const l = 10 + 10 * random()\n      const c = (125 - 75) * random() + 86\n      const h = 360 * random()\n      const color = new Color(l, c, h, 'lch')\n      return color\n    } else if (mode === 'rgb') {\n      const r = 255 * random()\n      const g = 255 * random()\n      const b = 255 * random()\n      const color = new Color(r, g, b)\n      return color\n    } else if (mode === 'lab') {\n      const l = 100 * random()\n      const a = 256 * random() - 128\n      const b = 256 * random() - 128\n      const color = new Color(l, a, b, 'lab')\n      return color\n    } else if (mode === 'grey') {\n      const grey = 255 * random()\n      const color = new Color(grey, grey, grey)\n      return color\n    } else {\n      throw new Error('Unsupported random color mode')\n    }\n  }\n\n  // Test if given value is a color string\n  static test(color) {\n    return typeof color === 'string' && (isHex.test(color) || isRgb.test(color))\n  }\n\n  cmyk() {\n    // Get the rgb values for the current color\n    const { _a, _b, _c } = this.rgb()\n    const [r, g, b] = [_a, _b, _c].map((v) => v / 255)\n\n    // Get the cmyk values in an unbounded format\n    const k = Math.min(1 - r, 1 - g, 1 - b)\n\n    if (k === 1) {\n      // Catch the black case\n      return new Color(0, 0, 0, 1, 'cmyk')\n    }\n\n    const c = (1 - r - k) / (1 - k)\n    const m = (1 - g - k) / (1 - k)\n    const y = (1 - b - k) / (1 - k)\n\n    // Construct the new color\n    const color = new Color(c, m, y, k, 'cmyk')\n    return color\n  }\n\n  hsl() {\n    // Get the rgb values\n    const { _a, _b, _c } = this.rgb()\n    const [r, g, b] = [_a, _b, _c].map((v) => v / 255)\n\n    // Find the maximum and minimum values to get the lightness\n    const max = Math.max(r, g, b)\n    const min = Math.min(r, g, b)\n    const l = (max + min) / 2\n\n    // If the r, g, v values are identical then we are grey\n    const isGrey = max === min\n\n    // Calculate the hue and saturation\n    const delta = max - min\n    const s = isGrey\n      ? 0\n      : l > 0.5\n        ? delta / (2 - max - min)\n        : delta / (max + min)\n    const h = isGrey\n      ? 0\n      : max === r\n        ? ((g - b) / delta + (g < b ? 6 : 0)) / 6\n        : max === g\n          ? ((b - r) / delta + 2) / 6\n          : max === b\n            ? ((r - g) / delta + 4) / 6\n            : 0\n\n    // Construct and return the new color\n    const color = new Color(360 * h, 100 * s, 100 * l, 'hsl')\n    return color\n  }\n\n  init(a = 0, b = 0, c = 0, d = 0, space = 'rgb') {\n    // This catches the case when a falsy value is passed like ''\n    a = !a ? 0 : a\n\n    // Reset all values in case the init function is rerun with new color space\n    if (this.space) {\n      for (const component in this.space) {\n        delete this[this.space[component]]\n      }\n    }\n\n    if (typeof a === 'number') {\n      // Allow for the case that we don't need d...\n      space = typeof d === 'string' ? d : space\n      d = typeof d === 'string' ? 0 : d\n\n      // Assign the values straight to the color\n      Object.assign(this, { _a: a, _b: b, _c: c, _d: d, space })\n      // If the user gave us an array, make the color from it\n    } else if (a instanceof Array) {\n      this.space = b || (typeof a[3] === 'string' ? a[3] : a[4]) || 'rgb'\n      Object.assign(this, { _a: a[0], _b: a[1], _c: a[2], _d: a[3] || 0 })\n    } else if (a instanceof Object) {\n      // Set the object up and assign its values directly\n      const values = getParameters(a, b)\n      Object.assign(this, values)\n    } else if (typeof a === 'string') {\n      if (isRgb.test(a)) {\n        const noWhitespace = a.replace(whitespace, '')\n        const [_a, _b, _c] = rgb\n          .exec(noWhitespace)\n          .slice(1, 4)\n          .map((v) => parseInt(v))\n        Object.assign(this, { _a, _b, _c, _d: 0, space: 'rgb' })\n      } else if (isHex.test(a)) {\n        const hexParse = (v) => parseInt(v, 16)\n        const [, _a, _b, _c] = hex.exec(sixDigitHex(a)).map(hexParse)\n        Object.assign(this, { _a, _b, _c, _d: 0, space: 'rgb' })\n      } else throw Error(\"Unsupported string format, can't construct Color\")\n    }\n\n    // Now add the components as a convenience\n    const { _a, _b, _c, _d } = this\n    const components =\n      this.space === 'rgb'\n        ? { r: _a, g: _b, b: _c }\n        : this.space === 'xyz'\n          ? { x: _a, y: _b, z: _c }\n          : this.space === 'hsl'\n            ? { h: _a, s: _b, l: _c }\n            : this.space === 'lab'\n              ? { l: _a, a: _b, b: _c }\n              : this.space === 'lch'\n                ? { l: _a, c: _b, h: _c }\n                : this.space === 'cmyk'\n                  ? { c: _a, m: _b, y: _c, k: _d }\n                  : {}\n    Object.assign(this, components)\n  }\n\n  lab() {\n    // Get the xyz color\n    const { x, y, z } = this.xyz()\n\n    // Get the lab components\n    const l = 116 * y - 16\n    const a = 500 * (x - y)\n    const b = 200 * (y - z)\n\n    // Construct and return a new color\n    const color = new Color(l, a, b, 'lab')\n    return color\n  }\n\n  lch() {\n    // Get the lab color directly\n    const { l, a, b } = this.lab()\n\n    // Get the chromaticity and the hue using polar coordinates\n    const c = Math.sqrt(a ** 2 + b ** 2)\n    let h = (180 * Math.atan2(b, a)) / Math.PI\n    if (h < 0) {\n      h *= -1\n      h = 360 - h\n    }\n\n    // Make a new color and return it\n    const color = new Color(l, c, h, 'lch')\n    return color\n  }\n  /*\n  Conversion Methods\n  */\n\n  rgb() {\n    if (this.space === 'rgb') {\n      return this\n    } else if (cieSpace(this.space)) {\n      // Convert to the xyz color space\n      let { x, y, z } = this\n      if (this.space === 'lab' || this.space === 'lch') {\n        // Get the values in the lab space\n        let { l, a, b } = this\n        if (this.space === 'lch') {\n          const { c, h } = this\n          const dToR = Math.PI / 180\n          a = c * Math.cos(dToR * h)\n          b = c * Math.sin(dToR * h)\n        }\n\n        // Undo the nonlinear function\n        const yL = (l + 16) / 116\n        const xL = a / 500 + yL\n        const zL = yL - b / 200\n\n        // Get the xyz values\n        const ct = 16 / 116\n        const mx = 0.008856\n        const nm = 7.787\n        x = 0.95047 * (xL ** 3 > mx ? xL ** 3 : (xL - ct) / nm)\n        y = 1.0 * (yL ** 3 > mx ? yL ** 3 : (yL - ct) / nm)\n        z = 1.08883 * (zL ** 3 > mx ? zL ** 3 : (zL - ct) / nm)\n      }\n\n      // Convert xyz to unbounded rgb values\n      const rU = x * 3.2406 + y * -1.5372 + z * -0.4986\n      const gU = x * -0.9689 + y * 1.8758 + z * 0.0415\n      const bU = x * 0.0557 + y * -0.204 + z * 1.057\n\n      // Convert the values to true rgb values\n      const pow = Math.pow\n      const bd = 0.0031308\n      const r = rU > bd ? 1.055 * pow(rU, 1 / 2.4) - 0.055 : 12.92 * rU\n      const g = gU > bd ? 1.055 * pow(gU, 1 / 2.4) - 0.055 : 12.92 * gU\n      const b = bU > bd ? 1.055 * pow(bU, 1 / 2.4) - 0.055 : 12.92 * bU\n\n      // Make and return the color\n      const color = new Color(255 * r, 255 * g, 255 * b)\n      return color\n    } else if (this.space === 'hsl') {\n      // https://bgrins.github.io/TinyColor/docs/tinycolor.html\n      // Get the current hsl values\n      let { h, s, l } = this\n      h /= 360\n      s /= 100\n      l /= 100\n\n      // If we are grey, then just make the color directly\n      if (s === 0) {\n        l *= 255\n        const color = new Color(l, l, l)\n        return color\n      }\n\n      // TODO I have no idea what this does :D If you figure it out, tell me!\n      const q = l < 0.5 ? l * (1 + s) : l + s - l * s\n      const p = 2 * l - q\n\n      // Get the rgb values\n      const r = 255 * hueToRgb(p, q, h + 1 / 3)\n      const g = 255 * hueToRgb(p, q, h)\n      const b = 255 * hueToRgb(p, q, h - 1 / 3)\n\n      // Make a new color\n      const color = new Color(r, g, b)\n      return color\n    } else if (this.space === 'cmyk') {\n      // https://gist.github.com/felipesabino/5066336\n      // Get the normalised cmyk values\n      const { c, m, y, k } = this\n\n      // Get the rgb values\n      const r = 255 * (1 - Math.min(1, c * (1 - k) + k))\n      const g = 255 * (1 - Math.min(1, m * (1 - k) + k))\n      const b = 255 * (1 - Math.min(1, y * (1 - k) + k))\n\n      // Form the color and return it\n      const color = new Color(r, g, b)\n      return color\n    } else {\n      return this\n    }\n  }\n\n  toArray() {\n    const { _a, _b, _c, _d, space } = this\n    return [_a, _b, _c, _d, space]\n  }\n\n  toHex() {\n    const [r, g, b] = this._clamped().map(componentHex)\n    return `#${r}${g}${b}`\n  }\n\n  toRgb() {\n    const [rV, gV, bV] = this._clamped()\n    const string = `rgb(${rV},${gV},${bV})`\n    return string\n  }\n\n  toString() {\n    return this.toHex()\n  }\n\n  xyz() {\n    // Normalise the red, green and blue values\n    const { _a: r255, _b: g255, _c: b255 } = this.rgb()\n    const [r, g, b] = [r255, g255, b255].map((v) => v / 255)\n\n    // Convert to the lab rgb space\n    const rL = r > 0.04045 ? Math.pow((r + 0.055) / 1.055, 2.4) : r / 12.92\n    const gL = g > 0.04045 ? Math.pow((g + 0.055) / 1.055, 2.4) : g / 12.92\n    const bL = b > 0.04045 ? Math.pow((b + 0.055) / 1.055, 2.4) : b / 12.92\n\n    // Convert to the xyz color space without bounding the values\n    const xU = (rL * 0.4124 + gL * 0.3576 + bL * 0.1805) / 0.95047\n    const yU = (rL * 0.2126 + gL * 0.7152 + bL * 0.0722) / 1.0\n    const zU = (rL * 0.0193 + gL * 0.1192 + bL * 0.9505) / 1.08883\n\n    // Get the proper xyz values by applying the bounding\n    const x = xU > 0.008856 ? Math.pow(xU, 1 / 3) : 7.787 * xU + 16 / 116\n    const y = yU > 0.008856 ? Math.pow(yU, 1 / 3) : 7.787 * yU + 16 / 116\n    const z = zU > 0.008856 ? Math.pow(zU, 1 / 3) : 7.787 * zU + 16 / 116\n\n    // Make and return the color\n    const color = new Color(x, y, z, 'xyz')\n    return color\n  }\n\n  /*\n  Input and Output methods\n  */\n\n  _clamped() {\n    const { _a, _b, _c } = this.rgb()\n    const { max, min, round } = Math\n    const format = (v) => max(0, min(round(v), 255))\n    return [_a, _b, _c].map(format)\n  }\n\n  /*\n  Constructing colors\n  */\n}\n", "import Matrix from './Matrix.js'\n\nexport default class Point {\n  // Initialize\n  constructor(...args) {\n    this.init(...args)\n  }\n\n  // Clone point\n  clone() {\n    return new Point(this)\n  }\n\n  init(x, y) {\n    const base = { x: 0, y: 0 }\n\n    // ensure source as object\n    const source = Array.isArray(x)\n      ? { x: x[0], y: x[1] }\n      : typeof x === 'object'\n        ? { x: x.x, y: x.y }\n        : { x: x, y: y }\n\n    // merge source\n    this.x = source.x == null ? base.x : source.x\n    this.y = source.y == null ? base.y : source.y\n\n    return this\n  }\n\n  toArray() {\n    return [this.x, this.y]\n  }\n\n  transform(m) {\n    return this.clone().transformO(m)\n  }\n\n  // Transform point with matrix\n  transformO(m) {\n    if (!Matrix.isMatrixLike(m)) {\n      m = new Matrix(m)\n    }\n\n    const { x, y } = this\n\n    // Perform the matrix multiplication\n    this.x = m.a * x + m.c * y + m.e\n    this.y = m.b * x + m.d * y + m.f\n\n    return this\n  }\n}\n\nexport function point(x, y) {\n  return new Point(x, y).transformO(this.screenCTM().inverseO())\n}\n", "import { delimiter } from '../modules/core/regex.js'\nimport { radians } from '../utils/utils.js'\nimport { register } from '../utils/adopter.js'\nimport Element from '../elements/Element.js'\nimport Point from './Point.js'\n\nfunction closeEnough(a, b, threshold) {\n  return Math.abs(b - a) < (threshold || 1e-6)\n}\n\nexport default class Matrix {\n  constructor(...args) {\n    this.init(...args)\n  }\n\n  static formatTransforms(o) {\n    // Get all of the parameters required to form the matrix\n    const flipBoth = o.flip === 'both' || o.flip === true\n    const flipX = o.flip && (flipBoth || o.flip === 'x') ? -1 : 1\n    const flipY = o.flip && (flipBoth || o.flip === 'y') ? -1 : 1\n    const skewX =\n      o.skew && o.skew.length\n        ? o.skew[0]\n        : isFinite(o.skew)\n          ? o.skew\n          : isFinite(o.skewX)\n            ? o.skewX\n            : 0\n    const skewY =\n      o.skew && o.skew.length\n        ? o.skew[1]\n        : isFinite(o.skew)\n          ? o.skew\n          : isFinite(o.skewY)\n            ? o.skewY\n            : 0\n    const scaleX =\n      o.scale && o.scale.length\n        ? o.scale[0] * flipX\n        : isFinite(o.scale)\n          ? o.scale * flipX\n          : isFinite(o.scaleX)\n            ? o.scaleX * flipX\n            : flipX\n    const scaleY =\n      o.scale && o.scale.length\n        ? o.scale[1] * flipY\n        : isFinite(o.scale)\n          ? o.scale * flipY\n          : isFinite(o.scaleY)\n            ? o.scaleY * flipY\n            : flipY\n    const shear = o.shear || 0\n    const theta = o.rotate || o.theta || 0\n    const origin = new Point(\n      o.origin || o.around || o.ox || o.originX,\n      o.oy || o.originY\n    )\n    const ox = origin.x\n    const oy = origin.y\n    // We need Point to be invalid if nothing was passed because we cannot default to 0 here. That is why NaN\n    const position = new Point(\n      o.position || o.px || o.positionX || NaN,\n      o.py || o.positionY || NaN\n    )\n    const px = position.x\n    const py = position.y\n    const translate = new Point(\n      o.translate || o.tx || o.translateX,\n      o.ty || o.translateY\n    )\n    const tx = translate.x\n    const ty = translate.y\n    const relative = new Point(\n      o.relative || o.rx || o.relativeX,\n      o.ry || o.relativeY\n    )\n    const rx = relative.x\n    const ry = relative.y\n\n    // Populate all of the values\n    return {\n      scaleX,\n      scaleY,\n      skewX,\n      skewY,\n      shear,\n      theta,\n      rx,\n      ry,\n      tx,\n      ty,\n      ox,\n      oy,\n      px,\n      py\n    }\n  }\n\n  static fromArray(a) {\n    return { a: a[0], b: a[1], c: a[2], d: a[3], e: a[4], f: a[5] }\n  }\n\n  static isMatrixLike(o) {\n    return (\n      o.a != null ||\n      o.b != null ||\n      o.c != null ||\n      o.d != null ||\n      o.e != null ||\n      o.f != null\n    )\n  }\n\n  // left matrix, right matrix, target matrix which is overwritten\n  static matrixMultiply(l, r, o) {\n    // Work out the product directly\n    const a = l.a * r.a + l.c * r.b\n    const b = l.b * r.a + l.d * r.b\n    const c = l.a * r.c + l.c * r.d\n    const d = l.b * r.c + l.d * r.d\n    const e = l.e + l.a * r.e + l.c * r.f\n    const f = l.f + l.b * r.e + l.d * r.f\n\n    // make sure to use local variables because l/r and o could be the same\n    o.a = a\n    o.b = b\n    o.c = c\n    o.d = d\n    o.e = e\n    o.f = f\n\n    return o\n  }\n\n  around(cx, cy, matrix) {\n    return this.clone().aroundO(cx, cy, matrix)\n  }\n\n  // Transform around a center point\n  aroundO(cx, cy, matrix) {\n    const dx = cx || 0\n    const dy = cy || 0\n    return this.translateO(-dx, -dy).lmultiplyO(matrix).translateO(dx, dy)\n  }\n\n  // Clones this matrix\n  clone() {\n    return new Matrix(this)\n  }\n\n  // Decomposes this matrix into its affine parameters\n  decompose(cx = 0, cy = 0) {\n    // Get the parameters from the matrix\n    const a = this.a\n    const b = this.b\n    const c = this.c\n    const d = this.d\n    const e = this.e\n    const f = this.f\n\n    // Figure out if the winding direction is clockwise or counterclockwise\n    const determinant = a * d - b * c\n    const ccw = determinant > 0 ? 1 : -1\n\n    // Since we only shear in x, we can use the x basis to get the x scale\n    // and the rotation of the resulting matrix\n    const sx = ccw * Math.sqrt(a * a + b * b)\n    const thetaRad = Math.atan2(ccw * b, ccw * a)\n    const theta = (180 / Math.PI) * thetaRad\n    const ct = Math.cos(thetaRad)\n    const st = Math.sin(thetaRad)\n\n    // We can then solve the y basis vector simultaneously to get the other\n    // two affine parameters directly from these parameters\n    const lam = (a * c + b * d) / determinant\n    const sy = (c * sx) / (lam * a - b) || (d * sx) / (lam * b + a)\n\n    // Use the translations\n    const tx = e - cx + cx * ct * sx + cy * (lam * ct * sx - st * sy)\n    const ty = f - cy + cx * st * sx + cy * (lam * st * sx + ct * sy)\n\n    // Construct the decomposition and return it\n    return {\n      // Return the affine parameters\n      scaleX: sx,\n      scaleY: sy,\n      shear: lam,\n      rotate: theta,\n      translateX: tx,\n      translateY: ty,\n      originX: cx,\n      originY: cy,\n\n      // Return the matrix parameters\n      a: this.a,\n      b: this.b,\n      c: this.c,\n      d: this.d,\n      e: this.e,\n      f: this.f\n    }\n  }\n\n  // Check if two matrices are equal\n  equals(other) {\n    if (other === this) return true\n    const comp = new Matrix(other)\n    return (\n      closeEnough(this.a, comp.a) &&\n      closeEnough(this.b, comp.b) &&\n      closeEnough(this.c, comp.c) &&\n      closeEnough(this.d, comp.d) &&\n      closeEnough(this.e, comp.e) &&\n      closeEnough(this.f, comp.f)\n    )\n  }\n\n  // Flip matrix on x or y, at a given offset\n  flip(axis, around) {\n    return this.clone().flipO(axis, around)\n  }\n\n  flipO(axis, around) {\n    return axis === 'x'\n      ? this.scaleO(-1, 1, around, 0)\n      : axis === 'y'\n        ? this.scaleO(1, -1, 0, around)\n        : this.scaleO(-1, -1, axis, around || axis) // Define an x, y flip point\n  }\n\n  // Initialize\n  init(source) {\n    const base = Matrix.fromArray([1, 0, 0, 1, 0, 0])\n\n    // ensure source as object\n    source =\n      source instanceof Element\n        ? source.matrixify()\n        : typeof source === 'string'\n          ? Matrix.fromArray(source.split(delimiter).map(parseFloat))\n          : Array.isArray(source)\n            ? Matrix.fromArray(source)\n            : typeof source === 'object' && Matrix.isMatrixLike(source)\n              ? source\n              : typeof source === 'object'\n                ? new Matrix().transform(source)\n                : arguments.length === 6\n                  ? Matrix.fromArray([].slice.call(arguments))\n                  : base\n\n    // Merge the source matrix with the base matrix\n    this.a = source.a != null ? source.a : base.a\n    this.b = source.b != null ? source.b : base.b\n    this.c = source.c != null ? source.c : base.c\n    this.d = source.d != null ? source.d : base.d\n    this.e = source.e != null ? source.e : base.e\n    this.f = source.f != null ? source.f : base.f\n\n    return this\n  }\n\n  inverse() {\n    return this.clone().inverseO()\n  }\n\n  // Inverses matrix\n  inverseO() {\n    // Get the current parameters out of the matrix\n    const a = this.a\n    const b = this.b\n    const c = this.c\n    const d = this.d\n    const e = this.e\n    const f = this.f\n\n    // Invert the 2x2 matrix in the top left\n    const det = a * d - b * c\n    if (!det) throw new Error('Cannot invert ' + this)\n\n    // Calculate the top 2x2 matrix\n    const na = d / det\n    const nb = -b / det\n    const nc = -c / det\n    const nd = a / det\n\n    // Apply the inverted matrix to the top right\n    const ne = -(na * e + nc * f)\n    const nf = -(nb * e + nd * f)\n\n    // Construct the inverted matrix\n    this.a = na\n    this.b = nb\n    this.c = nc\n    this.d = nd\n    this.e = ne\n    this.f = nf\n\n    return this\n  }\n\n  lmultiply(matrix) {\n    return this.clone().lmultiplyO(matrix)\n  }\n\n  lmultiplyO(matrix) {\n    const r = this\n    const l = matrix instanceof Matrix ? matrix : new Matrix(matrix)\n\n    return Matrix.matrixMultiply(l, r, this)\n  }\n\n  // Left multiplies by the given matrix\n  multiply(matrix) {\n    return this.clone().multiplyO(matrix)\n  }\n\n  multiplyO(matrix) {\n    // Get the matrices\n    const l = this\n    const r = matrix instanceof Matrix ? matrix : new Matrix(matrix)\n\n    return Matrix.matrixMultiply(l, r, this)\n  }\n\n  // Rotate matrix\n  rotate(r, cx, cy) {\n    return this.clone().rotateO(r, cx, cy)\n  }\n\n  rotateO(r, cx = 0, cy = 0) {\n    // Convert degrees to radians\n    r = radians(r)\n\n    const cos = Math.cos(r)\n    const sin = Math.sin(r)\n\n    const { a, b, c, d, e, f } = this\n\n    this.a = a * cos - b * sin\n    this.b = b * cos + a * sin\n    this.c = c * cos - d * sin\n    this.d = d * cos + c * sin\n    this.e = e * cos - f * sin + cy * sin - cx * cos + cx\n    this.f = f * cos + e * sin - cx * sin - cy * cos + cy\n\n    return this\n  }\n\n  // Scale matrix\n  scale() {\n    return this.clone().scaleO(...arguments)\n  }\n\n  scaleO(x, y = x, cx = 0, cy = 0) {\n    // Support uniform scaling\n    if (arguments.length === 3) {\n      cy = cx\n      cx = y\n      y = x\n    }\n\n    const { a, b, c, d, e, f } = this\n\n    this.a = a * x\n    this.b = b * y\n    this.c = c * x\n    this.d = d * y\n    this.e = e * x - cx * x + cx\n    this.f = f * y - cy * y + cy\n\n    return this\n  }\n\n  // Shear matrix\n  shear(a, cx, cy) {\n    return this.clone().shearO(a, cx, cy)\n  }\n\n  // eslint-disable-next-line no-unused-vars\n  shearO(lx, cx = 0, cy = 0) {\n    const { a, b, c, d, e, f } = this\n\n    this.a = a + b * lx\n    this.c = c + d * lx\n    this.e = e + f * lx - cy * lx\n\n    return this\n  }\n\n  // Skew Matrix\n  skew() {\n    return this.clone().skewO(...arguments)\n  }\n\n  skewO(x, y = x, cx = 0, cy = 0) {\n    // support uniformal skew\n    if (arguments.length === 3) {\n      cy = cx\n      cx = y\n      y = x\n    }\n\n    // Convert degrees to radians\n    x = radians(x)\n    y = radians(y)\n\n    const lx = Math.tan(x)\n    const ly = Math.tan(y)\n\n    const { a, b, c, d, e, f } = this\n\n    this.a = a + b * lx\n    this.b = b + a * ly\n    this.c = c + d * lx\n    this.d = d + c * ly\n    this.e = e + f * lx - cy * lx\n    this.f = f + e * ly - cx * ly\n\n    return this\n  }\n\n  // SkewX\n  skewX(x, cx, cy) {\n    return this.skew(x, 0, cx, cy)\n  }\n\n  // SkewY\n  skewY(y, cx, cy) {\n    return this.skew(0, y, cx, cy)\n  }\n\n  toArray() {\n    return [this.a, this.b, this.c, this.d, this.e, this.f]\n  }\n\n  // Convert matrix to string\n  toString() {\n    return (\n      'matrix(' +\n      this.a +\n      ',' +\n      this.b +\n      ',' +\n      this.c +\n      ',' +\n      this.d +\n      ',' +\n      this.e +\n      ',' +\n      this.f +\n      ')'\n    )\n  }\n\n  // Transform a matrix into another matrix by manipulating the space\n  transform(o) {\n    // Check if o is a matrix and then left multiply it directly\n    if (Matrix.isMatrixLike(o)) {\n      const matrix = new Matrix(o)\n      return matrix.multiplyO(this)\n    }\n\n    // Get the proposed transformations and the current transformations\n    const t = Matrix.formatTransforms(o)\n    const current = this\n    const { x: ox, y: oy } = new Point(t.ox, t.oy).transform(current)\n\n    // Construct the resulting matrix\n    const transformer = new Matrix()\n      .translateO(t.rx, t.ry)\n      .lmultiplyO(current)\n      .translateO(-ox, -oy)\n      .scaleO(t.scaleX, t.scaleY)\n      .skewO(t.skewX, t.skewY)\n      .shearO(t.shear)\n      .rotateO(t.theta)\n      .translateO(ox, oy)\n\n    // If we want the origin at a particular place, we force it there\n    if (isFinite(t.px) || isFinite(t.py)) {\n      const origin = new Point(ox, oy).transform(transformer)\n      // TODO: Replace t.px with isFinite(t.px)\n      // Doesn't work because t.px is also 0 if it wasn't passed\n      const dx = isFinite(t.px) ? t.px - origin.x : 0\n      const dy = isFinite(t.py) ? t.py - origin.y : 0\n      transformer.translateO(dx, dy)\n    }\n\n    // Translate now after positioning\n    transformer.translateO(t.tx, t.ty)\n    return transformer\n  }\n\n  // Translate matrix\n  translate(x, y) {\n    return this.clone().translateO(x, y)\n  }\n\n  translateO(x, y) {\n    this.e += x || 0\n    this.f += y || 0\n    return this\n  }\n\n  valueOf() {\n    return {\n      a: this.a,\n      b: this.b,\n      c: this.c,\n      d: this.d,\n      e: this.e,\n      f: this.f\n    }\n  }\n}\n\nexport function ctm() {\n  return new Matrix(this.node.getCTM())\n}\n\nexport function screenCTM() {\n  try {\n    /* https://bugzilla.mozilla.org/show_bug.cgi?id=1344537\n       This is needed because FF does not return the transformation matrix\n       for the inner coordinate system when getScreenCTM() is called on nested svgs.\n       However all other Browsers do that */\n    if (typeof this.isRoot === 'function' && !this.isRoot()) {\n      const rect = this.rect(1, 1)\n      const m = rect.node.getScreenCTM()\n      rect.remove()\n      return new Matrix(m)\n    }\n    return new Matrix(this.node.getScreenCTM())\n  } catch (e) {\n    console.warn(\n      `Cannot get CTM from SVG node ${this.node.nodeName}. Is the element rendered?`\n    )\n    return new Matrix()\n  }\n}\n\nregister(Matrix, 'Matrix')\n", "import { globals } from '../../utils/window.js'\nimport { makeInstance } from '../../utils/adopter.js'\n\nexport default function parser() {\n  // Reuse cached element if possible\n  if (!parser.nodes) {\n    const svg = makeInstance().size(2, 0)\n    svg.node.style.cssText = [\n      'opacity: 0',\n      'position: absolute',\n      'left: -100%',\n      'top: -100%',\n      'overflow: hidden'\n    ].join(';')\n\n    svg.attr('focusable', 'false')\n    svg.attr('aria-hidden', 'true')\n\n    const path = svg.path().node\n\n    parser.nodes = { svg, path }\n  }\n\n  if (!parser.nodes.svg.node.parentNode) {\n    const b = globals.document.body || globals.document.documentElement\n    parser.nodes.svg.addTo(b)\n  }\n\n  return parser.nodes\n}\n", "import { delimiter } from '../modules/core/regex.js'\nimport { globals } from '../utils/window.js'\nimport { register } from '../utils/adopter.js'\nimport { registerMethods } from '../utils/methods.js'\nimport Matrix from './Matrix.js'\nimport Point from './Point.js'\nimport parser from '../modules/core/parser.js'\n\nexport function isNulledBox(box) {\n  return !box.width && !box.height && !box.x && !box.y\n}\n\nexport function domContains(node) {\n  return (\n    node === globals.document ||\n    (\n      globals.document.documentElement.contains ||\n      function (node) {\n        // This is IE - it does not support contains() for top-level SVGs\n        while (node.parentNode) {\n          node = node.parentNode\n        }\n        return node === globals.document\n      }\n    ).call(globals.document.documentElement, node)\n  )\n}\n\nexport default class Box {\n  constructor(...args) {\n    this.init(...args)\n  }\n\n  addOffset() {\n    // offset by window scroll position, because getBoundingClientRect changes when window is scrolled\n    this.x += globals.window.pageXOffset\n    this.y += globals.window.pageYOffset\n    return new Box(this)\n  }\n\n  init(source) {\n    const base = [0, 0, 0, 0]\n    source =\n      typeof source === 'string'\n        ? source.split(delimiter).map(parseFloat)\n        : Array.isArray(source)\n          ? source\n          : typeof source === 'object'\n            ? [\n                source.left != null ? source.left : source.x,\n                source.top != null ? source.top : source.y,\n                source.width,\n                source.height\n              ]\n            : arguments.length === 4\n              ? [].slice.call(arguments)\n              : base\n\n    this.x = source[0] || 0\n    this.y = source[1] || 0\n    this.width = this.w = source[2] || 0\n    this.height = this.h = source[3] || 0\n\n    // Add more bounding box properties\n    this.x2 = this.x + this.w\n    this.y2 = this.y + this.h\n    this.cx = this.x + this.w / 2\n    this.cy = this.y + this.h / 2\n\n    return this\n  }\n\n  isNulled() {\n    return isNulledBox(this)\n  }\n\n  // Merge rect box with another, return a new instance\n  merge(box) {\n    const x = Math.min(this.x, box.x)\n    const y = Math.min(this.y, box.y)\n    const width = Math.max(this.x + this.width, box.x + box.width) - x\n    const height = Math.max(this.y + this.height, box.y + box.height) - y\n\n    return new Box(x, y, width, height)\n  }\n\n  toArray() {\n    return [this.x, this.y, this.width, this.height]\n  }\n\n  toString() {\n    return this.x + ' ' + this.y + ' ' + this.width + ' ' + this.height\n  }\n\n  transform(m) {\n    if (!(m instanceof Matrix)) {\n      m = new Matrix(m)\n    }\n\n    let xMin = Infinity\n    let xMax = -Infinity\n    let yMin = Infinity\n    let yMax = -Infinity\n\n    const pts = [\n      new Point(this.x, this.y),\n      new Point(this.x2, this.y),\n      new Point(this.x, this.y2),\n      new Point(this.x2, this.y2)\n    ]\n\n    pts.forEach(function (p) {\n      p = p.transform(m)\n      xMin = Math.min(xMin, p.x)\n      xMax = Math.max(xMax, p.x)\n      yMin = Math.min(yMin, p.y)\n      yMax = Math.max(yMax, p.y)\n    })\n\n    return new Box(xMin, yMin, xMax - xMin, yMax - yMin)\n  }\n}\n\nfunction getBox(el, getBBoxFn, retry) {\n  let box\n\n  try {\n    // Try to get the box with the provided function\n    box = getBBoxFn(el.node)\n\n    // If the box is worthless and not even in the dom, retry\n    // by throwing an error here...\n    if (isNulledBox(box) && !domContains(el.node)) {\n      throw new Error('Element not in the dom')\n    }\n  } catch (e) {\n    // ... and calling the retry handler here\n    box = retry(el)\n  }\n\n  return box\n}\n\nexport function bbox() {\n  // Function to get bbox is getBBox()\n  const getBBox = (node) => node.getBBox()\n\n  // Take all measures so that a stupid browser renders the element\n  // so we can get the bbox from it when we try again\n  const retry = (el) => {\n    try {\n      const clone = el.clone().addTo(parser().svg).show()\n      const box = clone.node.getBBox()\n      clone.remove()\n      return box\n    } catch (e) {\n      // We give up...\n      throw new Error(\n        `Getting bbox of element \"${\n          el.node.nodeName\n        }\" is not possible: ${e.toString()}`\n      )\n    }\n  }\n\n  const box = getBox(this, getBBox, retry)\n  const bbox = new Box(box)\n\n  return bbox\n}\n\nexport function rbox(el) {\n  const getRBox = (node) => node.getBoundingClientRect()\n  const retry = (el) => {\n    // There is no point in trying tricks here because if we insert the element into the dom ourselves\n    // it obviously will be at the wrong position\n    throw new Error(\n      `Getting rbox of element \"${el.node.nodeName}\" is not possible`\n    )\n  }\n\n  const box = getBox(this, getRBox, retry)\n  const rbox = new Box(box)\n\n  // If an element was passed, we want the bbox in the coordinate system of that element\n  if (el) {\n    return rbox.transform(el.screenCTM().inverseO())\n  }\n\n  // Else we want it in absolute screen coordinates\n  // Therefore we need to add the scrollOffset\n  return rbox.addOffset()\n}\n\n// Checks whether the given point is inside the bounding box\nexport function inside(x, y) {\n  const box = this.bbox()\n\n  return (\n    x > box.x && y > box.y && x < box.x + box.width && y < box.y + box.height\n  )\n}\n\nregisterMethods({\n  viewbox: {\n    viewbox(x, y, width, height) {\n      // act as getter\n      if (x == null) return new Box(this.attr('viewBox'))\n\n      // act as setter\n      return this.attr('viewBox', new Box(x, y, width, height))\n    },\n\n    zoom(level, point) {\n      // Its best to rely on the attributes here and here is why:\n      // clientXYZ: Doesn't work on non-root svgs because they dont have a CSSBox (silly!)\n      // getBoundingClientRect: Doesn't work because Chrome just ignores width and height of nested svgs completely\n      //                        that means, their clientRect is always as big as the content.\n      //                        Furthermore this size is incorrect if the element is further transformed by its parents\n      // computedStyle: Only returns meaningful values if css was used with px. We dont go this route here!\n      // getBBox: returns the bounding box of its content - that doesn't help!\n      let { width, height } = this.attr(['width', 'height'])\n\n      // Width and height is a string when a number with a unit is present which we can't use\n      // So we try clientXYZ\n      if (\n        (!width && !height) ||\n        typeof width === 'string' ||\n        typeof height === 'string'\n      ) {\n        width = this.node.clientWidth\n        height = this.node.clientHeight\n      }\n\n      // Giving up...\n      if (!width || !height) {\n        throw new Error(\n          'Impossible to get absolute width and height. Please provide an absolute width and height attribute on the zooming element'\n        )\n      }\n\n      const v = this.viewbox()\n\n      const zoomX = width / v.width\n      const zoomY = height / v.height\n      const zoom = Math.min(zoomX, zoomY)\n\n      if (level == null) {\n        return zoom\n      }\n\n      let zoomAmount = zoom / level\n\n      // Set the zoomAmount to the highest value which is safe to process and recover from\n      // The * 100 is a bit of wiggle room for the matrix transformation\n      if (zoomAmount === Infinity) zoomAmount = Number.MAX_SAFE_INTEGER / 100\n\n      point =\n        point || new Point(width / 2 / zoomX + v.x, height / 2 / zoomY + v.y)\n\n      const box = new Box(v).transform(\n        new Matrix({ scale: zoomAmount, origin: point })\n      )\n\n      return this.viewbox(box)\n    }\n  }\n})\n\nregister(Box, 'Box')\n", "import { extend } from '../utils/adopter.js'\n// import { subClassArray } from './ArrayPolyfill.js'\n\nclass List extends Array {\n  constructor(arr = [], ...args) {\n    super(arr, ...args)\n    if (typeof arr === 'number') return this\n    this.length = 0\n    this.push(...arr)\n  }\n}\n\n/* = subClassArray('List', Array, function (arr = []) {\n  // This catches the case, that native map tries to create an array with new Array(1)\n  if (typeof arr === 'number') return this\n  this.length = 0\n  this.push(...arr)\n}) */\n\nexport default List\n\nextend([List], {\n  each(fnOrMethodName, ...args) {\n    if (typeof fnOrMethodName === 'function') {\n      return this.map((el, i, arr) => {\n        return fnOrMethodName.call(el, el, i, arr)\n      })\n    } else {\n      return this.map((el) => {\n        return el[fnOrMethodName](...args)\n      })\n    }\n  },\n\n  toArray() {\n    return Array.prototype.concat.apply([], this)\n  }\n})\n\nconst reserved = ['toArray', 'constructor', 'each']\n\nList.extend = function (methods) {\n  methods = methods.reduce((obj, name) => {\n    // Don't overwrite own methods\n    if (reserved.includes(name)) return obj\n\n    // Don't add private methods\n    if (name[0] === '_') return obj\n\n    // Allow access to original Array methods through a prefix\n    if (name in Array.prototype) {\n      obj['$' + name] = Array.prototype[name]\n    }\n\n    // Relay every call to each()\n    obj[name] = function (...attrs) {\n      return this.each(name, ...attrs)\n    }\n    return obj\n  }, {})\n\n  extend([List], methods)\n}\n", "import { adopt } from '../../utils/adopter.js'\nimport { globals } from '../../utils/window.js'\nimport { map } from '../../utils/utils.js'\nimport List from '../../types/List.js'\n\nexport default function baseFind(query, parent) {\n  return new List(\n    map((parent || globals.document).querySelectorAll(query), function (node) {\n      return adopt(node)\n    })\n  )\n}\n\n// Scoped find method\nexport function find(query) {\n  return baseFind(query, this.node)\n}\n\nexport function findOne(query) {\n  return adopt(this.node.querySelector(query))\n}\n", "import { delimiter } from './regex.js'\nimport { makeInstance } from '../../utils/adopter.js'\nimport { globals } from '../../utils/window.js'\n\nlet listenerId = 0\nexport const windowEvents = {}\n\nexport function getEvents(instance) {\n  let n = instance.getEventHolder()\n\n  // We dont want to save events in global space\n  if (n === globals.window) n = windowEvents\n  if (!n.events) n.events = {}\n  return n.events\n}\n\nexport function getEventTarget(instance) {\n  return instance.getEventTarget()\n}\n\nexport function clearEvents(instance) {\n  let n = instance.getEventHolder()\n  if (n === globals.window) n = windowEvents\n  if (n.events) n.events = {}\n}\n\n// Add event binder in the SVG namespace\nexport function on(node, events, listener, binding, options) {\n  const l = listener.bind(binding || node)\n  const instance = makeInstance(node)\n  const bag = getEvents(instance)\n  const n = getEventTarget(instance)\n\n  // events can be an array of events or a string of events\n  events = Array.isArray(events) ? events : events.split(delimiter)\n\n  // add id to listener\n  if (!listener._svgjsListenerId) {\n    listener._svgjsListenerId = ++listenerId\n  }\n\n  events.forEach(function (event) {\n    const ev = event.split('.')[0]\n    const ns = event.split('.')[1] || '*'\n\n    // ensure valid object\n    bag[ev] = bag[ev] || {}\n    bag[ev][ns] = bag[ev][ns] || {}\n\n    // reference listener\n    bag[ev][ns][listener._svgjsListenerId] = l\n\n    // add listener\n    n.addEventListener(ev, l, options || false)\n  })\n}\n\n// Add event unbinder in the SVG namespace\nexport function off(node, events, listener, options) {\n  const instance = makeInstance(node)\n  const bag = getEvents(instance)\n  const n = getEventTarget(instance)\n\n  // listener can be a function or a number\n  if (typeof listener === 'function') {\n    listener = listener._svgjsListenerId\n    if (!listener) return\n  }\n\n  // events can be an array of events or a string or undefined\n  events = Array.isArray(events) ? events : (events || '').split(delimiter)\n\n  events.forEach(function (event) {\n    const ev = event && event.split('.')[0]\n    const ns = event && event.split('.')[1]\n    let namespace, l\n\n    if (listener) {\n      // remove listener reference\n      if (bag[ev] && bag[ev][ns || '*']) {\n        // removeListener\n        n.removeEventListener(\n          ev,\n          bag[ev][ns || '*'][listener],\n          options || false\n        )\n\n        delete bag[ev][ns || '*'][listener]\n      }\n    } else if (ev && ns) {\n      // remove all listeners for a namespaced event\n      if (bag[ev] && bag[ev][ns]) {\n        for (l in bag[ev][ns]) {\n          off(n, [ev, ns].join('.'), l)\n        }\n\n        delete bag[ev][ns]\n      }\n    } else if (ns) {\n      // remove all listeners for a specific namespace\n      for (event in bag) {\n        for (namespace in bag[event]) {\n          if (ns === namespace) {\n            off(n, [event, ns].join('.'))\n          }\n        }\n      }\n    } else if (ev) {\n      // remove all listeners for the event\n      if (bag[ev]) {\n        for (namespace in bag[ev]) {\n          off(n, [ev, namespace].join('.'))\n        }\n\n        delete bag[ev]\n      }\n    } else {\n      // remove all listeners on a given node\n      for (event in bag) {\n        off(n, event)\n      }\n\n      clearEvents(instance)\n    }\n  })\n}\n\nexport function dispatch(node, event, data, options) {\n  const n = getEventTarget(node)\n\n  // Dispatch event\n  if (event instanceof globals.window.Event) {\n    n.dispatchEvent(event)\n  } else {\n    event = new globals.window.CustomEvent(event, {\n      detail: data,\n      cancelable: true,\n      ...options\n    })\n    n.dispatchEvent(event)\n  }\n  return event\n}\n", "import { dispatch, off, on } from '../modules/core/event.js'\nimport { register } from '../utils/adopter.js'\nimport Base from './Base.js'\n\nexport default class EventTarget extends Base {\n  addEventListener() {}\n\n  dispatch(event, data, options) {\n    return dispatch(this, event, data, options)\n  }\n\n  dispatchEvent(event) {\n    const bag = this.getEventHolder().events\n    if (!bag) return true\n\n    const events = bag[event.type]\n\n    for (const i in events) {\n      for (const j in events[i]) {\n        events[i][j](event)\n      }\n    }\n\n    return !event.defaultPrevented\n  }\n\n  // Fire given event\n  fire(event, data, options) {\n    this.dispatch(event, data, options)\n    return this\n  }\n\n  getEventHolder() {\n    return this\n  }\n\n  getEventTarget() {\n    return this\n  }\n\n  // Unbind event from listener\n  off(event, listener, options) {\n    off(this, event, listener, options)\n    return this\n  }\n\n  // Bind given event to listener\n  on(event, listener, binding, options) {\n    on(this, event, listener, binding, options)\n    return this\n  }\n\n  removeEventListener() {}\n}\n\nregister(EventTarget, 'EventTarget')\n", "export function noop() {}\n\n// Default animation values\nexport const timeline = {\n  duration: 400,\n  ease: '>',\n  delay: 0\n}\n\n// Default attribute values\nexport const attrs = {\n  // fill and stroke\n  'fill-opacity': 1,\n  'stroke-opacity': 1,\n  'stroke-width': 0,\n  'stroke-linejoin': 'miter',\n  'stroke-linecap': 'butt',\n  fill: '#000000',\n  stroke: '#000000',\n  opacity: 1,\n\n  // position\n  x: 0,\n  y: 0,\n  cx: 0,\n  cy: 0,\n\n  // size\n  width: 0,\n  height: 0,\n\n  // radius\n  r: 0,\n  rx: 0,\n  ry: 0,\n\n  // gradient\n  offset: 0,\n  'stop-opacity': 1,\n  'stop-color': '#000000',\n\n  // text\n  'text-anchor': 'start'\n}\n", "import { delimiter } from '../modules/core/regex.js'\n\nexport default class SVGArray extends Array {\n  constructor(...args) {\n    super(...args)\n    this.init(...args)\n  }\n\n  clone() {\n    return new this.constructor(this)\n  }\n\n  init(arr) {\n    // This catches the case, that native map tries to create an array with new Array(1)\n    if (typeof arr === 'number') return this\n    this.length = 0\n    this.push(...this.parse(arr))\n    return this\n  }\n\n  // Parse whitespace separated string\n  parse(array = []) {\n    // If already is an array, no need to parse it\n    if (array instanceof Array) return array\n\n    return array.trim().split(delimiter).map(parseFloat)\n  }\n\n  toArray() {\n    return Array.prototype.concat.apply([], this)\n  }\n\n  toSet() {\n    return new Set(this)\n  }\n\n  toString() {\n    return this.join(' ')\n  }\n\n  // Flattens the array if needed\n  valueOf() {\n    const ret = []\n    ret.push(...this)\n    return ret\n  }\n}\n", "import { numberAndUnit } from '../modules/core/regex.js'\n\n// Module for unit conversions\nexport default class SVGNumber {\n  // Initialize\n  constructor(...args) {\n    this.init(...args)\n  }\n\n  convert(unit) {\n    return new SVGNumber(this.value, unit)\n  }\n\n  // Divide number\n  divide(number) {\n    number = new SVGNumber(number)\n    return new SVGNumber(this / number, this.unit || number.unit)\n  }\n\n  init(value, unit) {\n    unit = Array.isArray(value) ? value[1] : unit\n    value = Array.isArray(value) ? value[0] : value\n\n    // initialize defaults\n    this.value = 0\n    this.unit = unit || ''\n\n    // parse value\n    if (typeof value === 'number') {\n      // ensure a valid numeric value\n      this.value = isNaN(value)\n        ? 0\n        : !isFinite(value)\n          ? value < 0\n            ? -3.4e38\n            : +3.4e38\n          : value\n    } else if (typeof value === 'string') {\n      unit = value.match(numberAndUnit)\n\n      if (unit) {\n        // make value numeric\n        this.value = parseFloat(unit[1])\n\n        // normalize\n        if (unit[5] === '%') {\n          this.value /= 100\n        } else if (unit[5] === 's') {\n          this.value *= 1000\n        }\n\n        // store unit\n        this.unit = unit[5]\n      }\n    } else {\n      if (value instanceof SVGNumber) {\n        this.value = value.valueOf()\n        this.unit = value.unit\n      }\n    }\n\n    return this\n  }\n\n  // Subtract number\n  minus(number) {\n    number = new SVGNumber(number)\n    return new SVGNumber(this - number, this.unit || number.unit)\n  }\n\n  // Add number\n  plus(number) {\n    number = new SVGNumber(number)\n    return new SVGNumber(this + number, this.unit || number.unit)\n  }\n\n  // Multiply number\n  times(number) {\n    number = new SVGNumber(number)\n    return new SVGNumber(this * number, this.unit || number.unit)\n  }\n\n  toArray() {\n    return [this.value, this.unit]\n  }\n\n  toJSON() {\n    return this.toString()\n  }\n\n  toString() {\n    return (\n      (this.unit === '%'\n        ? ~~(this.value * 1e8) / 1e6\n        : this.unit === 's'\n          ? this.value / 1e3\n          : this.value) + this.unit\n    )\n  }\n\n  valueOf() {\n    return this.value\n  }\n}\n", "import { attrs as defaults } from './defaults.js'\nimport { isNumber } from './regex.js'\nimport Color from '../../types/Color.js'\nimport SVGArray from '../../types/SVGArray.js'\nimport SVGNumber from '../../types/SVGNumber.js'\n\nconst colorAttributes = new Set([\n  'fill',\n  'stroke',\n  'color',\n  'bgcolor',\n  'stop-color',\n  'flood-color',\n  'lighting-color'\n])\n\nconst hooks = []\nexport function registerAttrHook(fn) {\n  hooks.push(fn)\n}\n\n// Set svg element attribute\nexport default function attr(attr, val, ns) {\n  // act as full getter\n  if (attr == null) {\n    // get an object of attributes\n    attr = {}\n    val = this.node.attributes\n\n    for (const node of val) {\n      attr[node.nodeName] = isNumber.test(node.nodeValue)\n        ? parseFloat(node.nodeValue)\n        : node.nodeValue\n    }\n\n    return attr\n  } else if (attr instanceof Array) {\n    // loop through array and get all values\n    return attr.reduce((last, curr) => {\n      last[curr] = this.attr(curr)\n      return last\n    }, {})\n  } else if (typeof attr === 'object' && attr.constructor === Object) {\n    // apply every attribute individually if an object is passed\n    for (val in attr) this.attr(val, attr[val])\n  } else if (val === null) {\n    // remove value\n    this.node.removeAttribute(attr)\n  } else if (val == null) {\n    // act as a getter if the first and only argument is not an object\n    val = this.node.getAttribute(attr)\n    return val == null\n      ? defaults[attr]\n      : isNumber.test(val)\n        ? parseFloat(val)\n        : val\n  } else {\n    // Loop through hooks and execute them to convert value\n    val = hooks.reduce((_val, hook) => {\n      return hook(attr, _val, this)\n    }, val)\n\n    // ensure correct numeric values (also accepts NaN and Infinity)\n    if (typeof val === 'number') {\n      val = new SVGNumber(val)\n    } else if (colorAttributes.has(attr) && Color.isColor(val)) {\n      // ensure full hex color\n      val = new Color(val)\n    } else if (val.constructor === Array) {\n      // Check for plain arrays and parse array values\n      val = new SVGArray(val)\n    }\n\n    // if the passed attribute is leading...\n    if (attr === 'leading') {\n      // ... call the leading method instead\n      if (this.leading) {\n        this.leading(val)\n      }\n    } else {\n      // set given attribute on node\n      typeof ns === 'string'\n        ? this.node.setAttributeNS(ns, attr, val.toString())\n        : this.node.setAttribute(attr, val.toString())\n    }\n\n    // rebuild if required\n    if (this.rebuild && (attr === 'font-size' || attr === 'x')) {\n      this.rebuild()\n    }\n  }\n\n  return this\n}\n", "import {\n  adopt,\n  assignNewId,\n  eid,\n  extend,\n  makeInstance,\n  create,\n  register\n} from '../utils/adopter.js'\nimport { find, findOne } from '../modules/core/selector.js'\nimport { globals } from '../utils/window.js'\nimport { map } from '../utils/utils.js'\nimport { svg, html } from '../modules/core/namespaces.js'\nimport EventTarget from '../types/EventTarget.js'\nimport List from '../types/List.js'\nimport attr from '../modules/core/attr.js'\n\nexport default class Dom extends EventTarget {\n  constructor(node, attrs) {\n    super()\n    this.node = node\n    this.type = node.nodeName\n\n    if (attrs && node !== attrs) {\n      this.attr(attrs)\n    }\n  }\n\n  // Add given element at a position\n  add(element, i) {\n    element = makeInstance(element)\n\n    // If non-root svg nodes are added we have to remove their namespaces\n    if (\n      element.removeNamespace &&\n      this.node instanceof globals.window.SVGElement\n    ) {\n      element.removeNamespace()\n    }\n\n    if (i == null) {\n      this.node.appendChild(element.node)\n    } else if (element.node !== this.node.childNodes[i]) {\n      this.node.insertBefore(element.node, this.node.childNodes[i])\n    }\n\n    return this\n  }\n\n  // Add element to given container and return self\n  addTo(parent, i) {\n    return makeInstance(parent).put(this, i)\n  }\n\n  // Returns all child elements\n  children() {\n    return new List(\n      map(this.node.children, function (node) {\n        return adopt(node)\n      })\n    )\n  }\n\n  // Remove all elements in this container\n  clear() {\n    // remove children\n    while (this.node.hasChildNodes()) {\n      this.node.removeChild(this.node.lastChild)\n    }\n\n    return this\n  }\n\n  // Clone element\n  clone(deep = true, assignNewIds = true) {\n    // write dom data to the dom so the clone can pickup the data\n    this.writeDataToDom()\n\n    // clone element\n    let nodeClone = this.node.cloneNode(deep)\n    if (assignNewIds) {\n      // assign new id\n      nodeClone = assignNewId(nodeClone)\n    }\n    return new this.constructor(nodeClone)\n  }\n\n  // Iterates over all children and invokes a given block\n  each(block, deep) {\n    const children = this.children()\n    let i, il\n\n    for (i = 0, il = children.length; i < il; i++) {\n      block.apply(children[i], [i, children])\n\n      if (deep) {\n        children[i].each(block, deep)\n      }\n    }\n\n    return this\n  }\n\n  element(nodeName, attrs) {\n    return this.put(new Dom(create(nodeName), attrs))\n  }\n\n  // Get first child\n  first() {\n    return adopt(this.node.firstChild)\n  }\n\n  // Get a element at the given index\n  get(i) {\n    return adopt(this.node.childNodes[i])\n  }\n\n  getEventHolder() {\n    return this.node\n  }\n\n  getEventTarget() {\n    return this.node\n  }\n\n  // Checks if the given element is a child\n  has(element) {\n    return this.index(element) >= 0\n  }\n\n  html(htmlOrFn, outerHTML) {\n    return this.xml(htmlOrFn, outerHTML, html)\n  }\n\n  // Get / set id\n  id(id) {\n    // generate new id if no id set\n    if (typeof id === 'undefined' && !this.node.id) {\n      this.node.id = eid(this.type)\n    }\n\n    // don't set directly with this.node.id to make `null` work correctly\n    return this.attr('id', id)\n  }\n\n  // Gets index of given element\n  index(element) {\n    return [].slice.call(this.node.childNodes).indexOf(element.node)\n  }\n\n  // Get the last child\n  last() {\n    return adopt(this.node.lastChild)\n  }\n\n  // matches the element vs a css selector\n  matches(selector) {\n    const el = this.node\n    const matcher =\n      el.matches ||\n      el.matchesSelector ||\n      el.msMatchesSelector ||\n      el.mozMatchesSelector ||\n      el.webkitMatchesSelector ||\n      el.oMatchesSelector ||\n      null\n    return matcher && matcher.call(el, selector)\n  }\n\n  // Returns the parent element instance\n  parent(type) {\n    let parent = this\n\n    // check for parent\n    if (!parent.node.parentNode) return null\n\n    // get parent element\n    parent = adopt(parent.node.parentNode)\n\n    if (!type) return parent\n\n    // loop through ancestors if type is given\n    do {\n      if (\n        typeof type === 'string' ? parent.matches(type) : parent instanceof type\n      )\n        return parent\n    } while ((parent = adopt(parent.node.parentNode)))\n\n    return parent\n  }\n\n  // Basically does the same as `add()` but returns the added element instead\n  put(element, i) {\n    element = makeInstance(element)\n    this.add(element, i)\n    return element\n  }\n\n  // Add element to given container and return container\n  putIn(parent, i) {\n    return makeInstance(parent).add(this, i)\n  }\n\n  // Remove element\n  remove() {\n    if (this.parent()) {\n      this.parent().removeElement(this)\n    }\n\n    return this\n  }\n\n  // Remove a given child\n  removeElement(element) {\n    this.node.removeChild(element.node)\n\n    return this\n  }\n\n  // Replace this with element\n  replace(element) {\n    element = makeInstance(element)\n\n    if (this.node.parentNode) {\n      this.node.parentNode.replaceChild(element.node, this.node)\n    }\n\n    return element\n  }\n\n  round(precision = 2, map = null) {\n    const factor = 10 ** precision\n    const attrs = this.attr(map)\n\n    for (const i in attrs) {\n      if (typeof attrs[i] === 'number') {\n        attrs[i] = Math.round(attrs[i] * factor) / factor\n      }\n    }\n\n    this.attr(attrs)\n    return this\n  }\n\n  // Import / Export raw svg\n  svg(svgOrFn, outerSVG) {\n    return this.xml(svgOrFn, outerSVG, svg)\n  }\n\n  // Return id on string conversion\n  toString() {\n    return this.id()\n  }\n\n  words(text) {\n    // This is faster than removing all children and adding a new one\n    this.node.textContent = text\n    return this\n  }\n\n  wrap(node) {\n    const parent = this.parent()\n\n    if (!parent) {\n      return this.addTo(node)\n    }\n\n    const position = parent.index(this)\n    return parent.put(node, position).put(this)\n  }\n\n  // write svgjs data to the dom\n  writeDataToDom() {\n    // dump variables recursively\n    this.each(function () {\n      this.writeDataToDom()\n    })\n\n    return this\n  }\n\n  // Import / Export raw svg\n  xml(xmlOrFn, outerXML, ns) {\n    if (typeof xmlOrFn === 'boolean') {\n      ns = outerXML\n      outerXML = xmlOrFn\n      xmlOrFn = null\n    }\n\n    // act as getter if no svg string is given\n    if (xmlOrFn == null || typeof xmlOrFn === 'function') {\n      // The default for exports is, that the outerNode is included\n      outerXML = outerXML == null ? true : outerXML\n\n      // write svgjs data to the dom\n      this.writeDataToDom()\n      let current = this\n\n      // An export modifier was passed\n      if (xmlOrFn != null) {\n        current = adopt(current.node.cloneNode(true))\n\n        // If the user wants outerHTML we need to process this node, too\n        if (outerXML) {\n          const result = xmlOrFn(current)\n          current = result || current\n\n          // The user does not want this node? Well, then he gets nothing\n          if (result === false) return ''\n        }\n\n        // Deep loop through all children and apply modifier\n        current.each(function () {\n          const result = xmlOrFn(this)\n          const _this = result || this\n\n          // If modifier returns false, discard node\n          if (result === false) {\n            this.remove()\n\n            // If modifier returns new node, use it\n          } else if (result && this !== _this) {\n            this.replace(_this)\n          }\n        }, true)\n      }\n\n      // Return outer or inner content\n      return outerXML ? current.node.outerHTML : current.node.innerHTML\n    }\n\n    // Act as setter if we got a string\n\n    // The default for import is, that the current node is not replaced\n    outerXML = outerXML == null ? false : outerXML\n\n    // Create temporary holder\n    const well = create('wrapper', ns)\n    const fragment = globals.document.createDocumentFragment()\n\n    // Dump raw svg\n    well.innerHTML = xmlOrFn\n\n    // Transplant nodes into the fragment\n    for (let len = well.children.length; len--; ) {\n      fragment.appendChild(well.firstElementChild)\n    }\n\n    const parent = this.parent()\n\n    // Add the whole fragment at once\n    return outerXML ? this.replace(fragment) && parent : this.add(fragment)\n  }\n}\n\nextend(Dom, { attr, find, findOne })\nregister(Dom, 'Dom')\n", "import { bbox, rbox, inside } from '../types/Box.js'\nimport { ctm, screenCTM } from '../types/Matrix.js'\nimport {\n  extend,\n  getClass,\n  makeInstance,\n  register,\n  root\n} from '../utils/adopter.js'\nimport { globals } from '../utils/window.js'\nimport { point } from '../types/Point.js'\nimport { proportionalSize, writeDataToDom } from '../utils/utils.js'\nimport { reference } from '../modules/core/regex.js'\nimport Dom from './Dom.js'\nimport List from '../types/List.js'\nimport SVGNumber from '../types/SVGNumber.js'\n\nexport default class Element extends Dom {\n  constructor(node, attrs) {\n    super(node, attrs)\n\n    // initialize data object\n    this.dom = {}\n\n    // create circular reference\n    this.node.instance = this\n\n    if (node.hasAttribute('data-svgjs') || node.hasAttribute('svgjs:data')) {\n      // pull svgjs data from the dom (getAttributeNS doesn't work in html5)\n      this.setData(\n        JSON.parse(node.getAttribute('data-svgjs')) ??\n          JSON.parse(node.getAttribute('svgjs:data')) ??\n          {}\n      )\n    }\n  }\n\n  // Move element by its center\n  center(x, y) {\n    return this.cx(x).cy(y)\n  }\n\n  // Move by center over x-axis\n  cx(x) {\n    return x == null\n      ? this.x() + this.width() / 2\n      : this.x(x - this.width() / 2)\n  }\n\n  // Move by center over y-axis\n  cy(y) {\n    return y == null\n      ? this.y() + this.height() / 2\n      : this.y(y - this.height() / 2)\n  }\n\n  // Get defs\n  defs() {\n    const root = this.root()\n    return root && root.defs()\n  }\n\n  // Relative move over x and y axes\n  dmove(x, y) {\n    return this.dx(x).dy(y)\n  }\n\n  // Relative move over x axis\n  dx(x = 0) {\n    return this.x(new SVGNumber(x).plus(this.x()))\n  }\n\n  // Relative move over y axis\n  dy(y = 0) {\n    return this.y(new SVGNumber(y).plus(this.y()))\n  }\n\n  getEventHolder() {\n    return this\n  }\n\n  // Set height of element\n  height(height) {\n    return this.attr('height', height)\n  }\n\n  // Move element to given x and y values\n  move(x, y) {\n    return this.x(x).y(y)\n  }\n\n  // return array of all ancestors of given type up to the root svg\n  parents(until = this.root()) {\n    const isSelector = typeof until === 'string'\n    if (!isSelector) {\n      until = makeInstance(until)\n    }\n    const parents = new List()\n    let parent = this\n\n    while (\n      (parent = parent.parent()) &&\n      parent.node !== globals.document &&\n      parent.nodeName !== '#document-fragment'\n    ) {\n      parents.push(parent)\n\n      if (!isSelector && parent.node === until.node) {\n        break\n      }\n      if (isSelector && parent.matches(until)) {\n        break\n      }\n      if (parent.node === this.root().node) {\n        // We worked our way to the root and didn't match `until`\n        return null\n      }\n    }\n\n    return parents\n  }\n\n  // Get referenced element form attribute value\n  reference(attr) {\n    attr = this.attr(attr)\n    if (!attr) return null\n\n    const m = (attr + '').match(reference)\n    return m ? makeInstance(m[1]) : null\n  }\n\n  // Get parent document\n  root() {\n    const p = this.parent(getClass(root))\n    return p && p.root()\n  }\n\n  // set given data to the elements data property\n  setData(o) {\n    this.dom = o\n    return this\n  }\n\n  // Set element size to given width and height\n  size(width, height) {\n    const p = proportionalSize(this, width, height)\n\n    return this.width(new SVGNumber(p.width)).height(new SVGNumber(p.height))\n  }\n\n  // Set width of element\n  width(width) {\n    return this.attr('width', width)\n  }\n\n  // write svgjs data to the dom\n  writeDataToDom() {\n    writeDataToDom(this, this.dom)\n    return super.writeDataToDom()\n  }\n\n  // Move over x-axis\n  x(x) {\n    return this.attr('x', x)\n  }\n\n  // Move over y-axis\n  y(y) {\n    return this.attr('y', y)\n  }\n}\n\nextend(Element, {\n  bbox,\n  rbox,\n  inside,\n  point,\n  ctm,\n  screenCTM\n})\n\nregister(Element, 'Element')\n", "import { registerMethods } from '../../utils/methods.js'\nimport Color from '../../types/Color.js'\nimport Element from '../../elements/Element.js'\nimport Matrix from '../../types/Matrix.js'\nimport Point from '../../types/Point.js'\nimport SVGNumber from '../../types/SVGNumber.js'\n\n// Define list of available attributes for stroke and fill\nconst sugar = {\n  stroke: [\n    'color',\n    'width',\n    'opacity',\n    'linecap',\n    'linejoin',\n    'miterlimit',\n    'dasharray',\n    'dashoffset'\n  ],\n  fill: ['color', 'opacity', 'rule'],\n  prefix: function (t, a) {\n    return a === 'color' ? t : t + '-' + a\n  }\n}\n\n// Add sugar for fill and stroke\n;['fill', 'stroke'].forEach(function (m) {\n  const extension = {}\n  let i\n\n  extension[m] = function (o) {\n    if (typeof o === 'undefined') {\n      return this.attr(m)\n    }\n    if (\n      typeof o === 'string' ||\n      o instanceof Color ||\n      Color.isRgb(o) ||\n      o instanceof Element\n    ) {\n      this.attr(m, o)\n    } else {\n      // set all attributes from sugar.fill and sugar.stroke list\n      for (i = sugar[m].length - 1; i >= 0; i--) {\n        if (o[sugar[m][i]] != null) {\n          this.attr(sugar.prefix(m, sugar[m][i]), o[sugar[m][i]])\n        }\n      }\n    }\n\n    return this\n  }\n\n  registerMethods(['Element', 'Runner'], extension)\n})\n\nregisterMethods(['Element', 'Runner'], {\n  // Let the user set the matrix directly\n  matrix: function (mat, b, c, d, e, f) {\n    // Act as a getter\n    if (mat == null) {\n      return new Matrix(this)\n    }\n\n    // Act as a setter, the user can pass a matrix or a set of numbers\n    return this.attr('transform', new Matrix(mat, b, c, d, e, f))\n  },\n\n  // Map rotation to transform\n  rotate: function (angle, cx, cy) {\n    return this.transform({ rotate: angle, ox: cx, oy: cy }, true)\n  },\n\n  // Map skew to transform\n  skew: function (x, y, cx, cy) {\n    return arguments.length === 1 || arguments.length === 3\n      ? this.transform({ skew: x, ox: y, oy: cx }, true)\n      : this.transform({ skew: [x, y], ox: cx, oy: cy }, true)\n  },\n\n  shear: function (lam, cx, cy) {\n    return this.transform({ shear: lam, ox: cx, oy: cy }, true)\n  },\n\n  // Map scale to transform\n  scale: function (x, y, cx, cy) {\n    return arguments.length === 1 || arguments.length === 3\n      ? this.transform({ scale: x, ox: y, oy: cx }, true)\n      : this.transform({ scale: [x, y], ox: cx, oy: cy }, true)\n  },\n\n  // Map translate to transform\n  translate: function (x, y) {\n    return this.transform({ translate: [x, y] }, true)\n  },\n\n  // Map relative translations to transform\n  relative: function (x, y) {\n    return this.transform({ relative: [x, y] }, true)\n  },\n\n  // Map flip to transform\n  flip: function (direction = 'both', origin = 'center') {\n    if ('xybothtrue'.indexOf(direction) === -1) {\n      origin = direction\n      direction = 'both'\n    }\n\n    return this.transform({ flip: direction, origin: origin }, true)\n  },\n\n  // Opacity\n  opacity: function (value) {\n    return this.attr('opacity', value)\n  }\n})\n\nregisterMethods('radius', {\n  // Add x and y radius\n  radius: function (x, y = x) {\n    const type = (this._element || this).type\n    return type === 'radialGradient'\n      ? this.attr('r', new SVGNumber(x))\n      : this.rx(x).ry(y)\n  }\n})\n\nregisterMethods('Path', {\n  // Get path length\n  length: function () {\n    return this.node.getTotalLength()\n  },\n  // Get point at length\n  pointAt: function (length) {\n    return new Point(this.node.getPointAtLength(length))\n  }\n})\n\nregisterMethods(['Element', 'Runner'], {\n  // Set font\n  font: function (a, v) {\n    if (typeof a === 'object') {\n      for (v in a) this.font(v, a[v])\n      return this\n    }\n\n    return a === 'leading'\n      ? this.leading(v)\n      : a === 'anchor'\n        ? this.attr('text-anchor', v)\n        : a === 'size' ||\n            a === 'family' ||\n            a === 'weight' ||\n            a === 'stretch' ||\n            a === 'variant' ||\n            a === 'style'\n          ? this.attr('font-' + a, v)\n          : this.attr(a, v)\n  }\n})\n\n// Add events to elements\nconst methods = [\n  'click',\n  'dblclick',\n  'mousedown',\n  'mouseup',\n  'mouseover',\n  'mouseout',\n  'mousemove',\n  'mouseenter',\n  'mouseleave',\n  'touchstart',\n  'touchmove',\n  'touchleave',\n  'touchend',\n  'touchcancel',\n  'contextmenu',\n  'wheel',\n  'pointerdown',\n  'pointermove',\n  'pointerup',\n  'pointerleave',\n  'pointercancel'\n].reduce(function (last, event) {\n  // add event to Element\n  const fn = function (f) {\n    if (f === null) {\n      this.off(event)\n    } else {\n      this.on(event, f)\n    }\n    return this\n  }\n\n  last[event] = fn\n  return last\n}, {})\n\nregisterMethods('Element', methods)\n", "import { getOrigin, isDescriptive } from '../../utils/utils.js'\nimport { delimiter, transforms } from '../core/regex.js'\nimport { registerMethods } from '../../utils/methods.js'\nimport Matrix from '../../types/Matrix.js'\n\n// Reset all transformations\nexport function untransform() {\n  return this.attr('transform', null)\n}\n\n// merge the whole transformation chain into one matrix and returns it\nexport function matrixify() {\n  const matrix = (this.attr('transform') || '')\n    // split transformations\n    .split(transforms)\n    .slice(0, -1)\n    .map(function (str) {\n      // generate key => value pairs\n      const kv = str.trim().split('(')\n      return [\n        kv[0],\n        kv[1].split(delimiter).map(function (str) {\n          return parseFloat(str)\n        })\n      ]\n    })\n    .reverse()\n    // merge every transformation into one matrix\n    .reduce(function (matrix, transform) {\n      if (transform[0] === 'matrix') {\n        return matrix.lmultiply(Matrix.fromArray(transform[1]))\n      }\n      return matrix[transform[0]].apply(matrix, transform[1])\n    }, new Matrix())\n\n  return matrix\n}\n\n// add an element to another parent without changing the visual representation on the screen\nexport function toParent(parent, i) {\n  if (this === parent) return this\n\n  if (isDescriptive(this.node)) return this.addTo(parent, i)\n\n  const ctm = this.screenCTM()\n  const pCtm = parent.screenCTM().inverse()\n\n  this.addTo(parent, i).untransform().transform(pCtm.multiply(ctm))\n\n  return this\n}\n\n// same as above with parent equals root-svg\nexport function toRoot(i) {\n  return this.toParent(this.root(), i)\n}\n\n// Add transformations\nexport function transform(o, relative) {\n  // Act as a getter if no object was passed\n  if (o == null || typeof o === 'string') {\n    const decomposed = new Matrix(this).decompose()\n    return o == null ? decomposed : decomposed[o]\n  }\n\n  if (!Matrix.isMatrixLike(o)) {\n    // Set the origin according to the defined transform\n    o = { ...o, origin: getOrigin(o, this) }\n  }\n\n  // The user can pass a boolean, an Element or an Matrix or nothing\n  const cleanRelative = relative === true ? this : relative || false\n  const result = new Matrix(cleanRelative).transform(o)\n  return this.attr('transform', result)\n}\n\nregisterMethods('Element', {\n  untransform,\n  matrixify,\n  toParent,\n  toRoot,\n  transform\n})\n", "import { register } from '../utils/adopter.js'\nimport Element from './Element.js'\n\nexport default class Container extends Element {\n  flatten() {\n    this.each(function () {\n      if (this instanceof Container) {\n        return this.flatten().ungroup()\n      }\n    })\n\n    return this\n  }\n\n  ungroup(parent = this.parent(), index = parent.index(this)) {\n    // when parent != this, we want append all elements to the end\n    index = index === -1 ? parent.children().length : index\n\n    this.each(function (i, children) {\n      // reverse each\n      return children[children.length - i - 1].toParent(parent, index)\n    })\n\n    return this.remove()\n  }\n}\n\nregister(Container, 'Container')\n", "import { nodeOrNew, register } from '../utils/adopter.js'\nimport Container from './Container.js'\n\nexport default class Defs extends Container {\n  constructor(node, attrs = node) {\n    super(nodeOrNew('defs', node), attrs)\n  }\n\n  flatten() {\n    return this\n  }\n\n  ungroup() {\n    return this\n  }\n}\n\nregister(Defs, 'Defs')\n", "import { register } from '../utils/adopter.js'\nimport Element from './Element.js'\n\nexport default class Shape extends Element {}\n\nregister(Shape, 'Shape')\n", "import SVGNumber from '../../types/SVGNumber.js'\n\n// Radius x value\nexport function rx(rx) {\n  return this.attr('rx', rx)\n}\n\n// Radius y value\nexport function ry(ry) {\n  return this.attr('ry', ry)\n}\n\n// Move over x-axis\nexport function x(x) {\n  return x == null ? this.cx() - this.rx() : this.cx(x + this.rx())\n}\n\n// Move over y-axis\nexport function y(y) {\n  return y == null ? this.cy() - this.ry() : this.cy(y + this.ry())\n}\n\n// Move by center over x-axis\nexport function cx(x) {\n  return this.attr('cx', x)\n}\n\n// Move by center over y-axis\nexport function cy(y) {\n  return this.attr('cy', y)\n}\n\n// Set width of element\nexport function width(width) {\n  return width == null ? this.rx() * 2 : this.rx(new SVGNumber(width).divide(2))\n}\n\n// Set height of element\nexport function height(height) {\n  return height == null\n    ? this.ry() * 2\n    : this.ry(new SVGNumber(height).divide(2))\n}\n", "import {\n  extend,\n  nodeOrNew,\n  register,\n  wrapWithAttrCheck\n} from '../utils/adopter.js'\nimport { proportionalSize } from '../utils/utils.js'\nimport { registerMethods } from '../utils/methods.js'\nimport SVGNumber from '../types/SVGNumber.js'\nimport Shape from './Shape.js'\nimport * as circled from '../modules/core/circled.js'\n\nexport default class Ellipse extends Shape {\n  constructor(node, attrs = node) {\n    super(nodeOrNew('ellipse', node), attrs)\n  }\n\n  size(width, height) {\n    const p = proportionalSize(this, width, height)\n\n    return this.rx(new SVGNumber(p.width).divide(2)).ry(\n      new SVGNumber(p.height).divide(2)\n    )\n  }\n}\n\nextend(Ellipse, circled)\n\nregisterMethods('Container', {\n  // Create an ellipse\n  ellipse: wrapWithAttrCheck(function (width = 0, height = width) {\n    return this.put(new Ellipse()).size(width, height).move(0, 0)\n  })\n})\n\nregister(Ellipse, 'Ellipse')\n", "import Dom from './Dom.js'\nimport { globals } from '../utils/window.js'\nimport { register, create } from '../utils/adopter.js'\n\nclass Fragment extends Dom {\n  constructor(node = globals.document.createDocumentFragment()) {\n    super(node)\n  }\n\n  // Import / Export raw xml\n  xml(xmlOrFn, outerXML, ns) {\n    if (typeof xmlOrFn === 'boolean') {\n      ns = outerXML\n      outerXML = xmlOrFn\n      xmlOrFn = null\n    }\n\n    // because this is a fragment we have to put all elements into a wrapper first\n    // before we can get the innerXML from it\n    if (xmlOrFn == null || typeof xmlOrFn === 'function') {\n      const wrapper = new Dom(create('wrapper', ns))\n      wrapper.add(this.node.cloneNode(true))\n\n      return wrapper.xml(false, ns)\n    }\n\n    // Act as setter if we got a string\n    return super.xml(xmlOrFn, false, ns)\n  }\n}\n\nregister(Fragment, 'Fragment')\n\nexport default Fragment\n", "import SVGNumber from '../../types/SVGNumber.js'\n\nexport function from(x, y) {\n  return (this._element || this).type === 'radialGradient'\n    ? this.attr({ fx: new SVGNumber(x), fy: new SVGNumber(y) })\n    : this.attr({ x1: new SVGNumber(x), y1: new SVGNumber(y) })\n}\n\nexport function to(x, y) {\n  return (this._element || this).type === 'radialGradient'\n    ? this.attr({ cx: new SVGNumber(x), cy: new SVGNumber(y) })\n    : this.attr({ x2: new SVGNumber(x), y2: new SVGNumber(y) })\n}\n", "import {\n  extend,\n  nodeOrNew,\n  register,\n  wrapWithAttrCheck\n} from '../utils/adopter.js'\nimport { registerMethods } from '../utils/methods.js'\nimport Box from '../types/Box.js'\nimport Container from './Container.js'\nimport baseFind from '../modules/core/selector.js'\nimport * as gradiented from '../modules/core/gradiented.js'\n\nexport default class Gradient extends Container {\n  constructor(type, attrs) {\n    super(\n      nodeOrNew(type + 'Gradient', typeof type === 'string' ? null : type),\n      attrs\n    )\n  }\n\n  // custom attr to handle transform\n  attr(a, b, c) {\n    if (a === 'transform') a = 'gradientTransform'\n    return super.attr(a, b, c)\n  }\n\n  bbox() {\n    return new Box()\n  }\n\n  targets() {\n    return baseFind('svg [fill*=' + this.id() + ']')\n  }\n\n  // Alias string conversion to fill\n  toString() {\n    return this.url()\n  }\n\n  // Update gradient\n  update(block) {\n    // remove all stops\n    this.clear()\n\n    // invoke passed block\n    if (typeof block === 'function') {\n      block.call(this, this)\n    }\n\n    return this\n  }\n\n  // Return the fill id\n  url() {\n    return 'url(#' + this.id() + ')'\n  }\n}\n\nextend(Gradient, gradiented)\n\nregisterMethods({\n  Container: {\n    // Create gradient element in defs\n    gradient(...args) {\n      return this.defs().gradient(...args)\n    }\n  },\n  // define gradient\n  Defs: {\n    gradient: wrapWithAttrCheck(function (type, block) {\n      return this.put(new Gradient(type)).update(block)\n    })\n  }\n})\n\nregister(Gradient, 'Gradient')\n", "import { nodeOrNew, register, wrapWithAttrCheck } from '../utils/adopter.js'\nimport { registerMethods } from '../utils/methods.js'\nimport Box from '../types/Box.js'\nimport Container from './Container.js'\nimport baseFind from '../modules/core/selector.js'\n\nexport default class Pattern extends Container {\n  // Initialize node\n  constructor(node, attrs = node) {\n    super(nodeOrNew('pattern', node), attrs)\n  }\n\n  // custom attr to handle transform\n  attr(a, b, c) {\n    if (a === 'transform') a = 'patternTransform'\n    return super.attr(a, b, c)\n  }\n\n  bbox() {\n    return new Box()\n  }\n\n  targets() {\n    return baseFind('svg [fill*=' + this.id() + ']')\n  }\n\n  // Alias string conversion to fill\n  toString() {\n    return this.url()\n  }\n\n  // Update pattern by rebuilding\n  update(block) {\n    // remove content\n    this.clear()\n\n    // invoke passed block\n    if (typeof block === 'function') {\n      block.call(this, this)\n    }\n\n    return this\n  }\n\n  // Return the fill id\n  url() {\n    return 'url(#' + this.id() + ')'\n  }\n}\n\nregisterMethods({\n  Container: {\n    // Create pattern element in defs\n    pattern(...args) {\n      return this.defs().pattern(...args)\n    }\n  },\n  Defs: {\n    pattern: wrapWithAttrCheck(function (width, height, block) {\n      return this.put(new Pattern()).update(block).attr({\n        x: 0,\n        y: 0,\n        width: width,\n        height: height,\n        patternUnits: 'userSpaceOnUse'\n      })\n    })\n  }\n})\n\nregister(Pattern, 'Pattern')\n", "import { isImage } from '../modules/core/regex.js'\nimport { nodeOrNew, register, wrapWithAttrCheck } from '../utils/adopter.js'\nimport { off, on } from '../modules/core/event.js'\nimport { registerAttrHook } from '../modules/core/attr.js'\nimport { registerMethods } from '../utils/methods.js'\nimport { xlink } from '../modules/core/namespaces.js'\nimport Pattern from './Pattern.js'\nimport Shape from './Shape.js'\nimport { globals } from '../utils/window.js'\n\nexport default class Image extends Shape {\n  constructor(node, attrs = node) {\n    super(nodeOrNew('image', node), attrs)\n  }\n\n  // (re)load image\n  load(url, callback) {\n    if (!url) return this\n\n    const img = new globals.window.Image()\n\n    on(\n      img,\n      'load',\n      function (e) {\n        const p = this.parent(Pattern)\n\n        // ensure image size\n        if (this.width() === 0 && this.height() === 0) {\n          this.size(img.width, img.height)\n        }\n\n        if (p instanceof Pattern) {\n          // ensure pattern size if not set\n          if (p.width() === 0 && p.height() === 0) {\n            p.size(this.width(), this.height())\n          }\n        }\n\n        if (typeof callback === 'function') {\n          callback.call(this, e)\n        }\n      },\n      this\n    )\n\n    on(img, 'load error', function () {\n      // dont forget to unbind memory leaking events\n      off(img)\n    })\n\n    return this.attr('href', (img.src = url), xlink)\n  }\n}\n\nregisterAttrHook(function (attr, val, _this) {\n  // convert image fill and stroke to patterns\n  if (attr === 'fill' || attr === 'stroke') {\n    if (isImage.test(val)) {\n      val = _this.root().defs().image(val)\n    }\n  }\n\n  if (val instanceof Image) {\n    val = _this\n      .root()\n      .defs()\n      .pattern(0, 0, (pattern) => {\n        pattern.add(val)\n      })\n  }\n\n  return val\n})\n\nregisterMethods({\n  Container: {\n    // create image element, load image and set its size\n    image: wrapWithAttrCheck(function (source, callback) {\n      return this.put(new Image()).size(0, 0).load(source, callback)\n    })\n  }\n})\n\nregister(Image, 'Image')\n", "import { delimiter } from '../modules/core/regex.js'\nimport SVGArray from './SVGArray.js'\nimport Box from './Box.js'\nimport Matrix from './Matrix.js'\n\nexport default class PointArray extends SVGArray {\n  // Get bounding box of points\n  bbox() {\n    let maxX = -Infinity\n    let maxY = -Infinity\n    let minX = Infinity\n    let minY = Infinity\n    this.forEach(function (el) {\n      maxX = Math.max(el[0], maxX)\n      maxY = Math.max(el[1], maxY)\n      minX = Math.min(el[0], minX)\n      minY = Math.min(el[1], minY)\n    })\n    return new Box(minX, minY, maxX - minX, maxY - minY)\n  }\n\n  // Move point string\n  move(x, y) {\n    const box = this.bbox()\n\n    // get relative offset\n    x -= box.x\n    y -= box.y\n\n    // move every point\n    if (!isNaN(x) && !isNaN(y)) {\n      for (let i = this.length - 1; i >= 0; i--) {\n        this[i] = [this[i][0] + x, this[i][1] + y]\n      }\n    }\n\n    return this\n  }\n\n  // Parse point string and flat array\n  parse(array = [0, 0]) {\n    const points = []\n\n    // if it is an array, we flatten it and therefore clone it to 1 depths\n    if (array instanceof Array) {\n      array = Array.prototype.concat.apply([], array)\n    } else {\n      // Else, it is considered as a string\n      // parse points\n      array = array.trim().split(delimiter).map(parseFloat)\n    }\n\n    // validate points - https://svgwg.org/svg2-draft/shapes.html#DataTypePoints\n    // Odd number of coordinates is an error. In such cases, drop the last odd coordinate.\n    if (array.length % 2 !== 0) array.pop()\n\n    // wrap points in two-tuples\n    for (let i = 0, len = array.length; i < len; i = i + 2) {\n      points.push([array[i], array[i + 1]])\n    }\n\n    return points\n  }\n\n  // Resize poly string\n  size(width, height) {\n    let i\n    const box = this.bbox()\n\n    // recalculate position of all points according to new size\n    for (i = this.length - 1; i >= 0; i--) {\n      if (box.width)\n        this[i][0] = ((this[i][0] - box.x) * width) / box.width + box.x\n      if (box.height)\n        this[i][1] = ((this[i][1] - box.y) * height) / box.height + box.y\n    }\n\n    return this\n  }\n\n  // Convert array to line object\n  toLine() {\n    return {\n      x1: this[0][0],\n      y1: this[0][1],\n      x2: this[1][0],\n      y2: this[1][1]\n    }\n  }\n\n  // Convert array to string\n  toString() {\n    const array = []\n    // convert to a poly point string\n    for (let i = 0, il = this.length; i < il; i++) {\n      array.push(this[i].join(','))\n    }\n\n    return array.join(' ')\n  }\n\n  transform(m) {\n    return this.clone().transformO(m)\n  }\n\n  // transform points with matrix (similar to Point.transform)\n  transformO(m) {\n    if (!Matrix.isMatrixLike(m)) {\n      m = new Matrix(m)\n    }\n\n    for (let i = this.length; i--; ) {\n      // Perform the matrix multiplication\n      const [x, y] = this[i]\n      this[i][0] = m.a * x + m.c * y + m.e\n      this[i][1] = m.b * x + m.d * y + m.f\n    }\n\n    return this\n  }\n}\n", "import PointArray from '../../types/PointArray.js'\n\nexport const MorphArray = PointArray\n\n// Move by left top corner over x-axis\nexport function x(x) {\n  return x == null ? this.bbox().x : this.move(x, this.bbox().y)\n}\n\n// Move by left top corner over y-axis\nexport function y(y) {\n  return y == null ? this.bbox().y : this.move(this.bbox().x, y)\n}\n\n// Set width of element\nexport function width(width) {\n  const b = this.bbox()\n  return width == null ? b.width : this.size(width, b.height)\n}\n\n// Set height of element\nexport function height(height) {\n  const b = this.bbox()\n  return height == null ? b.height : this.size(b.width, height)\n}\n", "import {\n  extend,\n  nodeOrNew,\n  register,\n  wrapWithAttrCheck\n} from '../utils/adopter.js'\nimport { proportionalSize } from '../utils/utils.js'\nimport { registerMethods } from '../utils/methods.js'\nimport PointArray from '../types/PointArray.js'\nimport Shape from './Shape.js'\nimport * as pointed from '../modules/core/pointed.js'\n\nexport default class Line extends Shape {\n  // Initialize node\n  constructor(node, attrs = node) {\n    super(nodeOrNew('line', node), attrs)\n  }\n\n  // Get array\n  array() {\n    return new PointArray([\n      [this.attr('x1'), this.attr('y1')],\n      [this.attr('x2'), this.attr('y2')]\n    ])\n  }\n\n  // Move by left top corner\n  move(x, y) {\n    return this.attr(this.array().move(x, y).toLine())\n  }\n\n  // Overwrite native plot() method\n  plot(x1, y1, x2, y2) {\n    if (x1 == null) {\n      return this.array()\n    } else if (typeof y1 !== 'undefined') {\n      x1 = { x1, y1, x2, y2 }\n    } else {\n      x1 = new PointArray(x1).toLine()\n    }\n\n    return this.attr(x1)\n  }\n\n  // Set element size to given width and height\n  size(width, height) {\n    const p = proportionalSize(this, width, height)\n    return this.attr(this.array().size(p.width, p.height).toLine())\n  }\n}\n\nextend(Line, pointed)\n\nregisterMethods({\n  Container: {\n    // Create a line element\n    line: wrapWithAttrCheck(function (...args) {\n      // make sure plot is called as a setter\n      // x1 is not necessarily a number, it can also be an array, a string and a PointArray\n      return Line.prototype.plot.apply(\n        this.put(new Line()),\n        args[0] != null ? args : [0, 0, 0, 0]\n      )\n    })\n  }\n})\n\nregister(Line, 'Line')\n", "import { nodeOrNew, register, wrapWithAttrCheck } from '../utils/adopter.js'\nimport { registerMethods } from '../utils/methods.js'\nimport Container from './Container.js'\n\nexport default class Marker extends Container {\n  // Initialize node\n  constructor(node, attrs = node) {\n    super(nodeOrNew('marker', node), attrs)\n  }\n\n  // Set height of element\n  height(height) {\n    return this.attr('markerHeight', height)\n  }\n\n  orient(orient) {\n    return this.attr('orient', orient)\n  }\n\n  // Set marker refX and refY\n  ref(x, y) {\n    return this.attr('refX', x).attr('refY', y)\n  }\n\n  // Return the fill id\n  toString() {\n    return 'url(#' + this.id() + ')'\n  }\n\n  // Update marker\n  update(block) {\n    // remove all content\n    this.clear()\n\n    // invoke passed block\n    if (typeof block === 'function') {\n      block.call(this, this)\n    }\n\n    return this\n  }\n\n  // Set width of element\n  width(width) {\n    return this.attr('markerWidth', width)\n  }\n}\n\nregisterMethods({\n  Container: {\n    marker(...args) {\n      // Create marker element in defs\n      return this.defs().marker(...args)\n    }\n  },\n  Defs: {\n    // Create marker\n    marker: wrapWithAttrCheck(function (width, height, block) {\n      // Set default viewbox to match the width and height, set ref to cx and cy and set orient to auto\n      return this.put(new Marker())\n        .size(width, height)\n        .ref(width / 2, height / 2)\n        .viewbox(0, 0, width, height)\n        .attr('orient', 'auto')\n        .update(block)\n    })\n  },\n  marker: {\n    // Create and attach markers\n    marker(marker, width, height, block) {\n      let attr = ['marker']\n\n      // Build attribute name\n      if (marker !== 'all') attr.push(marker)\n      attr = attr.join('-')\n\n      // Set marker attribute\n      marker =\n        arguments[1] instanceof Marker\n          ? arguments[1]\n          : this.defs().marker(width, height, block)\n\n      return this.attr(attr, marker)\n    }\n  }\n})\n\nregister(Marker, 'Marker')\n", "import { timeline } from '../modules/core/defaults.js'\nimport { extend } from '../utils/adopter.js'\n\n/***\nBase Class\n==========\nThe base stepper class that will be\n***/\n\nfunction makeSetterGetter(k, f) {\n  return function (v) {\n    if (v == null) return this[k]\n    this[k] = v\n    if (f) f.call(this)\n    return this\n  }\n}\n\nexport const easing = {\n  '-': function (pos) {\n    return pos\n  },\n  '<>': function (pos) {\n    return -Math.cos(pos * Math.PI) / 2 + 0.5\n  },\n  '>': function (pos) {\n    return Math.sin((pos * Math.PI) / 2)\n  },\n  '<': function (pos) {\n    return -Math.cos((pos * Math.PI) / 2) + 1\n  },\n  bezier: function (x1, y1, x2, y2) {\n    // see https://www.w3.org/TR/css-easing-1/#cubic-bezier-algo\n    return function (t) {\n      if (t < 0) {\n        if (x1 > 0) {\n          return (y1 / x1) * t\n        } else if (x2 > 0) {\n          return (y2 / x2) * t\n        } else {\n          return 0\n        }\n      } else if (t > 1) {\n        if (x2 < 1) {\n          return ((1 - y2) / (1 - x2)) * t + (y2 - x2) / (1 - x2)\n        } else if (x1 < 1) {\n          return ((1 - y1) / (1 - x1)) * t + (y1 - x1) / (1 - x1)\n        } else {\n          return 1\n        }\n      } else {\n        return 3 * t * (1 - t) ** 2 * y1 + 3 * t ** 2 * (1 - t) * y2 + t ** 3\n      }\n    }\n  },\n  // see https://www.w3.org/TR/css-easing-1/#step-timing-function-algo\n  steps: function (steps, stepPosition = 'end') {\n    // deal with \"jump-\" prefix\n    stepPosition = stepPosition.split('-').reverse()[0]\n\n    let jumps = steps\n    if (stepPosition === 'none') {\n      --jumps\n    } else if (stepPosition === 'both') {\n      ++jumps\n    }\n\n    // The beforeFlag is essentially useless\n    return (t, beforeFlag = false) => {\n      // Step is called currentStep in referenced url\n      let step = Math.floor(t * steps)\n      const jumping = (t * step) % 1 === 0\n\n      if (stepPosition === 'start' || stepPosition === 'both') {\n        ++step\n      }\n\n      if (beforeFlag && jumping) {\n        --step\n      }\n\n      if (t >= 0 && step < 0) {\n        step = 0\n      }\n\n      if (t <= 1 && step > jumps) {\n        step = jumps\n      }\n\n      return step / jumps\n    }\n  }\n}\n\nexport class Stepper {\n  done() {\n    return false\n  }\n}\n\n/***\nEasing Functions\n================\n***/\n\nexport class Ease extends Stepper {\n  constructor(fn = timeline.ease) {\n    super()\n    this.ease = easing[fn] || fn\n  }\n\n  step(from, to, pos) {\n    if (typeof from !== 'number') {\n      return pos < 1 ? from : to\n    }\n    return from + (to - from) * this.ease(pos)\n  }\n}\n\n/***\nController Types\n================\n***/\n\nexport class Controller extends Stepper {\n  constructor(fn) {\n    super()\n    this.stepper = fn\n  }\n\n  done(c) {\n    return c.done\n  }\n\n  step(current, target, dt, c) {\n    return this.stepper(current, target, dt, c)\n  }\n}\n\nfunction recalculate() {\n  // Apply the default parameters\n  const duration = (this._duration || 500) / 1000\n  const overshoot = this._overshoot || 0\n\n  // Calculate the PID natural response\n  const eps = 1e-10\n  const pi = Math.PI\n  const os = Math.log(overshoot / 100 + eps)\n  const zeta = -os / Math.sqrt(pi * pi + os * os)\n  const wn = 3.9 / (zeta * duration)\n\n  // Calculate the Spring values\n  this.d = 2 * zeta * wn\n  this.k = wn * wn\n}\n\nexport class Spring extends Controller {\n  constructor(duration = 500, overshoot = 0) {\n    super()\n    this.duration(duration).overshoot(overshoot)\n  }\n\n  step(current, target, dt, c) {\n    if (typeof current === 'string') return current\n    c.done = dt === Infinity\n    if (dt === Infinity) return target\n    if (dt === 0) return current\n\n    if (dt > 100) dt = 16\n\n    dt /= 1000\n\n    // Get the previous velocity\n    const velocity = c.velocity || 0\n\n    // Apply the control to get the new position and store it\n    const acceleration = -this.d * velocity - this.k * (current - target)\n    const newPosition = current + velocity * dt + (acceleration * dt * dt) / 2\n\n    // Store the velocity\n    c.velocity = velocity + acceleration * dt\n\n    // Figure out if we have converged, and if so, pass the value\n    c.done = Math.abs(target - newPosition) + Math.abs(velocity) < 0.002\n    return c.done ? target : newPosition\n  }\n}\n\nextend(Spring, {\n  duration: makeSetterGetter('_duration', recalculate),\n  overshoot: makeSetterGetter('_overshoot', recalculate)\n})\n\nexport class PID extends Controller {\n  constructor(p = 0.1, i = 0.01, d = 0, windup = 1000) {\n    super()\n    this.p(p).i(i).d(d).windup(windup)\n  }\n\n  step(current, target, dt, c) {\n    if (typeof current === 'string') return current\n    c.done = dt === Infinity\n\n    if (dt === Infinity) return target\n    if (dt === 0) return current\n\n    const p = target - current\n    let i = (c.integral || 0) + p * dt\n    const d = (p - (c.error || 0)) / dt\n    const windup = this._windup\n\n    // antiwindup\n    if (windup !== false) {\n      i = Math.max(-windup, Math.min(i, windup))\n    }\n\n    c.error = p\n    c.integral = i\n\n    c.done = Math.abs(p) < 0.001\n\n    return c.done ? target : current + (this.P * p + this.I * i + this.D * d)\n  }\n}\n\nextend(PID, {\n  windup: makeSetterGetter('_windup'),\n  p: makeSetterGetter('P'),\n  i: makeSetterGetter('I'),\n  d: makeSetterGetter('D')\n})\n", "import { isPathLetter } from '../modules/core/regex.js'\nimport Point from '../types/Point.js'\n\nconst segmentParameters = {\n  M: 2,\n  L: 2,\n  H: 1,\n  V: 1,\n  C: 6,\n  S: 4,\n  Q: 4,\n  T: 2,\n  A: 7,\n  Z: 0\n}\n\nconst pathHandlers = {\n  M: function (c, p, p0) {\n    p.x = p0.x = c[0]\n    p.y = p0.y = c[1]\n\n    return ['M', p.x, p.y]\n  },\n  L: function (c, p) {\n    p.x = c[0]\n    p.y = c[1]\n    return ['L', c[0], c[1]]\n  },\n  H: function (c, p) {\n    p.x = c[0]\n    return ['H', c[0]]\n  },\n  V: function (c, p) {\n    p.y = c[0]\n    return ['V', c[0]]\n  },\n  C: function (c, p) {\n    p.x = c[4]\n    p.y = c[5]\n    return ['C', c[0], c[1], c[2], c[3], c[4], c[5]]\n  },\n  S: function (c, p) {\n    p.x = c[2]\n    p.y = c[3]\n    return ['S', c[0], c[1], c[2], c[3]]\n  },\n  Q: function (c, p) {\n    p.x = c[2]\n    p.y = c[3]\n    return ['Q', c[0], c[1], c[2], c[3]]\n  },\n  T: function (c, p) {\n    p.x = c[0]\n    p.y = c[1]\n    return ['T', c[0], c[1]]\n  },\n  Z: function (c, p, p0) {\n    p.x = p0.x\n    p.y = p0.y\n    return ['Z']\n  },\n  A: function (c, p) {\n    p.x = c[5]\n    p.y = c[6]\n    return ['A', c[0], c[1], c[2], c[3], c[4], c[5], c[6]]\n  }\n}\n\nconst mlhvqtcsaz = 'mlhvqtcsaz'.split('')\n\nfor (let i = 0, il = mlhvqtcsaz.length; i < il; ++i) {\n  pathHandlers[mlhvqtcsaz[i]] = (function (i) {\n    return function (c, p, p0) {\n      if (i === 'H') c[0] = c[0] + p.x\n      else if (i === 'V') c[0] = c[0] + p.y\n      else if (i === 'A') {\n        c[5] = c[5] + p.x\n        c[6] = c[6] + p.y\n      } else {\n        for (let j = 0, jl = c.length; j < jl; ++j) {\n          c[j] = c[j] + (j % 2 ? p.y : p.x)\n        }\n      }\n\n      return pathHandlers[i](c, p, p0)\n    }\n  })(mlhvqtcsaz[i].toUpperCase())\n}\n\nfunction makeAbsolut(parser) {\n  const command = parser.segment[0]\n  return pathHandlers[command](parser.segment.slice(1), parser.p, parser.p0)\n}\n\nfunction segmentComplete(parser) {\n  return (\n    parser.segment.length &&\n    parser.segment.length - 1 ===\n      segmentParameters[parser.segment[0].toUpperCase()]\n  )\n}\n\nfunction startNewSegment(parser, token) {\n  parser.inNumber && finalizeNumber(parser, false)\n  const pathLetter = isPathLetter.test(token)\n\n  if (pathLetter) {\n    parser.segment = [token]\n  } else {\n    const lastCommand = parser.lastCommand\n    const small = lastCommand.toLowerCase()\n    const isSmall = lastCommand === small\n    parser.segment = [small === 'm' ? (isSmall ? 'l' : 'L') : lastCommand]\n  }\n\n  parser.inSegment = true\n  parser.lastCommand = parser.segment[0]\n\n  return pathLetter\n}\n\nfunction finalizeNumber(parser, inNumber) {\n  if (!parser.inNumber) throw new Error('Parser Error')\n  parser.number && parser.segment.push(parseFloat(parser.number))\n  parser.inNumber = inNumber\n  parser.number = ''\n  parser.pointSeen = false\n  parser.hasExponent = false\n\n  if (segmentComplete(parser)) {\n    finalizeSegment(parser)\n  }\n}\n\nfunction finalizeSegment(parser) {\n  parser.inSegment = false\n  if (parser.absolute) {\n    parser.segment = makeAbsolut(parser)\n  }\n  parser.segments.push(parser.segment)\n}\n\nfunction isArcFlag(parser) {\n  if (!parser.segment.length) return false\n  const isArc = parser.segment[0].toUpperCase() === 'A'\n  const length = parser.segment.length\n\n  return isArc && (length === 4 || length === 5)\n}\n\nfunction isExponential(parser) {\n  return parser.lastToken.toUpperCase() === 'E'\n}\n\nconst pathDelimiters = new Set([' ', ',', '\\t', '\\n', '\\r', '\\f'])\nexport function pathParser(d, toAbsolute = true) {\n  let index = 0\n  let token = ''\n  const parser = {\n    segment: [],\n    inNumber: false,\n    number: '',\n    lastToken: '',\n    inSegment: false,\n    segments: [],\n    pointSeen: false,\n    hasExponent: false,\n    absolute: toAbsolute,\n    p0: new Point(),\n    p: new Point()\n  }\n\n  while (((parser.lastToken = token), (token = d.charAt(index++)))) {\n    if (!parser.inSegment) {\n      if (startNewSegment(parser, token)) {\n        continue\n      }\n    }\n\n    if (token === '.') {\n      if (parser.pointSeen || parser.hasExponent) {\n        finalizeNumber(parser, false)\n        --index\n        continue\n      }\n      parser.inNumber = true\n      parser.pointSeen = true\n      parser.number += token\n      continue\n    }\n\n    if (!isNaN(parseInt(token))) {\n      if (parser.number === '0' || isArcFlag(parser)) {\n        parser.inNumber = true\n        parser.number = token\n        finalizeNumber(parser, true)\n        continue\n      }\n\n      parser.inNumber = true\n      parser.number += token\n      continue\n    }\n\n    if (pathDelimiters.has(token)) {\n      if (parser.inNumber) {\n        finalizeNumber(parser, false)\n      }\n      continue\n    }\n\n    if (token === '-' || token === '+') {\n      if (parser.inNumber && !isExponential(parser)) {\n        finalizeNumber(parser, false)\n        --index\n        continue\n      }\n      parser.number += token\n      parser.inNumber = true\n      continue\n    }\n\n    if (token.toUpperCase() === 'E') {\n      parser.number += token\n      parser.hasExponent = true\n      continue\n    }\n\n    if (isPathLetter.test(token)) {\n      if (parser.inNumber) {\n        finalizeNumber(parser, false)\n      } else if (!segmentComplete(parser)) {\n        throw new Error('parser Error')\n      } else {\n        finalizeSegment(parser)\n      }\n      --index\n    }\n  }\n\n  if (parser.inNumber) {\n    finalizeNumber(parser, false)\n  }\n\n  if (parser.inSegment && segmentComplete(parser)) {\n    finalizeSegment(parser)\n  }\n\n  return parser.segments\n}\n", "import SVGArray from './SVGArray.js'\nimport parser from '../modules/core/parser.js'\nimport Box from './Box.js'\nimport { pathParser } from '../utils/pathParser.js'\n\nfunction arrayToString(a) {\n  let s = ''\n  for (let i = 0, il = a.length; i < il; i++) {\n    s += a[i][0]\n\n    if (a[i][1] != null) {\n      s += a[i][1]\n\n      if (a[i][2] != null) {\n        s += ' '\n        s += a[i][2]\n\n        if (a[i][3] != null) {\n          s += ' '\n          s += a[i][3]\n          s += ' '\n          s += a[i][4]\n\n          if (a[i][5] != null) {\n            s += ' '\n            s += a[i][5]\n            s += ' '\n            s += a[i][6]\n\n            if (a[i][7] != null) {\n              s += ' '\n              s += a[i][7]\n            }\n          }\n        }\n      }\n    }\n  }\n\n  return s + ' '\n}\n\nexport default class PathArray extends SVGArray {\n  // Get bounding box of path\n  bbox() {\n    parser().path.setAttribute('d', this.toString())\n    return new Box(parser.nodes.path.getBBox())\n  }\n\n  // Move path string\n  move(x, y) {\n    // get bounding box of current situation\n    const box = this.bbox()\n\n    // get relative offset\n    x -= box.x\n    y -= box.y\n\n    if (!isNaN(x) && !isNaN(y)) {\n      // move every point\n      for (let l, i = this.length - 1; i >= 0; i--) {\n        l = this[i][0]\n\n        if (l === 'M' || l === 'L' || l === 'T') {\n          this[i][1] += x\n          this[i][2] += y\n        } else if (l === 'H') {\n          this[i][1] += x\n        } else if (l === 'V') {\n          this[i][1] += y\n        } else if (l === 'C' || l === 'S' || l === 'Q') {\n          this[i][1] += x\n          this[i][2] += y\n          this[i][3] += x\n          this[i][4] += y\n\n          if (l === 'C') {\n            this[i][5] += x\n            this[i][6] += y\n          }\n        } else if (l === 'A') {\n          this[i][6] += x\n          this[i][7] += y\n        }\n      }\n    }\n\n    return this\n  }\n\n  // Absolutize and parse path to array\n  parse(d = 'M0 0') {\n    if (Array.isArray(d)) {\n      d = Array.prototype.concat.apply([], d).toString()\n    }\n\n    return pathParser(d)\n  }\n\n  // Resize path string\n  size(width, height) {\n    // get bounding box of current situation\n    const box = this.bbox()\n    let i, l\n\n    // If the box width or height is 0 then we ignore\n    // transformations on the respective axis\n    box.width = box.width === 0 ? 1 : box.width\n    box.height = box.height === 0 ? 1 : box.height\n\n    // recalculate position of all points according to new size\n    for (i = this.length - 1; i >= 0; i--) {\n      l = this[i][0]\n\n      if (l === 'M' || l === 'L' || l === 'T') {\n        this[i][1] = ((this[i][1] - box.x) * width) / box.width + box.x\n        this[i][2] = ((this[i][2] - box.y) * height) / box.height + box.y\n      } else if (l === 'H') {\n        this[i][1] = ((this[i][1] - box.x) * width) / box.width + box.x\n      } else if (l === 'V') {\n        this[i][1] = ((this[i][1] - box.y) * height) / box.height + box.y\n      } else if (l === 'C' || l === 'S' || l === 'Q') {\n        this[i][1] = ((this[i][1] - box.x) * width) / box.width + box.x\n        this[i][2] = ((this[i][2] - box.y) * height) / box.height + box.y\n        this[i][3] = ((this[i][3] - box.x) * width) / box.width + box.x\n        this[i][4] = ((this[i][4] - box.y) * height) / box.height + box.y\n\n        if (l === 'C') {\n          this[i][5] = ((this[i][5] - box.x) * width) / box.width + box.x\n          this[i][6] = ((this[i][6] - box.y) * height) / box.height + box.y\n        }\n      } else if (l === 'A') {\n        // resize radii\n        this[i][1] = (this[i][1] * width) / box.width\n        this[i][2] = (this[i][2] * height) / box.height\n\n        // move position values\n        this[i][6] = ((this[i][6] - box.x) * width) / box.width + box.x\n        this[i][7] = ((this[i][7] - box.y) * height) / box.height + box.y\n      }\n    }\n\n    return this\n  }\n\n  // Convert array to string\n  toString() {\n    return arrayToString(this)\n  }\n}\n", "import { Ease } from './Controller.js'\nimport {\n  delimiter,\n  numberAndUnit,\n  isPathLetter\n} from '../modules/core/regex.js'\nimport { extend } from '../utils/adopter.js'\nimport Color from '../types/Color.js'\nimport PathArray from '../types/PathArray.js'\nimport SVGArray from '../types/SVGArray.js'\nimport SVGNumber from '../types/SVGNumber.js'\n\nconst getClassForType = (value) => {\n  const type = typeof value\n\n  if (type === 'number') {\n    return SVGNumber\n  } else if (type === 'string') {\n    if (Color.isColor(value)) {\n      return Color\n    } else if (delimiter.test(value)) {\n      return isPathLetter.test(value) ? PathArray : SVGArray\n    } else if (numberAndUnit.test(value)) {\n      return SVGNumber\n    } else {\n      return NonMorphable\n    }\n  } else if (morphableTypes.indexOf(value.constructor) > -1) {\n    return value.constructor\n  } else if (Array.isArray(value)) {\n    return SVGArray\n  } else if (type === 'object') {\n    return ObjectBag\n  } else {\n    return NonMorphable\n  }\n}\n\nexport default class Morphable {\n  constructor(stepper) {\n    this._stepper = stepper || new Ease('-')\n\n    this._from = null\n    this._to = null\n    this._type = null\n    this._context = null\n    this._morphObj = null\n  }\n\n  at(pos) {\n    return this._morphObj.morph(\n      this._from,\n      this._to,\n      pos,\n      this._stepper,\n      this._context\n    )\n  }\n\n  done() {\n    const complete = this._context.map(this._stepper.done).reduce(function (\n      last,\n      curr\n    ) {\n      return last && curr\n    }, true)\n    return complete\n  }\n\n  from(val) {\n    if (val == null) {\n      return this._from\n    }\n\n    this._from = this._set(val)\n    return this\n  }\n\n  stepper(stepper) {\n    if (stepper == null) return this._stepper\n    this._stepper = stepper\n    return this\n  }\n\n  to(val) {\n    if (val == null) {\n      return this._to\n    }\n\n    this._to = this._set(val)\n    return this\n  }\n\n  type(type) {\n    // getter\n    if (type == null) {\n      return this._type\n    }\n\n    // setter\n    this._type = type\n    return this\n  }\n\n  _set(value) {\n    if (!this._type) {\n      this.type(getClassForType(value))\n    }\n\n    let result = new this._type(value)\n    if (this._type === Color) {\n      result = this._to\n        ? result[this._to[4]]()\n        : this._from\n          ? result[this._from[4]]()\n          : result\n    }\n\n    if (this._type === ObjectBag) {\n      result = this._to\n        ? result.align(this._to)\n        : this._from\n          ? result.align(this._from)\n          : result\n    }\n\n    result = result.toConsumable()\n\n    this._morphObj = this._morphObj || new this._type()\n    this._context =\n      this._context ||\n      Array.apply(null, Array(result.length))\n        .map(Object)\n        .map(function (o) {\n          o.done = true\n          return o\n        })\n    return result\n  }\n}\n\nexport class NonMorphable {\n  constructor(...args) {\n    this.init(...args)\n  }\n\n  init(val) {\n    val = Array.isArray(val) ? val[0] : val\n    this.value = val\n    return this\n  }\n\n  toArray() {\n    return [this.value]\n  }\n\n  valueOf() {\n    return this.value\n  }\n}\n\nexport class TransformBag {\n  constructor(...args) {\n    this.init(...args)\n  }\n\n  init(obj) {\n    if (Array.isArray(obj)) {\n      obj = {\n        scaleX: obj[0],\n        scaleY: obj[1],\n        shear: obj[2],\n        rotate: obj[3],\n        translateX: obj[4],\n        translateY: obj[5],\n        originX: obj[6],\n        originY: obj[7]\n      }\n    }\n\n    Object.assign(this, TransformBag.defaults, obj)\n    return this\n  }\n\n  toArray() {\n    const v = this\n\n    return [\n      v.scaleX,\n      v.scaleY,\n      v.shear,\n      v.rotate,\n      v.translateX,\n      v.translateY,\n      v.originX,\n      v.originY\n    ]\n  }\n}\n\nTransformBag.defaults = {\n  scaleX: 1,\n  scaleY: 1,\n  shear: 0,\n  rotate: 0,\n  translateX: 0,\n  translateY: 0,\n  originX: 0,\n  originY: 0\n}\n\nconst sortByKey = (a, b) => {\n  return a[0] < b[0] ? -1 : a[0] > b[0] ? 1 : 0\n}\n\nexport class ObjectBag {\n  constructor(...args) {\n    this.init(...args)\n  }\n\n  align(other) {\n    const values = this.values\n    for (let i = 0, il = values.length; i < il; ++i) {\n      // If the type is the same we only need to check if the color is in the correct format\n      if (values[i + 1] === other[i + 1]) {\n        if (values[i + 1] === Color && other[i + 7] !== values[i + 7]) {\n          const space = other[i + 7]\n          const color = new Color(this.values.splice(i + 3, 5))\n            [space]()\n            .toArray()\n          this.values.splice(i + 3, 0, ...color)\n        }\n\n        i += values[i + 2] + 2\n        continue\n      }\n\n      if (!other[i + 1]) {\n        return this\n      }\n\n      // The types differ, so we overwrite the new type with the old one\n      // And initialize it with the types default (e.g. black for color or 0 for number)\n      const defaultObject = new other[i + 1]().toArray()\n\n      // Than we fix the values array\n      const toDelete = values[i + 2] + 3\n\n      values.splice(\n        i,\n        toDelete,\n        other[i],\n        other[i + 1],\n        other[i + 2],\n        ...defaultObject\n      )\n\n      i += values[i + 2] + 2\n    }\n    return this\n  }\n\n  init(objOrArr) {\n    this.values = []\n\n    if (Array.isArray(objOrArr)) {\n      this.values = objOrArr.slice()\n      return\n    }\n\n    objOrArr = objOrArr || {}\n    const entries = []\n\n    for (const i in objOrArr) {\n      const Type = getClassForType(objOrArr[i])\n      const val = new Type(objOrArr[i]).toArray()\n      entries.push([i, Type, val.length, ...val])\n    }\n\n    entries.sort(sortByKey)\n\n    this.values = entries.reduce((last, curr) => last.concat(curr), [])\n    return this\n  }\n\n  toArray() {\n    return this.values\n  }\n\n  valueOf() {\n    const obj = {}\n    const arr = this.values\n\n    // for (var i = 0, len = arr.length; i < len; i += 2) {\n    while (arr.length) {\n      const key = arr.shift()\n      const Type = arr.shift()\n      const num = arr.shift()\n      const values = arr.splice(0, num)\n      obj[key] = new Type(values) // .valueOf()\n    }\n\n    return obj\n  }\n}\n\nconst morphableTypes = [NonMorphable, TransformBag, ObjectBag]\n\nexport function registerMorphableType(type = []) {\n  morphableTypes.push(...[].concat(type))\n}\n\nexport function makeMorphable() {\n  extend(morphableTypes, {\n    to(val) {\n      return new Morphable()\n        .type(this.constructor)\n        .from(this.toArray()) // this.valueOf())\n        .to(val)\n    },\n    fromArray(arr) {\n      this.init(arr)\n      return this\n    },\n    toConsumable() {\n      return this.toArray()\n    },\n    morph(from, to, pos, stepper, context) {\n      const mapper = function (i, index) {\n        return stepper.step(i, to[index], pos, context[index], context)\n      }\n\n      return this.fromArray(from.map(mapper))\n    }\n  })\n}\n", "import { nodeOrNew, register, wrapWithAttrCheck } from '../utils/adopter.js'\nimport { proportionalSize } from '../utils/utils.js'\nimport { registerMethods } from '../utils/methods.js'\nimport PathArray from '../types/PathArray.js'\nimport Shape from './Shape.js'\n\nexport default class Path extends Shape {\n  // Initialize node\n  constructor(node, attrs = node) {\n    super(nodeOrNew('path', node), attrs)\n  }\n\n  // Get array\n  array() {\n    return this._array || (this._array = new PathArray(this.attr('d')))\n  }\n\n  // Clear array cache\n  clear() {\n    delete this._array\n    return this\n  }\n\n  // Set height of element\n  height(height) {\n    return height == null\n      ? this.bbox().height\n      : this.size(this.bbox().width, height)\n  }\n\n  // Move by left top corner\n  move(x, y) {\n    return this.attr('d', this.array().move(x, y))\n  }\n\n  // Plot new path\n  plot(d) {\n    return d == null\n      ? this.array()\n      : this.clear().attr(\n          'd',\n          typeof d === 'string' ? d : (this._array = new PathArray(d))\n        )\n  }\n\n  // Set element size to given width and height\n  size(width, height) {\n    const p = proportionalSize(this, width, height)\n    return this.attr('d', this.array().size(p.width, p.height))\n  }\n\n  // Set width of element\n  width(width) {\n    return width == null\n      ? this.bbox().width\n      : this.size(width, this.bbox().height)\n  }\n\n  // Move by left top corner over x-axis\n  x(x) {\n    return x == null ? this.bbox().x : this.move(x, this.bbox().y)\n  }\n\n  // Move by left top corner over y-axis\n  y(y) {\n    return y == null ? this.bbox().y : this.move(this.bbox().x, y)\n  }\n}\n\n// Define morphable array\nPath.prototype.MorphArray = PathArray\n\n// Add parent method\nregisterMethods({\n  Container: {\n    // Create a wrapped path element\n    path: wrapWithAttrCheck(function (d) {\n      // make sure plot is called as a setter\n      return this.put(new Path()).plot(d || new PathArray())\n    })\n  }\n})\n\nregister(Path, 'Path')\n", "import { proportionalSize } from '../../utils/utils.js'\nimport PointArray from '../../types/PointArray.js'\n\n// Get array\nexport function array() {\n  return this._array || (this._array = new PointArray(this.attr('points')))\n}\n\n// Clear array cache\nexport function clear() {\n  delete this._array\n  return this\n}\n\n// Move by left top corner\nexport function move(x, y) {\n  return this.attr('points', this.array().move(x, y))\n}\n\n// Plot new path\nexport function plot(p) {\n  return p == null\n    ? this.array()\n    : this.clear().attr(\n        'points',\n        typeof p === 'string' ? p : (this._array = new PointArray(p))\n      )\n}\n\n// Set element size to given width and height\nexport function size(width, height) {\n  const p = proportionalSize(this, width, height)\n  return this.attr('points', this.array().size(p.width, p.height))\n}\n", "import {\n  extend,\n  nodeOrNew,\n  register,\n  wrapWithAttrCheck\n} from '../utils/adopter.js'\nimport { registerMethods } from '../utils/methods.js'\nimport PointArray from '../types/PointArray.js'\nimport Shape from './Shape.js'\nimport * as pointed from '../modules/core/pointed.js'\nimport * as poly from '../modules/core/poly.js'\n\nexport default class Polygon extends Shape {\n  // Initialize node\n  constructor(node, attrs = node) {\n    super(nodeOrNew('polygon', node), attrs)\n  }\n}\n\nregisterMethods({\n  Container: {\n    // Create a wrapped polygon element\n    polygon: wrapWithAttrCheck(function (p) {\n      // make sure plot is called as a setter\n      return this.put(new Polygon()).plot(p || new PointArray())\n    })\n  }\n})\n\nextend(Polygon, pointed)\nextend(Polygon, poly)\nregister(Polygon, 'Polygon')\n", "import {\n  extend,\n  nodeOrNew,\n  register,\n  wrapWithAttrCheck\n} from '../utils/adopter.js'\nimport { registerMethods } from '../utils/methods.js'\nimport PointArray from '../types/PointArray.js'\nimport Shape from './Shape.js'\nimport * as pointed from '../modules/core/pointed.js'\nimport * as poly from '../modules/core/poly.js'\n\nexport default class Polyline extends Shape {\n  // Initialize node\n  constructor(node, attrs = node) {\n    super(nodeOrNew('polyline', node), attrs)\n  }\n}\n\nregisterMethods({\n  Container: {\n    // Create a wrapped polygon element\n    polyline: wrapWithAttrCheck(function (p) {\n      // make sure plot is called as a setter\n      return this.put(new Polyline()).plot(p || new PointArray())\n    })\n  }\n})\n\nextend(Polyline, pointed)\nextend(Polyline, poly)\nregister(Polyline, 'Polyline')\n", "import {\n  extend,\n  nodeOrNew,\n  register,\n  wrapWithAttrCheck\n} from '../utils/adopter.js'\nimport { registerMethods } from '../utils/methods.js'\nimport { rx, ry } from '../modules/core/circled.js'\nimport Shape from './Shape.js'\n\nexport default class Rect extends Shape {\n  // Initialize node\n  constructor(node, attrs = node) {\n    super(nodeOrNew('rect', node), attrs)\n  }\n}\n\nextend(Rect, { rx, ry })\n\nregisterMethods({\n  Container: {\n    // Create a rect element\n    rect: wrapWithAttrCheck(function (width, height) {\n      return this.put(new Rect()).size(width, height)\n    })\n  }\n})\n\nregister(Rect, 'Rect')\n", "export default class Queue {\n  constructor() {\n    this._first = null\n    this._last = null\n  }\n\n  // Shows us the first item in the list\n  first() {\n    return this._first && this._first.value\n  }\n\n  // Shows us the last item in the list\n  last() {\n    return this._last && this._last.value\n  }\n\n  push(value) {\n    // An item stores an id and the provided value\n    const item =\n      typeof value.next !== 'undefined'\n        ? value\n        : { value: value, next: null, prev: null }\n\n    // Deal with the queue being empty or populated\n    if (this._last) {\n      item.prev = this._last\n      this._last.next = item\n      this._last = item\n    } else {\n      this._last = item\n      this._first = item\n    }\n\n    // Return the current item\n    return item\n  }\n\n  // Removes the item that was returned from the push\n  remove(item) {\n    // Relink the previous item\n    if (item.prev) item.prev.next = item.next\n    if (item.next) item.next.prev = item.prev\n    if (item === this._last) this._last = item.prev\n    if (item === this._first) this._first = item.next\n\n    // Invalidate item\n    item.prev = null\n    item.next = null\n  }\n\n  shift() {\n    // Check if we have a value\n    const remove = this._first\n    if (!remove) return null\n\n    // If we do, remove it and relink things\n    this._first = remove.next\n    if (this._first) this._first.prev = null\n    this._last = this._first ? this._last : null\n    return remove.value\n  }\n}\n", "import { globals } from '../utils/window.js'\nimport Queue from './Queue.js'\n\nconst Animator = {\n  nextDraw: null,\n  frames: new Queue(),\n  timeouts: new Queue(),\n  immediates: new Queue(),\n  timer: () => globals.window.performance || globals.window.Date,\n  transforms: [],\n\n  frame(fn) {\n    // Store the node\n    const node = Animator.frames.push({ run: fn })\n\n    // Request an animation frame if we don't have one\n    if (Animator.nextDraw === null) {\n      Animator.nextDraw = globals.window.requestAnimationFrame(Animator._draw)\n    }\n\n    // Return the node so we can remove it easily\n    return node\n  },\n\n  timeout(fn, delay) {\n    delay = delay || 0\n\n    // Work out when the event should fire\n    const time = Animator.timer().now() + delay\n\n    // Add the timeout to the end of the queue\n    const node = Animator.timeouts.push({ run: fn, time: time })\n\n    // Request another animation frame if we need one\n    if (Animator.nextDraw === null) {\n      Animator.nextDraw = globals.window.requestAnimationFrame(Animator._draw)\n    }\n\n    return node\n  },\n\n  immediate(fn) {\n    // Add the immediate fn to the end of the queue\n    const node = Animator.immediates.push(fn)\n    // Request another animation frame if we need one\n    if (Animator.nextDraw === null) {\n      Animator.nextDraw = globals.window.requestAnimationFrame(Animator._draw)\n    }\n\n    return node\n  },\n\n  cancelFrame(node) {\n    node != null && Animator.frames.remove(node)\n  },\n\n  clearTimeout(node) {\n    node != null && Animator.timeouts.remove(node)\n  },\n\n  cancelImmediate(node) {\n    node != null && Animator.immediates.remove(node)\n  },\n\n  _draw(now) {\n    // Run all the timeouts we can run, if they are not ready yet, add them\n    // to the end of the queue immediately! (bad timeouts!!! [sarcasm])\n    let nextTimeout = null\n    const lastTimeout = Animator.timeouts.last()\n    while ((nextTimeout = Animator.timeouts.shift())) {\n      // Run the timeout if its time, or push it to the end\n      if (now >= nextTimeout.time) {\n        nextTimeout.run()\n      } else {\n        Animator.timeouts.push(nextTimeout)\n      }\n\n      // If we hit the last item, we should stop shifting out more items\n      if (nextTimeout === lastTimeout) break\n    }\n\n    // Run all of the animation frames\n    let nextFrame = null\n    const lastFrame = Animator.frames.last()\n    while (nextFrame !== lastFrame && (nextFrame = Animator.frames.shift())) {\n      nextFrame.run(now)\n    }\n\n    let nextImmediate = null\n    while ((nextImmediate = Animator.immediates.shift())) {\n      nextImmediate()\n    }\n\n    // If we have remaining timeouts or frames, draw until we don't anymore\n    Animator.nextDraw =\n      Animator.timeouts.first() || Animator.frames.first()\n        ? globals.window.requestAnimationFrame(Animator._draw)\n        : null\n  }\n}\n\nexport default Animator\n", "import { globals } from '../utils/window.js'\nimport { registerMethods } from '../utils/methods.js'\nimport Animator from './Animator.js'\nimport EventTarget from '../types/EventTarget.js'\n\nconst makeSchedule = function (runnerInfo) {\n  const start = runnerInfo.start\n  const duration = runnerInfo.runner.duration()\n  const end = start + duration\n  return {\n    start: start,\n    duration: duration,\n    end: end,\n    runner: runnerInfo.runner\n  }\n}\n\nconst defaultSource = function () {\n  const w = globals.window\n  return (w.performance || w.Date).now()\n}\n\nexport default class Timeline extends EventTarget {\n  // Construct a new timeline on the given element\n  constructor(timeSource = defaultSource) {\n    super()\n\n    this._timeSource = timeSource\n\n    // terminate resets all variables to their initial state\n    this.terminate()\n  }\n\n  active() {\n    return !!this._nextFrame\n  }\n\n  finish() {\n    // Go to end and pause\n    this.time(this.getEndTimeOfTimeline() + 1)\n    return this.pause()\n  }\n\n  // Calculates the end of the timeline\n  getEndTime() {\n    const lastRunnerInfo = this.getLastRunnerInfo()\n    const lastDuration = lastRunnerInfo ? lastRunnerInfo.runner.duration() : 0\n    const lastStartTime = lastRunnerInfo ? lastRunnerInfo.start : this._time\n    return lastStartTime + lastDuration\n  }\n\n  getEndTimeOfTimeline() {\n    const endTimes = this._runners.map((i) => i.start + i.runner.duration())\n    return Math.max(0, ...endTimes)\n  }\n\n  getLastRunnerInfo() {\n    return this.getRunnerInfoById(this._lastRunnerId)\n  }\n\n  getRunnerInfoById(id) {\n    return this._runners[this._runnerIds.indexOf(id)] || null\n  }\n\n  pause() {\n    this._paused = true\n    return this._continue()\n  }\n\n  persist(dtOrForever) {\n    if (dtOrForever == null) return this._persist\n    this._persist = dtOrForever\n    return this\n  }\n\n  play() {\n    // Now make sure we are not paused and continue the animation\n    this._paused = false\n    return this.updateTime()._continue()\n  }\n\n  reverse(yes) {\n    const currentSpeed = this.speed()\n    if (yes == null) return this.speed(-currentSpeed)\n\n    const positive = Math.abs(currentSpeed)\n    return this.speed(yes ? -positive : positive)\n  }\n\n  // schedules a runner on the timeline\n  schedule(runner, delay, when) {\n    if (runner == null) {\n      return this._runners.map(makeSchedule)\n    }\n\n    // The start time for the next animation can either be given explicitly,\n    // derived from the current timeline time or it can be relative to the\n    // last start time to chain animations directly\n\n    let absoluteStartTime = 0\n    const endTime = this.getEndTime()\n    delay = delay || 0\n\n    // Work out when to start the animation\n    if (when == null || when === 'last' || when === 'after') {\n      // Take the last time and increment\n      absoluteStartTime = endTime\n    } else if (when === 'absolute' || when === 'start') {\n      absoluteStartTime = delay\n      delay = 0\n    } else if (when === 'now') {\n      absoluteStartTime = this._time\n    } else if (when === 'relative') {\n      const runnerInfo = this.getRunnerInfoById(runner.id)\n      if (runnerInfo) {\n        absoluteStartTime = runnerInfo.start + delay\n        delay = 0\n      }\n    } else if (when === 'with-last') {\n      const lastRunnerInfo = this.getLastRunnerInfo()\n      const lastStartTime = lastRunnerInfo ? lastRunnerInfo.start : this._time\n      absoluteStartTime = lastStartTime\n    } else {\n      throw new Error('Invalid value for the \"when\" parameter')\n    }\n\n    // Manage runner\n    runner.unschedule()\n    runner.timeline(this)\n\n    const persist = runner.persist()\n    const runnerInfo = {\n      persist: persist === null ? this._persist : persist,\n      start: absoluteStartTime + delay,\n      runner\n    }\n\n    this._lastRunnerId = runner.id\n\n    this._runners.push(runnerInfo)\n    this._runners.sort((a, b) => a.start - b.start)\n    this._runnerIds = this._runners.map((info) => info.runner.id)\n\n    this.updateTime()._continue()\n    return this\n  }\n\n  seek(dt) {\n    return this.time(this._time + dt)\n  }\n\n  source(fn) {\n    if (fn == null) return this._timeSource\n    this._timeSource = fn\n    return this\n  }\n\n  speed(speed) {\n    if (speed == null) return this._speed\n    this._speed = speed\n    return this\n  }\n\n  stop() {\n    // Go to start and pause\n    this.time(0)\n    return this.pause()\n  }\n\n  time(time) {\n    if (time == null) return this._time\n    this._time = time\n    return this._continue(true)\n  }\n\n  // Remove the runner from this timeline\n  unschedule(runner) {\n    const index = this._runnerIds.indexOf(runner.id)\n    if (index < 0) return this\n\n    this._runners.splice(index, 1)\n    this._runnerIds.splice(index, 1)\n\n    runner.timeline(null)\n    return this\n  }\n\n  // Makes sure, that after pausing the time doesn't jump\n  updateTime() {\n    if (!this.active()) {\n      this._lastSourceTime = this._timeSource()\n    }\n    return this\n  }\n\n  // Checks if we are running and continues the animation\n  _continue(immediateStep = false) {\n    Animator.cancelFrame(this._nextFrame)\n    this._nextFrame = null\n\n    if (immediateStep) return this._stepImmediate()\n    if (this._paused) return this\n\n    this._nextFrame = Animator.frame(this._step)\n    return this\n  }\n\n  _stepFn(immediateStep = false) {\n    // Get the time delta from the last time and update the time\n    const time = this._timeSource()\n    let dtSource = time - this._lastSourceTime\n\n    if (immediateStep) dtSource = 0\n\n    const dtTime = this._speed * dtSource + (this._time - this._lastStepTime)\n    this._lastSourceTime = time\n\n    // Only update the time if we use the timeSource.\n    // Otherwise use the current time\n    if (!immediateStep) {\n      // Update the time\n      this._time += dtTime\n      this._time = this._time < 0 ? 0 : this._time\n    }\n    this._lastStepTime = this._time\n    this.fire('time', this._time)\n\n    // This is for the case that the timeline was seeked so that the time\n    // is now before the startTime of the runner. That is why we need to set\n    // the runner to position 0\n\n    // FIXME:\n    // However, resetting in insertion order leads to bugs. Considering the case,\n    // where 2 runners change the same attribute but in different times,\n    // resetting both of them will lead to the case where the later defined\n    // runner always wins the reset even if the other runner started earlier\n    // and therefore should win the attribute battle\n    // this can be solved by resetting them backwards\n    for (let k = this._runners.length; k--; ) {\n      // Get and run the current runner and ignore it if its inactive\n      const runnerInfo = this._runners[k]\n      const runner = runnerInfo.runner\n\n      // Make sure that we give the actual difference\n      // between runner start time and now\n      const dtToStart = this._time - runnerInfo.start\n\n      // Dont run runner if not started yet\n      // and try to reset it\n      if (dtToStart <= 0) {\n        runner.reset()\n      }\n    }\n\n    // Run all of the runners directly\n    let runnersLeft = false\n    for (let i = 0, len = this._runners.length; i < len; i++) {\n      // Get and run the current runner and ignore it if its inactive\n      const runnerInfo = this._runners[i]\n      const runner = runnerInfo.runner\n      let dt = dtTime\n\n      // Make sure that we give the actual difference\n      // between runner start time and now\n      const dtToStart = this._time - runnerInfo.start\n\n      // Dont run runner if not started yet\n      if (dtToStart <= 0) {\n        runnersLeft = true\n        continue\n      } else if (dtToStart < dt) {\n        // Adjust dt to make sure that animation is on point\n        dt = dtToStart\n      }\n\n      if (!runner.active()) continue\n\n      // If this runner is still going, signal that we need another animation\n      // frame, otherwise, remove the completed runner\n      const finished = runner.step(dt).done\n      if (!finished) {\n        runnersLeft = true\n        // continue\n      } else if (runnerInfo.persist !== true) {\n        // runner is finished. And runner might get removed\n        const endTime = runner.duration() - runner.time() + this._time\n\n        if (endTime + runnerInfo.persist < this._time) {\n          // Delete runner and correct index\n          runner.unschedule()\n          --i\n          --len\n        }\n      }\n    }\n\n    // Basically: we continue when there are runners right from us in time\n    // when -->, and when runners are left from us when <--\n    if (\n      (runnersLeft && !(this._speed < 0 && this._time === 0)) ||\n      (this._runnerIds.length && this._speed < 0 && this._time > 0)\n    ) {\n      this._continue()\n    } else {\n      this.pause()\n      this.fire('finished')\n    }\n\n    return this\n  }\n\n  terminate() {\n    // cleanup memory\n\n    // Store the timing variables\n    this._startTime = 0\n    this._speed = 1.0\n\n    // Determines how long a runner is hold in memory. Can be a dt or true/false\n    this._persist = 0\n\n    // Keep track of the running animations and their starting parameters\n    this._nextFrame = null\n    this._paused = true\n    this._runners = []\n    this._runnerIds = []\n    this._lastRunnerId = -1\n    this._time = 0\n    this._lastSourceTime = 0\n    this._lastStepTime = 0\n\n    // Make sure that step is always called in class context\n    this._step = this._stepFn.bind(this, false)\n    this._stepImmediate = this._stepFn.bind(this, true)\n  }\n}\n\nregisterMethods({\n  Element: {\n    timeline: function (timeline) {\n      if (timeline == null) {\n        this._timeline = this._timeline || new Timeline()\n        return this._timeline\n      } else {\n        this._timeline = timeline\n        return this\n      }\n    }\n  }\n})\n", "import { <PERSON>, Ease, Stepper } from './Controller.js'\nimport { extend, register } from '../utils/adopter.js'\nimport { from, to } from '../modules/core/gradiented.js'\nimport { getOrigin } from '../utils/utils.js'\nimport { noop, timeline } from '../modules/core/defaults.js'\nimport { registerMethods } from '../utils/methods.js'\nimport { rx, ry } from '../modules/core/circled.js'\nimport Animator from './Animator.js'\nimport Box from '../types/Box.js'\nimport EventTarget from '../types/EventTarget.js'\nimport Matrix from '../types/Matrix.js'\nimport Morphable, { TransformBag, ObjectBag } from './Morphable.js'\nimport Point from '../types/Point.js'\nimport SVGNumber from '../types/SVGNumber.js'\nimport Timeline from './Timeline.js'\n\nexport default class Runner extends EventTarget {\n  constructor(options) {\n    super()\n\n    // Store a unique id on the runner, so that we can identify it later\n    this.id = Runner.id++\n\n    // Ensure a default value\n    options = options == null ? timeline.duration : options\n\n    // Ensure that we get a controller\n    options = typeof options === 'function' ? new Controller(options) : options\n\n    // Declare all of the variables\n    this._element = null\n    this._timeline = null\n    this.done = false\n    this._queue = []\n\n    // Work out the stepper and the duration\n    this._duration = typeof options === 'number' && options\n    this._isDeclarative = options instanceof Controller\n    this._stepper = this._isDeclarative ? options : new Ease()\n\n    // We copy the current values from the timeline because they can change\n    this._history = {}\n\n    // Store the state of the runner\n    this.enabled = true\n    this._time = 0\n    this._lastTime = 0\n\n    // At creation, the runner is in reset state\n    this._reseted = true\n\n    // Save transforms applied to this runner\n    this.transforms = new Matrix()\n    this.transformId = 1\n\n    // Looping variables\n    this._haveReversed = false\n    this._reverse = false\n    this._loopsDone = 0\n    this._swing = false\n    this._wait = 0\n    this._times = 1\n\n    this._frameId = null\n\n    // Stores how long a runner is stored after being done\n    this._persist = this._isDeclarative ? true : null\n  }\n\n  static sanitise(duration, delay, when) {\n    // Initialise the default parameters\n    let times = 1\n    let swing = false\n    let wait = 0\n    duration = duration ?? timeline.duration\n    delay = delay ?? timeline.delay\n    when = when || 'last'\n\n    // If we have an object, unpack the values\n    if (typeof duration === 'object' && !(duration instanceof Stepper)) {\n      delay = duration.delay ?? delay\n      when = duration.when ?? when\n      swing = duration.swing || swing\n      times = duration.times ?? times\n      wait = duration.wait ?? wait\n      duration = duration.duration ?? timeline.duration\n    }\n\n    return {\n      duration: duration,\n      delay: delay,\n      swing: swing,\n      times: times,\n      wait: wait,\n      when: when\n    }\n  }\n\n  active(enabled) {\n    if (enabled == null) return this.enabled\n    this.enabled = enabled\n    return this\n  }\n\n  /*\n  Private Methods\n  ===============\n  Methods that shouldn't be used externally\n  */\n  addTransform(transform) {\n    this.transforms.lmultiplyO(transform)\n    return this\n  }\n\n  after(fn) {\n    return this.on('finished', fn)\n  }\n\n  animate(duration, delay, when) {\n    const o = Runner.sanitise(duration, delay, when)\n    const runner = new Runner(o.duration)\n    if (this._timeline) runner.timeline(this._timeline)\n    if (this._element) runner.element(this._element)\n    return runner.loop(o).schedule(o.delay, o.when)\n  }\n\n  clearTransform() {\n    this.transforms = new Matrix()\n    return this\n  }\n\n  // TODO: Keep track of all transformations so that deletion is faster\n  clearTransformsFromQueue() {\n    if (\n      !this.done ||\n      !this._timeline ||\n      !this._timeline._runnerIds.includes(this.id)\n    ) {\n      this._queue = this._queue.filter((item) => {\n        return !item.isTransform\n      })\n    }\n  }\n\n  delay(delay) {\n    return this.animate(0, delay)\n  }\n\n  duration() {\n    return this._times * (this._wait + this._duration) - this._wait\n  }\n\n  during(fn) {\n    return this.queue(null, fn)\n  }\n\n  ease(fn) {\n    this._stepper = new Ease(fn)\n    return this\n  }\n  /*\n  Runner Definitions\n  ==================\n  These methods help us define the runtime behaviour of the Runner or they\n  help us make new runners from the current runner\n  */\n\n  element(element) {\n    if (element == null) return this._element\n    this._element = element\n    element._prepareRunner()\n    return this\n  }\n\n  finish() {\n    return this.step(Infinity)\n  }\n\n  loop(times, swing, wait) {\n    // Deal with the user passing in an object\n    if (typeof times === 'object') {\n      swing = times.swing\n      wait = times.wait\n      times = times.times\n    }\n\n    // Sanitise the values and store them\n    this._times = times || Infinity\n    this._swing = swing || false\n    this._wait = wait || 0\n\n    // Allow true to be passed\n    if (this._times === true) {\n      this._times = Infinity\n    }\n\n    return this\n  }\n\n  loops(p) {\n    const loopDuration = this._duration + this._wait\n    if (p == null) {\n      const loopsDone = Math.floor(this._time / loopDuration)\n      const relativeTime = this._time - loopsDone * loopDuration\n      const position = relativeTime / this._duration\n      return Math.min(loopsDone + position, this._times)\n    }\n    const whole = Math.floor(p)\n    const partial = p % 1\n    const time = loopDuration * whole + this._duration * partial\n    return this.time(time)\n  }\n\n  persist(dtOrForever) {\n    if (dtOrForever == null) return this._persist\n    this._persist = dtOrForever\n    return this\n  }\n\n  position(p) {\n    // Get all of the variables we need\n    const x = this._time\n    const d = this._duration\n    const w = this._wait\n    const t = this._times\n    const s = this._swing\n    const r = this._reverse\n    let position\n\n    if (p == null) {\n      /*\n      This function converts a time to a position in the range [0, 1]\n      The full explanation can be found in this desmos demonstration\n        https://www.desmos.com/calculator/u4fbavgche\n      The logic is slightly simplified here because we can use booleans\n      */\n\n      // Figure out the value without thinking about the start or end time\n      const f = function (x) {\n        const swinging = s * Math.floor((x % (2 * (w + d))) / (w + d))\n        const backwards = (swinging && !r) || (!swinging && r)\n        const uncliped =\n          (Math.pow(-1, backwards) * (x % (w + d))) / d + backwards\n        const clipped = Math.max(Math.min(uncliped, 1), 0)\n        return clipped\n      }\n\n      // Figure out the value by incorporating the start time\n      const endTime = t * (w + d) - w\n      position =\n        x <= 0\n          ? Math.round(f(1e-5))\n          : x < endTime\n            ? f(x)\n            : Math.round(f(endTime - 1e-5))\n      return position\n    }\n\n    // Work out the loops done and add the position to the loops done\n    const loopsDone = Math.floor(this.loops())\n    const swingForward = s && loopsDone % 2 === 0\n    const forwards = (swingForward && !r) || (r && swingForward)\n    position = loopsDone + (forwards ? p : 1 - p)\n    return this.loops(position)\n  }\n\n  progress(p) {\n    if (p == null) {\n      return Math.min(1, this._time / this.duration())\n    }\n    return this.time(p * this.duration())\n  }\n\n  /*\n  Basic Functionality\n  ===================\n  These methods allow us to attach basic functions to the runner directly\n  */\n  queue(initFn, runFn, retargetFn, isTransform) {\n    this._queue.push({\n      initialiser: initFn || noop,\n      runner: runFn || noop,\n      retarget: retargetFn,\n      isTransform: isTransform,\n      initialised: false,\n      finished: false\n    })\n    const timeline = this.timeline()\n    timeline && this.timeline()._continue()\n    return this\n  }\n\n  reset() {\n    if (this._reseted) return this\n    this.time(0)\n    this._reseted = true\n    return this\n  }\n\n  reverse(reverse) {\n    this._reverse = reverse == null ? !this._reverse : reverse\n    return this\n  }\n\n  schedule(timeline, delay, when) {\n    // The user doesn't need to pass a timeline if we already have one\n    if (!(timeline instanceof Timeline)) {\n      when = delay\n      delay = timeline\n      timeline = this.timeline()\n    }\n\n    // If there is no timeline, yell at the user...\n    if (!timeline) {\n      throw Error('Runner cannot be scheduled without timeline')\n    }\n\n    // Schedule the runner on the timeline provided\n    timeline.schedule(this, delay, when)\n    return this\n  }\n\n  step(dt) {\n    // If we are inactive, this stepper just gets skipped\n    if (!this.enabled) return this\n\n    // Update the time and get the new position\n    dt = dt == null ? 16 : dt\n    this._time += dt\n    const position = this.position()\n\n    // Figure out if we need to run the stepper in this frame\n    const running = this._lastPosition !== position && this._time >= 0\n    this._lastPosition = position\n\n    // Figure out if we just started\n    const duration = this.duration()\n    const justStarted = this._lastTime <= 0 && this._time > 0\n    const justFinished = this._lastTime < duration && this._time >= duration\n\n    this._lastTime = this._time\n    if (justStarted) {\n      this.fire('start', this)\n    }\n\n    // Work out if the runner is finished set the done flag here so animations\n    // know, that they are running in the last step (this is good for\n    // transformations which can be merged)\n    const declarative = this._isDeclarative\n    this.done = !declarative && !justFinished && this._time >= duration\n\n    // Runner is running. So its not in reset state anymore\n    this._reseted = false\n\n    let converged = false\n    // Call initialise and the run function\n    if (running || declarative) {\n      this._initialise(running)\n\n      // clear the transforms on this runner so they dont get added again and again\n      this.transforms = new Matrix()\n      converged = this._run(declarative ? dt : position)\n\n      this.fire('step', this)\n    }\n    // correct the done flag here\n    // declarative animations itself know when they converged\n    this.done = this.done || (converged && declarative)\n    if (justFinished) {\n      this.fire('finished', this)\n    }\n    return this\n  }\n\n  /*\n  Runner animation methods\n  ========================\n  Control how the animation plays\n  */\n  time(time) {\n    if (time == null) {\n      return this._time\n    }\n    const dt = time - this._time\n    this.step(dt)\n    return this\n  }\n\n  timeline(timeline) {\n    // check explicitly for undefined so we can set the timeline to null\n    if (typeof timeline === 'undefined') return this._timeline\n    this._timeline = timeline\n    return this\n  }\n\n  unschedule() {\n    const timeline = this.timeline()\n    timeline && timeline.unschedule(this)\n    return this\n  }\n\n  // Run each initialise function in the runner if required\n  _initialise(running) {\n    // If we aren't running, we shouldn't initialise when not declarative\n    if (!running && !this._isDeclarative) return\n\n    // Loop through all of the initialisers\n    for (let i = 0, len = this._queue.length; i < len; ++i) {\n      // Get the current initialiser\n      const current = this._queue[i]\n\n      // Determine whether we need to initialise\n      const needsIt = this._isDeclarative || (!current.initialised && running)\n      running = !current.finished\n\n      // Call the initialiser if we need to\n      if (needsIt && running) {\n        current.initialiser.call(this)\n        current.initialised = true\n      }\n    }\n  }\n\n  // Save a morpher to the morpher list so that we can retarget it later\n  _rememberMorpher(method, morpher) {\n    this._history[method] = {\n      morpher: morpher,\n      caller: this._queue[this._queue.length - 1]\n    }\n\n    // We have to resume the timeline in case a controller\n    // is already done without being ever run\n    // This can happen when e.g. this is done:\n    //    anim = el.animate(new SVG.Spring)\n    // and later\n    //    anim.move(...)\n    if (this._isDeclarative) {\n      const timeline = this.timeline()\n      timeline && timeline.play()\n    }\n  }\n\n  // Try to set the target for a morpher if the morpher exists, otherwise\n  // Run each run function for the position or dt given\n  _run(positionOrDt) {\n    // Run all of the _queue directly\n    let allfinished = true\n    for (let i = 0, len = this._queue.length; i < len; ++i) {\n      // Get the current function to run\n      const current = this._queue[i]\n\n      // Run the function if its not finished, we keep track of the finished\n      // flag for the sake of declarative _queue\n      const converged = current.runner.call(this, positionOrDt)\n      current.finished = current.finished || converged === true\n      allfinished = allfinished && current.finished\n    }\n\n    // We report when all of the constructors are finished\n    return allfinished\n  }\n\n  // do nothing and return false\n  _tryRetarget(method, target, extra) {\n    if (this._history[method]) {\n      // if the last method wasn't even initialised, throw it away\n      if (!this._history[method].caller.initialised) {\n        const index = this._queue.indexOf(this._history[method].caller)\n        this._queue.splice(index, 1)\n        return false\n      }\n\n      // for the case of transformations, we use the special retarget function\n      // which has access to the outer scope\n      if (this._history[method].caller.retarget) {\n        this._history[method].caller.retarget.call(this, target, extra)\n        // for everything else a simple morpher change is sufficient\n      } else {\n        this._history[method].morpher.to(target)\n      }\n\n      this._history[method].caller.finished = false\n      const timeline = this.timeline()\n      timeline && timeline.play()\n      return true\n    }\n    return false\n  }\n}\n\nRunner.id = 0\n\nexport class FakeRunner {\n  constructor(transforms = new Matrix(), id = -1, done = true) {\n    this.transforms = transforms\n    this.id = id\n    this.done = done\n  }\n\n  clearTransformsFromQueue() {}\n}\n\nextend([Runner, FakeRunner], {\n  mergeWith(runner) {\n    return new FakeRunner(\n      runner.transforms.lmultiply(this.transforms),\n      runner.id\n    )\n  }\n})\n\n// FakeRunner.emptyRunner = new FakeRunner()\n\nconst lmultiply = (last, curr) => last.lmultiplyO(curr)\nconst getRunnerTransform = (runner) => runner.transforms\n\nfunction mergeTransforms() {\n  // Find the matrix to apply to the element and apply it\n  const runners = this._transformationRunners.runners\n  const netTransform = runners\n    .map(getRunnerTransform)\n    .reduce(lmultiply, new Matrix())\n\n  this.transform(netTransform)\n\n  this._transformationRunners.merge()\n\n  if (this._transformationRunners.length() === 1) {\n    this._frameId = null\n  }\n}\n\nexport class RunnerArray {\n  constructor() {\n    this.runners = []\n    this.ids = []\n  }\n\n  add(runner) {\n    if (this.runners.includes(runner)) return\n    const id = runner.id + 1\n\n    this.runners.push(runner)\n    this.ids.push(id)\n\n    return this\n  }\n\n  clearBefore(id) {\n    const deleteCnt = this.ids.indexOf(id + 1) || 1\n    this.ids.splice(0, deleteCnt, 0)\n    this.runners\n      .splice(0, deleteCnt, new FakeRunner())\n      .forEach((r) => r.clearTransformsFromQueue())\n    return this\n  }\n\n  edit(id, newRunner) {\n    const index = this.ids.indexOf(id + 1)\n    this.ids.splice(index, 1, id + 1)\n    this.runners.splice(index, 1, newRunner)\n    return this\n  }\n\n  getByID(id) {\n    return this.runners[this.ids.indexOf(id + 1)]\n  }\n\n  length() {\n    return this.ids.length\n  }\n\n  merge() {\n    let lastRunner = null\n    for (let i = 0; i < this.runners.length; ++i) {\n      const runner = this.runners[i]\n\n      const condition =\n        lastRunner &&\n        runner.done &&\n        lastRunner.done &&\n        // don't merge runner when persisted on timeline\n        (!runner._timeline ||\n          !runner._timeline._runnerIds.includes(runner.id)) &&\n        (!lastRunner._timeline ||\n          !lastRunner._timeline._runnerIds.includes(lastRunner.id))\n\n      if (condition) {\n        // the +1 happens in the function\n        this.remove(runner.id)\n        const newRunner = runner.mergeWith(lastRunner)\n        this.edit(lastRunner.id, newRunner)\n        lastRunner = newRunner\n        --i\n      } else {\n        lastRunner = runner\n      }\n    }\n\n    return this\n  }\n\n  remove(id) {\n    const index = this.ids.indexOf(id + 1)\n    this.ids.splice(index, 1)\n    this.runners.splice(index, 1)\n    return this\n  }\n}\n\nregisterMethods({\n  Element: {\n    animate(duration, delay, when) {\n      const o = Runner.sanitise(duration, delay, when)\n      const timeline = this.timeline()\n      return new Runner(o.duration)\n        .loop(o)\n        .element(this)\n        .timeline(timeline.play())\n        .schedule(o.delay, o.when)\n    },\n\n    delay(by, when) {\n      return this.animate(0, by, when)\n    },\n\n    // this function searches for all runners on the element and deletes the ones\n    // which run before the current one. This is because absolute transformations\n    // overwrite anything anyway so there is no need to waste time computing\n    // other runners\n    _clearTransformRunnersBefore(currentRunner) {\n      this._transformationRunners.clearBefore(currentRunner.id)\n    },\n\n    _currentTransform(current) {\n      return (\n        this._transformationRunners.runners\n          // we need the equal sign here to make sure, that also transformations\n          // on the same runner which execute before the current transformation are\n          // taken into account\n          .filter((runner) => runner.id <= current.id)\n          .map(getRunnerTransform)\n          .reduce(lmultiply, new Matrix())\n      )\n    },\n\n    _addRunner(runner) {\n      this._transformationRunners.add(runner)\n\n      // Make sure that the runner merge is executed at the very end of\n      // all Animator functions. That is why we use immediate here to execute\n      // the merge right after all frames are run\n      Animator.cancelImmediate(this._frameId)\n      this._frameId = Animator.immediate(mergeTransforms.bind(this))\n    },\n\n    _prepareRunner() {\n      if (this._frameId == null) {\n        this._transformationRunners = new RunnerArray().add(\n          new FakeRunner(new Matrix(this))\n        )\n      }\n    }\n  }\n})\n\n// Will output the elements from array A that are not in the array B\nconst difference = (a, b) => a.filter((x) => !b.includes(x))\n\nextend(Runner, {\n  attr(a, v) {\n    return this.styleAttr('attr', a, v)\n  },\n\n  // Add animatable styles\n  css(s, v) {\n    return this.styleAttr('css', s, v)\n  },\n\n  styleAttr(type, nameOrAttrs, val) {\n    if (typeof nameOrAttrs === 'string') {\n      return this.styleAttr(type, { [nameOrAttrs]: val })\n    }\n\n    let attrs = nameOrAttrs\n    if (this._tryRetarget(type, attrs)) return this\n\n    let morpher = new Morphable(this._stepper).to(attrs)\n    let keys = Object.keys(attrs)\n\n    this.queue(\n      function () {\n        morpher = morpher.from(this.element()[type](keys))\n      },\n      function (pos) {\n        this.element()[type](morpher.at(pos).valueOf())\n        return morpher.done()\n      },\n      function (newToAttrs) {\n        // Check if any new keys were added\n        const newKeys = Object.keys(newToAttrs)\n        const differences = difference(newKeys, keys)\n\n        // If their are new keys, initialize them and add them to morpher\n        if (differences.length) {\n          // Get the values\n          const addedFromAttrs = this.element()[type](differences)\n\n          // Get the already initialized values\n          const oldFromAttrs = new ObjectBag(morpher.from()).valueOf()\n\n          // Merge old and new\n          Object.assign(oldFromAttrs, addedFromAttrs)\n          morpher.from(oldFromAttrs)\n        }\n\n        // Get the object from the morpher\n        const oldToAttrs = new ObjectBag(morpher.to()).valueOf()\n\n        // Merge in new attributes\n        Object.assign(oldToAttrs, newToAttrs)\n\n        // Change morpher target\n        morpher.to(oldToAttrs)\n\n        // Make sure that we save the work we did so we don't need it to do again\n        keys = newKeys\n        attrs = newToAttrs\n      }\n    )\n\n    this._rememberMorpher(type, morpher)\n    return this\n  },\n\n  zoom(level, point) {\n    if (this._tryRetarget('zoom', level, point)) return this\n\n    let morpher = new Morphable(this._stepper).to(new SVGNumber(level))\n\n    this.queue(\n      function () {\n        morpher = morpher.from(this.element().zoom())\n      },\n      function (pos) {\n        this.element().zoom(morpher.at(pos), point)\n        return morpher.done()\n      },\n      function (newLevel, newPoint) {\n        point = newPoint\n        morpher.to(newLevel)\n      }\n    )\n\n    this._rememberMorpher('zoom', morpher)\n    return this\n  },\n\n  /**\n   ** absolute transformations\n   **/\n\n  //\n  // M v -----|-----(D M v = F v)------|----->  T v\n  //\n  // 1. define the final state (T) and decompose it (once)\n  //    t = [tx, ty, the, lam, sy, sx]\n  // 2. on every frame: pull the current state of all previous transforms\n  //    (M - m can change)\n  //   and then write this as m = [tx0, ty0, the0, lam0, sy0, sx0]\n  // 3. Find the interpolated matrix F(pos) = m + pos * (t - m)\n  //   - Note F(0) = M\n  //   - Note F(1) = T\n  // 4. Now you get the delta matrix as a result: D = F * inv(M)\n\n  transform(transforms, relative, affine) {\n    // If we have a declarative function, we should retarget it if possible\n    relative = transforms.relative || relative\n    if (\n      this._isDeclarative &&\n      !relative &&\n      this._tryRetarget('transform', transforms)\n    ) {\n      return this\n    }\n\n    // Parse the parameters\n    const isMatrix = Matrix.isMatrixLike(transforms)\n    affine =\n      transforms.affine != null\n        ? transforms.affine\n        : affine != null\n          ? affine\n          : !isMatrix\n\n    // Create a morpher and set its type\n    const morpher = new Morphable(this._stepper).type(\n      affine ? TransformBag : Matrix\n    )\n\n    let origin\n    let element\n    let current\n    let currentAngle\n    let startTransform\n\n    function setup() {\n      // make sure element and origin is defined\n      element = element || this.element()\n      origin = origin || getOrigin(transforms, element)\n\n      startTransform = new Matrix(relative ? undefined : element)\n\n      // add the runner to the element so it can merge transformations\n      element._addRunner(this)\n\n      // Deactivate all transforms that have run so far if we are absolute\n      if (!relative) {\n        element._clearTransformRunnersBefore(this)\n      }\n    }\n\n    function run(pos) {\n      // clear all other transforms before this in case something is saved\n      // on this runner. We are absolute. We dont need these!\n      if (!relative) this.clearTransform()\n\n      const { x, y } = new Point(origin).transform(\n        element._currentTransform(this)\n      )\n\n      let target = new Matrix({ ...transforms, origin: [x, y] })\n      let start = this._isDeclarative && current ? current : startTransform\n\n      if (affine) {\n        target = target.decompose(x, y)\n        start = start.decompose(x, y)\n\n        // Get the current and target angle as it was set\n        const rTarget = target.rotate\n        const rCurrent = start.rotate\n\n        // Figure out the shortest path to rotate directly\n        const possibilities = [rTarget - 360, rTarget, rTarget + 360]\n        const distances = possibilities.map((a) => Math.abs(a - rCurrent))\n        const shortest = Math.min(...distances)\n        const index = distances.indexOf(shortest)\n        target.rotate = possibilities[index]\n      }\n\n      if (relative) {\n        // we have to be careful here not to overwrite the rotation\n        // with the rotate method of Matrix\n        if (!isMatrix) {\n          target.rotate = transforms.rotate || 0\n        }\n        if (this._isDeclarative && currentAngle) {\n          start.rotate = currentAngle\n        }\n      }\n\n      morpher.from(start)\n      morpher.to(target)\n\n      const affineParameters = morpher.at(pos)\n      currentAngle = affineParameters.rotate\n      current = new Matrix(affineParameters)\n\n      this.addTransform(current)\n      element._addRunner(this)\n      return morpher.done()\n    }\n\n    function retarget(newTransforms) {\n      // only get a new origin if it changed since the last call\n      if (\n        (newTransforms.origin || 'center').toString() !==\n        (transforms.origin || 'center').toString()\n      ) {\n        origin = getOrigin(newTransforms, element)\n      }\n\n      // overwrite the old transformations with the new ones\n      transforms = { ...newTransforms, origin }\n    }\n\n    this.queue(setup, run, retarget, true)\n    this._isDeclarative && this._rememberMorpher('transform', morpher)\n    return this\n  },\n\n  // Animatable x-axis\n  x(x) {\n    return this._queueNumber('x', x)\n  },\n\n  // Animatable y-axis\n  y(y) {\n    return this._queueNumber('y', y)\n  },\n\n  ax(x) {\n    return this._queueNumber('ax', x)\n  },\n\n  ay(y) {\n    return this._queueNumber('ay', y)\n  },\n\n  dx(x = 0) {\n    return this._queueNumberDelta('x', x)\n  },\n\n  dy(y = 0) {\n    return this._queueNumberDelta('y', y)\n  },\n\n  dmove(x, y) {\n    return this.dx(x).dy(y)\n  },\n\n  _queueNumberDelta(method, to) {\n    to = new SVGNumber(to)\n\n    // Try to change the target if we have this method already registered\n    if (this._tryRetarget(method, to)) return this\n\n    // Make a morpher and queue the animation\n    const morpher = new Morphable(this._stepper).to(to)\n    let from = null\n    this.queue(\n      function () {\n        from = this.element()[method]()\n        morpher.from(from)\n        morpher.to(from + to)\n      },\n      function (pos) {\n        this.element()[method](morpher.at(pos))\n        return morpher.done()\n      },\n      function (newTo) {\n        morpher.to(from + new SVGNumber(newTo))\n      }\n    )\n\n    // Register the morpher so that if it is changed again, we can retarget it\n    this._rememberMorpher(method, morpher)\n    return this\n  },\n\n  _queueObject(method, to) {\n    // Try to change the target if we have this method already registered\n    if (this._tryRetarget(method, to)) return this\n\n    // Make a morpher and queue the animation\n    const morpher = new Morphable(this._stepper).to(to)\n    this.queue(\n      function () {\n        morpher.from(this.element()[method]())\n      },\n      function (pos) {\n        this.element()[method](morpher.at(pos))\n        return morpher.done()\n      }\n    )\n\n    // Register the morpher so that if it is changed again, we can retarget it\n    this._rememberMorpher(method, morpher)\n    return this\n  },\n\n  _queueNumber(method, value) {\n    return this._queueObject(method, new SVGNumber(value))\n  },\n\n  // Animatable center x-axis\n  cx(x) {\n    return this._queueNumber('cx', x)\n  },\n\n  // Animatable center y-axis\n  cy(y) {\n    return this._queueNumber('cy', y)\n  },\n\n  // Add animatable move\n  move(x, y) {\n    return this.x(x).y(y)\n  },\n\n  amove(x, y) {\n    return this.ax(x).ay(y)\n  },\n\n  // Add animatable center\n  center(x, y) {\n    return this.cx(x).cy(y)\n  },\n\n  // Add animatable size\n  size(width, height) {\n    // animate bbox based size for all other elements\n    let box\n\n    if (!width || !height) {\n      box = this._element.bbox()\n    }\n\n    if (!width) {\n      width = (box.width / box.height) * height\n    }\n\n    if (!height) {\n      height = (box.height / box.width) * width\n    }\n\n    return this.width(width).height(height)\n  },\n\n  // Add animatable width\n  width(width) {\n    return this._queueNumber('width', width)\n  },\n\n  // Add animatable height\n  height(height) {\n    return this._queueNumber('height', height)\n  },\n\n  // Add animatable plot\n  plot(a, b, c, d) {\n    // Lines can be plotted with 4 arguments\n    if (arguments.length === 4) {\n      return this.plot([a, b, c, d])\n    }\n\n    if (this._tryRetarget('plot', a)) return this\n\n    const morpher = new Morphable(this._stepper)\n      .type(this._element.MorphArray)\n      .to(a)\n\n    this.queue(\n      function () {\n        morpher.from(this._element.array())\n      },\n      function (pos) {\n        this._element.plot(morpher.at(pos))\n        return morpher.done()\n      }\n    )\n\n    this._rememberMorpher('plot', morpher)\n    return this\n  },\n\n  // Add leading method\n  leading(value) {\n    return this._queueNumber('leading', value)\n  },\n\n  // Add animatable viewbox\n  viewbox(x, y, width, height) {\n    return this._queueObject('viewbox', new Box(x, y, width, height))\n  },\n\n  update(o) {\n    if (typeof o !== 'object') {\n      return this.update({\n        offset: arguments[0],\n        color: arguments[1],\n        opacity: arguments[2]\n      })\n    }\n\n    if (o.opacity != null) this.attr('stop-opacity', o.opacity)\n    if (o.color != null) this.attr('stop-color', o.color)\n    if (o.offset != null) this.attr('offset', o.offset)\n\n    return this\n  }\n})\n\nextend(Runner, { rx, ry, from, to })\nregister(Runner, 'Runner')\n", "import {\n  adopt,\n  nodeOrNew,\n  register,\n  wrapWithAttrCheck\n} from '../utils/adopter.js'\nimport { svg, xlink, xmlns } from '../modules/core/namespaces.js'\nimport { registerMethods } from '../utils/methods.js'\nimport Container from './Container.js'\nimport Defs from './Defs.js'\nimport { globals } from '../utils/window.js'\n\nexport default class Svg extends Container {\n  constructor(node, attrs = node) {\n    super(nodeOrNew('svg', node), attrs)\n    this.namespace()\n  }\n\n  // Creates and returns defs element\n  defs() {\n    if (!this.isRoot()) return this.root().defs()\n\n    return adopt(this.node.querySelector('defs')) || this.put(new Defs())\n  }\n\n  isRoot() {\n    return (\n      !this.node.parentNode ||\n      (!(this.node.parentNode instanceof globals.window.SVGElement) &&\n        this.node.parentNode.nodeName !== '#document-fragment')\n    )\n  }\n\n  // Add namespaces\n  namespace() {\n    if (!this.isRoot()) return this.root().namespace()\n    return this.attr({ xmlns: svg, version: '1.1' }).attr(\n      'xmlns:xlink',\n      xlink,\n      xmlns\n    )\n  }\n\n  removeNamespace() {\n    return this.attr({ xmlns: null, version: null })\n      .attr('xmlns:xlink', null, xmlns)\n      .attr('xmlns:svgjs', null, xmlns)\n  }\n\n  // Check if this is a root svg\n  // If not, call root() from this element\n  root() {\n    if (this.isRoot()) return this\n    return super.root()\n  }\n}\n\nregisterMethods({\n  Container: {\n    // Create nested svg document\n    nested: wrapWithAttrCheck(function () {\n      return this.put(new Svg())\n    })\n  }\n})\n\nregister(Svg, 'Svg', true)\n", "import { nodeOrNew, register, wrapWithAttrCheck } from '../utils/adopter.js'\nimport { registerMethods } from '../utils/methods.js'\nimport Container from './Container.js'\n\nexport default class Symbol extends Container {\n  // Initialize node\n  constructor(node, attrs = node) {\n    super(nodeOrNew('symbol', node), attrs)\n  }\n}\n\nregisterMethods({\n  Container: {\n    symbol: wrapWithAttrCheck(function () {\n      return this.put(new Symbol())\n    })\n  }\n})\n\nregister(Symbol, 'Symbol')\n", "import { globals } from '../../utils/window.js'\n\n// Create plain text node\nexport function plain(text) {\n  // clear if build mode is disabled\n  if (this._build === false) {\n    this.clear()\n  }\n\n  // create text node\n  this.node.appendChild(globals.document.createTextNode(text))\n\n  return this\n}\n\n// Get length of text element\nexport function length() {\n  return this.node.getComputedTextLength()\n}\n\n// Move over x-axis\n// Text is moved by its bounding box\n// text-anchor does NOT matter\nexport function x(x, box = this.bbox()) {\n  if (x == null) {\n    return box.x\n  }\n\n  return this.attr('x', this.attr('x') + x - box.x)\n}\n\n// Move over y-axis\nexport function y(y, box = this.bbox()) {\n  if (y == null) {\n    return box.y\n  }\n\n  return this.attr('y', this.attr('y') + y - box.y)\n}\n\nexport function move(x, y, box = this.bbox()) {\n  return this.x(x, box).y(y, box)\n}\n\n// Move center over x-axis\nexport function cx(x, box = this.bbox()) {\n  if (x == null) {\n    return box.cx\n  }\n\n  return this.attr('x', this.attr('x') + x - box.cx)\n}\n\n// Move center over y-axis\nexport function cy(y, box = this.bbox()) {\n  if (y == null) {\n    return box.cy\n  }\n\n  return this.attr('y', this.attr('y') + y - box.cy)\n}\n\nexport function center(x, y, box = this.bbox()) {\n  return this.cx(x, box).cy(y, box)\n}\n\nexport function ax(x) {\n  return this.attr('x', x)\n}\n\nexport function ay(y) {\n  return this.attr('y', y)\n}\n\nexport function amove(x, y) {\n  return this.ax(x).ay(y)\n}\n\n// Enable / disable build mode\nexport function build(build) {\n  this._build = !!build\n  return this\n}\n", "import {\n  adopt,\n  extend,\n  nodeOrNew,\n  register,\n  wrapWithAttrCheck\n} from '../utils/adopter.js'\nimport { registerMethods } from '../utils/methods.js'\nimport SVGNumber from '../types/SVGNumber.js'\nimport Shape from './Shape.js'\nimport { globals } from '../utils/window.js'\nimport * as textable from '../modules/core/textable.js'\nimport { isDescriptive, writeDataToDom } from '../utils/utils.js'\n\nexport default class Text extends Shape {\n  // Initialize node\n  constructor(node, attrs = node) {\n    super(nodeOrNew('text', node), attrs)\n\n    this.dom.leading = this.dom.leading ?? new SVGNumber(1.3) // store leading value for rebuilding\n    this._rebuild = true // enable automatic updating of dy values\n    this._build = false // disable build mode for adding multiple lines\n  }\n\n  // Set / get leading\n  leading(value) {\n    // act as getter\n    if (value == null) {\n      return this.dom.leading\n    }\n\n    // act as setter\n    this.dom.leading = new SVGNumber(value)\n\n    return this.rebuild()\n  }\n\n  // Rebuild appearance type\n  rebuild(rebuild) {\n    // store new rebuild flag if given\n    if (typeof rebuild === 'boolean') {\n      this._rebuild = rebuild\n    }\n\n    // define position of all lines\n    if (this._rebuild) {\n      const self = this\n      let blankLineOffset = 0\n      const leading = this.dom.leading\n\n      this.each(function (i) {\n        if (isDescriptive(this.node)) return\n\n        const fontSize = globals.window\n          .getComputedStyle(this.node)\n          .getPropertyValue('font-size')\n\n        const dy = leading * new SVGNumber(fontSize)\n\n        if (this.dom.newLined) {\n          this.attr('x', self.attr('x'))\n\n          if (this.text() === '\\n') {\n            blankLineOffset += dy\n          } else {\n            this.attr('dy', i ? dy + blankLineOffset : 0)\n            blankLineOffset = 0\n          }\n        }\n      })\n\n      this.fire('rebuild')\n    }\n\n    return this\n  }\n\n  // overwrite method from parent to set data properly\n  setData(o) {\n    this.dom = o\n    this.dom.leading = new SVGNumber(o.leading || 1.3)\n    return this\n  }\n\n  writeDataToDom() {\n    writeDataToDom(this, this.dom, { leading: 1.3 })\n    return this\n  }\n\n  // Set the text content\n  text(text) {\n    // act as getter\n    if (text === undefined) {\n      const children = this.node.childNodes\n      let firstLine = 0\n      text = ''\n\n      for (let i = 0, len = children.length; i < len; ++i) {\n        // skip textPaths - they are no lines\n        if (children[i].nodeName === 'textPath' || isDescriptive(children[i])) {\n          if (i === 0) firstLine = i + 1\n          continue\n        }\n\n        // add newline if its not the first child and newLined is set to true\n        if (\n          i !== firstLine &&\n          children[i].nodeType !== 3 &&\n          adopt(children[i]).dom.newLined === true\n        ) {\n          text += '\\n'\n        }\n\n        // add content of this node\n        text += children[i].textContent\n      }\n\n      return text\n    }\n\n    // remove existing content\n    this.clear().build(true)\n\n    if (typeof text === 'function') {\n      // call block\n      text.call(this, this)\n    } else {\n      // store text and make sure text is not blank\n      text = (text + '').split('\\n')\n\n      // build new lines\n      for (let j = 0, jl = text.length; j < jl; j++) {\n        this.newLine(text[j])\n      }\n    }\n\n    // disable build mode and rebuild lines\n    return this.build(false).rebuild()\n  }\n}\n\nextend(Text, textable)\n\nregisterMethods({\n  Container: {\n    // Create text element\n    text: wrapWithAttrCheck(function (text = '') {\n      return this.put(new Text()).text(text)\n    }),\n\n    // Create plain text element\n    plain: wrapWithAttrCheck(function (text = '') {\n      return this.put(new Text()).plain(text)\n    })\n  }\n})\n\nregister(Text, 'Text')\n", "import {\n  extend,\n  nodeOrNew,\n  register,\n  wrapWithAttrCheck\n} from '../utils/adopter.js'\nimport { globals } from '../utils/window.js'\nimport { registerMethods } from '../utils/methods.js'\nimport SVGNumber from '../types/SVGNumber.js'\nimport Shape from './Shape.js'\nimport Text from './Text.js'\nimport * as textable from '../modules/core/textable.js'\n\nexport default class Tspan extends Shape {\n  // Initialize node\n  constructor(node, attrs = node) {\n    super(nodeOrNew('tspan', node), attrs)\n    this._build = false // disable build mode for adding multiple lines\n  }\n\n  // Shortcut dx\n  dx(dx) {\n    return this.attr('dx', dx)\n  }\n\n  // Shortcut dy\n  dy(dy) {\n    return this.attr('dy', dy)\n  }\n\n  // Create new line\n  newLine() {\n    // mark new line\n    this.dom.newLined = true\n\n    // fetch parent\n    const text = this.parent()\n\n    // early return in case we are not in a text element\n    if (!(text instanceof Text)) {\n      return this\n    }\n\n    const i = text.index(this)\n\n    const fontSize = globals.window\n      .getComputedStyle(this.node)\n      .getPropertyValue('font-size')\n    const dy = text.dom.leading * new SVGNumber(fontSize)\n\n    // apply new position\n    return this.dy(i ? dy : 0).attr('x', text.x())\n  }\n\n  // Set text content\n  text(text) {\n    if (text == null)\n      return this.node.textContent + (this.dom.newLined ? '\\n' : '')\n\n    if (typeof text === 'function') {\n      this.clear().build(true)\n      text.call(this, this)\n      this.build(false)\n    } else {\n      this.plain(text)\n    }\n\n    return this\n  }\n}\n\nextend(Tspan, textable)\n\nregisterMethods({\n  Tspan: {\n    tspan: wrapWithAttrCheck(function (text = '') {\n      const tspan = new Tspan()\n\n      // clear if build mode is disabled\n      if (!this._build) {\n        this.clear()\n      }\n\n      // add new tspan\n      return this.put(tspan).text(text)\n    })\n  },\n  Text: {\n    newLine: function (text = '') {\n      return this.tspan(text).newLine()\n    }\n  }\n})\n\nregister(Tspan, 'Tspan')\n", "import { cx, cy, height, width, x, y } from '../modules/core/circled.js'\nimport {\n  extend,\n  nodeOrNew,\n  register,\n  wrapWithAttrCheck\n} from '../utils/adopter.js'\nimport { registerMethods } from '../utils/methods.js'\nimport SVGNumber from '../types/SVGNumber.js'\nimport Shape from './Shape.js'\n\nexport default class Circle extends Shape {\n  constructor(node, attrs = node) {\n    super(nodeOrNew('circle', node), attrs)\n  }\n\n  radius(r) {\n    return this.attr('r', r)\n  }\n\n  // Radius x value\n  rx(rx) {\n    return this.attr('r', rx)\n  }\n\n  // Alias radius x value\n  ry(ry) {\n    return this.rx(ry)\n  }\n\n  size(size) {\n    return this.radius(new SVGNumber(size).divide(2))\n  }\n}\n\nextend(Circle, { x, y, cx, cy, width, height })\n\nregisterMethods({\n  Container: {\n    // Create circle element\n    circle: wrapWithAttrCheck(function (size = 0) {\n      return this.put(new Circle()).size(size).move(0, 0)\n    })\n  }\n})\n\nregister(Circle, 'Circle')\n", "import { nodeOrNew, register, wrapWithAttrCheck } from '../utils/adopter.js'\nimport { registerMethods } from '../utils/methods.js'\nimport Container from './Container.js'\nimport baseFind from '../modules/core/selector.js'\n\nexport default class ClipPath extends Container {\n  constructor(node, attrs = node) {\n    super(nodeOrNew('clipPath', node), attrs)\n  }\n\n  // Unclip all clipped elements and remove itself\n  remove() {\n    // unclip all targets\n    this.targets().forEach(function (el) {\n      el.unclip()\n    })\n\n    // remove clipPath from parent\n    return super.remove()\n  }\n\n  targets() {\n    return baseFind('svg [clip-path*=' + this.id() + ']')\n  }\n}\n\nregisterMethods({\n  Container: {\n    // Create clipping element\n    clip: wrapWithAttrCheck(function () {\n      return this.defs().put(new ClipPath())\n    })\n  },\n  Element: {\n    // Distribute clipPath to svg element\n    clipper() {\n      return this.reference('clip-path')\n    },\n\n    clipWith(element) {\n      // use given clip or create a new one\n      const clipper =\n        element instanceof ClipPath\n          ? element\n          : this.parent().clip().add(element)\n\n      // apply mask\n      return this.attr('clip-path', 'url(#' + clipper.id() + ')')\n    },\n\n    // Unclip element\n    unclip() {\n      return this.attr('clip-path', null)\n    }\n  }\n})\n\nregister(ClipPath, 'ClipPath')\n", "import { nodeOrNew, register, wrapWithAttrCheck } from '../utils/adopter.js'\nimport { registerMethods } from '../utils/methods.js'\nimport Element from './Element.js'\n\nexport default class ForeignObject extends Element {\n  constructor(node, attrs = node) {\n    super(nodeOrNew('foreignObject', node), attrs)\n  }\n}\n\nregisterMethods({\n  Container: {\n    foreignObject: wrapWithAttrCheck(function (width, height) {\n      return this.put(new ForeignObject()).size(width, height)\n    })\n  }\n})\n\nregister(ForeignObject, 'ForeignObject')\n", "import Matrix from '../../types/Matrix.js'\nimport Point from '../../types/Point.js'\nimport Box from '../../types/Box.js'\nimport { proportionalSize } from '../../utils/utils.js'\nimport { getWindow } from '../../utils/window.js'\n\nexport function dmove(dx, dy) {\n  this.children().forEach((child) => {\n    let bbox\n\n    // We have to wrap this for elements that dont have a bbox\n    // e.g. title and other descriptive elements\n    try {\n      // Get the childs bbox\n      // Bug: https://bugzilla.mozilla.org/show_bug.cgi?id=1905039\n      // Because bbox for nested svgs returns the contents bbox in the coordinate space of the svg itself (weird!), we cant use bbox for svgs\n      // Therefore we have to use getBoundingClientRect. But THAT is broken (as explained in the bug).\n      // Funnily enough the broken behavior would work for us but that breaks it in chrome\n      // So we have to replicate the broken behavior of FF by just reading the attributes of the svg itself\n      bbox =\n        child.node instanceof getWindow().SVGSVGElement\n          ? new Box(child.attr(['x', 'y', 'width', 'height']))\n          : child.bbox()\n    } catch (e) {\n      return\n    }\n\n    // Get childs matrix\n    const m = new Matrix(child)\n    // Translate childs matrix by amount and\n    // transform it back into parents space\n    const matrix = m.translate(dx, dy).transform(m.inverse())\n    // Calculate new x and y from old box\n    const p = new Point(bbox.x, bbox.y).transform(matrix)\n    // Move element\n    child.move(p.x, p.y)\n  })\n\n  return this\n}\n\nexport function dx(dx) {\n  return this.dmove(dx, 0)\n}\n\nexport function dy(dy) {\n  return this.dmove(0, dy)\n}\n\nexport function height(height, box = this.bbox()) {\n  if (height == null) return box.height\n  return this.size(box.width, height, box)\n}\n\nexport function move(x = 0, y = 0, box = this.bbox()) {\n  const dx = x - box.x\n  const dy = y - box.y\n\n  return this.dmove(dx, dy)\n}\n\nexport function size(width, height, box = this.bbox()) {\n  const p = proportionalSize(this, width, height, box)\n  const scaleX = p.width / box.width\n  const scaleY = p.height / box.height\n\n  this.children().forEach((child) => {\n    const o = new Point(box).transform(new Matrix(child).inverse())\n    child.scale(scaleX, scaleY, o.x, o.y)\n  })\n\n  return this\n}\n\nexport function width(width, box = this.bbox()) {\n  if (width == null) return box.width\n  return this.size(width, box.height, box)\n}\n\nexport function x(x, box = this.bbox()) {\n  if (x == null) return box.x\n  return this.move(x, box.y, box)\n}\n\nexport function y(y, box = this.bbox()) {\n  if (y == null) return box.y\n  return this.move(box.x, y, box)\n}\n", "import {\n  nodeOrNew,\n  register,\n  wrapWithAttrCheck,\n  extend\n} from '../utils/adopter.js'\nimport { registerMethods } from '../utils/methods.js'\nimport Container from './Container.js'\nimport * as containerGeometry from '../modules/core/containerGeometry.js'\n\nexport default class G extends Container {\n  constructor(node, attrs = node) {\n    super(nodeOrNew('g', node), attrs)\n  }\n}\n\nextend(G, containerGeometry)\n\nregisterMethods({\n  Container: {\n    // Create a group element\n    group: wrapWithAttrCheck(function () {\n      return this.put(new G())\n    })\n  }\n})\n\nregister(G, 'G')\n", "import {\n  nodeOr<PERSON><PERSON>,\n  register,\n  wrapWithAttrCheck,\n  extend\n} from '../utils/adopter.js'\nimport { registerMethods } from '../utils/methods.js'\nimport { xlink } from '../modules/core/namespaces.js'\nimport Container from './Container.js'\nimport * as containerGeometry from '../modules/core/containerGeometry.js'\n\nexport default class A extends Container {\n  constructor(node, attrs = node) {\n    super(nodeOrNew('a', node), attrs)\n  }\n\n  // Link target attribute\n  target(target) {\n    return this.attr('target', target)\n  }\n\n  // Link url\n  to(url) {\n    return this.attr('href', url, xlink)\n  }\n}\n\nextend(A, containerGeometry)\n\nregisterMethods({\n  Container: {\n    // Create a hyperlink element\n    link: wrapWithAttrCheck(function (url) {\n      return this.put(new A()).to(url)\n    })\n  },\n  Element: {\n    unlink() {\n      const link = this.linker()\n\n      if (!link) return this\n\n      const parent = link.parent()\n\n      if (!parent) {\n        return this.remove()\n      }\n\n      const index = parent.index(link)\n      parent.add(this, index)\n\n      link.remove()\n      return this\n    },\n    linkTo(url) {\n      // reuse old link if possible\n      let link = this.linker()\n\n      if (!link) {\n        link = new A()\n        this.wrap(link)\n      }\n\n      if (typeof url === 'function') {\n        url.call(link, link)\n      } else {\n        link.to(url)\n      }\n\n      return this\n    },\n    linker() {\n      const link = this.parent()\n      if (link && link.node.nodeName.toLowerCase() === 'a') {\n        return link\n      }\n\n      return null\n    }\n  }\n})\n\nregister(A, 'A')\n", "import { nodeOrNew, register, wrapWithAttrCheck } from '../utils/adopter.js'\nimport { registerMethods } from '../utils/methods.js'\nimport Container from './Container.js'\nimport baseFind from '../modules/core/selector.js'\n\nexport default class Mask extends Container {\n  // Initialize node\n  constructor(node, attrs = node) {\n    super(nodeOrNew('mask', node), attrs)\n  }\n\n  // Unmask all masked elements and remove itself\n  remove() {\n    // unmask all targets\n    this.targets().forEach(function (el) {\n      el.unmask()\n    })\n\n    // remove mask from parent\n    return super.remove()\n  }\n\n  targets() {\n    return baseFind('svg [mask*=' + this.id() + ']')\n  }\n}\n\nregisterMethods({\n  Container: {\n    mask: wrapWithAttrCheck(function () {\n      return this.defs().put(new Mask())\n    })\n  },\n  Element: {\n    // Distribute mask to svg element\n    masker() {\n      return this.reference('mask')\n    },\n\n    maskWith(element) {\n      // use given mask or create a new one\n      const masker =\n        element instanceof Mask ? element : this.parent().mask().add(element)\n\n      // apply mask\n      return this.attr('mask', 'url(#' + masker.id() + ')')\n    },\n\n    // Unmask element\n    unmask() {\n      return this.attr('mask', null)\n    }\n  }\n})\n\nregister(Mask, 'Mask')\n", "import { nodeOrNew, register } from '../utils/adopter.js'\nimport Element from './Element.js'\nimport SVGNumber from '../types/SVGNumber.js'\nimport { registerMethods } from '../utils/methods.js'\n\nexport default class Stop extends Element {\n  constructor(node, attrs = node) {\n    super(nodeOrNew('stop', node), attrs)\n  }\n\n  // add color stops\n  update(o) {\n    if (typeof o === 'number' || o instanceof SVGNumber) {\n      o = {\n        offset: arguments[0],\n        color: arguments[1],\n        opacity: arguments[2]\n      }\n    }\n\n    // set attributes\n    if (o.opacity != null) this.attr('stop-opacity', o.opacity)\n    if (o.color != null) this.attr('stop-color', o.color)\n    if (o.offset != null) this.attr('offset', new SVGNumber(o.offset))\n\n    return this\n  }\n}\n\nregisterMethods({\n  Gradient: {\n    // Add a color stop\n    stop: function (offset, color, opacity) {\n      return this.put(new Stop()).update(offset, color, opacity)\n    }\n  }\n})\n\nregister(Stop, 'Stop')\n", "import { nodeOrNew, register } from '../utils/adopter.js'\nimport { registerMethods } from '../utils/methods.js'\nimport { unCamelCase } from '../utils/utils.js'\nimport Element from './Element.js'\n\nfunction cssRule(selector, rule) {\n  if (!selector) return ''\n  if (!rule) return selector\n\n  let ret = selector + '{'\n\n  for (const i in rule) {\n    ret += unCamelCase(i) + ':' + rule[i] + ';'\n  }\n\n  ret += '}'\n\n  return ret\n}\n\nexport default class Style extends Element {\n  constructor(node, attrs = node) {\n    super(nodeOrNew('style', node), attrs)\n  }\n\n  addText(w = '') {\n    this.node.textContent += w\n    return this\n  }\n\n  font(name, src, params = {}) {\n    return this.rule('@font-face', {\n      fontFamily: name,\n      src: src,\n      ...params\n    })\n  }\n\n  rule(selector, obj) {\n    return this.addText(cssRule(selector, obj))\n  }\n}\n\nregisterMethods('Dom', {\n  style(selector, obj) {\n    return this.put(new Style()).rule(selector, obj)\n  },\n  fontface(name, src, params) {\n    return this.put(new Style()).font(name, src, params)\n  }\n})\n\nregister(Style, 'Style')\n", "import { nodeOrNew, register, wrapWithAttrCheck } from '../utils/adopter.js'\nimport { registerMethods } from '../utils/methods.js'\nimport { xlink } from '../modules/core/namespaces.js'\nimport Path from './Path.js'\nimport PathArray from '../types/PathArray.js'\nimport Text from './Text.js'\nimport baseFind from '../modules/core/selector.js'\n\nexport default class TextPath extends Text {\n  // Initialize node\n  constructor(node, attrs = node) {\n    super(nodeOrNew('textPath', node), attrs)\n  }\n\n  // return the array of the path track element\n  array() {\n    const track = this.track()\n\n    return track ? track.array() : null\n  }\n\n  // Plot path if any\n  plot(d) {\n    const track = this.track()\n    let pathArray = null\n\n    if (track) {\n      pathArray = track.plot(d)\n    }\n\n    return d == null ? pathArray : this\n  }\n\n  // Get the path element\n  track() {\n    return this.reference('href')\n  }\n}\n\nregisterMethods({\n  Container: {\n    textPath: wrapWithAttrCheck(function (text, path) {\n      // Convert text to instance if needed\n      if (!(text instanceof Text)) {\n        text = this.text(text)\n      }\n\n      return text.path(path)\n    })\n  },\n  Text: {\n    // Create path for text to run on\n    path: wrapWithAttrCheck(function (track, importNodes = true) {\n      const textPath = new TextPath()\n\n      // if track is a path, reuse it\n      if (!(track instanceof Path)) {\n        // create path element\n        track = this.defs().path(track)\n      }\n\n      // link textPath to path and add content\n      textPath.attr('href', '#' + track, xlink)\n\n      // Transplant all nodes from text to textPath\n      let node\n      if (importNodes) {\n        while ((node = this.node.firstChild)) {\n          textPath.node.appendChild(node)\n        }\n      }\n\n      // add textPath element as child node and return textPath\n      return this.put(textPath)\n    }),\n\n    // Get the textPath children\n    textPath() {\n      return this.findOne('textPath')\n    }\n  },\n  Path: {\n    // creates a textPath from this path\n    text: wrapWithAttrCheck(function (text) {\n      // Convert text to instance if needed\n      if (!(text instanceof Text)) {\n        text = new Text().addTo(this.parent()).text(text)\n      }\n\n      // Create textPath from text and path and return\n      return text.path(this)\n    }),\n\n    targets() {\n      return baseFind('svg textPath').filter((node) => {\n        return (node.attr('href') || '').includes(this.id())\n      })\n\n      // Does not work in IE11. Use when IE support is dropped\n      // return baseFind('svg textPath[*|href*=' + this.id() + ']')\n    }\n  }\n})\n\nTextPath.prototype.MorphArray = PathArray\nregister(TextPath, 'TextPath')\n", "import { nodeOrNew, register, wrapWithAttrCheck } from '../utils/adopter.js'\nimport { registerMethods } from '../utils/methods.js'\nimport { xlink } from '../modules/core/namespaces.js'\nimport Shape from './Shape.js'\n\nexport default class Use extends Shape {\n  constructor(node, attrs = node) {\n    super(nodeOrNew('use', node), attrs)\n  }\n\n  // Use element as a reference\n  use(element, file) {\n    // Set lined element\n    return this.attr('href', (file || '') + '#' + element, xlink)\n  }\n}\n\nregisterMethods({\n  Container: {\n    // Create a use element\n    use: wrapWithAttrCheck(function (element, file) {\n      return this.put(new Use()).use(element, file)\n    })\n  }\n})\n\nregister(Use, 'Use')\n", "/* Optional Modules */\nimport './modules/optional/arrange.js'\nimport './modules/optional/class.js'\nimport './modules/optional/css.js'\nimport './modules/optional/data.js'\nimport './modules/optional/memory.js'\nimport './modules/optional/sugar.js'\nimport './modules/optional/transform.js'\n\nimport { extend, makeInstance } from './utils/adopter.js'\nimport { getMethodNames, getMethodsFor } from './utils/methods.js'\nimport Box from './types/Box.js'\nimport Color from './types/Color.js'\nimport Container from './elements/Container.js'\nimport Defs from './elements/Defs.js'\nimport Dom from './elements/Dom.js'\nimport Element from './elements/Element.js'\nimport Ellipse from './elements/Ellipse.js'\nimport EventTarget from './types/EventTarget.js'\nimport Fragment from './elements/Fragment.js'\nimport Gradient from './elements/Gradient.js'\nimport Image from './elements/Image.js'\nimport Line from './elements/Line.js'\nimport List from './types/List.js'\nimport Marker from './elements/Marker.js'\nimport Matrix from './types/Matrix.js'\nimport Morphable, {\n  NonMorphable,\n  ObjectBag,\n  TransformBag,\n  makeMorphable,\n  registerMorphableType\n} from './animation/Morphable.js'\nimport Path from './elements/Path.js'\nimport PathArray from './types/PathArray.js'\nimport Pattern from './elements/Pattern.js'\nimport PointArray from './types/PointArray.js'\nimport Point from './types/Point.js'\nimport Polygon from './elements/Polygon.js'\nimport Polyline from './elements/Polyline.js'\nimport Rect from './elements/Rect.js'\nimport Runner from './animation/Runner.js'\nimport SVGArray from './types/SVGArray.js'\nimport SVGNumber from './types/SVGNumber.js'\nimport Shape from './elements/Shape.js'\nimport Svg from './elements/Svg.js'\nimport Symbol from './elements/Symbol.js'\nimport Text from './elements/Text.js'\nimport Tspan from './elements/Tspan.js'\nimport * as defaults from './modules/core/defaults.js'\nimport * as utils from './utils/utils.js'\nimport * as namespaces from './modules/core/namespaces.js'\nimport * as regex from './modules/core/regex.js'\n\nexport {\n  Morphable,\n  registerMorphableType,\n  makeMorphable,\n  TransformBag,\n  ObjectBag,\n  NonMorphable\n}\n\nexport { defaults, utils, namespaces, regex }\nexport const SVG = makeInstance\nexport { default as parser } from './modules/core/parser.js'\nexport { default as find } from './modules/core/selector.js'\nexport * from './modules/core/event.js'\nexport * from './utils/adopter.js'\nexport {\n  getWindow,\n  registerWindow,\n  restoreWindow,\n  saveWindow,\n  withWindow\n} from './utils/window.js'\n\n/* Animation Modules */\nexport { default as Animator } from './animation/Animator.js'\nexport {\n  Controller,\n  Ease,\n  PID,\n  Spring,\n  easing\n} from './animation/Controller.js'\nexport { default as Queue } from './animation/Queue.js'\nexport { default as Runner } from './animation/Runner.js'\nexport { default as Timeline } from './animation/Timeline.js'\n\n/* Types */\nexport { default as Array } from './types/SVGArray.js'\nexport { default as Box } from './types/Box.js'\nexport { default as Color } from './types/Color.js'\nexport { default as EventTarget } from './types/EventTarget.js'\nexport { default as Matrix } from './types/Matrix.js'\nexport { default as Number } from './types/SVGNumber.js'\nexport { default as PathArray } from './types/PathArray.js'\nexport { default as Point } from './types/Point.js'\nexport { default as PointArray } from './types/PointArray.js'\nexport { default as List } from './types/List.js'\n\n/* Elements */\nexport { default as Circle } from './elements/Circle.js'\nexport { default as ClipPath } from './elements/ClipPath.js'\nexport { default as Container } from './elements/Container.js'\nexport { default as Defs } from './elements/Defs.js'\nexport { default as Dom } from './elements/Dom.js'\nexport { default as Element } from './elements/Element.js'\nexport { default as Ellipse } from './elements/Ellipse.js'\nexport { default as ForeignObject } from './elements/ForeignObject.js'\nexport { default as Fragment } from './elements/Fragment.js'\nexport { default as Gradient } from './elements/Gradient.js'\nexport { default as G } from './elements/G.js'\nexport { default as A } from './elements/A.js'\nexport { default as Image } from './elements/Image.js'\nexport { default as Line } from './elements/Line.js'\nexport { default as Marker } from './elements/Marker.js'\nexport { default as Mask } from './elements/Mask.js'\nexport { default as Path } from './elements/Path.js'\nexport { default as Pattern } from './elements/Pattern.js'\nexport { default as Polygon } from './elements/Polygon.js'\nexport { default as Polyline } from './elements/Polyline.js'\nexport { default as Rect } from './elements/Rect.js'\nexport { default as Shape } from './elements/Shape.js'\nexport { default as Stop } from './elements/Stop.js'\nexport { default as Style } from './elements/Style.js'\nexport { default as Svg } from './elements/Svg.js'\nexport { default as Symbol } from './elements/Symbol.js'\nexport { default as Text } from './elements/Text.js'\nexport { default as TextPath } from './elements/TextPath.js'\nexport { default as Tspan } from './elements/Tspan.js'\nexport { default as Use } from './elements/Use.js'\n\nextend([Svg, Symbol, Image, Pattern, Marker], getMethodsFor('viewbox'))\n\nextend([Line, Polyline, Polygon, Path], getMethodsFor('marker'))\n\nextend(Text, getMethodsFor('Text'))\nextend(Path, getMethodsFor('Path'))\n\nextend(Defs, getMethodsFor('Defs'))\n\nextend([Text, Tspan], getMethodsFor('Tspan'))\n\nextend([Rect, Ellipse, Gradient, Runner], getMethodsFor('radius'))\n\nextend(EventTarget, getMethodsFor('EventTarget'))\nextend(Dom, getMethodsFor('Dom'))\nextend(Element, getMethodsFor('Element'))\nextend(Shape, getMethodsFor('Shape'))\nextend([Container, Fragment], getMethodsFor('Container'))\nextend(Gradient, getMethodsFor('Gradient'))\n\nextend(Runner, getMethodsFor('Runner'))\n\nList.extend(getMethodNames())\n\nregisterMorphableType([\n  SVGNumber,\n  Color,\n  Box,\n  Matrix,\n  SVGArray,\n  PointArray,\n  PathArray,\n  Point\n])\n\nmakeMorphable()\n"], "names": ["methods", "names", "registerMethods", "name", "m", "Array", "isArray", "_name", "addMethodNames", "Object", "getOwnPropertyNames", "assign", "getMethodsFor", "getMethodNames", "Set", "_names", "push", "map", "array", "block", "i", "il", "length", "result", "filter", "radians", "d", "Math", "PI", "degrees", "r", "unCamelCase", "s", "replace", "g", "toLowerCase", "capitalize", "char<PERSON>t", "toUpperCase", "slice", "proportionalSize", "element", "width", "height", "box", "bbox", "<PERSON><PERSON><PERSON><PERSON>", "o", "origin", "ox", "originX", "oy", "originY", "x", "y", "condX", "condY", "includes", "descriptiveElements", "isDescriptive", "has", "nodeName", "writeDataToDom", "data", "defaults", "cloned", "key", "valueOf", "keys", "node", "setAttribute", "JSON", "stringify", "removeAttribute", "svg", "html", "xmlns", "xlink", "globals", "window", "document", "registerWindow", "win", "doc", "save", "saveWindow", "restoreWindow", "with<PERSON><PERSON><PERSON>", "fn", "getWindow", "Base", "elements", "root", "create", "ns", "createElementNS", "makeInstance", "isHTML", "adopter", "querySelector", "wrapper", "createElement", "innerHTML", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "nodeOrNew", "Node", "ownerDocument", "defaultView", "adopt", "instance", "Fragment", "className", "mockAdopt", "mock", "register", "asRoot", "prototype", "getClass", "did", "eid", "assignNewId", "children", "id", "extend", "modules", "wrapWithAttrCheck", "args", "constructor", "apply", "attr", "siblings", "parent", "position", "index", "next", "prev", "forward", "p", "add", "remove", "backward", "front", "back", "before", "after", "insertBefore", "insertAfter", "numberAndUnit", "hex", "rgb", "reference", "transforms", "whitespace", "isHex", "isRgb", "isBlank", "isNumber", "isImage", "delimiter", "isPathLetter", "classes", "trim", "split", "hasClass", "indexOf", "addClass", "join", "removeClass", "c", "toggleClass", "css", "style", "val", "ret", "arguments", "cssText", "el", "for<PERSON>ach", "t", "cased", "getPropertyValue", "setProperty", "test", "show", "hide", "visible", "a", "v", "attributes", "parse", "e", "remember", "k", "memory", "forget", "_memory", "sixDigitHex", "substring", "componentHex", "component", "integer", "round", "bounded", "max", "min", "toString", "is", "object", "space", "getParameters", "b", "params", "_a", "_b", "_c", "_d", "z", "h", "l", "cieSpace", "hueToRgb", "q", "Color", "inputs", "init", "isColor", "color", "random", "mode", "sin", "pi", "grey", "Error", "cmyk", "hsl", "<PERSON><PERSON><PERSON>", "delta", "values", "noWhitespace", "exec", "parseInt", "hexParse", "components", "lab", "xyz", "lch", "sqrt", "atan2", "dToR", "cos", "yL", "xL", "zL", "ct", "mx", "nm", "rU", "gU", "bU", "pow", "bd", "toArray", "toHex", "_clamped", "toRgb", "rV", "gV", "bV", "string", "r255", "g255", "b255", "rL", "gL", "bL", "xU", "yU", "zU", "format", "Point", "clone", "base", "source", "transform", "transformO", "Matrix", "isMatrixLike", "f", "point", "screenCTM", "inverseO", "close<PERSON>nough", "threshold", "abs", "formatTransforms", "flipBoth", "flip", "flipX", "flipY", "skewX", "skew", "isFinite", "skewY", "scaleX", "scale", "scaleY", "shear", "theta", "rotate", "around", "px", "positionX", "NaN", "py", "positionY", "translate", "tx", "translateX", "ty", "translateY", "relative", "rx", "relativeX", "ry", "relativeY", "fromArray", "matrixMultiply", "cx", "cy", "matrix", "aroundO", "dx", "dy", "translateO", "lmultiplyO", "decompose", "determinant", "ccw", "sx", "thetaRad", "st", "lam", "sy", "equals", "other", "comp", "axis", "flipO", "scaleO", "Element", "matrixify", "parseFloat", "call", "inverse", "det", "na", "nb", "nc", "nd", "ne", "nf", "l<PERSON>ltip<PERSON>", "multiply", "multiplyO", "rotateO", "shearO", "lx", "skewO", "tan", "ly", "current", "transformer", "ctm", "getCTM", "isRoot", "rect", "getScreenCTM", "console", "warn", "parser", "nodes", "size", "path", "parentNode", "body", "documentElement", "addTo", "isNulledBox", "domContains", "contains", "Box", "addOffset", "pageXOffset", "pageYOffset", "left", "top", "w", "x2", "y2", "isNulled", "merge", "xMin", "Infinity", "xMax", "yMin", "yMax", "pts", "getBox", "getBBoxFn", "retry", "getBBox", "rbox", "getRBox", "getBoundingClientRect", "inside", "viewbox", "zoom", "level", "clientWidth", "clientHeight", "zoomX", "zoomY", "zoomAmount", "Number", "MAX_SAFE_INTEGER", "List", "arr", "each", "fnOrMethodName", "concat", "reserved", "reduce", "obj", "attrs", "baseFind", "query", "querySelectorAll", "find", "findOne", "listenerId", "windowEvents", "getEvents", "n", "getEventHolder", "events", "getEventTarget", "clearEvents", "on", "listener", "binding", "options", "bind", "bag", "_svgjsListenerId", "event", "ev", "addEventListener", "off", "namespace", "removeEventListener", "dispatch", "Event", "dispatchEvent", "CustomEvent", "detail", "cancelable", "EventTarget", "type", "j", "defaultPrevented", "fire", "noop", "timeline", "duration", "ease", "delay", "fill", "stroke", "opacity", "offset", "SVGArray", "toSet", "SVGNumber", "convert", "unit", "value", "divide", "number", "isNaN", "match", "minus", "plus", "times", "toJSON", "colorAttributes", "hooks", "registerAttrHook", "nodeValue", "last", "curr", "getAttribute", "_val", "hook", "leading", "setAttributeNS", "rebuild", "Dom", "removeNamespace", "SVGElement", "append<PERSON><PERSON><PERSON>", "childNodes", "put", "clear", "hasChildNodes", "<PERSON><PERSON><PERSON><PERSON>", "deep", "assignNewIds", "nodeClone", "cloneNode", "first", "get", "htmlOrFn", "outerHTML", "xml", "matches", "selector", "matcher", "matchesSelector", "msMatchesSelector", "mozMatchesSelector", "webkitMatchesSelector", "oMatchesSelector", "putIn", "removeElement", "<PERSON><PERSON><PERSON><PERSON>", "precision", "factor", "svgOrFn", "outerSVG", "words", "text", "textContent", "wrap", "xmlOrFn", "outerXML", "_this", "well", "fragment", "createDocumentFragment", "len", "first<PERSON><PERSON><PERSON><PERSON><PERSON>", "dom", "hasAttribute", "setData", "center", "defs", "dmove", "move", "parents", "until", "isSelector", "sugar", "prefix", "extension", "mat", "angle", "direction", "radius", "_element", "getTotalLength", "pointAt", "getPointAtLength", "font", "untransform", "str", "kv", "reverse", "to<PERSON>arent", "pCtm", "toRoot", "decomposed", "cleanRelative", "Container", "flatten", "ungroup", "Defs", "<PERSON><PERSON><PERSON>", "Ellipse", "circled", "ellipse", "from", "fx", "fy", "x1", "y1", "to", "Gradient", "targets", "url", "update", "gradiented", "gradient", "Pattern", "pattern", "patternUnits", "Image", "load", "callback", "img", "src", "image", "PointArray", "maxX", "maxY", "minX", "minY", "points", "pop", "toLine", "MorphArray", "Line", "plot", "pointed", "line", "<PERSON><PERSON>", "orient", "ref", "marker", "makeSetterGetter", "easing", "pos", "bezier", "steps", "stepPosition", "jumps", "beforeFlag", "step", "floor", "jumping", "Stepper", "done", "Ease", "Controller", "stepper", "target", "dt", "recalculate", "_duration", "overshoot", "_overshoot", "eps", "os", "log", "zeta", "wn", "Spring", "velocity", "acceleration", "newPosition", "PID", "windup", "integral", "error", "_windup", "P", "I", "D", "segmentParameters", "M", "L", "H", "V", "C", "S", "Q", "T", "A", "Z", "pathHandlers", "p0", "mlhvqtcsaz", "jl", "makeAbsolut", "command", "segment", "segmentComplete", "startNewSegment", "token", "inNumber", "finalizeNumber", "pathLetter", "lastCommand", "small", "isSmall", "inSegment", "pointSeen", "hasExponent", "finalizeSegment", "absolute", "segments", "isArcFlag", "isArc", "isExponential", "lastToken", "pathDelimiters", "<PERSON><PERSON><PERSON><PERSON>", "toAbsolute", "arrayToString", "PathArray", "getClassForType", "NonMorphable", "morphableTypes", "ObjectBag", "<PERSON><PERSON><PERSON><PERSON>", "_stepper", "_from", "_to", "_type", "_context", "_morphObj", "at", "morph", "complete", "_set", "align", "toConsumable", "TransformBag", "sortByKey", "splice", "defaultObject", "toDelete", "obj<PERSON>r<PERSON><PERSON>", "entries", "Type", "sort", "shift", "num", "registerMorphableType", "makeMorphable", "context", "mapper", "Path", "_array", "Polygon", "polygon", "poly", "Polyline", "polyline", "Rect", "Queue", "_first", "_last", "item", "Animator", "nextDraw", "frames", "timeouts", "immediates", "timer", "performance", "Date", "frame", "run", "requestAnimationFrame", "_draw", "timeout", "time", "now", "immediate", "cancelFrame", "clearTimeout", "cancelImmediate", "nextTimeout", "lastTimeout", "next<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "nextImmediate", "makeSchedule", "runnerInfo", "start", "runner", "end", "defaultSource", "Timeline", "timeSource", "_timeSource", "terminate", "active", "_nextFrame", "finish", "getEndTimeOfTimeline", "pause", "getEndTime", "lastRunnerInfo", "getLastRunnerInfo", "lastDuration", "lastStartTime", "_time", "endTimes", "_runners", "getRunnerInfoById", "_lastRunnerId", "_runnerIds", "_paused", "_continue", "persist", "dt<PERSON>r<PERSON><PERSON><PERSON>", "_persist", "play", "updateTime", "yes", "currentSpeed", "speed", "positive", "schedule", "when", "absoluteStartTime", "endTime", "unschedule", "info", "seek", "_speed", "stop", "_lastSourceTime", "immediateStep", "_stepImmediate", "_step", "_stepFn", "dtSource", "dtTime", "_lastStepTime", "dtToStart", "reset", "runnersLeft", "finished", "_startTime", "_timeline", "Runner", "_queue", "_isDeclarative", "_history", "enabled", "_lastTime", "_reseted", "transformId", "_haveReversed", "_reverse", "_loopsDone", "_swing", "_wait", "_times", "_frameId", "sanitise", "swing", "wait", "addTransform", "animate", "loop", "clearTransform", "clearTransformsFromQueue", "isTransform", "during", "queue", "_prepareRunner", "loops", "loopDuration", "loopsDone", "relativeTime", "whole", "partial", "swinging", "backwards", "uncliped", "clipped", "swingForward", "forwards", "progress", "initFn", "runFn", "retargetFn", "initialiser", "retarget", "initialised", "running", "_lastPosition", "justStarted", "justFinished", "declarative", "converged", "_initialise", "_run", "needsIt", "_rememberMorpher", "method", "morpher", "caller", "positionOrDt", "allfinished", "_tryRetarget", "extra", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mergeWith", "getRunnerTransform", "mergeTransforms", "runners", "_transformationRunners", "netTransform", "RunnerArray", "ids", "clearBefore", "deleteCnt", "edit", "<PERSON><PERSON><PERSON><PERSON>", "getByID", "lastRunner", "condition", "by", "_clearTransformRunnersBefore", "current<PERSON><PERSON>ner", "_currentTransform", "_addRunner", "difference", "styleAttr", "nameOrAttrs", "newToAttrs", "newKeys", "differences", "addedFromAttrs", "oldFromAttrs", "oldToAttrs", "newLevel", "newPoint", "affine", "isMatrix", "currentAngle", "startTransform", "setup", "undefined", "r<PERSON>arget", "r<PERSON><PERSON>rent", "possibilities", "distances", "shortest", "affineParameters", "newTransforms", "_queueNumber", "ax", "ay", "_queueNumberDelta", "newTo", "_queueObject", "amove", "Svg", "version", "nested", "Symbol", "symbol", "plain", "_build", "createTextNode", "getComputedTextLength", "build", "Text", "_rebuild", "self", "blankLineOffset", "fontSize", "getComputedStyle", "newLined", "firstLine", "nodeType", "newLine", "textable", "Tspan", "tspan", "Circle", "circle", "<PERSON><PERSON><PERSON><PERSON>", "unclip", "clip", "clipper", "clipWith", "ForeignObject", "foreignObject", "child", "SVGSVGElement", "G", "containerGeometry", "group", "link", "unlink", "linker", "linkTo", "Mask", "unmask", "mask", "masker", "mask<PERSON>ith", "Stop", "cssRule", "rule", "Style", "addText", "fontFamily", "fontface", "TextPath", "track", "pathArray", "textPath", "importNodes", "Use", "use", "file", "SVG"], "mappings": ";;;;;;;;;;AAAA,MAAMA,SAAO,GAAG,EAAE,CAAA;AAClB,MAAMC,KAAK,GAAG,EAAE,CAAA;AAET,SAASC,eAAeA,CAACC,IAAI,EAAEC,CAAC,EAAE;AACvC,EAAA,IAAIC,KAAK,CAACC,OAAO,CAACH,IAAI,CAAC,EAAE;AACvB,IAAA,KAAK,MAAMI,KAAK,IAAIJ,IAAI,EAAE;AACxBD,MAAAA,eAAe,CAACK,KAAK,EAAEH,CAAC,CAAC,CAAA;AAC3B,KAAA;AACA,IAAA,OAAA;AACF,GAAA;AAEA,EAAA,IAAI,OAAOD,IAAI,KAAK,QAAQ,EAAE;AAC5B,IAAA,KAAK,MAAMI,KAAK,IAAIJ,IAAI,EAAE;AACxBD,MAAAA,eAAe,CAACK,KAAK,EAAEJ,IAAI,CAACI,KAAK,CAAC,CAAC,CAAA;AACrC,KAAA;AACA,IAAA,OAAA;AACF,GAAA;AAEAC,EAAAA,cAAc,CAACC,MAAM,CAACC,mBAAmB,CAACN,CAAC,CAAC,CAAC,CAAA;AAC7CJ,EAAAA,SAAO,CAACG,IAAI,CAAC,GAAGM,MAAM,CAACE,MAAM,CAACX,SAAO,CAACG,IAAI,CAAC,IAAI,EAAE,EAAEC,CAAC,CAAC,CAAA;AACvD,CAAA;AAEO,SAASQ,aAAaA,CAACT,IAAI,EAAE;AAClC,EAAA,OAAOH,SAAO,CAACG,IAAI,CAAC,IAAI,EAAE,CAAA;AAC5B,CAAA;AAEO,SAASU,cAAcA,GAAG;AAC/B,EAAA,OAAO,CAAC,GAAG,IAAIC,GAAG,CAACb,KAAK,CAAC,CAAC,CAAA;AAC5B,CAAA;AAEO,SAASO,cAAcA,CAACO,MAAM,EAAE;AACrCd,EAAAA,KAAK,CAACe,IAAI,CAAC,GAAGD,MAAM,CAAC,CAAA;AACvB;;AChCA;AACO,SAASE,GAAGA,CAACC,KAAK,EAAEC,KAAK,EAAE;AAChC,EAAA,IAAIC,CAAC,CAAA;AACL,EAAA,MAAMC,EAAE,GAAGH,KAAK,CAACI,MAAM,CAAA;EACvB,MAAMC,MAAM,GAAG,EAAE,CAAA;EAEjB,KAAKH,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,EAAE,EAAED,CAAC,EAAE,EAAE;IACvBG,MAAM,CAACP,IAAI,CAACG,KAAK,CAACD,KAAK,CAACE,CAAC,CAAC,CAAC,CAAC,CAAA;AAC9B,GAAA;AAEA,EAAA,OAAOG,MAAM,CAAA;AACf,CAAA;;AAEA;AACO,SAASC,MAAMA,CAACN,KAAK,EAAEC,KAAK,EAAE;AACnC,EAAA,IAAIC,CAAC,CAAA;AACL,EAAA,MAAMC,EAAE,GAAGH,KAAK,CAACI,MAAM,CAAA;EACvB,MAAMC,MAAM,GAAG,EAAE,CAAA;EAEjB,KAAKH,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,EAAE,EAAED,CAAC,EAAE,EAAE;AACvB,IAAA,IAAID,KAAK,CAACD,KAAK,CAACE,CAAC,CAAC,CAAC,EAAE;AACnBG,MAAAA,MAAM,CAACP,IAAI,CAACE,KAAK,CAACE,CAAC,CAAC,CAAC,CAAA;AACvB,KAAA;AACF,GAAA;AAEA,EAAA,OAAOG,MAAM,CAAA;AACf,CAAA;;AAEA;AACO,SAASE,OAAOA,CAACC,CAAC,EAAE;EACzB,OAASA,CAAC,GAAG,GAAG,GAAIC,IAAI,CAACC,EAAE,GAAI,GAAG,CAAA;AACpC,CAAA;;AAEA;AACO,SAASC,OAAOA,CAACC,CAAC,EAAE;EACzB,OAASA,CAAC,GAAG,GAAG,GAAIH,IAAI,CAACC,EAAE,GAAI,GAAG,CAAA;AACpC,CAAA;;AAEA;AACO,SAASG,WAAWA,CAACC,CAAC,EAAE;EAC7B,OAAOA,CAAC,CAACC,OAAO,CAAC,UAAU,EAAE,UAAU7B,CAAC,EAAE8B,CAAC,EAAE;AAC3C,IAAA,OAAO,GAAG,GAAGA,CAAC,CAACC,WAAW,EAAE,CAAA;AAC9B,GAAC,CAAC,CAAA;AACJ,CAAA;;AAEA;AACO,SAASC,UAAUA,CAACJ,CAAC,EAAE;AAC5B,EAAA,OAAOA,CAAC,CAACK,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,EAAE,GAAGN,CAAC,CAACO,KAAK,CAAC,CAAC,CAAC,CAAA;AAC/C,CAAA;;AAEA;AACO,SAASC,gBAAgBA,CAACC,OAAO,EAAEC,KAAK,EAAEC,MAAM,EAAEC,GAAG,EAAE;AAC5D,EAAA,IAAIF,KAAK,IAAI,IAAI,IAAIC,MAAM,IAAI,IAAI,EAAE;AACnCC,IAAAA,GAAG,GAAGA,GAAG,IAAIH,OAAO,CAACI,IAAI,EAAE,CAAA;IAE3B,IAAIH,KAAK,IAAI,IAAI,EAAE;MACjBA,KAAK,GAAIE,GAAG,CAACF,KAAK,GAAGE,GAAG,CAACD,MAAM,GAAIA,MAAM,CAAA;AAC3C,KAAC,MAAM,IAAIA,MAAM,IAAI,IAAI,EAAE;MACzBA,MAAM,GAAIC,GAAG,CAACD,MAAM,GAAGC,GAAG,CAACF,KAAK,GAAIA,KAAK,CAAA;AAC3C,KAAA;AACF,GAAA;EAEA,OAAO;AACLA,IAAAA,KAAK,EAAEA,KAAK;AACZC,IAAAA,MAAM,EAAEA,MAAAA;GACT,CAAA;AACH,CAAA;;AAEA;AACA;AACA;AACA;AACA;AACO,SAASG,SAASA,CAACC,CAAC,EAAEN,OAAO,EAAE;AACpC,EAAA,MAAMO,MAAM,GAAGD,CAAC,CAACC,MAAM,CAAA;AACvB;EACA,IAAIC,EAAE,GAAGF,CAAC,CAACE,EAAE,IAAI,IAAI,GAAGF,CAAC,CAACE,EAAE,GAAGF,CAAC,CAACG,OAAO,IAAI,IAAI,GAAGH,CAAC,CAACG,OAAO,GAAG,QAAQ,CAAA;EACvE,IAAIC,EAAE,GAAGJ,CAAC,CAACI,EAAE,IAAI,IAAI,GAAGJ,CAAC,CAACI,EAAE,GAAGJ,CAAC,CAACK,OAAO,IAAI,IAAI,GAAGL,CAAC,CAACK,OAAO,GAAG,QAAQ,CAAA;;AAEvE;EACA,IAAIJ,MAAM,IAAI,IAAI,EAAE;AACjB,IAAA,CAACC,EAAE,EAAEE,EAAE,CAAC,GAAG9C,KAAK,CAACC,OAAO,CAAC0C,MAAM,CAAC,GAC7BA,MAAM,GACN,OAAOA,MAAM,KAAK,QAAQ,GACxB,CAACA,MAAM,CAACK,CAAC,EAAEL,MAAM,CAACM,CAAC,CAAC,GACpB,CAACN,MAAM,EAAEA,MAAM,CAAC,CAAA;AACxB,GAAA;;AAEA;AACA,EAAA,MAAMO,KAAK,GAAG,OAAON,EAAE,KAAK,QAAQ,CAAA;AACpC,EAAA,MAAMO,KAAK,GAAG,OAAOL,EAAE,KAAK,QAAQ,CAAA;EACpC,IAAII,KAAK,IAAIC,KAAK,EAAE;IAClB,MAAM;MAAEb,MAAM;MAAED,KAAK;MAAEW,CAAC;AAAEC,MAAAA,CAAAA;AAAE,KAAC,GAAGb,OAAO,CAACI,IAAI,EAAE,CAAA;;AAE9C;AACA,IAAA,IAAIU,KAAK,EAAE;MACTN,EAAE,GAAGA,EAAE,CAACQ,QAAQ,CAAC,MAAM,CAAC,GACpBJ,CAAC,GACDJ,EAAE,CAACQ,QAAQ,CAAC,OAAO,CAAC,GAClBJ,CAAC,GAAGX,KAAK,GACTW,CAAC,GAAGX,KAAK,GAAG,CAAC,CAAA;AACrB,KAAA;AAEA,IAAA,IAAIc,KAAK,EAAE;MACTL,EAAE,GAAGA,EAAE,CAACM,QAAQ,CAAC,KAAK,CAAC,GACnBH,CAAC,GACDH,EAAE,CAACM,QAAQ,CAAC,QAAQ,CAAC,GACnBH,CAAC,GAAGX,MAAM,GACVW,CAAC,GAAGX,MAAM,GAAG,CAAC,CAAA;AACtB,KAAA;AACF,GAAA;;AAEA;AACA,EAAA,OAAO,CAACM,EAAE,EAAEE,EAAE,CAAC,CAAA;AACjB,CAAA;AAEA,MAAMO,mBAAmB,GAAG,IAAI5C,GAAG,CAAC,CAAC,MAAM,EAAE,UAAU,EAAE,OAAO,CAAC,CAAC,CAAA;AAC3D,MAAM6C,aAAa,GAAIlB,OAAO,IACnCiB,mBAAmB,CAACE,GAAG,CAACnB,OAAO,CAACoB,QAAQ,CAAC,CAAA;AAEpC,MAAMC,cAAc,GAAGA,CAACrB,OAAO,EAAEsB,IAAI,EAAEC,QAAQ,GAAG,EAAE,KAAK;AAC9D,EAAA,MAAMC,MAAM,GAAG;IAAE,GAAGF,IAAAA;GAAM,CAAA;AAE1B,EAAA,KAAK,MAAMG,GAAG,IAAID,MAAM,EAAE;AACxB,IAAA,IAAIA,MAAM,CAACC,GAAG,CAAC,CAACC,OAAO,EAAE,KAAKH,QAAQ,CAACE,GAAG,CAAC,EAAE;MAC3C,OAAOD,MAAM,CAACC,GAAG,CAAC,CAAA;AACpB,KAAA;AACF,GAAA;EAEA,IAAIzD,MAAM,CAAC2D,IAAI,CAACH,MAAM,CAAC,CAAC3C,MAAM,EAAE;AAC9BmB,IAAAA,OAAO,CAAC4B,IAAI,CAACC,YAAY,CAAC,YAAY,EAAEC,IAAI,CAACC,SAAS,CAACP,MAAM,CAAC,CAAC,CAAC;AAClE,GAAC,MAAM;AACLxB,IAAAA,OAAO,CAAC4B,IAAI,CAACI,eAAe,CAAC,YAAY,CAAC,CAAA;AAC1ChC,IAAAA,OAAO,CAAC4B,IAAI,CAACI,eAAe,CAAC,YAAY,CAAC,CAAA;AAC5C,GAAA;AACF,CAAC;;;;;;;;;;;;;;;;ACvID;AACO,MAAMC,GAAG,GAAG,4BAA4B,CAAA;AACxC,MAAMC,IAAI,GAAG,8BAA8B,CAAA;AAC3C,MAAMC,KAAK,GAAG,+BAA+B,CAAA;AAC7C,MAAMC,KAAK,GAAG,8BAA8B;;;;;;;;;;ACJ5C,MAAMC,OAAO,GAAG;EACrBC,MAAM,EAAE,OAAOA,MAAM,KAAK,WAAW,GAAG,IAAI,GAAGA,MAAM;AACrDC,EAAAA,QAAQ,EAAE,OAAOA,QAAQ,KAAK,WAAW,GAAG,IAAI,GAAGA,QAAAA;AACrD,CAAC,CAAA;AAEM,SAASC,cAAcA,CAACC,GAAG,GAAG,IAAI,EAAEC,GAAG,GAAG,IAAI,EAAE;EACrDL,OAAO,CAACC,MAAM,GAAGG,GAAG,CAAA;EACpBJ,OAAO,CAACE,QAAQ,GAAGG,GAAG,CAAA;AACxB,CAAA;AAEA,MAAMC,IAAI,GAAG,EAAE,CAAA;AAER,SAASC,UAAUA,GAAG;AAC3BD,EAAAA,IAAI,CAACL,MAAM,GAAGD,OAAO,CAACC,MAAM,CAAA;AAC5BK,EAAAA,IAAI,CAACJ,QAAQ,GAAGF,OAAO,CAACE,QAAQ,CAAA;AAClC,CAAA;AAEO,SAASM,aAAaA,GAAG;AAC9BR,EAAAA,OAAO,CAACC,MAAM,GAAGK,IAAI,CAACL,MAAM,CAAA;AAC5BD,EAAAA,OAAO,CAACE,QAAQ,GAAGI,IAAI,CAACJ,QAAQ,CAAA;AAClC,CAAA;AAEO,SAASO,UAAUA,CAACL,GAAG,EAAEM,EAAE,EAAE;AAClCH,EAAAA,UAAU,EAAE,CAAA;AACZJ,EAAAA,cAAc,CAACC,GAAG,EAAEA,GAAG,CAACF,QAAQ,CAAC,CAAA;AACjCQ,EAAAA,EAAE,CAACN,GAAG,EAAEA,GAAG,CAACF,QAAQ,CAAC,CAAA;AACrBM,EAAAA,aAAa,EAAE,CAAA;AACjB,CAAA;AAEO,SAASG,SAASA,GAAG;EAC1B,OAAOX,OAAO,CAACC,MAAM,CAAA;AACvB;;AC/Be,MAAMW,IAAI,CAAC;AACxB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;;ACFF,MAAMC,QAAQ,GAAG,EAAE,CAAA;AACZ,MAAMC,IAAI,GAAG,sBAAqB;;AAEzC;AACO,SAASC,MAAMA,CAAC1F,IAAI,EAAE2F,EAAE,GAAGpB,GAAG,EAAE;AACrC;EACA,OAAOI,OAAO,CAACE,QAAQ,CAACe,eAAe,CAACD,EAAE,EAAE3F,IAAI,CAAC,CAAA;AACnD,CAAA;AAEO,SAAS6F,YAAYA,CAACvD,OAAO,EAAEwD,MAAM,GAAG,KAAK,EAAE;AACpD,EAAA,IAAIxD,OAAO,YAAYiD,IAAI,EAAE,OAAOjD,OAAO,CAAA;AAE3C,EAAA,IAAI,OAAOA,OAAO,KAAK,QAAQ,EAAE;IAC/B,OAAOyD,OAAO,CAACzD,OAAO,CAAC,CAAA;AACzB,GAAA;EAEA,IAAIA,OAAO,IAAI,IAAI,EAAE;AACnB,IAAA,OAAO,IAAIkD,QAAQ,CAACC,IAAI,CAAC,EAAE,CAAA;AAC7B,GAAA;AAEA,EAAA,IAAI,OAAOnD,OAAO,KAAK,QAAQ,IAAIA,OAAO,CAACJ,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;IAC5D,OAAO6D,OAAO,CAACpB,OAAO,CAACE,QAAQ,CAACmB,aAAa,CAAC1D,OAAO,CAAC,CAAC,CAAA;AACzD,GAAA;;AAEA;AACA,EAAA,MAAM2D,OAAO,GAAGH,MAAM,GAAGnB,OAAO,CAACE,QAAQ,CAACqB,aAAa,CAAC,KAAK,CAAC,GAAGR,MAAM,CAAC,KAAK,CAAC,CAAA;EAC9EO,OAAO,CAACE,SAAS,GAAG7D,OAAO,CAAA;;AAE3B;AACA;AACAA,EAAAA,OAAO,GAAGyD,OAAO,CAACE,OAAO,CAACG,UAAU,CAAC,CAAA;;AAErC;AACAH,EAAAA,OAAO,CAACI,WAAW,CAACJ,OAAO,CAACG,UAAU,CAAC,CAAA;AACvC,EAAA,OAAO9D,OAAO,CAAA;AAChB,CAAA;AAEO,SAASgE,SAASA,CAACtG,IAAI,EAAEkE,IAAI,EAAE;AACpC,EAAA,OAAOA,IAAI,KACRA,IAAI,YAAYS,OAAO,CAACC,MAAM,CAAC2B,IAAI,IACjCrC,IAAI,CAACsC,aAAa,IACjBtC,IAAI,YAAYA,IAAI,CAACsC,aAAa,CAACC,WAAW,CAACF,IAAK,CAAC,GACvDrC,IAAI,GACJwB,MAAM,CAAC1F,IAAI,CAAC,CAAA;AAClB,CAAA;;AAEA;AACO,SAAS0G,KAAKA,CAACxC,IAAI,EAAE;AAC1B;AACA,EAAA,IAAI,CAACA,IAAI,EAAE,OAAO,IAAI,CAAA;;AAEtB;EACA,IAAIA,IAAI,CAACyC,QAAQ,YAAYpB,IAAI,EAAE,OAAOrB,IAAI,CAACyC,QAAQ,CAAA;AAEvD,EAAA,IAAIzC,IAAI,CAACR,QAAQ,KAAK,oBAAoB,EAAE;AAC1C,IAAA,OAAO,IAAI8B,QAAQ,CAACoB,QAAQ,CAAC1C,IAAI,CAAC,CAAA;AACpC,GAAA;;AAEA;EACA,IAAI2C,SAAS,GAAG5E,UAAU,CAACiC,IAAI,CAACR,QAAQ,IAAI,KAAK,CAAC,CAAA;;AAElD;AACA,EAAA,IAAImD,SAAS,KAAK,gBAAgB,IAAIA,SAAS,KAAK,gBAAgB,EAAE;AACpEA,IAAAA,SAAS,GAAG,UAAU,CAAA;;AAEtB;AACF,GAAC,MAAM,IAAI,CAACrB,QAAQ,CAACqB,SAAS,CAAC,EAAE;AAC/BA,IAAAA,SAAS,GAAG,KAAK,CAAA;AACnB,GAAA;AAEA,EAAA,OAAO,IAAIrB,QAAQ,CAACqB,SAAS,CAAC,CAAC3C,IAAI,CAAC,CAAA;AACtC,CAAA;AAEA,IAAI6B,OAAO,GAAGW,KAAK,CAAA;AAEZ,SAASI,SAASA,CAACC,IAAI,GAAGL,KAAK,EAAE;AACtCX,EAAAA,OAAO,GAAGgB,IAAI,CAAA;AAChB,CAAA;AAEO,SAASC,QAAQA,CAAC1E,OAAO,EAAEtC,IAAI,GAAGsC,OAAO,CAACtC,IAAI,EAAEiH,MAAM,GAAG,KAAK,EAAE;AACrEzB,EAAAA,QAAQ,CAACxF,IAAI,CAAC,GAAGsC,OAAO,CAAA;AACxB,EAAA,IAAI2E,MAAM,EAAEzB,QAAQ,CAACC,IAAI,CAAC,GAAGnD,OAAO,CAAA;EAEpCjC,cAAc,CAACC,MAAM,CAACC,mBAAmB,CAAC+B,OAAO,CAAC4E,SAAS,CAAC,CAAC,CAAA;AAE7D,EAAA,OAAO5E,OAAO,CAAA;AAChB,CAAA;AAEO,SAAS6E,QAAQA,CAACnH,IAAI,EAAE;EAC7B,OAAOwF,QAAQ,CAACxF,IAAI,CAAC,CAAA;AACvB,CAAA;;AAEA;AACA,IAAIoH,GAAG,GAAG,IAAI,CAAA;;AAEd;AACO,SAASC,GAAGA,CAACrH,IAAI,EAAE;EACxB,OAAO,OAAO,GAAGiC,UAAU,CAACjC,IAAI,CAAC,GAAGoH,GAAG,EAAE,CAAA;AAC3C,CAAA;;AAEA;AACO,SAASE,WAAWA,CAACpD,IAAI,EAAE;AAChC;AACA,EAAA,KAAK,IAAIjD,CAAC,GAAGiD,IAAI,CAACqD,QAAQ,CAACpG,MAAM,GAAG,CAAC,EAAEF,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;AAClDqG,IAAAA,WAAW,CAACpD,IAAI,CAACqD,QAAQ,CAACtG,CAAC,CAAC,CAAC,CAAA;AAC/B,GAAA;EAEA,IAAIiD,IAAI,CAACsD,EAAE,EAAE;IACXtD,IAAI,CAACsD,EAAE,GAAGH,GAAG,CAACnD,IAAI,CAACR,QAAQ,CAAC,CAAA;AAC5B,IAAA,OAAOQ,IAAI,CAAA;AACb,GAAA;AAEA,EAAA,OAAOA,IAAI,CAAA;AACb,CAAA;;AAEA;AACO,SAASuD,MAAMA,CAACC,OAAO,EAAE7H,OAAO,EAAE;EACvC,IAAIkE,GAAG,EAAE9C,CAAC,CAAA;AAEVyG,EAAAA,OAAO,GAAGxH,KAAK,CAACC,OAAO,CAACuH,OAAO,CAAC,GAAGA,OAAO,GAAG,CAACA,OAAO,CAAC,CAAA;AAEtD,EAAA,KAAKzG,CAAC,GAAGyG,OAAO,CAACvG,MAAM,GAAG,CAAC,EAAEF,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;IACxC,KAAK8C,GAAG,IAAIlE,OAAO,EAAE;AACnB6H,MAAAA,OAAO,CAACzG,CAAC,CAAC,CAACiG,SAAS,CAACnD,GAAG,CAAC,GAAGlE,OAAO,CAACkE,GAAG,CAAC,CAAA;AAC1C,KAAA;AACF,GAAA;AACF,CAAA;AAEO,SAAS4D,iBAAiBA,CAACtC,EAAE,EAAE;EACpC,OAAO,UAAU,GAAGuC,IAAI,EAAE;IACxB,MAAMhF,CAAC,GAAGgF,IAAI,CAACA,IAAI,CAACzG,MAAM,GAAG,CAAC,CAAC,CAAA;AAE/B,IAAA,IAAIyB,CAAC,IAAIA,CAAC,CAACiF,WAAW,KAAKvH,MAAM,IAAI,EAAEsC,CAAC,YAAY1C,KAAK,CAAC,EAAE;MAC1D,OAAOmF,EAAE,CAACyC,KAAK,CAAC,IAAI,EAAEF,IAAI,CAACxF,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC2F,IAAI,CAACnF,CAAC,CAAC,CAAA;AAClD,KAAC,MAAM;AACL,MAAA,OAAOyC,EAAE,CAACyC,KAAK,CAAC,IAAI,EAAEF,IAAI,CAAC,CAAA;AAC7B,KAAA;GACD,CAAA;AACH;;AC7IA;AACO,SAASI,QAAQA,GAAG;EACzB,OAAO,IAAI,CAACC,MAAM,EAAE,CAACV,QAAQ,EAAE,CAAA;AACjC,CAAA;;AAEA;AACO,SAASW,QAAQA,GAAG;EACzB,OAAO,IAAI,CAACD,MAAM,EAAE,CAACE,KAAK,CAAC,IAAI,CAAC,CAAA;AAClC,CAAA;;AAEA;AACO,SAASC,IAAIA,GAAG;AACrB,EAAA,OAAO,IAAI,CAACJ,QAAQ,EAAE,CAAC,IAAI,CAACE,QAAQ,EAAE,GAAG,CAAC,CAAC,CAAA;AAC7C,CAAA;;AAEA;AACO,SAASG,IAAIA,GAAG;AACrB,EAAA,OAAO,IAAI,CAACL,QAAQ,EAAE,CAAC,IAAI,CAACE,QAAQ,EAAE,GAAG,CAAC,CAAC,CAAA;AAC7C,CAAA;;AAEA;AACO,SAASI,OAAOA,GAAG;AACxB,EAAA,MAAMrH,CAAC,GAAG,IAAI,CAACiH,QAAQ,EAAE,CAAA;AACzB,EAAA,MAAMK,CAAC,GAAG,IAAI,CAACN,MAAM,EAAE,CAAA;;AAEvB;AACAM,EAAAA,CAAC,CAACC,GAAG,CAAC,IAAI,CAACC,MAAM,EAAE,EAAExH,CAAC,GAAG,CAAC,CAAC,CAAA;AAE3B,EAAA,OAAO,IAAI,CAAA;AACb,CAAA;;AAEA;AACO,SAASyH,QAAQA,GAAG;AACzB,EAAA,MAAMzH,CAAC,GAAG,IAAI,CAACiH,QAAQ,EAAE,CAAA;AACzB,EAAA,MAAMK,CAAC,GAAG,IAAI,CAACN,MAAM,EAAE,CAAA;AAEvBM,EAAAA,CAAC,CAACC,GAAG,CAAC,IAAI,CAACC,MAAM,EAAE,EAAExH,CAAC,GAAGA,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAA;AAEnC,EAAA,OAAO,IAAI,CAAA;AACb,CAAA;;AAEA;AACO,SAAS0H,KAAKA,GAAG;AACtB,EAAA,MAAMJ,CAAC,GAAG,IAAI,CAACN,MAAM,EAAE,CAAA;;AAEvB;EACAM,CAAC,CAACC,GAAG,CAAC,IAAI,CAACC,MAAM,EAAE,CAAC,CAAA;AAEpB,EAAA,OAAO,IAAI,CAAA;AACb,CAAA;;AAEA;AACO,SAASG,IAAIA,GAAG;AACrB,EAAA,MAAML,CAAC,GAAG,IAAI,CAACN,MAAM,EAAE,CAAA;;AAEvB;EACAM,CAAC,CAACC,GAAG,CAAC,IAAI,CAACC,MAAM,EAAE,EAAE,CAAC,CAAC,CAAA;AAEvB,EAAA,OAAO,IAAI,CAAA;AACb,CAAA;;AAEA;AACO,SAASI,MAAMA,CAACvG,OAAO,EAAE;AAC9BA,EAAAA,OAAO,GAAGuD,YAAY,CAACvD,OAAO,CAAC,CAAA;EAC/BA,OAAO,CAACmG,MAAM,EAAE,CAAA;AAEhB,EAAA,MAAMxH,CAAC,GAAG,IAAI,CAACiH,QAAQ,EAAE,CAAA;EAEzB,IAAI,CAACD,MAAM,EAAE,CAACO,GAAG,CAAClG,OAAO,EAAErB,CAAC,CAAC,CAAA;AAE7B,EAAA,OAAO,IAAI,CAAA;AACb,CAAA;;AAEA;AACO,SAAS6H,KAAKA,CAACxG,OAAO,EAAE;AAC7BA,EAAAA,OAAO,GAAGuD,YAAY,CAACvD,OAAO,CAAC,CAAA;EAC/BA,OAAO,CAACmG,MAAM,EAAE,CAAA;AAEhB,EAAA,MAAMxH,CAAC,GAAG,IAAI,CAACiH,QAAQ,EAAE,CAAA;AAEzB,EAAA,IAAI,CAACD,MAAM,EAAE,CAACO,GAAG,CAAClG,OAAO,EAAErB,CAAC,GAAG,CAAC,CAAC,CAAA;AAEjC,EAAA,OAAO,IAAI,CAAA;AACb,CAAA;AAEO,SAAS8H,YAAYA,CAACzG,OAAO,EAAE;AACpCA,EAAAA,OAAO,GAAGuD,YAAY,CAACvD,OAAO,CAAC,CAAA;AAC/BA,EAAAA,OAAO,CAACuG,MAAM,CAAC,IAAI,CAAC,CAAA;AACpB,EAAA,OAAO,IAAI,CAAA;AACb,CAAA;AAEO,SAASG,WAAWA,CAAC1G,OAAO,EAAE;AACnCA,EAAAA,OAAO,GAAGuD,YAAY,CAACvD,OAAO,CAAC,CAAA;AAC/BA,EAAAA,OAAO,CAACwG,KAAK,CAAC,IAAI,CAAC,CAAA;AACnB,EAAA,OAAO,IAAI,CAAA;AACb,CAAA;AAEA/I,eAAe,CAAC,KAAK,EAAE;EACrBiI,QAAQ;EACRE,QAAQ;EACRE,IAAI;EACJC,IAAI;EACJC,OAAO;EACPI,QAAQ;EACRC,KAAK;EACLC,IAAI;EACJC,MAAM;EACNC,KAAK;EACLC,YAAY;AACZC,EAAAA,WAAAA;AACF,CAAC,CAAC;;ACjHF;AACO,MAAMC,aAAa,GACxB,oDAAoD,CAAA;;AAEtD;AACO,MAAMC,GAAG,GAAG,2CAA2C,CAAA;;AAE9D;AACO,MAAMC,GAAG,GAAG,0BAA0B,CAAA;;AAE7C;AACO,MAAMC,SAAS,GAAG,wBAAwB,CAAA;;AAEjD;AACO,MAAMC,UAAU,GAAG,YAAY,CAAA;;AAEtC;AACO,MAAMC,UAAU,GAAG,KAAK,CAAA;;AAE/B;AACO,MAAMC,KAAK,GAAG,gCAAgC,CAAA;;AAErD;AACO,MAAMC,KAAK,GAAG,QAAQ,CAAA;;AAE7B;AACO,MAAMC,OAAO,GAAG,UAAU,CAAA;;AAEjC;AACO,MAAMC,QAAQ,GAAG,yCAAyC,CAAA;;AAEjE;AACO,MAAMC,OAAO,GAAG,uCAAuC,CAAA;;AAE9D;AACO,MAAMC,SAAS,GAAG,QAAQ,CAAA;;AAEjC;AACO,MAAMC,YAAY,GAAG,eAAe;;;;;;;;;;;;;;;;;;;ACnC3C;AACO,SAASC,OAAOA,GAAG;AACxB,EAAA,MAAM/B,IAAI,GAAG,IAAI,CAACA,IAAI,CAAC,OAAO,CAAC,CAAA;AAC/B,EAAA,OAAOA,IAAI,IAAI,IAAI,GAAG,EAAE,GAAGA,IAAI,CAACgC,IAAI,EAAE,CAACC,KAAK,CAACJ,SAAS,CAAC,CAAA;AACzD,CAAA;;AAEA;AACO,SAASK,QAAQA,CAACjK,IAAI,EAAE;AAC7B,EAAA,OAAO,IAAI,CAAC8J,OAAO,EAAE,CAACI,OAAO,CAAClK,IAAI,CAAC,KAAK,CAAC,CAAC,CAAA;AAC5C,CAAA;;AAEA;AACO,SAASmK,QAAQA,CAACnK,IAAI,EAAE;AAC7B,EAAA,IAAI,CAAC,IAAI,CAACiK,QAAQ,CAACjK,IAAI,CAAC,EAAE;AACxB,IAAA,MAAMe,KAAK,GAAG,IAAI,CAAC+I,OAAO,EAAE,CAAA;AAC5B/I,IAAAA,KAAK,CAACF,IAAI,CAACb,IAAI,CAAC,CAAA;IAChB,IAAI,CAAC+H,IAAI,CAAC,OAAO,EAAEhH,KAAK,CAACqJ,IAAI,CAAC,GAAG,CAAC,CAAC,CAAA;AACrC,GAAA;AAEA,EAAA,OAAO,IAAI,CAAA;AACb,CAAA;;AAEA;AACO,SAASC,WAAWA,CAACrK,IAAI,EAAE;AAChC,EAAA,IAAI,IAAI,CAACiK,QAAQ,CAACjK,IAAI,CAAC,EAAE;AACvB,IAAA,IAAI,CAAC+H,IAAI,CACP,OAAO,EACP,IAAI,CAAC+B,OAAO,EAAE,CACXzI,MAAM,CAAC,UAAUiJ,CAAC,EAAE;MACnB,OAAOA,CAAC,KAAKtK,IAAI,CAAA;AACnB,KAAC,CAAC,CACDoK,IAAI,CAAC,GAAG,CACb,CAAC,CAAA;AACH,GAAA;AAEA,EAAA,OAAO,IAAI,CAAA;AACb,CAAA;;AAEA;AACO,SAASG,WAAWA,CAACvK,IAAI,EAAE;AAChC,EAAA,OAAO,IAAI,CAACiK,QAAQ,CAACjK,IAAI,CAAC,GAAG,IAAI,CAACqK,WAAW,CAACrK,IAAI,CAAC,GAAG,IAAI,CAACmK,QAAQ,CAACnK,IAAI,CAAC,CAAA;AAC3E,CAAA;AAEAD,eAAe,CAAC,KAAK,EAAE;EACrB+J,OAAO;EACPG,QAAQ;EACRE,QAAQ;EACRE,WAAW;AACXE,EAAAA,WAAAA;AACF,CAAC,CAAC;;ACjDF;AACO,SAASC,GAAGA,CAACC,KAAK,EAAEC,GAAG,EAAE;EAC9B,MAAMC,GAAG,GAAG,EAAE,CAAA;AACd,EAAA,IAAIC,SAAS,CAACzJ,MAAM,KAAK,CAAC,EAAE;AAC1B;AACA,IAAA,IAAI,CAAC+C,IAAI,CAACuG,KAAK,CAACI,OAAO,CACpBb,KAAK,CAAC,SAAS,CAAC,CAChB3I,MAAM,CAAC,UAAUyJ,EAAE,EAAE;AACpB,MAAA,OAAO,CAAC,CAACA,EAAE,CAAC3J,MAAM,CAAA;AACpB,KAAC,CAAC,CACD4J,OAAO,CAAC,UAAUD,EAAE,EAAE;AACrB,MAAA,MAAME,CAAC,GAAGF,EAAE,CAACd,KAAK,CAAC,SAAS,CAAC,CAAA;MAC7BW,GAAG,CAACK,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGA,CAAC,CAAC,CAAC,CAAC,CAAA;AAClB,KAAC,CAAC,CAAA;AACJ,IAAA,OAAOL,GAAG,CAAA;AACZ,GAAA;AAEA,EAAA,IAAIC,SAAS,CAACzJ,MAAM,GAAG,CAAC,EAAE;AACxB;AACA,IAAA,IAAIjB,KAAK,CAACC,OAAO,CAACsK,KAAK,CAAC,EAAE;AACxB,MAAA,KAAK,MAAMzK,IAAI,IAAIyK,KAAK,EAAE;QACxB,MAAMQ,KAAK,GAAGjL,IAAI,CAAA;AAClB2K,QAAAA,GAAG,CAAC3K,IAAI,CAAC,GAAG,IAAI,CAACkE,IAAI,CAACuG,KAAK,CAACS,gBAAgB,CAACD,KAAK,CAAC,CAAA;AACrD,OAAA;AACA,MAAA,OAAON,GAAG,CAAA;AACZ,KAAA;;AAEA;AACA,IAAA,IAAI,OAAOF,KAAK,KAAK,QAAQ,EAAE;MAC7B,OAAO,IAAI,CAACvG,IAAI,CAACuG,KAAK,CAACS,gBAAgB,CAACT,KAAK,CAAC,CAAA;AAChD,KAAA;;AAEA;AACA,IAAA,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;AAC7B,MAAA,KAAK,MAAMzK,IAAI,IAAIyK,KAAK,EAAE;AACxB;AACA,QAAA,IAAI,CAACvG,IAAI,CAACuG,KAAK,CAACU,WAAW,CACzBnL,IAAI,EACJyK,KAAK,CAACzK,IAAI,CAAC,IAAI,IAAI,IAAIyJ,OAAO,CAAC2B,IAAI,CAACX,KAAK,CAACzK,IAAI,CAAC,CAAC,GAAG,EAAE,GAAGyK,KAAK,CAACzK,IAAI,CACpE,CAAC,CAAA;AACH,OAAA;AACF,KAAA;AACF,GAAA;;AAEA;AACA,EAAA,IAAI4K,SAAS,CAACzJ,MAAM,KAAK,CAAC,EAAE;IAC1B,IAAI,CAAC+C,IAAI,CAACuG,KAAK,CAACU,WAAW,CACzBV,KAAK,EACLC,GAAG,IAAI,IAAI,IAAIjB,OAAO,CAAC2B,IAAI,CAACV,GAAG,CAAC,GAAG,EAAE,GAAGA,GAC1C,CAAC,CAAA;AACH,GAAA;AAEA,EAAA,OAAO,IAAI,CAAA;AACb,CAAA;;AAEA;AACO,SAASW,IAAIA,GAAG;AACrB,EAAA,OAAO,IAAI,CAACb,GAAG,CAAC,SAAS,EAAE,EAAE,CAAC,CAAA;AAChC,CAAA;;AAEA;AACO,SAASc,IAAIA,GAAG;AACrB,EAAA,OAAO,IAAI,CAACd,GAAG,CAAC,SAAS,EAAE,MAAM,CAAC,CAAA;AACpC,CAAA;;AAEA;AACO,SAASe,OAAOA,GAAG;AACxB,EAAA,OAAO,IAAI,CAACf,GAAG,CAAC,SAAS,CAAC,KAAK,MAAM,CAAA;AACvC,CAAA;AAEAzK,eAAe,CAAC,KAAK,EAAE;EACrByK,GAAG;EACHa,IAAI;EACJC,IAAI;AACJC,EAAAA,OAAAA;AACF,CAAC,CAAC;;AC3EF;AACO,SAAS3H,IAAIA,CAAC4H,CAAC,EAAEC,CAAC,EAAE9J,CAAC,EAAE;EAC5B,IAAI6J,CAAC,IAAI,IAAI,EAAE;AACb;AACA,IAAA,OAAO,IAAI,CAAC5H,IAAI,CACd9C,GAAG,CACDO,MAAM,CACJ,IAAI,CAAC6C,IAAI,CAACwH,UAAU,EACnBZ,EAAE,IAAKA,EAAE,CAACpH,QAAQ,CAACwG,OAAO,CAAC,OAAO,CAAC,KAAK,CAC3C,CAAC,EACAY,EAAE,IAAKA,EAAE,CAACpH,QAAQ,CAACtB,KAAK,CAAC,CAAC,CAC7B,CACF,CAAC,CAAA;AACH,GAAC,MAAM,IAAIoJ,CAAC,YAAYtL,KAAK,EAAE;IAC7B,MAAM0D,IAAI,GAAG,EAAE,CAAA;AACf,IAAA,KAAK,MAAMG,GAAG,IAAIyH,CAAC,EAAE;MACnB5H,IAAI,CAACG,GAAG,CAAC,GAAG,IAAI,CAACH,IAAI,CAACG,GAAG,CAAC,CAAA;AAC5B,KAAA;AACA,IAAA,OAAOH,IAAI,CAAA;AACb,GAAC,MAAM,IAAI,OAAO4H,CAAC,KAAK,QAAQ,EAAE;IAChC,KAAKC,CAAC,IAAID,CAAC,EAAE;MACX,IAAI,CAAC5H,IAAI,CAAC6H,CAAC,EAAED,CAAC,CAACC,CAAC,CAAC,CAAC,CAAA;AACpB,KAAA;AACF,GAAC,MAAM,IAAIb,SAAS,CAACzJ,MAAM,GAAG,CAAC,EAAE;IAC/B,IAAI;AACF,MAAA,OAAOiD,IAAI,CAACuH,KAAK,CAAC,IAAI,CAAC5D,IAAI,CAAC,OAAO,GAAGyD,CAAC,CAAC,CAAC,CAAA;KAC1C,CAAC,OAAOI,CAAC,EAAE;AACV,MAAA,OAAO,IAAI,CAAC7D,IAAI,CAAC,OAAO,GAAGyD,CAAC,CAAC,CAAA;AAC/B,KAAA;AACF,GAAC,MAAM;AACL,IAAA,IAAI,CAACzD,IAAI,CACP,OAAO,GAAGyD,CAAC,EACXC,CAAC,KAAK,IAAI,GACN,IAAI,GACJ9J,CAAC,KAAK,IAAI,IAAI,OAAO8J,CAAC,KAAK,QAAQ,IAAI,OAAOA,CAAC,KAAK,QAAQ,GAC1DA,CAAC,GACDrH,IAAI,CAACC,SAAS,CAACoH,CAAC,CACxB,CAAC,CAAA;AACH,GAAA;AAEA,EAAA,OAAO,IAAI,CAAA;AACb,CAAA;AAEA1L,eAAe,CAAC,KAAK,EAAE;AAAE6D,EAAAA,IAAAA;AAAK,CAAC,CAAC;;AC5ChC;AACO,SAASiI,QAAQA,CAACC,CAAC,EAAEL,CAAC,EAAE;AAC7B;AACA,EAAA,IAAI,OAAOb,SAAS,CAAC,CAAC,CAAC,KAAK,QAAQ,EAAE;AACpC,IAAA,KAAK,MAAM7G,GAAG,IAAI+H,CAAC,EAAE;MACnB,IAAI,CAACD,QAAQ,CAAC9H,GAAG,EAAE+H,CAAC,CAAC/H,GAAG,CAAC,CAAC,CAAA;AAC5B,KAAA;AACF,GAAC,MAAM,IAAI6G,SAAS,CAACzJ,MAAM,KAAK,CAAC,EAAE;AACjC;AACA,IAAA,OAAO,IAAI,CAAC4K,MAAM,EAAE,CAACD,CAAC,CAAC,CAAA;AACzB,GAAC,MAAM;AACL;IACA,IAAI,CAACC,MAAM,EAAE,CAACD,CAAC,CAAC,GAAGL,CAAC,CAAA;AACtB,GAAA;AAEA,EAAA,OAAO,IAAI,CAAA;AACb,CAAA;;AAEA;AACO,SAASO,MAAMA,GAAG;AACvB,EAAA,IAAIpB,SAAS,CAACzJ,MAAM,KAAK,CAAC,EAAE;AAC1B,IAAA,IAAI,CAAC8K,OAAO,GAAG,EAAE,CAAA;AACnB,GAAC,MAAM;AACL,IAAA,KAAK,IAAIhL,CAAC,GAAG2J,SAAS,CAACzJ,MAAM,GAAG,CAAC,EAAEF,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;MAC9C,OAAO,IAAI,CAAC8K,MAAM,EAAE,CAACnB,SAAS,CAAC3J,CAAC,CAAC,CAAC,CAAA;AACpC,KAAA;AACF,GAAA;AACA,EAAA,OAAO,IAAI,CAAA;AACb,CAAA;;AAEA;AACA;AACA;AACO,SAAS8K,MAAMA,GAAG;EACvB,OAAQ,IAAI,CAACE,OAAO,GAAG,IAAI,CAACA,OAAO,IAAI,EAAE,CAAA;AAC3C,CAAA;AAEAlM,eAAe,CAAC,KAAK,EAAE;EAAE8L,QAAQ;EAAEG,MAAM;AAAED,EAAAA,MAAAA;AAAO,CAAC,CAAC;;ACrCpD,SAASG,WAAWA,CAAChD,GAAG,EAAE;AACxB,EAAA,OAAOA,GAAG,CAAC/H,MAAM,KAAK,CAAC,GACnB,CACE,GAAG,EACH+H,GAAG,CAACiD,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,EACnBjD,GAAG,CAACiD,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,EACnBjD,GAAG,CAACiD,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,EACnBjD,GAAG,CAACiD,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,EACnBjD,GAAG,CAACiD,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,EACnBjD,GAAG,CAACiD,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,CACpB,CAAC/B,IAAI,CAAC,EAAE,CAAC,GACVlB,GAAG,CAAA;AACT,CAAA;AAEA,SAASkD,YAAYA,CAACC,SAAS,EAAE;AAC/B,EAAA,MAAMC,OAAO,GAAG9K,IAAI,CAAC+K,KAAK,CAACF,SAAS,CAAC,CAAA;AACrC,EAAA,MAAMG,OAAO,GAAGhL,IAAI,CAACiL,GAAG,CAAC,CAAC,EAAEjL,IAAI,CAACkL,GAAG,CAAC,GAAG,EAAEJ,OAAO,CAAC,CAAC,CAAA;AACnD,EAAA,MAAMpD,GAAG,GAAGsD,OAAO,CAACG,QAAQ,CAAC,EAAE,CAAC,CAAA;EAChC,OAAOzD,GAAG,CAAC/H,MAAM,KAAK,CAAC,GAAG,GAAG,GAAG+H,GAAG,GAAGA,GAAG,CAAA;AAC3C,CAAA;AAEA,SAAS0D,EAAEA,CAACC,MAAM,EAAEC,KAAK,EAAE;EACzB,KAAK,IAAI7L,CAAC,GAAG6L,KAAK,CAAC3L,MAAM,EAAEF,CAAC,EAAE,GAAI;IAChC,IAAI4L,MAAM,CAACC,KAAK,CAAC7L,CAAC,CAAC,CAAC,IAAI,IAAI,EAAE;AAC5B,MAAA,OAAO,KAAK,CAAA;AACd,KAAA;AACF,GAAA;AACA,EAAA,OAAO,IAAI,CAAA;AACb,CAAA;AAEA,SAAS8L,aAAaA,CAACvB,CAAC,EAAEwB,CAAC,EAAE;EAC3B,MAAMC,MAAM,GAAGL,EAAE,CAACpB,CAAC,EAAE,KAAK,CAAC,GACvB;IAAE0B,EAAE,EAAE1B,CAAC,CAAC7J,CAAC;IAAEwL,EAAE,EAAE3B,CAAC,CAACzJ,CAAC;IAAEqL,EAAE,EAAE5B,CAAC,CAACwB,CAAC;AAAEK,IAAAA,EAAE,EAAE,CAAC;AAAEP,IAAAA,KAAK,EAAE,KAAA;AAAM,GAAC,GAClDF,EAAE,CAACpB,CAAC,EAAE,KAAK,CAAC,GACV;IAAE0B,EAAE,EAAE1B,CAAC,CAACtI,CAAC;IAAEiK,EAAE,EAAE3B,CAAC,CAACrI,CAAC;IAAEiK,EAAE,EAAE5B,CAAC,CAAC8B,CAAC;AAAED,IAAAA,EAAE,EAAE,CAAC;AAAEP,IAAAA,KAAK,EAAE,KAAA;AAAM,GAAC,GAClDF,EAAE,CAACpB,CAAC,EAAE,KAAK,CAAC,GACV;IAAE0B,EAAE,EAAE1B,CAAC,CAAC+B,CAAC;IAAEJ,EAAE,EAAE3B,CAAC,CAAC3J,CAAC;IAAEuL,EAAE,EAAE5B,CAAC,CAACgC,CAAC;AAAEH,IAAAA,EAAE,EAAE,CAAC;AAAEP,IAAAA,KAAK,EAAE,KAAA;AAAM,GAAC,GAClDF,EAAE,CAACpB,CAAC,EAAE,KAAK,CAAC,GACV;IAAE0B,EAAE,EAAE1B,CAAC,CAACgC,CAAC;IAAEL,EAAE,EAAE3B,CAAC,CAACA,CAAC;IAAE4B,EAAE,EAAE5B,CAAC,CAACwB,CAAC;AAAEK,IAAAA,EAAE,EAAE,CAAC;AAAEP,IAAAA,KAAK,EAAE,KAAA;AAAM,GAAC,GAClDF,EAAE,CAACpB,CAAC,EAAE,KAAK,CAAC,GACV;IAAE0B,EAAE,EAAE1B,CAAC,CAACgC,CAAC;IAAEL,EAAE,EAAE3B,CAAC,CAAClB,CAAC;IAAE8C,EAAE,EAAE5B,CAAC,CAAC+B,CAAC;AAAEF,IAAAA,EAAE,EAAE,CAAC;AAAEP,IAAAA,KAAK,EAAE,KAAA;AAAM,GAAC,GAClDF,EAAE,CAACpB,CAAC,EAAE,MAAM,CAAC,GACX;IAAE0B,EAAE,EAAE1B,CAAC,CAAClB,CAAC;IAAE6C,EAAE,EAAE3B,CAAC,CAACvL,CAAC;IAAEmN,EAAE,EAAE5B,CAAC,CAACrI,CAAC;IAAEkK,EAAE,EAAE7B,CAAC,CAACM,CAAC;AAAEgB,IAAAA,KAAK,EAAE,MAAA;AAAO,GAAC,GACrD;AAAEI,IAAAA,EAAE,EAAE,CAAC;AAAEC,IAAAA,EAAE,EAAE,CAAC;AAAEC,IAAAA,EAAE,EAAE,CAAC;AAAEN,IAAAA,KAAK,EAAE,KAAA;GAAO,CAAA;AAEnDG,EAAAA,MAAM,CAACH,KAAK,GAAGE,CAAC,IAAIC,MAAM,CAACH,KAAK,CAAA;AAChC,EAAA,OAAOG,MAAM,CAAA;AACf,CAAA;AAEA,SAASQ,QAAQA,CAACX,KAAK,EAAE;EACvB,IAAIA,KAAK,KAAK,KAAK,IAAIA,KAAK,KAAK,KAAK,IAAIA,KAAK,KAAK,KAAK,EAAE;AACzD,IAAA,OAAO,IAAI,CAAA;AACb,GAAC,MAAM;AACL,IAAA,OAAO,KAAK,CAAA;AACd,GAAA;AACF,CAAA;AAEA,SAASY,QAAQA,CAACnF,CAAC,EAAEoF,CAAC,EAAE3C,CAAC,EAAE;AACzB,EAAA,IAAIA,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAI,CAAC,CAAA;AACjB,EAAA,IAAIA,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAI,CAAC,CAAA;AACjB,EAAA,IAAIA,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,OAAOzC,CAAC,GAAG,CAACoF,CAAC,GAAGpF,CAAC,IAAI,CAAC,GAAGyC,CAAC,CAAA;AACzC,EAAA,IAAIA,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,OAAO2C,CAAC,CAAA;EACvB,IAAI3C,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,OAAOzC,CAAC,GAAG,CAACoF,CAAC,GAAGpF,CAAC,KAAK,CAAC,GAAG,CAAC,GAAGyC,CAAC,CAAC,GAAG,CAAC,CAAA;AACnD,EAAA,OAAOzC,CAAC,CAAA;AACV,CAAA;AAEe,MAAMqF,KAAK,CAAC;EACzB/F,WAAWA,CAAC,GAAGgG,MAAM,EAAE;AACrB,IAAA,IAAI,CAACC,IAAI,CAAC,GAAGD,MAAM,CAAC,CAAA;AACtB,GAAA;;AAEA;EACA,OAAOE,OAAOA,CAACC,KAAK,EAAE;AACpB,IAAA,OACEA,KAAK,KAAKA,KAAK,YAAYJ,KAAK,IAAI,IAAI,CAACpE,KAAK,CAACwE,KAAK,CAAC,IAAI,IAAI,CAAC5C,IAAI,CAAC4C,KAAK,CAAC,CAAC,CAAA;AAE9E,GAAA;;AAEA;EACA,OAAOxE,KAAKA,CAACwE,KAAK,EAAE;IAClB,OACEA,KAAK,IACL,OAAOA,KAAK,CAACrM,CAAC,KAAK,QAAQ,IAC3B,OAAOqM,KAAK,CAACjM,CAAC,KAAK,QAAQ,IAC3B,OAAOiM,KAAK,CAAChB,CAAC,KAAK,QAAQ,CAAA;AAE/B,GAAA;;AAEA;AACF;AACA;AACE,EAAA,OAAOiB,MAAMA,CAACC,IAAI,GAAG,SAAS,EAAElD,CAAC,EAAE;AACjC;IACA,MAAM;MAAEiD,MAAM;MAAE1B,KAAK;MAAE4B,GAAG;AAAE1M,MAAAA,EAAE,EAAE2M,EAAAA;AAAG,KAAC,GAAG5M,IAAI,CAAA;;AAE3C;IACA,IAAI0M,IAAI,KAAK,SAAS,EAAE;MACtB,MAAMV,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,IAAIS,MAAM,EAAE,GAAG,EAAE,CAAA;MACnC,MAAM3D,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,IAAI2D,MAAM,EAAE,GAAG,EAAE,CAAA;AACnC,MAAA,MAAMV,CAAC,GAAG,GAAG,GAAGU,MAAM,EAAE,CAAA;AACxB,MAAA,MAAMD,KAAK,GAAG,IAAIJ,KAAK,CAACJ,CAAC,EAAElD,CAAC,EAAEiD,CAAC,EAAE,KAAK,CAAC,CAAA;AACvC,MAAA,OAAOS,KAAK,CAAA;AACd,KAAC,MAAM,IAAIE,IAAI,KAAK,MAAM,EAAE;MAC1BlD,CAAC,GAAGA,CAAC,IAAI,IAAI,GAAGiD,MAAM,EAAE,GAAGjD,CAAC,CAAA;MAC5B,MAAMrJ,CAAC,GAAG4K,KAAK,CAAC,EAAE,GAAG4B,GAAG,CAAE,CAAC,GAAGC,EAAE,GAAGpD,CAAC,GAAI,GAAG,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC,CAAA;MAC1D,MAAMjJ,CAAC,GAAGwK,KAAK,CAAC,EAAE,GAAG4B,GAAG,CAAE,CAAC,GAAGC,EAAE,GAAGpD,CAAC,GAAI,GAAG,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,CAAA;MACzD,MAAMgC,CAAC,GAAGT,KAAK,CAAC,GAAG,GAAG4B,GAAG,CAAE,CAAC,GAAGC,EAAE,GAAGpD,CAAC,GAAI,GAAG,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,CAAA;MAC1D,MAAMgD,KAAK,GAAG,IAAIJ,KAAK,CAACjM,CAAC,EAAEI,CAAC,EAAEiL,CAAC,CAAC,CAAA;AAChC,MAAA,OAAOgB,KAAK,CAAA;AACd,KAAC,MAAM,IAAIE,IAAI,KAAK,QAAQ,EAAE;MAC5B,MAAMV,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,IAAIS,MAAM,EAAE,GAAG,EAAE,CAAA;MACnC,MAAM3D,CAAC,GAAG,CAAC,EAAE,GAAG,CAAC,IAAI2D,MAAM,EAAE,GAAG,CAAC,CAAA;AACjC,MAAA,MAAMV,CAAC,GAAG,GAAG,GAAGU,MAAM,EAAE,CAAA;AACxB,MAAA,MAAMD,KAAK,GAAG,IAAIJ,KAAK,CAACJ,CAAC,EAAElD,CAAC,EAAEiD,CAAC,EAAE,KAAK,CAAC,CAAA;AACvC,MAAA,OAAOS,KAAK,CAAA;AACd,KAAC,MAAM,IAAIE,IAAI,KAAK,MAAM,EAAE;MAC1B,MAAMV,CAAC,GAAG,EAAE,GAAG,EAAE,GAAGS,MAAM,EAAE,CAAA;MAC5B,MAAM3D,CAAC,GAAG,CAAC,GAAG,GAAG,EAAE,IAAI2D,MAAM,EAAE,GAAG,EAAE,CAAA;AACpC,MAAA,MAAMV,CAAC,GAAG,GAAG,GAAGU,MAAM,EAAE,CAAA;AACxB,MAAA,MAAMD,KAAK,GAAG,IAAIJ,KAAK,CAACJ,CAAC,EAAElD,CAAC,EAAEiD,CAAC,EAAE,KAAK,CAAC,CAAA;AACvC,MAAA,OAAOS,KAAK,CAAA;AACd,KAAC,MAAM,IAAIE,IAAI,KAAK,KAAK,EAAE;AACzB,MAAA,MAAMvM,CAAC,GAAG,GAAG,GAAGsM,MAAM,EAAE,CAAA;AACxB,MAAA,MAAMlM,CAAC,GAAG,GAAG,GAAGkM,MAAM,EAAE,CAAA;AACxB,MAAA,MAAMjB,CAAC,GAAG,GAAG,GAAGiB,MAAM,EAAE,CAAA;MACxB,MAAMD,KAAK,GAAG,IAAIJ,KAAK,CAACjM,CAAC,EAAEI,CAAC,EAAEiL,CAAC,CAAC,CAAA;AAChC,MAAA,OAAOgB,KAAK,CAAA;AACd,KAAC,MAAM,IAAIE,IAAI,KAAK,KAAK,EAAE;AACzB,MAAA,MAAMV,CAAC,GAAG,GAAG,GAAGS,MAAM,EAAE,CAAA;MACxB,MAAMzC,CAAC,GAAG,GAAG,GAAGyC,MAAM,EAAE,GAAG,GAAG,CAAA;MAC9B,MAAMjB,CAAC,GAAG,GAAG,GAAGiB,MAAM,EAAE,GAAG,GAAG,CAAA;AAC9B,MAAA,MAAMD,KAAK,GAAG,IAAIJ,KAAK,CAACJ,CAAC,EAAEhC,CAAC,EAAEwB,CAAC,EAAE,KAAK,CAAC,CAAA;AACvC,MAAA,OAAOgB,KAAK,CAAA;AACd,KAAC,MAAM,IAAIE,IAAI,KAAK,MAAM,EAAE;AAC1B,MAAA,MAAMG,IAAI,GAAG,GAAG,GAAGJ,MAAM,EAAE,CAAA;MAC3B,MAAMD,KAAK,GAAG,IAAIJ,KAAK,CAACS,IAAI,EAAEA,IAAI,EAAEA,IAAI,CAAC,CAAA;AACzC,MAAA,OAAOL,KAAK,CAAA;AACd,KAAC,MAAM;AACL,MAAA,MAAM,IAAIM,KAAK,CAAC,+BAA+B,CAAC,CAAA;AAClD,KAAA;AACF,GAAA;;AAEA;EACA,OAAOlD,IAAIA,CAAC4C,KAAK,EAAE;AACjB,IAAA,OAAO,OAAOA,KAAK,KAAK,QAAQ,KAAKzE,KAAK,CAAC6B,IAAI,CAAC4C,KAAK,CAAC,IAAIxE,KAAK,CAAC4B,IAAI,CAAC4C,KAAK,CAAC,CAAC,CAAA;AAC9E,GAAA;AAEAO,EAAAA,IAAIA,GAAG;AACL;IACA,MAAM;MAAErB,EAAE;MAAEC,EAAE;AAAEC,MAAAA,EAAAA;AAAG,KAAC,GAAG,IAAI,CAACjE,GAAG,EAAE,CAAA;IACjC,MAAM,CAACxH,CAAC,EAAEI,CAAC,EAAEiL,CAAC,CAAC,GAAG,CAACE,EAAE,EAAEC,EAAE,EAAEC,EAAE,CAAC,CAACtM,GAAG,CAAE2K,CAAC,IAAKA,CAAC,GAAG,GAAG,CAAC,CAAA;;AAElD;AACA,IAAA,MAAMK,CAAC,GAAGtK,IAAI,CAACkL,GAAG,CAAC,CAAC,GAAG/K,CAAC,EAAE,CAAC,GAAGI,CAAC,EAAE,CAAC,GAAGiL,CAAC,CAAC,CAAA;IAEvC,IAAIlB,CAAC,KAAK,CAAC,EAAE;AACX;AACA,MAAA,OAAO,IAAI8B,KAAK,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,MAAM,CAAC,CAAA;AACtC,KAAA;AAEA,IAAA,MAAMtD,CAAC,GAAG,CAAC,CAAC,GAAG3I,CAAC,GAAGmK,CAAC,KAAK,CAAC,GAAGA,CAAC,CAAC,CAAA;AAC/B,IAAA,MAAM7L,CAAC,GAAG,CAAC,CAAC,GAAG8B,CAAC,GAAG+J,CAAC,KAAK,CAAC,GAAGA,CAAC,CAAC,CAAA;AAC/B,IAAA,MAAM3I,CAAC,GAAG,CAAC,CAAC,GAAG6J,CAAC,GAAGlB,CAAC,KAAK,CAAC,GAAGA,CAAC,CAAC,CAAA;;AAE/B;AACA,IAAA,MAAMkC,KAAK,GAAG,IAAIJ,KAAK,CAACtD,CAAC,EAAErK,CAAC,EAAEkD,CAAC,EAAE2I,CAAC,EAAE,MAAM,CAAC,CAAA;AAC3C,IAAA,OAAOkC,KAAK,CAAA;AACd,GAAA;AAEAQ,EAAAA,GAAGA,GAAG;AACJ;IACA,MAAM;MAAEtB,EAAE;MAAEC,EAAE;AAAEC,MAAAA,EAAAA;AAAG,KAAC,GAAG,IAAI,CAACjE,GAAG,EAAE,CAAA;IACjC,MAAM,CAACxH,CAAC,EAAEI,CAAC,EAAEiL,CAAC,CAAC,GAAG,CAACE,EAAE,EAAEC,EAAE,EAAEC,EAAE,CAAC,CAACtM,GAAG,CAAE2K,CAAC,IAAKA,CAAC,GAAG,GAAG,CAAC,CAAA;;AAElD;IACA,MAAMgB,GAAG,GAAGjL,IAAI,CAACiL,GAAG,CAAC9K,CAAC,EAAEI,CAAC,EAAEiL,CAAC,CAAC,CAAA;IAC7B,MAAMN,GAAG,GAAGlL,IAAI,CAACkL,GAAG,CAAC/K,CAAC,EAAEI,CAAC,EAAEiL,CAAC,CAAC,CAAA;AAC7B,IAAA,MAAMQ,CAAC,GAAG,CAACf,GAAG,GAAGC,GAAG,IAAI,CAAC,CAAA;;AAEzB;AACA,IAAA,MAAM+B,MAAM,GAAGhC,GAAG,KAAKC,GAAG,CAAA;;AAE1B;AACA,IAAA,MAAMgC,KAAK,GAAGjC,GAAG,GAAGC,GAAG,CAAA;IACvB,MAAM7K,CAAC,GAAG4M,MAAM,GACZ,CAAC,GACDjB,CAAC,GAAG,GAAG,GACLkB,KAAK,IAAI,CAAC,GAAGjC,GAAG,GAAGC,GAAG,CAAC,GACvBgC,KAAK,IAAIjC,GAAG,GAAGC,GAAG,CAAC,CAAA;AACzB,IAAA,MAAMa,CAAC,GAAGkB,MAAM,GACZ,CAAC,GACDhC,GAAG,KAAK9K,CAAC,GACP,CAAC,CAACI,CAAC,GAAGiL,CAAC,IAAI0B,KAAK,IAAI3M,CAAC,GAAGiL,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,GACvCP,GAAG,KAAK1K,CAAC,GACP,CAAC,CAACiL,CAAC,GAAGrL,CAAC,IAAI+M,KAAK,GAAG,CAAC,IAAI,CAAC,GACzBjC,GAAG,KAAKO,CAAC,GACP,CAAC,CAACrL,CAAC,GAAGI,CAAC,IAAI2M,KAAK,GAAG,CAAC,IAAI,CAAC,GACzB,CAAC,CAAA;;AAEX;AACA,IAAA,MAAMV,KAAK,GAAG,IAAIJ,KAAK,CAAC,GAAG,GAAGL,CAAC,EAAE,GAAG,GAAG1L,CAAC,EAAE,GAAG,GAAG2L,CAAC,EAAE,KAAK,CAAC,CAAA;AACzD,IAAA,OAAOQ,KAAK,CAAA;AACd,GAAA;EAEAF,IAAIA,CAACtC,CAAC,GAAG,CAAC,EAAEwB,CAAC,GAAG,CAAC,EAAE1C,CAAC,GAAG,CAAC,EAAE/I,CAAC,GAAG,CAAC,EAAEuL,KAAK,GAAG,KAAK,EAAE;AAC9C;AACAtB,IAAAA,CAAC,GAAG,CAACA,CAAC,GAAG,CAAC,GAAGA,CAAC,CAAA;;AAEd;IACA,IAAI,IAAI,CAACsB,KAAK,EAAE;AACd,MAAA,KAAK,MAAMT,SAAS,IAAI,IAAI,CAACS,KAAK,EAAE;QAClC,OAAO,IAAI,CAAC,IAAI,CAACA,KAAK,CAACT,SAAS,CAAC,CAAC,CAAA;AACpC,OAAA;AACF,KAAA;AAEA,IAAA,IAAI,OAAOb,CAAC,KAAK,QAAQ,EAAE;AACzB;MACAsB,KAAK,GAAG,OAAOvL,CAAC,KAAK,QAAQ,GAAGA,CAAC,GAAGuL,KAAK,CAAA;MACzCvL,CAAC,GAAG,OAAOA,CAAC,KAAK,QAAQ,GAAG,CAAC,GAAGA,CAAC,CAAA;;AAEjC;AACAjB,MAAAA,MAAM,CAACE,MAAM,CAAC,IAAI,EAAE;AAAE0M,QAAAA,EAAE,EAAE1B,CAAC;AAAE2B,QAAAA,EAAE,EAAEH,CAAC;AAAEI,QAAAA,EAAE,EAAE9C,CAAC;AAAE+C,QAAAA,EAAE,EAAE9L,CAAC;AAAEuL,QAAAA,KAAAA;AAAM,OAAC,CAAC,CAAA;AAC1D;AACF,KAAC,MAAM,IAAItB,CAAC,YAAYtL,KAAK,EAAE;MAC7B,IAAI,CAAC4M,KAAK,GAAGE,CAAC,KAAK,OAAOxB,CAAC,CAAC,CAAC,CAAC,KAAK,QAAQ,GAAGA,CAAC,CAAC,CAAC,CAAC,GAAGA,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,CAAA;AACnElL,MAAAA,MAAM,CAACE,MAAM,CAAC,IAAI,EAAE;AAAE0M,QAAAA,EAAE,EAAE1B,CAAC,CAAC,CAAC,CAAC;AAAE2B,QAAAA,EAAE,EAAE3B,CAAC,CAAC,CAAC,CAAC;AAAE4B,QAAAA,EAAE,EAAE5B,CAAC,CAAC,CAAC,CAAC;AAAE6B,QAAAA,EAAE,EAAE7B,CAAC,CAAC,CAAC,CAAC,IAAI,CAAA;AAAE,OAAC,CAAC,CAAA;AACtE,KAAC,MAAM,IAAIA,CAAC,YAAYlL,MAAM,EAAE;AAC9B;AACA,MAAA,MAAMqO,MAAM,GAAG5B,aAAa,CAACvB,CAAC,EAAEwB,CAAC,CAAC,CAAA;AAClC1M,MAAAA,MAAM,CAACE,MAAM,CAAC,IAAI,EAAEmO,MAAM,CAAC,CAAA;AAC7B,KAAC,MAAM,IAAI,OAAOnD,CAAC,KAAK,QAAQ,EAAE;AAChC,MAAA,IAAIhC,KAAK,CAAC4B,IAAI,CAACI,CAAC,CAAC,EAAE;QACjB,MAAMoD,YAAY,GAAGpD,CAAC,CAAC1J,OAAO,CAACwH,UAAU,EAAE,EAAE,CAAC,CAAA;AAC9C,QAAA,MAAM,CAAC4D,EAAE,EAAEC,EAAE,EAAEC,EAAE,CAAC,GAAGjE,GAAG,CACrB0F,IAAI,CAACD,YAAY,CAAC,CAClBxM,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CACXtB,GAAG,CAAE2K,CAAC,IAAKqD,QAAQ,CAACrD,CAAC,CAAC,CAAC,CAAA;AAC1BnL,QAAAA,MAAM,CAACE,MAAM,CAAC,IAAI,EAAE;UAAE0M,EAAE;UAAEC,EAAE;UAAEC,EAAE;AAAEC,UAAAA,EAAE,EAAE,CAAC;AAAEP,UAAAA,KAAK,EAAE,KAAA;AAAM,SAAC,CAAC,CAAA;OACzD,MAAM,IAAIvD,KAAK,CAAC6B,IAAI,CAACI,CAAC,CAAC,EAAE;QACxB,MAAMuD,QAAQ,GAAItD,CAAC,IAAKqD,QAAQ,CAACrD,CAAC,EAAE,EAAE,CAAC,CAAA;QACvC,MAAM,GAAGyB,EAAE,EAAEC,EAAE,EAAEC,EAAE,CAAC,GAAGlE,GAAG,CAAC2F,IAAI,CAAC3C,WAAW,CAACV,CAAC,CAAC,CAAC,CAAC1K,GAAG,CAACiO,QAAQ,CAAC,CAAA;AAC7DzO,QAAAA,MAAM,CAACE,MAAM,CAAC,IAAI,EAAE;UAAE0M,EAAE;UAAEC,EAAE;UAAEC,EAAE;AAAEC,UAAAA,EAAE,EAAE,CAAC;AAAEP,UAAAA,KAAK,EAAE,KAAA;AAAM,SAAC,CAAC,CAAA;AAC1D,OAAC,MAAM,MAAMwB,KAAK,CAAC,kDAAkD,CAAC,CAAA;AACxE,KAAA;;AAEA;IACA,MAAM;MAAEpB,EAAE;MAAEC,EAAE;MAAEC,EAAE;AAAEC,MAAAA,EAAAA;AAAG,KAAC,GAAG,IAAI,CAAA;AAC/B,IAAA,MAAM2B,UAAU,GACd,IAAI,CAAClC,KAAK,KAAK,KAAK,GAChB;AAAEnL,MAAAA,CAAC,EAAEuL,EAAE;AAAEnL,MAAAA,CAAC,EAAEoL,EAAE;AAAEH,MAAAA,CAAC,EAAEI,EAAAA;AAAG,KAAC,GACvB,IAAI,CAACN,KAAK,KAAK,KAAK,GAClB;AAAE5J,MAAAA,CAAC,EAAEgK,EAAE;AAAE/J,MAAAA,CAAC,EAAEgK,EAAE;AAAEG,MAAAA,CAAC,EAAEF,EAAAA;AAAG,KAAC,GACvB,IAAI,CAACN,KAAK,KAAK,KAAK,GAClB;AAAES,MAAAA,CAAC,EAAEL,EAAE;AAAErL,MAAAA,CAAC,EAAEsL,EAAE;AAAEK,MAAAA,CAAC,EAAEJ,EAAAA;AAAG,KAAC,GACvB,IAAI,CAACN,KAAK,KAAK,KAAK,GAClB;AAAEU,MAAAA,CAAC,EAAEN,EAAE;AAAE1B,MAAAA,CAAC,EAAE2B,EAAE;AAAEH,MAAAA,CAAC,EAAEI,EAAAA;AAAG,KAAC,GACvB,IAAI,CAACN,KAAK,KAAK,KAAK,GAClB;AAAEU,MAAAA,CAAC,EAAEN,EAAE;AAAE5C,MAAAA,CAAC,EAAE6C,EAAE;AAAEI,MAAAA,CAAC,EAAEH,EAAAA;AAAG,KAAC,GACvB,IAAI,CAACN,KAAK,KAAK,MAAM,GACnB;AAAExC,MAAAA,CAAC,EAAE4C,EAAE;AAAEjN,MAAAA,CAAC,EAAEkN,EAAE;AAAEhK,MAAAA,CAAC,EAAEiK,EAAE;AAAEtB,MAAAA,CAAC,EAAEuB,EAAAA;KAAI,GAC9B,EAAE,CAAA;AAClB/M,IAAAA,MAAM,CAACE,MAAM,CAAC,IAAI,EAAEwO,UAAU,CAAC,CAAA;AACjC,GAAA;AAEAC,EAAAA,GAAGA,GAAG;AACJ;IACA,MAAM;MAAE/L,CAAC;MAAEC,CAAC;AAAEmK,MAAAA,CAAAA;AAAE,KAAC,GAAG,IAAI,CAAC4B,GAAG,EAAE,CAAA;;AAE9B;AACA,IAAA,MAAM1B,CAAC,GAAG,GAAG,GAAGrK,CAAC,GAAG,EAAE,CAAA;AACtB,IAAA,MAAMqI,CAAC,GAAG,GAAG,IAAItI,CAAC,GAAGC,CAAC,CAAC,CAAA;AACvB,IAAA,MAAM6J,CAAC,GAAG,GAAG,IAAI7J,CAAC,GAAGmK,CAAC,CAAC,CAAA;;AAEvB;AACA,IAAA,MAAMU,KAAK,GAAG,IAAIJ,KAAK,CAACJ,CAAC,EAAEhC,CAAC,EAAEwB,CAAC,EAAE,KAAK,CAAC,CAAA;AACvC,IAAA,OAAOgB,KAAK,CAAA;AACd,GAAA;AAEAmB,EAAAA,GAAGA,GAAG;AACJ;IACA,MAAM;MAAE3B,CAAC;MAAEhC,CAAC;AAAEwB,MAAAA,CAAAA;AAAE,KAAC,GAAG,IAAI,CAACiC,GAAG,EAAE,CAAA;;AAE9B;AACA,IAAA,MAAM3E,CAAC,GAAG9I,IAAI,CAAC4N,IAAI,CAAC5D,CAAC,IAAI,CAAC,GAAGwB,CAAC,IAAI,CAAC,CAAC,CAAA;AACpC,IAAA,IAAIO,CAAC,GAAI,GAAG,GAAG/L,IAAI,CAAC6N,KAAK,CAACrC,CAAC,EAAExB,CAAC,CAAC,GAAIhK,IAAI,CAACC,EAAE,CAAA;IAC1C,IAAI8L,CAAC,GAAG,CAAC,EAAE;MACTA,CAAC,IAAI,CAAC,CAAC,CAAA;MACPA,CAAC,GAAG,GAAG,GAAGA,CAAC,CAAA;AACb,KAAA;;AAEA;AACA,IAAA,MAAMS,KAAK,GAAG,IAAIJ,KAAK,CAACJ,CAAC,EAAElD,CAAC,EAAEiD,CAAC,EAAE,KAAK,CAAC,CAAA;AACvC,IAAA,OAAOS,KAAK,CAAA;AACd,GAAA;AACA;AACF;AACA;;AAEE7E,EAAAA,GAAGA,GAAG;AACJ,IAAA,IAAI,IAAI,CAAC2D,KAAK,KAAK,KAAK,EAAE;AACxB,MAAA,OAAO,IAAI,CAAA;KACZ,MAAM,IAAIW,QAAQ,CAAC,IAAI,CAACX,KAAK,CAAC,EAAE;AAC/B;MACA,IAAI;QAAE5J,CAAC;QAAEC,CAAC;AAAEmK,QAAAA,CAAAA;AAAE,OAAC,GAAG,IAAI,CAAA;MACtB,IAAI,IAAI,CAACR,KAAK,KAAK,KAAK,IAAI,IAAI,CAACA,KAAK,KAAK,KAAK,EAAE;AAChD;QACA,IAAI;UAAEU,CAAC;UAAEhC,CAAC;AAAEwB,UAAAA,CAAAA;AAAE,SAAC,GAAG,IAAI,CAAA;AACtB,QAAA,IAAI,IAAI,CAACF,KAAK,KAAK,KAAK,EAAE;UACxB,MAAM;YAAExC,CAAC;AAAEiD,YAAAA,CAAAA;AAAE,WAAC,GAAG,IAAI,CAAA;AACrB,UAAA,MAAM+B,IAAI,GAAG9N,IAAI,CAACC,EAAE,GAAG,GAAG,CAAA;UAC1B+J,CAAC,GAAGlB,CAAC,GAAG9I,IAAI,CAAC+N,GAAG,CAACD,IAAI,GAAG/B,CAAC,CAAC,CAAA;UAC1BP,CAAC,GAAG1C,CAAC,GAAG9I,IAAI,CAAC2M,GAAG,CAACmB,IAAI,GAAG/B,CAAC,CAAC,CAAA;AAC5B,SAAA;;AAEA;AACA,QAAA,MAAMiC,EAAE,GAAG,CAAChC,CAAC,GAAG,EAAE,IAAI,GAAG,CAAA;AACzB,QAAA,MAAMiC,EAAE,GAAGjE,CAAC,GAAG,GAAG,GAAGgE,EAAE,CAAA;AACvB,QAAA,MAAME,EAAE,GAAGF,EAAE,GAAGxC,CAAC,GAAG,GAAG,CAAA;;AAEvB;AACA,QAAA,MAAM2C,EAAE,GAAG,EAAE,GAAG,GAAG,CAAA;QACnB,MAAMC,EAAE,GAAG,QAAQ,CAAA;QACnB,MAAMC,EAAE,GAAG,KAAK,CAAA;AAChB3M,QAAAA,CAAC,GAAG,OAAO,IAAIuM,EAAE,IAAI,CAAC,GAAGG,EAAE,GAAGH,EAAE,IAAI,CAAC,GAAG,CAACA,EAAE,GAAGE,EAAE,IAAIE,EAAE,CAAC,CAAA;AACvD1M,QAAAA,CAAC,GAAG,GAAG,IAAIqM,EAAE,IAAI,CAAC,GAAGI,EAAE,GAAGJ,EAAE,IAAI,CAAC,GAAG,CAACA,EAAE,GAAGG,EAAE,IAAIE,EAAE,CAAC,CAAA;AACnDvC,QAAAA,CAAC,GAAG,OAAO,IAAIoC,EAAE,IAAI,CAAC,GAAGE,EAAE,GAAGF,EAAE,IAAI,CAAC,GAAG,CAACA,EAAE,GAAGC,EAAE,IAAIE,EAAE,CAAC,CAAA;AACzD,OAAA;;AAEA;AACA,MAAA,MAAMC,EAAE,GAAG5M,CAAC,GAAG,MAAM,GAAGC,CAAC,GAAG,CAAC,MAAM,GAAGmK,CAAC,GAAG,CAAC,MAAM,CAAA;AACjD,MAAA,MAAMyC,EAAE,GAAG7M,CAAC,GAAG,CAAC,MAAM,GAAGC,CAAC,GAAG,MAAM,GAAGmK,CAAC,GAAG,MAAM,CAAA;AAChD,MAAA,MAAM0C,EAAE,GAAG9M,CAAC,GAAG,MAAM,GAAGC,CAAC,GAAG,CAAC,KAAK,GAAGmK,CAAC,GAAG,KAAK,CAAA;;AAE9C;AACA,MAAA,MAAM2C,GAAG,GAAGzO,IAAI,CAACyO,GAAG,CAAA;MACpB,MAAMC,EAAE,GAAG,SAAS,CAAA;MACpB,MAAMvO,CAAC,GAAGmO,EAAE,GAAGI,EAAE,GAAG,KAAK,GAAGD,GAAG,CAACH,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,GAAG,KAAK,GAAG,KAAK,GAAGA,EAAE,CAAA;MACjE,MAAM/N,CAAC,GAAGgO,EAAE,GAAGG,EAAE,GAAG,KAAK,GAAGD,GAAG,CAACF,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,GAAG,KAAK,GAAG,KAAK,GAAGA,EAAE,CAAA;MACjE,MAAM/C,CAAC,GAAGgD,EAAE,GAAGE,EAAE,GAAG,KAAK,GAAGD,GAAG,CAACD,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,GAAG,KAAK,GAAG,KAAK,GAAGA,EAAE,CAAA;;AAEjE;AACA,MAAA,MAAMhC,KAAK,GAAG,IAAIJ,KAAK,CAAC,GAAG,GAAGjM,CAAC,EAAE,GAAG,GAAGI,CAAC,EAAE,GAAG,GAAGiL,CAAC,CAAC,CAAA;AAClD,MAAA,OAAOgB,KAAK,CAAA;AACd,KAAC,MAAM,IAAI,IAAI,CAAClB,KAAK,KAAK,KAAK,EAAE;AAC/B;AACA;MACA,IAAI;QAAES,CAAC;QAAE1L,CAAC;AAAE2L,QAAAA,CAAAA;AAAE,OAAC,GAAG,IAAI,CAAA;AACtBD,MAAAA,CAAC,IAAI,GAAG,CAAA;AACR1L,MAAAA,CAAC,IAAI,GAAG,CAAA;AACR2L,MAAAA,CAAC,IAAI,GAAG,CAAA;;AAER;MACA,IAAI3L,CAAC,KAAK,CAAC,EAAE;AACX2L,QAAAA,CAAC,IAAI,GAAG,CAAA;QACR,MAAMQ,KAAK,GAAG,IAAIJ,KAAK,CAACJ,CAAC,EAAEA,CAAC,EAAEA,CAAC,CAAC,CAAA;AAChC,QAAA,OAAOQ,KAAK,CAAA;AACd,OAAA;;AAEA;AACA,MAAA,MAAML,CAAC,GAAGH,CAAC,GAAG,GAAG,GAAGA,CAAC,IAAI,CAAC,GAAG3L,CAAC,CAAC,GAAG2L,CAAC,GAAG3L,CAAC,GAAG2L,CAAC,GAAG3L,CAAC,CAAA;AAC/C,MAAA,MAAM0G,CAAC,GAAG,CAAC,GAAGiF,CAAC,GAAGG,CAAC,CAAA;;AAEnB;AACA,MAAA,MAAMhM,CAAC,GAAG,GAAG,GAAG+L,QAAQ,CAACnF,CAAC,EAAEoF,CAAC,EAAEJ,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAA;MACzC,MAAMxL,CAAC,GAAG,GAAG,GAAG2L,QAAQ,CAACnF,CAAC,EAAEoF,CAAC,EAAEJ,CAAC,CAAC,CAAA;AACjC,MAAA,MAAMP,CAAC,GAAG,GAAG,GAAGU,QAAQ,CAACnF,CAAC,EAAEoF,CAAC,EAAEJ,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAA;;AAEzC;MACA,MAAMS,KAAK,GAAG,IAAIJ,KAAK,CAACjM,CAAC,EAAEI,CAAC,EAAEiL,CAAC,CAAC,CAAA;AAChC,MAAA,OAAOgB,KAAK,CAAA;AACd,KAAC,MAAM,IAAI,IAAI,CAAClB,KAAK,KAAK,MAAM,EAAE;AAChC;AACA;MACA,MAAM;QAAExC,CAAC;QAAErK,CAAC;QAAEkD,CAAC;AAAE2I,QAAAA,CAAAA;AAAE,OAAC,GAAG,IAAI,CAAA;;AAE3B;MACA,MAAMnK,CAAC,GAAG,GAAG,IAAI,CAAC,GAAGH,IAAI,CAACkL,GAAG,CAAC,CAAC,EAAEpC,CAAC,IAAI,CAAC,GAAGwB,CAAC,CAAC,GAAGA,CAAC,CAAC,CAAC,CAAA;MAClD,MAAM/J,CAAC,GAAG,GAAG,IAAI,CAAC,GAAGP,IAAI,CAACkL,GAAG,CAAC,CAAC,EAAEzM,CAAC,IAAI,CAAC,GAAG6L,CAAC,CAAC,GAAGA,CAAC,CAAC,CAAC,CAAA;MAClD,MAAMkB,CAAC,GAAG,GAAG,IAAI,CAAC,GAAGxL,IAAI,CAACkL,GAAG,CAAC,CAAC,EAAEvJ,CAAC,IAAI,CAAC,GAAG2I,CAAC,CAAC,GAAGA,CAAC,CAAC,CAAC,CAAA;;AAElD;MACA,MAAMkC,KAAK,GAAG,IAAIJ,KAAK,CAACjM,CAAC,EAAEI,CAAC,EAAEiL,CAAC,CAAC,CAAA;AAChC,MAAA,OAAOgB,KAAK,CAAA;AACd,KAAC,MAAM;AACL,MAAA,OAAO,IAAI,CAAA;AACb,KAAA;AACF,GAAA;AAEAmC,EAAAA,OAAOA,GAAG;IACR,MAAM;MAAEjD,EAAE;MAAEC,EAAE;MAAEC,EAAE;MAAEC,EAAE;AAAEP,MAAAA,KAAAA;AAAM,KAAC,GAAG,IAAI,CAAA;IACtC,OAAO,CAACI,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEP,KAAK,CAAC,CAAA;AAChC,GAAA;AAEAsD,EAAAA,KAAKA,GAAG;AACN,IAAA,MAAM,CAACzO,CAAC,EAAEI,CAAC,EAAEiL,CAAC,CAAC,GAAG,IAAI,CAACqD,QAAQ,EAAE,CAACvP,GAAG,CAACsL,YAAY,CAAC,CAAA;AACnD,IAAA,OAAO,IAAIzK,CAAC,CAAA,EAAGI,CAAC,CAAA,EAAGiL,CAAC,CAAE,CAAA,CAAA;AACxB,GAAA;AAEAsD,EAAAA,KAAKA,GAAG;AACN,IAAA,MAAM,CAACC,EAAE,EAAEC,EAAE,EAAEC,EAAE,CAAC,GAAG,IAAI,CAACJ,QAAQ,EAAE,CAAA;IACpC,MAAMK,MAAM,GAAG,CAAOH,IAAAA,EAAAA,EAAE,IAAIC,EAAE,CAAA,CAAA,EAAIC,EAAE,CAAG,CAAA,CAAA,CAAA;AACvC,IAAA,OAAOC,MAAM,CAAA;AACf,GAAA;AAEA/D,EAAAA,QAAQA,GAAG;AACT,IAAA,OAAO,IAAI,CAACyD,KAAK,EAAE,CAAA;AACrB,GAAA;AAEAlB,EAAAA,GAAGA,GAAG;AACJ;IACA,MAAM;AAAEhC,MAAAA,EAAE,EAAEyD,IAAI;AAAExD,MAAAA,EAAE,EAAEyD,IAAI;AAAExD,MAAAA,EAAE,EAAEyD,IAAAA;AAAK,KAAC,GAAG,IAAI,CAAC1H,GAAG,EAAE,CAAA;IACnD,MAAM,CAACxH,CAAC,EAAEI,CAAC,EAAEiL,CAAC,CAAC,GAAG,CAAC2D,IAAI,EAAEC,IAAI,EAAEC,IAAI,CAAC,CAAC/P,GAAG,CAAE2K,CAAC,IAAKA,CAAC,GAAG,GAAG,CAAC,CAAA;;AAExD;IACA,MAAMqF,EAAE,GAAGnP,CAAC,GAAG,OAAO,GAAGH,IAAI,CAACyO,GAAG,CAAC,CAACtO,CAAC,GAAG,KAAK,IAAI,KAAK,EAAE,GAAG,CAAC,GAAGA,CAAC,GAAG,KAAK,CAAA;IACvE,MAAMoP,EAAE,GAAGhP,CAAC,GAAG,OAAO,GAAGP,IAAI,CAACyO,GAAG,CAAC,CAAClO,CAAC,GAAG,KAAK,IAAI,KAAK,EAAE,GAAG,CAAC,GAAGA,CAAC,GAAG,KAAK,CAAA;IACvE,MAAMiP,EAAE,GAAGhE,CAAC,GAAG,OAAO,GAAGxL,IAAI,CAACyO,GAAG,CAAC,CAACjD,CAAC,GAAG,KAAK,IAAI,KAAK,EAAE,GAAG,CAAC,GAAGA,CAAC,GAAG,KAAK,CAAA;;AAEvE;AACA,IAAA,MAAMiE,EAAE,GAAG,CAACH,EAAE,GAAG,MAAM,GAAGC,EAAE,GAAG,MAAM,GAAGC,EAAE,GAAG,MAAM,IAAI,OAAO,CAAA;AAC9D,IAAA,MAAME,EAAE,GAAG,CAACJ,EAAE,GAAG,MAAM,GAAGC,EAAE,GAAG,MAAM,GAAGC,EAAE,GAAG,MAAM,IAAI,GAAG,CAAA;AAC1D,IAAA,MAAMG,EAAE,GAAG,CAACL,EAAE,GAAG,MAAM,GAAGC,EAAE,GAAG,MAAM,GAAGC,EAAE,GAAG,MAAM,IAAI,OAAO,CAAA;;AAE9D;IACA,MAAM9N,CAAC,GAAG+N,EAAE,GAAG,QAAQ,GAAGzP,IAAI,CAACyO,GAAG,CAACgB,EAAE,EAAE,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK,GAAGA,EAAE,GAAG,EAAE,GAAG,GAAG,CAAA;IACrE,MAAM9N,CAAC,GAAG+N,EAAE,GAAG,QAAQ,GAAG1P,IAAI,CAACyO,GAAG,CAACiB,EAAE,EAAE,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK,GAAGA,EAAE,GAAG,EAAE,GAAG,GAAG,CAAA;IACrE,MAAM5D,CAAC,GAAG6D,EAAE,GAAG,QAAQ,GAAG3P,IAAI,CAACyO,GAAG,CAACkB,EAAE,EAAE,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK,GAAGA,EAAE,GAAG,EAAE,GAAG,GAAG,CAAA;;AAErE;AACA,IAAA,MAAMnD,KAAK,GAAG,IAAIJ,KAAK,CAAC1K,CAAC,EAAEC,CAAC,EAAEmK,CAAC,EAAE,KAAK,CAAC,CAAA;AACvC,IAAA,OAAOU,KAAK,CAAA;AACd,GAAA;;AAEA;AACF;AACA;;AAEEqC,EAAAA,QAAQA,GAAG;IACT,MAAM;MAAEnD,EAAE;MAAEC,EAAE;AAAEC,MAAAA,EAAAA;AAAG,KAAC,GAAG,IAAI,CAACjE,GAAG,EAAE,CAAA;IACjC,MAAM;MAAEsD,GAAG;MAAEC,GAAG;AAAEH,MAAAA,KAAAA;AAAM,KAAC,GAAG/K,IAAI,CAAA;AAChC,IAAA,MAAM4P,MAAM,GAAI3F,CAAC,IAAKgB,GAAG,CAAC,CAAC,EAAEC,GAAG,CAACH,KAAK,CAACd,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAA;IAChD,OAAO,CAACyB,EAAE,EAAEC,EAAE,EAAEC,EAAE,CAAC,CAACtM,GAAG,CAACsQ,MAAM,CAAC,CAAA;AACjC,GAAA;;AAEA;AACF;AACA;AACA;;AC/be,MAAMC,KAAK,CAAC;AACzB;EACAxJ,WAAWA,CAAC,GAAGD,IAAI,EAAE;AACnB,IAAA,IAAI,CAACkG,IAAI,CAAC,GAAGlG,IAAI,CAAC,CAAA;AACpB,GAAA;;AAEA;AACA0J,EAAAA,KAAKA,GAAG;AACN,IAAA,OAAO,IAAID,KAAK,CAAC,IAAI,CAAC,CAAA;AACxB,GAAA;AAEAvD,EAAAA,IAAIA,CAAC5K,CAAC,EAAEC,CAAC,EAAE;AACT,IAAA,MAAMoO,IAAI,GAAG;AAAErO,MAAAA,CAAC,EAAE,CAAC;AAAEC,MAAAA,CAAC,EAAE,CAAA;KAAG,CAAA;;AAE3B;IACA,MAAMqO,MAAM,GAAGtR,KAAK,CAACC,OAAO,CAAC+C,CAAC,CAAC,GAC3B;AAAEA,MAAAA,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC;MAAEC,CAAC,EAAED,CAAC,CAAC,CAAC,CAAA;AAAE,KAAC,GACpB,OAAOA,CAAC,KAAK,QAAQ,GACnB;MAAEA,CAAC,EAAEA,CAAC,CAACA,CAAC;MAAEC,CAAC,EAAED,CAAC,CAACC,CAAAA;AAAE,KAAC,GAClB;AAAED,MAAAA,CAAC,EAAEA,CAAC;AAAEC,MAAAA,CAAC,EAAEA,CAAAA;KAAG,CAAA;;AAEpB;AACA,IAAA,IAAI,CAACD,CAAC,GAAGsO,MAAM,CAACtO,CAAC,IAAI,IAAI,GAAGqO,IAAI,CAACrO,CAAC,GAAGsO,MAAM,CAACtO,CAAC,CAAA;AAC7C,IAAA,IAAI,CAACC,CAAC,GAAGqO,MAAM,CAACrO,CAAC,IAAI,IAAI,GAAGoO,IAAI,CAACpO,CAAC,GAAGqO,MAAM,CAACrO,CAAC,CAAA;AAE7C,IAAA,OAAO,IAAI,CAAA;AACb,GAAA;AAEAgN,EAAAA,OAAOA,GAAG;IACR,OAAO,CAAC,IAAI,CAACjN,CAAC,EAAE,IAAI,CAACC,CAAC,CAAC,CAAA;AACzB,GAAA;EAEAsO,SAASA,CAACxR,CAAC,EAAE;IACX,OAAO,IAAI,CAACqR,KAAK,EAAE,CAACI,UAAU,CAACzR,CAAC,CAAC,CAAA;AACnC,GAAA;;AAEA;EACAyR,UAAUA,CAACzR,CAAC,EAAE;AACZ,IAAA,IAAI,CAAC0R,MAAM,CAACC,YAAY,CAAC3R,CAAC,CAAC,EAAE;AAC3BA,MAAAA,CAAC,GAAG,IAAI0R,MAAM,CAAC1R,CAAC,CAAC,CAAA;AACnB,KAAA;IAEA,MAAM;MAAEiD,CAAC;AAAEC,MAAAA,CAAAA;AAAE,KAAC,GAAG,IAAI,CAAA;;AAErB;AACA,IAAA,IAAI,CAACD,CAAC,GAAGjD,CAAC,CAACuL,CAAC,GAAGtI,CAAC,GAAGjD,CAAC,CAACqK,CAAC,GAAGnH,CAAC,GAAGlD,CAAC,CAAC2L,CAAC,CAAA;AAChC,IAAA,IAAI,CAACzI,CAAC,GAAGlD,CAAC,CAAC+M,CAAC,GAAG9J,CAAC,GAAGjD,CAAC,CAACsB,CAAC,GAAG4B,CAAC,GAAGlD,CAAC,CAAC4R,CAAC,CAAA;AAEhC,IAAA,OAAO,IAAI,CAAA;AACb,GAAA;AACF,CAAA;AAEO,SAASC,KAAKA,CAAC5O,CAAC,EAAEC,CAAC,EAAE;AAC1B,EAAA,OAAO,IAAIkO,KAAK,CAACnO,CAAC,EAAEC,CAAC,CAAC,CAACuO,UAAU,CAAC,IAAI,CAACK,SAAS,EAAE,CAACC,QAAQ,EAAE,CAAC,CAAA;AAChE;;AClDA,SAASC,WAAWA,CAACzG,CAAC,EAAEwB,CAAC,EAAEkF,SAAS,EAAE;AACpC,EAAA,OAAO1Q,IAAI,CAAC2Q,GAAG,CAACnF,CAAC,GAAGxB,CAAC,CAAC,IAAiB,IAAI,CAAC,CAAA;AAC9C,CAAA;AAEe,MAAMmG,MAAM,CAAC;EAC1B9J,WAAWA,CAAC,GAAGD,IAAI,EAAE;AACnB,IAAA,IAAI,CAACkG,IAAI,CAAC,GAAGlG,IAAI,CAAC,CAAA;AACpB,GAAA;EAEA,OAAOwK,gBAAgBA,CAACxP,CAAC,EAAE;AACzB;AACA,IAAA,MAAMyP,QAAQ,GAAGzP,CAAC,CAAC0P,IAAI,KAAK,MAAM,IAAI1P,CAAC,CAAC0P,IAAI,KAAK,IAAI,CAAA;AACrD,IAAA,MAAMC,KAAK,GAAG3P,CAAC,CAAC0P,IAAI,KAAKD,QAAQ,IAAIzP,CAAC,CAAC0P,IAAI,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAA;AAC7D,IAAA,MAAME,KAAK,GAAG5P,CAAC,CAAC0P,IAAI,KAAKD,QAAQ,IAAIzP,CAAC,CAAC0P,IAAI,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAA;AAC7D,IAAA,MAAMG,KAAK,GACT7P,CAAC,CAAC8P,IAAI,IAAI9P,CAAC,CAAC8P,IAAI,CAACvR,MAAM,GACnByB,CAAC,CAAC8P,IAAI,CAAC,CAAC,CAAC,GACTC,QAAQ,CAAC/P,CAAC,CAAC8P,IAAI,CAAC,GACd9P,CAAC,CAAC8P,IAAI,GACNC,QAAQ,CAAC/P,CAAC,CAAC6P,KAAK,CAAC,GACf7P,CAAC,CAAC6P,KAAK,GACP,CAAC,CAAA;AACX,IAAA,MAAMG,KAAK,GACThQ,CAAC,CAAC8P,IAAI,IAAI9P,CAAC,CAAC8P,IAAI,CAACvR,MAAM,GACnByB,CAAC,CAAC8P,IAAI,CAAC,CAAC,CAAC,GACTC,QAAQ,CAAC/P,CAAC,CAAC8P,IAAI,CAAC,GACd9P,CAAC,CAAC8P,IAAI,GACNC,QAAQ,CAAC/P,CAAC,CAACgQ,KAAK,CAAC,GACfhQ,CAAC,CAACgQ,KAAK,GACP,CAAC,CAAA;IACX,MAAMC,MAAM,GACVjQ,CAAC,CAACkQ,KAAK,IAAIlQ,CAAC,CAACkQ,KAAK,CAAC3R,MAAM,GACrByB,CAAC,CAACkQ,KAAK,CAAC,CAAC,CAAC,GAAGP,KAAK,GAClBI,QAAQ,CAAC/P,CAAC,CAACkQ,KAAK,CAAC,GACflQ,CAAC,CAACkQ,KAAK,GAAGP,KAAK,GACfI,QAAQ,CAAC/P,CAAC,CAACiQ,MAAM,CAAC,GAChBjQ,CAAC,CAACiQ,MAAM,GAAGN,KAAK,GAChBA,KAAK,CAAA;IACf,MAAMQ,MAAM,GACVnQ,CAAC,CAACkQ,KAAK,IAAIlQ,CAAC,CAACkQ,KAAK,CAAC3R,MAAM,GACrByB,CAAC,CAACkQ,KAAK,CAAC,CAAC,CAAC,GAAGN,KAAK,GAClBG,QAAQ,CAAC/P,CAAC,CAACkQ,KAAK,CAAC,GACflQ,CAAC,CAACkQ,KAAK,GAAGN,KAAK,GACfG,QAAQ,CAAC/P,CAAC,CAACmQ,MAAM,CAAC,GAChBnQ,CAAC,CAACmQ,MAAM,GAAGP,KAAK,GAChBA,KAAK,CAAA;AACf,IAAA,MAAMQ,KAAK,GAAGpQ,CAAC,CAACoQ,KAAK,IAAI,CAAC,CAAA;IAC1B,MAAMC,KAAK,GAAGrQ,CAAC,CAACsQ,MAAM,IAAItQ,CAAC,CAACqQ,KAAK,IAAI,CAAC,CAAA;AACtC,IAAA,MAAMpQ,MAAM,GAAG,IAAIwO,KAAK,CACtBzO,CAAC,CAACC,MAAM,IAAID,CAAC,CAACuQ,MAAM,IAAIvQ,CAAC,CAACE,EAAE,IAAIF,CAAC,CAACG,OAAO,EACzCH,CAAC,CAACI,EAAE,IAAIJ,CAAC,CAACK,OACZ,CAAC,CAAA;AACD,IAAA,MAAMH,EAAE,GAAGD,MAAM,CAACK,CAAC,CAAA;AACnB,IAAA,MAAMF,EAAE,GAAGH,MAAM,CAACM,CAAC,CAAA;AACnB;AACA,IAAA,MAAM+E,QAAQ,GAAG,IAAImJ,KAAK,CACxBzO,CAAC,CAACsF,QAAQ,IAAItF,CAAC,CAACwQ,EAAE,IAAIxQ,CAAC,CAACyQ,SAAS,IAAIC,GAAG,EACxC1Q,CAAC,CAAC2Q,EAAE,IAAI3Q,CAAC,CAAC4Q,SAAS,IAAIF,GACzB,CAAC,CAAA;AACD,IAAA,MAAMF,EAAE,GAAGlL,QAAQ,CAAChF,CAAC,CAAA;AACrB,IAAA,MAAMqQ,EAAE,GAAGrL,QAAQ,CAAC/E,CAAC,CAAA;IACrB,MAAMsQ,SAAS,GAAG,IAAIpC,KAAK,CACzBzO,CAAC,CAAC6Q,SAAS,IAAI7Q,CAAC,CAAC8Q,EAAE,IAAI9Q,CAAC,CAAC+Q,UAAU,EACnC/Q,CAAC,CAACgR,EAAE,IAAIhR,CAAC,CAACiR,UACZ,CAAC,CAAA;AACD,IAAA,MAAMH,EAAE,GAAGD,SAAS,CAACvQ,CAAC,CAAA;AACtB,IAAA,MAAM0Q,EAAE,GAAGH,SAAS,CAACtQ,CAAC,CAAA;IACtB,MAAM2Q,QAAQ,GAAG,IAAIzC,KAAK,CACxBzO,CAAC,CAACkR,QAAQ,IAAIlR,CAAC,CAACmR,EAAE,IAAInR,CAAC,CAACoR,SAAS,EACjCpR,CAAC,CAACqR,EAAE,IAAIrR,CAAC,CAACsR,SACZ,CAAC,CAAA;AACD,IAAA,MAAMH,EAAE,GAAGD,QAAQ,CAAC5Q,CAAC,CAAA;AACrB,IAAA,MAAM+Q,EAAE,GAAGH,QAAQ,CAAC3Q,CAAC,CAAA;;AAErB;IACA,OAAO;MACL0P,MAAM;MACNE,MAAM;MACNN,KAAK;MACLG,KAAK;MACLI,KAAK;MACLC,KAAK;MACLc,EAAE;MACFE,EAAE;MACFP,EAAE;MACFE,EAAE;MACF9Q,EAAE;MACFE,EAAE;MACFoQ,EAAE;AACFG,MAAAA,EAAAA;KACD,CAAA;AACH,GAAA;EAEA,OAAOY,SAASA,CAAC3I,CAAC,EAAE;IAClB,OAAO;AAAEA,MAAAA,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC;AAAEwB,MAAAA,CAAC,EAAExB,CAAC,CAAC,CAAC,CAAC;AAAElB,MAAAA,CAAC,EAAEkB,CAAC,CAAC,CAAC,CAAC;AAAEjK,MAAAA,CAAC,EAAEiK,CAAC,CAAC,CAAC,CAAC;AAAEI,MAAAA,CAAC,EAAEJ,CAAC,CAAC,CAAC,CAAC;MAAEqG,CAAC,EAAErG,CAAC,CAAC,CAAC,CAAA;KAAG,CAAA;AACjE,GAAA;EAEA,OAAOoG,YAAYA,CAAChP,CAAC,EAAE;AACrB,IAAA,OACEA,CAAC,CAAC4I,CAAC,IAAI,IAAI,IACX5I,CAAC,CAACoK,CAAC,IAAI,IAAI,IACXpK,CAAC,CAAC0H,CAAC,IAAI,IAAI,IACX1H,CAAC,CAACrB,CAAC,IAAI,IAAI,IACXqB,CAAC,CAACgJ,CAAC,IAAI,IAAI,IACXhJ,CAAC,CAACiP,CAAC,IAAI,IAAI,CAAA;AAEf,GAAA;;AAEA;AACA,EAAA,OAAOuC,cAAcA,CAAC5G,CAAC,EAAE7L,CAAC,EAAEiB,CAAC,EAAE;AAC7B;AACA,IAAA,MAAM4I,CAAC,GAAGgC,CAAC,CAAChC,CAAC,GAAG7J,CAAC,CAAC6J,CAAC,GAAGgC,CAAC,CAAClD,CAAC,GAAG3I,CAAC,CAACqL,CAAC,CAAA;AAC/B,IAAA,MAAMA,CAAC,GAAGQ,CAAC,CAACR,CAAC,GAAGrL,CAAC,CAAC6J,CAAC,GAAGgC,CAAC,CAACjM,CAAC,GAAGI,CAAC,CAACqL,CAAC,CAAA;AAC/B,IAAA,MAAM1C,CAAC,GAAGkD,CAAC,CAAChC,CAAC,GAAG7J,CAAC,CAAC2I,CAAC,GAAGkD,CAAC,CAAClD,CAAC,GAAG3I,CAAC,CAACJ,CAAC,CAAA;AAC/B,IAAA,MAAMA,CAAC,GAAGiM,CAAC,CAACR,CAAC,GAAGrL,CAAC,CAAC2I,CAAC,GAAGkD,CAAC,CAACjM,CAAC,GAAGI,CAAC,CAACJ,CAAC,CAAA;IAC/B,MAAMqK,CAAC,GAAG4B,CAAC,CAAC5B,CAAC,GAAG4B,CAAC,CAAChC,CAAC,GAAG7J,CAAC,CAACiK,CAAC,GAAG4B,CAAC,CAAClD,CAAC,GAAG3I,CAAC,CAACkQ,CAAC,CAAA;IACrC,MAAMA,CAAC,GAAGrE,CAAC,CAACqE,CAAC,GAAGrE,CAAC,CAACR,CAAC,GAAGrL,CAAC,CAACiK,CAAC,GAAG4B,CAAC,CAACjM,CAAC,GAAGI,CAAC,CAACkQ,CAAC,CAAA;;AAErC;IACAjP,CAAC,CAAC4I,CAAC,GAAGA,CAAC,CAAA;IACP5I,CAAC,CAACoK,CAAC,GAAGA,CAAC,CAAA;IACPpK,CAAC,CAAC0H,CAAC,GAAGA,CAAC,CAAA;IACP1H,CAAC,CAACrB,CAAC,GAAGA,CAAC,CAAA;IACPqB,CAAC,CAACgJ,CAAC,GAAGA,CAAC,CAAA;IACPhJ,CAAC,CAACiP,CAAC,GAAGA,CAAC,CAAA;AAEP,IAAA,OAAOjP,CAAC,CAAA;AACV,GAAA;AAEAuQ,EAAAA,MAAMA,CAACkB,EAAE,EAAEC,EAAE,EAAEC,MAAM,EAAE;AACrB,IAAA,OAAO,IAAI,CAACjD,KAAK,EAAE,CAACkD,OAAO,CAACH,EAAE,EAAEC,EAAE,EAAEC,MAAM,CAAC,CAAA;AAC7C,GAAA;;AAEA;AACAC,EAAAA,OAAOA,CAACH,EAAE,EAAEC,EAAE,EAAEC,MAAM,EAAE;AACtB,IAAA,MAAME,EAAE,GAAGJ,EAAE,IAAI,CAAC,CAAA;AAClB,IAAA,MAAMK,EAAE,GAAGJ,EAAE,IAAI,CAAC,CAAA;IAClB,OAAO,IAAI,CAACK,UAAU,CAAC,CAACF,EAAE,EAAE,CAACC,EAAE,CAAC,CAACE,UAAU,CAACL,MAAM,CAAC,CAACI,UAAU,CAACF,EAAE,EAAEC,EAAE,CAAC,CAAA;AACxE,GAAA;;AAEA;AACApD,EAAAA,KAAKA,GAAG;AACN,IAAA,OAAO,IAAIK,MAAM,CAAC,IAAI,CAAC,CAAA;AACzB,GAAA;;AAEA;EACAkD,SAASA,CAACR,EAAE,GAAG,CAAC,EAAEC,EAAE,GAAG,CAAC,EAAE;AACxB;AACA,IAAA,MAAM9I,CAAC,GAAG,IAAI,CAACA,CAAC,CAAA;AAChB,IAAA,MAAMwB,CAAC,GAAG,IAAI,CAACA,CAAC,CAAA;AAChB,IAAA,MAAM1C,CAAC,GAAG,IAAI,CAACA,CAAC,CAAA;AAChB,IAAA,MAAM/I,CAAC,GAAG,IAAI,CAACA,CAAC,CAAA;AAChB,IAAA,MAAMqK,CAAC,GAAG,IAAI,CAACA,CAAC,CAAA;AAChB,IAAA,MAAMiG,CAAC,GAAG,IAAI,CAACA,CAAC,CAAA;;AAEhB;IACA,MAAMiD,WAAW,GAAGtJ,CAAC,GAAGjK,CAAC,GAAGyL,CAAC,GAAG1C,CAAC,CAAA;IACjC,MAAMyK,GAAG,GAAGD,WAAW,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAA;;AAEpC;AACA;AACA,IAAA,MAAME,EAAE,GAAGD,GAAG,GAAGvT,IAAI,CAAC4N,IAAI,CAAC5D,CAAC,GAAGA,CAAC,GAAGwB,CAAC,GAAGA,CAAC,CAAC,CAAA;AACzC,IAAA,MAAMiI,QAAQ,GAAGzT,IAAI,CAAC6N,KAAK,CAAC0F,GAAG,GAAG/H,CAAC,EAAE+H,GAAG,GAAGvJ,CAAC,CAAC,CAAA;IAC7C,MAAMyH,KAAK,GAAI,GAAG,GAAGzR,IAAI,CAACC,EAAE,GAAIwT,QAAQ,CAAA;AACxC,IAAA,MAAMtF,EAAE,GAAGnO,IAAI,CAAC+N,GAAG,CAAC0F,QAAQ,CAAC,CAAA;AAC7B,IAAA,MAAMC,EAAE,GAAG1T,IAAI,CAAC2M,GAAG,CAAC8G,QAAQ,CAAC,CAAA;;AAE7B;AACA;IACA,MAAME,GAAG,GAAG,CAAC3J,CAAC,GAAGlB,CAAC,GAAG0C,CAAC,GAAGzL,CAAC,IAAIuT,WAAW,CAAA;IACzC,MAAMM,EAAE,GAAI9K,CAAC,GAAG0K,EAAE,IAAKG,GAAG,GAAG3J,CAAC,GAAGwB,CAAC,CAAC,IAAKzL,CAAC,GAAGyT,EAAE,IAAKG,GAAG,GAAGnI,CAAC,GAAGxB,CAAC,CAAC,CAAA;;AAE/D;IACA,MAAMkI,EAAE,GAAG9H,CAAC,GAAGyI,EAAE,GAAGA,EAAE,GAAG1E,EAAE,GAAGqF,EAAE,GAAGV,EAAE,IAAIa,GAAG,GAAGxF,EAAE,GAAGqF,EAAE,GAAGE,EAAE,GAAGE,EAAE,CAAC,CAAA;IACjE,MAAMxB,EAAE,GAAG/B,CAAC,GAAGyC,EAAE,GAAGD,EAAE,GAAGa,EAAE,GAAGF,EAAE,GAAGV,EAAE,IAAIa,GAAG,GAAGD,EAAE,GAAGF,EAAE,GAAGrF,EAAE,GAAGyF,EAAE,CAAC,CAAA;;AAEjE;IACA,OAAO;AACL;AACAvC,MAAAA,MAAM,EAAEmC,EAAE;AACVjC,MAAAA,MAAM,EAAEqC,EAAE;AACVpC,MAAAA,KAAK,EAAEmC,GAAG;AACVjC,MAAAA,MAAM,EAAED,KAAK;AACbU,MAAAA,UAAU,EAAED,EAAE;AACdG,MAAAA,UAAU,EAAED,EAAE;AACd7Q,MAAAA,OAAO,EAAEsR,EAAE;AACXpR,MAAAA,OAAO,EAAEqR,EAAE;AAEX;MACA9I,CAAC,EAAE,IAAI,CAACA,CAAC;MACTwB,CAAC,EAAE,IAAI,CAACA,CAAC;MACT1C,CAAC,EAAE,IAAI,CAACA,CAAC;MACT/I,CAAC,EAAE,IAAI,CAACA,CAAC;MACTqK,CAAC,EAAE,IAAI,CAACA,CAAC;MACTiG,CAAC,EAAE,IAAI,CAACA,CAAAA;KACT,CAAA;AACH,GAAA;;AAEA;EACAwD,MAAMA,CAACC,KAAK,EAAE;AACZ,IAAA,IAAIA,KAAK,KAAK,IAAI,EAAE,OAAO,IAAI,CAAA;AAC/B,IAAA,MAAMC,IAAI,GAAG,IAAI5D,MAAM,CAAC2D,KAAK,CAAC,CAAA;AAC9B,IAAA,OACErD,WAAW,CAAC,IAAI,CAACzG,CAAC,EAAE+J,IAAI,CAAC/J,CAAC,CAAC,IAC3ByG,WAAW,CAAC,IAAI,CAACjF,CAAC,EAAEuI,IAAI,CAACvI,CAAC,CAAC,IAC3BiF,WAAW,CAAC,IAAI,CAAC3H,CAAC,EAAEiL,IAAI,CAACjL,CAAC,CAAC,IAC3B2H,WAAW,CAAC,IAAI,CAAC1Q,CAAC,EAAEgU,IAAI,CAAChU,CAAC,CAAC,IAC3B0Q,WAAW,CAAC,IAAI,CAACrG,CAAC,EAAE2J,IAAI,CAAC3J,CAAC,CAAC,IAC3BqG,WAAW,CAAC,IAAI,CAACJ,CAAC,EAAE0D,IAAI,CAAC1D,CAAC,CAAC,CAAA;AAE/B,GAAA;;AAEA;AACAS,EAAAA,IAAIA,CAACkD,IAAI,EAAErC,MAAM,EAAE;IACjB,OAAO,IAAI,CAAC7B,KAAK,EAAE,CAACmE,KAAK,CAACD,IAAI,EAAErC,MAAM,CAAC,CAAA;AACzC,GAAA;AAEAsC,EAAAA,KAAKA,CAACD,IAAI,EAAErC,MAAM,EAAE;IAClB,OAAOqC,IAAI,KAAK,GAAG,GACf,IAAI,CAACE,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,EAAEvC,MAAM,EAAE,CAAC,CAAC,GAC7BqC,IAAI,KAAK,GAAG,GACV,IAAI,CAACE,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAEvC,MAAM,CAAC,GAC7B,IAAI,CAACuC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAEF,IAAI,EAAErC,MAAM,IAAIqC,IAAI,CAAC,CAAC;AAClD,GAAA;;AAEA;EACA1H,IAAIA,CAAC0D,MAAM,EAAE;AACX,IAAA,MAAMD,IAAI,GAAGI,MAAM,CAACwC,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAA;;AAEjD;IACA3C,MAAM,GACJA,MAAM,YAAYmE,OAAO,GACrBnE,MAAM,CAACoE,SAAS,EAAE,GAClB,OAAOpE,MAAM,KAAK,QAAQ,GACxBG,MAAM,CAACwC,SAAS,CAAC3C,MAAM,CAACxH,KAAK,CAACJ,SAAS,CAAC,CAAC9I,GAAG,CAAC+U,UAAU,CAAC,CAAC,GACzD3V,KAAK,CAACC,OAAO,CAACqR,MAAM,CAAC,GACnBG,MAAM,CAACwC,SAAS,CAAC3C,MAAM,CAAC,GACxB,OAAOA,MAAM,KAAK,QAAQ,IAAIG,MAAM,CAACC,YAAY,CAACJ,MAAM,CAAC,GACvDA,MAAM,GACN,OAAOA,MAAM,KAAK,QAAQ,GACxB,IAAIG,MAAM,EAAE,CAACF,SAAS,CAACD,MAAM,CAAC,GAC9B5G,SAAS,CAACzJ,MAAM,KAAK,CAAC,GACpBwQ,MAAM,CAACwC,SAAS,CAAC,EAAE,CAAC/R,KAAK,CAAC0T,IAAI,CAAClL,SAAS,CAAC,CAAC,GAC1C2G,IAAI,CAAA;;AAEpB;AACA,IAAA,IAAI,CAAC/F,CAAC,GAAGgG,MAAM,CAAChG,CAAC,IAAI,IAAI,GAAGgG,MAAM,CAAChG,CAAC,GAAG+F,IAAI,CAAC/F,CAAC,CAAA;AAC7C,IAAA,IAAI,CAACwB,CAAC,GAAGwE,MAAM,CAACxE,CAAC,IAAI,IAAI,GAAGwE,MAAM,CAACxE,CAAC,GAAGuE,IAAI,CAACvE,CAAC,CAAA;AAC7C,IAAA,IAAI,CAAC1C,CAAC,GAAGkH,MAAM,CAAClH,CAAC,IAAI,IAAI,GAAGkH,MAAM,CAAClH,CAAC,GAAGiH,IAAI,CAACjH,CAAC,CAAA;AAC7C,IAAA,IAAI,CAAC/I,CAAC,GAAGiQ,MAAM,CAACjQ,CAAC,IAAI,IAAI,GAAGiQ,MAAM,CAACjQ,CAAC,GAAGgQ,IAAI,CAAChQ,CAAC,CAAA;AAC7C,IAAA,IAAI,CAACqK,CAAC,GAAG4F,MAAM,CAAC5F,CAAC,IAAI,IAAI,GAAG4F,MAAM,CAAC5F,CAAC,GAAG2F,IAAI,CAAC3F,CAAC,CAAA;AAC7C,IAAA,IAAI,CAACiG,CAAC,GAAGL,MAAM,CAACK,CAAC,IAAI,IAAI,GAAGL,MAAM,CAACK,CAAC,GAAGN,IAAI,CAACM,CAAC,CAAA;AAE7C,IAAA,OAAO,IAAI,CAAA;AACb,GAAA;AAEAkE,EAAAA,OAAOA,GAAG;IACR,OAAO,IAAI,CAACzE,KAAK,EAAE,CAACU,QAAQ,EAAE,CAAA;AAChC,GAAA;;AAEA;AACAA,EAAAA,QAAQA,GAAG;AACT;AACA,IAAA,MAAMxG,CAAC,GAAG,IAAI,CAACA,CAAC,CAAA;AAChB,IAAA,MAAMwB,CAAC,GAAG,IAAI,CAACA,CAAC,CAAA;AAChB,IAAA,MAAM1C,CAAC,GAAG,IAAI,CAACA,CAAC,CAAA;AAChB,IAAA,MAAM/I,CAAC,GAAG,IAAI,CAACA,CAAC,CAAA;AAChB,IAAA,MAAMqK,CAAC,GAAG,IAAI,CAACA,CAAC,CAAA;AAChB,IAAA,MAAMiG,CAAC,GAAG,IAAI,CAACA,CAAC,CAAA;;AAEhB;IACA,MAAMmE,GAAG,GAAGxK,CAAC,GAAGjK,CAAC,GAAGyL,CAAC,GAAG1C,CAAC,CAAA;IACzB,IAAI,CAAC0L,GAAG,EAAE,MAAM,IAAI1H,KAAK,CAAC,gBAAgB,GAAG,IAAI,CAAC,CAAA;;AAElD;AACA,IAAA,MAAM2H,EAAE,GAAG1U,CAAC,GAAGyU,GAAG,CAAA;AAClB,IAAA,MAAME,EAAE,GAAG,CAAClJ,CAAC,GAAGgJ,GAAG,CAAA;AACnB,IAAA,MAAMG,EAAE,GAAG,CAAC7L,CAAC,GAAG0L,GAAG,CAAA;AACnB,IAAA,MAAMI,EAAE,GAAG5K,CAAC,GAAGwK,GAAG,CAAA;;AAElB;IACA,MAAMK,EAAE,GAAG,EAAEJ,EAAE,GAAGrK,CAAC,GAAGuK,EAAE,GAAGtE,CAAC,CAAC,CAAA;IAC7B,MAAMyE,EAAE,GAAG,EAAEJ,EAAE,GAAGtK,CAAC,GAAGwK,EAAE,GAAGvE,CAAC,CAAC,CAAA;;AAE7B;IACA,IAAI,CAACrG,CAAC,GAAGyK,EAAE,CAAA;IACX,IAAI,CAACjJ,CAAC,GAAGkJ,EAAE,CAAA;IACX,IAAI,CAAC5L,CAAC,GAAG6L,EAAE,CAAA;IACX,IAAI,CAAC5U,CAAC,GAAG6U,EAAE,CAAA;IACX,IAAI,CAACxK,CAAC,GAAGyK,EAAE,CAAA;IACX,IAAI,CAACxE,CAAC,GAAGyE,EAAE,CAAA;AAEX,IAAA,OAAO,IAAI,CAAA;AACb,GAAA;EAEAC,SAASA,CAAChC,MAAM,EAAE;IAChB,OAAO,IAAI,CAACjD,KAAK,EAAE,CAACsD,UAAU,CAACL,MAAM,CAAC,CAAA;AACxC,GAAA;EAEAK,UAAUA,CAACL,MAAM,EAAE;IACjB,MAAM5S,CAAC,GAAG,IAAI,CAAA;AACd,IAAA,MAAM6L,CAAC,GAAG+G,MAAM,YAAY5C,MAAM,GAAG4C,MAAM,GAAG,IAAI5C,MAAM,CAAC4C,MAAM,CAAC,CAAA;IAEhE,OAAO5C,MAAM,CAACyC,cAAc,CAAC5G,CAAC,EAAE7L,CAAC,EAAE,IAAI,CAAC,CAAA;AAC1C,GAAA;;AAEA;EACA6U,QAAQA,CAACjC,MAAM,EAAE;IACf,OAAO,IAAI,CAACjD,KAAK,EAAE,CAACmF,SAAS,CAAClC,MAAM,CAAC,CAAA;AACvC,GAAA;EAEAkC,SAASA,CAAClC,MAAM,EAAE;AAChB;IACA,MAAM/G,CAAC,GAAG,IAAI,CAAA;AACd,IAAA,MAAM7L,CAAC,GAAG4S,MAAM,YAAY5C,MAAM,GAAG4C,MAAM,GAAG,IAAI5C,MAAM,CAAC4C,MAAM,CAAC,CAAA;IAEhE,OAAO5C,MAAM,CAACyC,cAAc,CAAC5G,CAAC,EAAE7L,CAAC,EAAE,IAAI,CAAC,CAAA;AAC1C,GAAA;;AAEA;AACAuR,EAAAA,MAAMA,CAACvR,CAAC,EAAE0S,EAAE,EAAEC,EAAE,EAAE;AAChB,IAAA,OAAO,IAAI,CAAChD,KAAK,EAAE,CAACoF,OAAO,CAAC/U,CAAC,EAAE0S,EAAE,EAAEC,EAAE,CAAC,CAAA;AACxC,GAAA;EAEAoC,OAAOA,CAAC/U,CAAC,EAAE0S,EAAE,GAAG,CAAC,EAAEC,EAAE,GAAG,CAAC,EAAE;AACzB;AACA3S,IAAAA,CAAC,GAAGL,OAAO,CAACK,CAAC,CAAC,CAAA;AAEd,IAAA,MAAM4N,GAAG,GAAG/N,IAAI,CAAC+N,GAAG,CAAC5N,CAAC,CAAC,CAAA;AACvB,IAAA,MAAMwM,GAAG,GAAG3M,IAAI,CAAC2M,GAAG,CAACxM,CAAC,CAAC,CAAA;IAEvB,MAAM;MAAE6J,CAAC;MAAEwB,CAAC;MAAE1C,CAAC;MAAE/I,CAAC;MAAEqK,CAAC;AAAEiG,MAAAA,CAAAA;AAAE,KAAC,GAAG,IAAI,CAAA;IAEjC,IAAI,CAACrG,CAAC,GAAGA,CAAC,GAAG+D,GAAG,GAAGvC,CAAC,GAAGmB,GAAG,CAAA;IAC1B,IAAI,CAACnB,CAAC,GAAGA,CAAC,GAAGuC,GAAG,GAAG/D,CAAC,GAAG2C,GAAG,CAAA;IAC1B,IAAI,CAAC7D,CAAC,GAAGA,CAAC,GAAGiF,GAAG,GAAGhO,CAAC,GAAG4M,GAAG,CAAA;IAC1B,IAAI,CAAC5M,CAAC,GAAGA,CAAC,GAAGgO,GAAG,GAAGjF,CAAC,GAAG6D,GAAG,CAAA;AAC1B,IAAA,IAAI,CAACvC,CAAC,GAAGA,CAAC,GAAG2D,GAAG,GAAGsC,CAAC,GAAG1D,GAAG,GAAGmG,EAAE,GAAGnG,GAAG,GAAGkG,EAAE,GAAG9E,GAAG,GAAG8E,EAAE,CAAA;AACrD,IAAA,IAAI,CAACxC,CAAC,GAAGA,CAAC,GAAGtC,GAAG,GAAG3D,CAAC,GAAGuC,GAAG,GAAGkG,EAAE,GAAGlG,GAAG,GAAGmG,EAAE,GAAG/E,GAAG,GAAG+E,EAAE,CAAA;AAErD,IAAA,OAAO,IAAI,CAAA;AACb,GAAA;;AAEA;AACAxB,EAAAA,KAAKA,GAAG;IACN,OAAO,IAAI,CAACxB,KAAK,EAAE,CAACoE,MAAM,CAAC,GAAG9K,SAAS,CAAC,CAAA;AAC1C,GAAA;AAEA8K,EAAAA,MAAMA,CAACxS,CAAC,EAAEC,CAAC,GAAGD,CAAC,EAAEmR,EAAE,GAAG,CAAC,EAAEC,EAAE,GAAG,CAAC,EAAE;AAC/B;AACA,IAAA,IAAI1J,SAAS,CAACzJ,MAAM,KAAK,CAAC,EAAE;AAC1BmT,MAAAA,EAAE,GAAGD,EAAE,CAAA;AACPA,MAAAA,EAAE,GAAGlR,CAAC,CAAA;AACNA,MAAAA,CAAC,GAAGD,CAAC,CAAA;AACP,KAAA;IAEA,MAAM;MAAEsI,CAAC;MAAEwB,CAAC;MAAE1C,CAAC;MAAE/I,CAAC;MAAEqK,CAAC;AAAEiG,MAAAA,CAAAA;AAAE,KAAC,GAAG,IAAI,CAAA;AAEjC,IAAA,IAAI,CAACrG,CAAC,GAAGA,CAAC,GAAGtI,CAAC,CAAA;AACd,IAAA,IAAI,CAAC8J,CAAC,GAAGA,CAAC,GAAG7J,CAAC,CAAA;AACd,IAAA,IAAI,CAACmH,CAAC,GAAGA,CAAC,GAAGpH,CAAC,CAAA;AACd,IAAA,IAAI,CAAC3B,CAAC,GAAGA,CAAC,GAAG4B,CAAC,CAAA;IACd,IAAI,CAACyI,CAAC,GAAGA,CAAC,GAAG1I,CAAC,GAAGmR,EAAE,GAAGnR,CAAC,GAAGmR,EAAE,CAAA;IAC5B,IAAI,CAACxC,CAAC,GAAGA,CAAC,GAAG1O,CAAC,GAAGmR,EAAE,GAAGnR,CAAC,GAAGmR,EAAE,CAAA;AAE5B,IAAA,OAAO,IAAI,CAAA;AACb,GAAA;;AAEA;AACAtB,EAAAA,KAAKA,CAACxH,CAAC,EAAE6I,EAAE,EAAEC,EAAE,EAAE;AACf,IAAA,OAAO,IAAI,CAAChD,KAAK,EAAE,CAACqF,MAAM,CAACnL,CAAC,EAAE6I,EAAE,EAAEC,EAAE,CAAC,CAAA;AACvC,GAAA;;AAEA;EACAqC,MAAMA,CAACC,EAAE,EAAEvC,EAAE,GAAG,CAAC,EAAEC,EAAE,GAAG,CAAC,EAAE;IACzB,MAAM;MAAE9I,CAAC;MAAEwB,CAAC;MAAE1C,CAAC;MAAE/I,CAAC;MAAEqK,CAAC;AAAEiG,MAAAA,CAAAA;AAAE,KAAC,GAAG,IAAI,CAAA;AAEjC,IAAA,IAAI,CAACrG,CAAC,GAAGA,CAAC,GAAGwB,CAAC,GAAG4J,EAAE,CAAA;AACnB,IAAA,IAAI,CAACtM,CAAC,GAAGA,CAAC,GAAG/I,CAAC,GAAGqV,EAAE,CAAA;IACnB,IAAI,CAAChL,CAAC,GAAGA,CAAC,GAAGiG,CAAC,GAAG+E,EAAE,GAAGtC,EAAE,GAAGsC,EAAE,CAAA;AAE7B,IAAA,OAAO,IAAI,CAAA;AACb,GAAA;;AAEA;AACAlE,EAAAA,IAAIA,GAAG;IACL,OAAO,IAAI,CAACpB,KAAK,EAAE,CAACuF,KAAK,CAAC,GAAGjM,SAAS,CAAC,CAAA;AACzC,GAAA;AAEAiM,EAAAA,KAAKA,CAAC3T,CAAC,EAAEC,CAAC,GAAGD,CAAC,EAAEmR,EAAE,GAAG,CAAC,EAAEC,EAAE,GAAG,CAAC,EAAE;AAC9B;AACA,IAAA,IAAI1J,SAAS,CAACzJ,MAAM,KAAK,CAAC,EAAE;AAC1BmT,MAAAA,EAAE,GAAGD,EAAE,CAAA;AACPA,MAAAA,EAAE,GAAGlR,CAAC,CAAA;AACNA,MAAAA,CAAC,GAAGD,CAAC,CAAA;AACP,KAAA;;AAEA;AACAA,IAAAA,CAAC,GAAG5B,OAAO,CAAC4B,CAAC,CAAC,CAAA;AACdC,IAAAA,CAAC,GAAG7B,OAAO,CAAC6B,CAAC,CAAC,CAAA;AAEd,IAAA,MAAMyT,EAAE,GAAGpV,IAAI,CAACsV,GAAG,CAAC5T,CAAC,CAAC,CAAA;AACtB,IAAA,MAAM6T,EAAE,GAAGvV,IAAI,CAACsV,GAAG,CAAC3T,CAAC,CAAC,CAAA;IAEtB,MAAM;MAAEqI,CAAC;MAAEwB,CAAC;MAAE1C,CAAC;MAAE/I,CAAC;MAAEqK,CAAC;AAAEiG,MAAAA,CAAAA;AAAE,KAAC,GAAG,IAAI,CAAA;AAEjC,IAAA,IAAI,CAACrG,CAAC,GAAGA,CAAC,GAAGwB,CAAC,GAAG4J,EAAE,CAAA;AACnB,IAAA,IAAI,CAAC5J,CAAC,GAAGA,CAAC,GAAGxB,CAAC,GAAGuL,EAAE,CAAA;AACnB,IAAA,IAAI,CAACzM,CAAC,GAAGA,CAAC,GAAG/I,CAAC,GAAGqV,EAAE,CAAA;AACnB,IAAA,IAAI,CAACrV,CAAC,GAAGA,CAAC,GAAG+I,CAAC,GAAGyM,EAAE,CAAA;IACnB,IAAI,CAACnL,CAAC,GAAGA,CAAC,GAAGiG,CAAC,GAAG+E,EAAE,GAAGtC,EAAE,GAAGsC,EAAE,CAAA;IAC7B,IAAI,CAAC/E,CAAC,GAAGA,CAAC,GAAGjG,CAAC,GAAGmL,EAAE,GAAG1C,EAAE,GAAG0C,EAAE,CAAA;AAE7B,IAAA,OAAO,IAAI,CAAA;AACb,GAAA;;AAEA;AACAtE,EAAAA,KAAKA,CAACvP,CAAC,EAAEmR,EAAE,EAAEC,EAAE,EAAE;IACf,OAAO,IAAI,CAAC5B,IAAI,CAACxP,CAAC,EAAE,CAAC,EAAEmR,EAAE,EAAEC,EAAE,CAAC,CAAA;AAChC,GAAA;;AAEA;AACA1B,EAAAA,KAAKA,CAACzP,CAAC,EAAEkR,EAAE,EAAEC,EAAE,EAAE;IACf,OAAO,IAAI,CAAC5B,IAAI,CAAC,CAAC,EAAEvP,CAAC,EAAEkR,EAAE,EAAEC,EAAE,CAAC,CAAA;AAChC,GAAA;AAEAnE,EAAAA,OAAOA,GAAG;IACR,OAAO,CAAC,IAAI,CAAC3E,CAAC,EAAE,IAAI,CAACwB,CAAC,EAAE,IAAI,CAAC1C,CAAC,EAAE,IAAI,CAAC/I,CAAC,EAAE,IAAI,CAACqK,CAAC,EAAE,IAAI,CAACiG,CAAC,CAAC,CAAA;AACzD,GAAA;;AAEA;AACAlF,EAAAA,QAAQA,GAAG;AACT,IAAA,OACE,SAAS,GACT,IAAI,CAACnB,CAAC,GACN,GAAG,GACH,IAAI,CAACwB,CAAC,GACN,GAAG,GACH,IAAI,CAAC1C,CAAC,GACN,GAAG,GACH,IAAI,CAAC/I,CAAC,GACN,GAAG,GACH,IAAI,CAACqK,CAAC,GACN,GAAG,GACH,IAAI,CAACiG,CAAC,GACN,GAAG,CAAA;AAEP,GAAA;;AAEA;EACAJ,SAASA,CAAC7O,CAAC,EAAE;AACX;AACA,IAAA,IAAI+O,MAAM,CAACC,YAAY,CAAChP,CAAC,CAAC,EAAE;AAC1B,MAAA,MAAM2R,MAAM,GAAG,IAAI5C,MAAM,CAAC/O,CAAC,CAAC,CAAA;AAC5B,MAAA,OAAO2R,MAAM,CAACkC,SAAS,CAAC,IAAI,CAAC,CAAA;AAC/B,KAAA;;AAEA;AACA,IAAA,MAAMzL,CAAC,GAAG2G,MAAM,CAACS,gBAAgB,CAACxP,CAAC,CAAC,CAAA;IACpC,MAAMoU,OAAO,GAAG,IAAI,CAAA;IACpB,MAAM;AAAE9T,MAAAA,CAAC,EAAEJ,EAAE;AAAEK,MAAAA,CAAC,EAAEH,EAAAA;AAAG,KAAC,GAAG,IAAIqO,KAAK,CAACrG,CAAC,CAAClI,EAAE,EAAEkI,CAAC,CAAChI,EAAE,CAAC,CAACyO,SAAS,CAACuF,OAAO,CAAC,CAAA;;AAEjE;AACA,IAAA,MAAMC,WAAW,GAAG,IAAItF,MAAM,EAAE,CAC7BgD,UAAU,CAAC3J,CAAC,CAAC+I,EAAE,EAAE/I,CAAC,CAACiJ,EAAE,CAAC,CACtBW,UAAU,CAACoC,OAAO,CAAC,CACnBrC,UAAU,CAAC,CAAC7R,EAAE,EAAE,CAACE,EAAE,CAAC,CACpB0S,MAAM,CAAC1K,CAAC,CAAC6H,MAAM,EAAE7H,CAAC,CAAC+H,MAAM,CAAC,CAC1B8D,KAAK,CAAC7L,CAAC,CAACyH,KAAK,EAAEzH,CAAC,CAAC4H,KAAK,CAAC,CACvB+D,MAAM,CAAC3L,CAAC,CAACgI,KAAK,CAAC,CACf0D,OAAO,CAAC1L,CAAC,CAACiI,KAAK,CAAC,CAChB0B,UAAU,CAAC7R,EAAE,EAAEE,EAAE,CAAC,CAAA;;AAErB;AACA,IAAA,IAAI2P,QAAQ,CAAC3H,CAAC,CAACoI,EAAE,CAAC,IAAIT,QAAQ,CAAC3H,CAAC,CAACuI,EAAE,CAAC,EAAE;AACpC,MAAA,MAAM1Q,MAAM,GAAG,IAAIwO,KAAK,CAACvO,EAAE,EAAEE,EAAE,CAAC,CAACyO,SAAS,CAACwF,WAAW,CAAC,CAAA;AACvD;AACA;AACA,MAAA,MAAMxC,EAAE,GAAG9B,QAAQ,CAAC3H,CAAC,CAACoI,EAAE,CAAC,GAAGpI,CAAC,CAACoI,EAAE,GAAGvQ,MAAM,CAACK,CAAC,GAAG,CAAC,CAAA;AAC/C,MAAA,MAAMwR,EAAE,GAAG/B,QAAQ,CAAC3H,CAAC,CAACuI,EAAE,CAAC,GAAGvI,CAAC,CAACuI,EAAE,GAAG1Q,MAAM,CAACM,CAAC,GAAG,CAAC,CAAA;AAC/C8T,MAAAA,WAAW,CAACtC,UAAU,CAACF,EAAE,EAAEC,EAAE,CAAC,CAAA;AAChC,KAAA;;AAEA;IACAuC,WAAW,CAACtC,UAAU,CAAC3J,CAAC,CAAC0I,EAAE,EAAE1I,CAAC,CAAC4I,EAAE,CAAC,CAAA;AAClC,IAAA,OAAOqD,WAAW,CAAA;AACpB,GAAA;;AAEA;AACAxD,EAAAA,SAASA,CAACvQ,CAAC,EAAEC,CAAC,EAAE;IACd,OAAO,IAAI,CAACmO,KAAK,EAAE,CAACqD,UAAU,CAACzR,CAAC,EAAEC,CAAC,CAAC,CAAA;AACtC,GAAA;AAEAwR,EAAAA,UAAUA,CAACzR,CAAC,EAAEC,CAAC,EAAE;AACf,IAAA,IAAI,CAACyI,CAAC,IAAI1I,CAAC,IAAI,CAAC,CAAA;AAChB,IAAA,IAAI,CAAC2O,CAAC,IAAI1O,CAAC,IAAI,CAAC,CAAA;AAChB,IAAA,OAAO,IAAI,CAAA;AACb,GAAA;AAEAa,EAAAA,OAAOA,GAAG;IACR,OAAO;MACLwH,CAAC,EAAE,IAAI,CAACA,CAAC;MACTwB,CAAC,EAAE,IAAI,CAACA,CAAC;MACT1C,CAAC,EAAE,IAAI,CAACA,CAAC;MACT/I,CAAC,EAAE,IAAI,CAACA,CAAC;MACTqK,CAAC,EAAE,IAAI,CAACA,CAAC;MACTiG,CAAC,EAAE,IAAI,CAACA,CAAAA;KACT,CAAA;AACH,GAAA;AACF,CAAA;AAEO,SAASqF,GAAGA,GAAG;EACpB,OAAO,IAAIvF,MAAM,CAAC,IAAI,CAACzN,IAAI,CAACiT,MAAM,EAAE,CAAC,CAAA;AACvC,CAAA;AAEO,SAASpF,SAASA,GAAG;EAC1B,IAAI;AACF;AACJ;AACA;AACA;AACI,IAAA,IAAI,OAAO,IAAI,CAACqF,MAAM,KAAK,UAAU,IAAI,CAAC,IAAI,CAACA,MAAM,EAAE,EAAE;MACvD,MAAMC,IAAI,GAAG,IAAI,CAACA,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAAA;MAC5B,MAAMpX,CAAC,GAAGoX,IAAI,CAACnT,IAAI,CAACoT,YAAY,EAAE,CAAA;MAClCD,IAAI,CAAC5O,MAAM,EAAE,CAAA;AACb,MAAA,OAAO,IAAIkJ,MAAM,CAAC1R,CAAC,CAAC,CAAA;AACtB,KAAA;IACA,OAAO,IAAI0R,MAAM,CAAC,IAAI,CAACzN,IAAI,CAACoT,YAAY,EAAE,CAAC,CAAA;GAC5C,CAAC,OAAO1L,CAAC,EAAE;IACV2L,OAAO,CAACC,IAAI,CACV,CAAgC,6BAAA,EAAA,IAAI,CAACtT,IAAI,CAACR,QAAQ,CAAA,0BAAA,CACpD,CAAC,CAAA;IACD,OAAO,IAAIiO,MAAM,EAAE,CAAA;AACrB,GAAA;AACF,CAAA;AAEA3K,QAAQ,CAAC2K,MAAM,EAAE,QAAQ,CAAC;;AC3hBX,SAAS8F,MAAMA,GAAG;AAC/B;AACA,EAAA,IAAI,CAACA,MAAM,CAACC,KAAK,EAAE;IACjB,MAAMnT,GAAG,GAAGsB,YAAY,EAAE,CAAC8R,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAAA;IACrCpT,GAAG,CAACL,IAAI,CAACuG,KAAK,CAACI,OAAO,GAAG,CACvB,YAAY,EACZ,oBAAoB,EACpB,aAAa,EACb,YAAY,EACZ,kBAAkB,CACnB,CAACT,IAAI,CAAC,GAAG,CAAC,CAAA;AAEX7F,IAAAA,GAAG,CAACwD,IAAI,CAAC,WAAW,EAAE,OAAO,CAAC,CAAA;AAC9BxD,IAAAA,GAAG,CAACwD,IAAI,CAAC,aAAa,EAAE,MAAM,CAAC,CAAA;IAE/B,MAAM6P,IAAI,GAAGrT,GAAG,CAACqT,IAAI,EAAE,CAAC1T,IAAI,CAAA;IAE5BuT,MAAM,CAACC,KAAK,GAAG;MAAEnT,GAAG;AAAEqT,MAAAA,IAAAA;KAAM,CAAA;AAC9B,GAAA;EAEA,IAAI,CAACH,MAAM,CAACC,KAAK,CAACnT,GAAG,CAACL,IAAI,CAAC2T,UAAU,EAAE;AACrC,IAAA,MAAM7K,CAAC,GAAGrI,OAAO,CAACE,QAAQ,CAACiT,IAAI,IAAInT,OAAO,CAACE,QAAQ,CAACkT,eAAe,CAAA;IACnEN,MAAM,CAACC,KAAK,CAACnT,GAAG,CAACyT,KAAK,CAAChL,CAAC,CAAC,CAAA;AAC3B,GAAA;EAEA,OAAOyK,MAAM,CAACC,KAAK,CAAA;AACrB;;ACrBO,SAASO,WAAWA,CAACxV,GAAG,EAAE;AAC/B,EAAA,OAAO,CAACA,GAAG,CAACF,KAAK,IAAI,CAACE,GAAG,CAACD,MAAM,IAAI,CAACC,GAAG,CAACS,CAAC,IAAI,CAACT,GAAG,CAACU,CAAC,CAAA;AACtD,CAAA;AAEO,SAAS+U,WAAWA,CAAChU,IAAI,EAAE;AAChC,EAAA,OACEA,IAAI,KAAKS,OAAO,CAACE,QAAQ,IACzB,CACEF,OAAO,CAACE,QAAQ,CAACkT,eAAe,CAACI,QAAQ,IACzC,UAAUjU,IAAI,EAAE;AACd;IACA,OAAOA,IAAI,CAAC2T,UAAU,EAAE;MACtB3T,IAAI,GAAGA,IAAI,CAAC2T,UAAU,CAAA;AACxB,KAAA;AACA,IAAA,OAAO3T,IAAI,KAAKS,OAAO,CAACE,QAAQ,CAAA;GACjC,EACDiR,IAAI,CAACnR,OAAO,CAACE,QAAQ,CAACkT,eAAe,EAAE7T,IAAI,CAAC,CAAA;AAElD,CAAA;AAEe,MAAMkU,GAAG,CAAC;EACvBvQ,WAAWA,CAAC,GAAGD,IAAI,EAAE;AACnB,IAAA,IAAI,CAACkG,IAAI,CAAC,GAAGlG,IAAI,CAAC,CAAA;AACpB,GAAA;AAEAyQ,EAAAA,SAASA,GAAG;AACV;AACA,IAAA,IAAI,CAACnV,CAAC,IAAIyB,OAAO,CAACC,MAAM,CAAC0T,WAAW,CAAA;AACpC,IAAA,IAAI,CAACnV,CAAC,IAAIwB,OAAO,CAACC,MAAM,CAAC2T,WAAW,CAAA;AACpC,IAAA,OAAO,IAAIH,GAAG,CAAC,IAAI,CAAC,CAAA;AACtB,GAAA;EAEAtK,IAAIA,CAAC0D,MAAM,EAAE;IACX,MAAMD,IAAI,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAA;AACzBC,IAAAA,MAAM,GACJ,OAAOA,MAAM,KAAK,QAAQ,GACtBA,MAAM,CAACxH,KAAK,CAACJ,SAAS,CAAC,CAAC9I,GAAG,CAAC+U,UAAU,CAAC,GACvC3V,KAAK,CAACC,OAAO,CAACqR,MAAM,CAAC,GACnBA,MAAM,GACN,OAAOA,MAAM,KAAK,QAAQ,GACxB,CACEA,MAAM,CAACgH,IAAI,IAAI,IAAI,GAAGhH,MAAM,CAACgH,IAAI,GAAGhH,MAAM,CAACtO,CAAC,EAC5CsO,MAAM,CAACiH,GAAG,IAAI,IAAI,GAAGjH,MAAM,CAACiH,GAAG,GAAGjH,MAAM,CAACrO,CAAC,EAC1CqO,MAAM,CAACjP,KAAK,EACZiP,MAAM,CAAChP,MAAM,CACd,GACDoI,SAAS,CAACzJ,MAAM,KAAK,CAAC,GACpB,EAAE,CAACiB,KAAK,CAAC0T,IAAI,CAAClL,SAAS,CAAC,GACxB2G,IAAI,CAAA;IAEhB,IAAI,CAACrO,CAAC,GAAGsO,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,CAAA;IACvB,IAAI,CAACrO,CAAC,GAAGqO,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,CAAA;AACvB,IAAA,IAAI,CAACjP,KAAK,GAAG,IAAI,CAACmW,CAAC,GAAGlH,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,CAAA;AACpC,IAAA,IAAI,CAAChP,MAAM,GAAG,IAAI,CAAC+K,CAAC,GAAGiE,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,CAAA;;AAErC;IACA,IAAI,CAACmH,EAAE,GAAG,IAAI,CAACzV,CAAC,GAAG,IAAI,CAACwV,CAAC,CAAA;IACzB,IAAI,CAACE,EAAE,GAAG,IAAI,CAACzV,CAAC,GAAG,IAAI,CAACoK,CAAC,CAAA;IACzB,IAAI,CAAC8G,EAAE,GAAG,IAAI,CAACnR,CAAC,GAAG,IAAI,CAACwV,CAAC,GAAG,CAAC,CAAA;IAC7B,IAAI,CAACpE,EAAE,GAAG,IAAI,CAACnR,CAAC,GAAG,IAAI,CAACoK,CAAC,GAAG,CAAC,CAAA;AAE7B,IAAA,OAAO,IAAI,CAAA;AACb,GAAA;AAEAsL,EAAAA,QAAQA,GAAG;IACT,OAAOZ,WAAW,CAAC,IAAI,CAAC,CAAA;AAC1B,GAAA;;AAEA;EACAa,KAAKA,CAACrW,GAAG,EAAE;AACT,IAAA,MAAMS,CAAC,GAAG1B,IAAI,CAACkL,GAAG,CAAC,IAAI,CAACxJ,CAAC,EAAET,GAAG,CAACS,CAAC,CAAC,CAAA;AACjC,IAAA,MAAMC,CAAC,GAAG3B,IAAI,CAACkL,GAAG,CAAC,IAAI,CAACvJ,CAAC,EAAEV,GAAG,CAACU,CAAC,CAAC,CAAA;IACjC,MAAMZ,KAAK,GAAGf,IAAI,CAACiL,GAAG,CAAC,IAAI,CAACvJ,CAAC,GAAG,IAAI,CAACX,KAAK,EAAEE,GAAG,CAACS,CAAC,GAAGT,GAAG,CAACF,KAAK,CAAC,GAAGW,CAAC,CAAA;IAClE,MAAMV,MAAM,GAAGhB,IAAI,CAACiL,GAAG,CAAC,IAAI,CAACtJ,CAAC,GAAG,IAAI,CAACX,MAAM,EAAEC,GAAG,CAACU,CAAC,GAAGV,GAAG,CAACD,MAAM,CAAC,GAAGW,CAAC,CAAA;IAErE,OAAO,IAAIiV,GAAG,CAAClV,CAAC,EAAEC,CAAC,EAAEZ,KAAK,EAAEC,MAAM,CAAC,CAAA;AACrC,GAAA;AAEA2N,EAAAA,OAAOA,GAAG;AACR,IAAA,OAAO,CAAC,IAAI,CAACjN,CAAC,EAAE,IAAI,CAACC,CAAC,EAAE,IAAI,CAACZ,KAAK,EAAE,IAAI,CAACC,MAAM,CAAC,CAAA;AAClD,GAAA;AAEAmK,EAAAA,QAAQA,GAAG;IACT,OAAO,IAAI,CAACzJ,CAAC,GAAG,GAAG,GAAG,IAAI,CAACC,CAAC,GAAG,GAAG,GAAG,IAAI,CAACZ,KAAK,GAAG,GAAG,GAAG,IAAI,CAACC,MAAM,CAAA;AACrE,GAAA;EAEAiP,SAASA,CAACxR,CAAC,EAAE;AACX,IAAA,IAAI,EAAEA,CAAC,YAAY0R,MAAM,CAAC,EAAE;AAC1B1R,MAAAA,CAAC,GAAG,IAAI0R,MAAM,CAAC1R,CAAC,CAAC,CAAA;AACnB,KAAA;IAEA,IAAI8Y,IAAI,GAAGC,QAAQ,CAAA;IACnB,IAAIC,IAAI,GAAG,CAACD,QAAQ,CAAA;IACpB,IAAIE,IAAI,GAAGF,QAAQ,CAAA;IACnB,IAAIG,IAAI,GAAG,CAACH,QAAQ,CAAA;IAEpB,MAAMI,GAAG,GAAG,CACV,IAAI/H,KAAK,CAAC,IAAI,CAACnO,CAAC,EAAE,IAAI,CAACC,CAAC,CAAC,EACzB,IAAIkO,KAAK,CAAC,IAAI,CAACsH,EAAE,EAAE,IAAI,CAACxV,CAAC,CAAC,EAC1B,IAAIkO,KAAK,CAAC,IAAI,CAACnO,CAAC,EAAE,IAAI,CAAC0V,EAAE,CAAC,EAC1B,IAAIvH,KAAK,CAAC,IAAI,CAACsH,EAAE,EAAE,IAAI,CAACC,EAAE,CAAC,CAC5B,CAAA;AAEDQ,IAAAA,GAAG,CAACrO,OAAO,CAAC,UAAUxC,CAAC,EAAE;AACvBA,MAAAA,CAAC,GAAGA,CAAC,CAACkJ,SAAS,CAACxR,CAAC,CAAC,CAAA;MAClB8Y,IAAI,GAAGvX,IAAI,CAACkL,GAAG,CAACqM,IAAI,EAAExQ,CAAC,CAACrF,CAAC,CAAC,CAAA;MAC1B+V,IAAI,GAAGzX,IAAI,CAACiL,GAAG,CAACwM,IAAI,EAAE1Q,CAAC,CAACrF,CAAC,CAAC,CAAA;MAC1BgW,IAAI,GAAG1X,IAAI,CAACkL,GAAG,CAACwM,IAAI,EAAE3Q,CAAC,CAACpF,CAAC,CAAC,CAAA;MAC1BgW,IAAI,GAAG3X,IAAI,CAACiL,GAAG,CAAC0M,IAAI,EAAE5Q,CAAC,CAACpF,CAAC,CAAC,CAAA;AAC5B,KAAC,CAAC,CAAA;AAEF,IAAA,OAAO,IAAIiV,GAAG,CAACW,IAAI,EAAEG,IAAI,EAAED,IAAI,GAAGF,IAAI,EAAEI,IAAI,GAAGD,IAAI,CAAC,CAAA;AACtD,GAAA;AACF,CAAA;AAEA,SAASG,MAAMA,CAACvO,EAAE,EAAEwO,SAAS,EAAEC,KAAK,EAAE;AACpC,EAAA,IAAI9W,GAAG,CAAA;EAEP,IAAI;AACF;AACAA,IAAAA,GAAG,GAAG6W,SAAS,CAACxO,EAAE,CAAC5G,IAAI,CAAC,CAAA;;AAExB;AACA;AACA,IAAA,IAAI+T,WAAW,CAACxV,GAAG,CAAC,IAAI,CAACyV,WAAW,CAACpN,EAAE,CAAC5G,IAAI,CAAC,EAAE;AAC7C,MAAA,MAAM,IAAIoK,KAAK,CAAC,wBAAwB,CAAC,CAAA;AAC3C,KAAA;GACD,CAAC,OAAO1C,CAAC,EAAE;AACV;AACAnJ,IAAAA,GAAG,GAAG8W,KAAK,CAACzO,EAAE,CAAC,CAAA;AACjB,GAAA;AAEA,EAAA,OAAOrI,GAAG,CAAA;AACZ,CAAA;AAEO,SAASC,IAAIA,GAAG;AACrB;EACA,MAAM8W,OAAO,GAAItV,IAAI,IAAKA,IAAI,CAACsV,OAAO,EAAE,CAAA;;AAExC;AACA;EACA,MAAMD,KAAK,GAAIzO,EAAE,IAAK;IACpB,IAAI;AACF,MAAA,MAAMwG,KAAK,GAAGxG,EAAE,CAACwG,KAAK,EAAE,CAAC0G,KAAK,CAACP,MAAM,EAAE,CAAClT,GAAG,CAAC,CAAC8G,IAAI,EAAE,CAAA;MACnD,MAAM5I,GAAG,GAAG6O,KAAK,CAACpN,IAAI,CAACsV,OAAO,EAAE,CAAA;MAChClI,KAAK,CAAC7I,MAAM,EAAE,CAAA;AACd,MAAA,OAAOhG,GAAG,CAAA;KACX,CAAC,OAAOmJ,CAAC,EAAE;AACV;AACA,MAAA,MAAM,IAAI0C,KAAK,CACb,CACExD,yBAAAA,EAAAA,EAAE,CAAC5G,IAAI,CAACR,QAAQ,CAAA,mBAAA,EACIkI,CAAC,CAACe,QAAQ,EAAE,EACpC,CAAC,CAAA;AACH,KAAA;GACD,CAAA;EAED,MAAMlK,GAAG,GAAG4W,MAAM,CAAC,IAAI,EAAEG,OAAO,EAAED,KAAK,CAAC,CAAA;AACxC,EAAA,MAAM7W,IAAI,GAAG,IAAI0V,GAAG,CAAC3V,GAAG,CAAC,CAAA;AAEzB,EAAA,OAAOC,IAAI,CAAA;AACb,CAAA;AAEO,SAAS+W,IAAIA,CAAC3O,EAAE,EAAE;EACvB,MAAM4O,OAAO,GAAIxV,IAAI,IAAKA,IAAI,CAACyV,qBAAqB,EAAE,CAAA;EACtD,MAAMJ,KAAK,GAAIzO,EAAE,IAAK;AACpB;AACA;IACA,MAAM,IAAIwD,KAAK,CACb,CAA4BxD,yBAAAA,EAAAA,EAAE,CAAC5G,IAAI,CAACR,QAAQ,CAAA,iBAAA,CAC9C,CAAC,CAAA;GACF,CAAA;EAED,MAAMjB,GAAG,GAAG4W,MAAM,CAAC,IAAI,EAAEK,OAAO,EAAEH,KAAK,CAAC,CAAA;AACxC,EAAA,MAAME,IAAI,GAAG,IAAIrB,GAAG,CAAC3V,GAAG,CAAC,CAAA;;AAEzB;AACA,EAAA,IAAIqI,EAAE,EAAE;AACN,IAAA,OAAO2O,IAAI,CAAChI,SAAS,CAAC3G,EAAE,CAACiH,SAAS,EAAE,CAACC,QAAQ,EAAE,CAAC,CAAA;AAClD,GAAA;;AAEA;AACA;AACA,EAAA,OAAOyH,IAAI,CAACpB,SAAS,EAAE,CAAA;AACzB,CAAA;;AAEA;AACO,SAASuB,MAAMA,CAAC1W,CAAC,EAAEC,CAAC,EAAE;AAC3B,EAAA,MAAMV,GAAG,GAAG,IAAI,CAACC,IAAI,EAAE,CAAA;AAEvB,EAAA,OACEQ,CAAC,GAAGT,GAAG,CAACS,CAAC,IAAIC,CAAC,GAAGV,GAAG,CAACU,CAAC,IAAID,CAAC,GAAGT,GAAG,CAACS,CAAC,GAAGT,GAAG,CAACF,KAAK,IAAIY,CAAC,GAAGV,GAAG,CAACU,CAAC,GAAGV,GAAG,CAACD,MAAM,CAAA;AAE7E,CAAA;AAEAzC,eAAe,CAAC;AACd8Z,EAAAA,OAAO,EAAE;IACPA,OAAOA,CAAC3W,CAAC,EAAEC,CAAC,EAAEZ,KAAK,EAAEC,MAAM,EAAE;AAC3B;AACA,MAAA,IAAIU,CAAC,IAAI,IAAI,EAAE,OAAO,IAAIkV,GAAG,CAAC,IAAI,CAACrQ,IAAI,CAAC,SAAS,CAAC,CAAC,CAAA;;AAEnD;AACA,MAAA,OAAO,IAAI,CAACA,IAAI,CAAC,SAAS,EAAE,IAAIqQ,GAAG,CAAClV,CAAC,EAAEC,CAAC,EAAEZ,KAAK,EAAEC,MAAM,CAAC,CAAC,CAAA;KAC1D;AAEDsX,IAAAA,IAAIA,CAACC,KAAK,EAAEjI,KAAK,EAAE;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;MACA,IAAI;QAAEvP,KAAK;AAAEC,QAAAA,MAAAA;OAAQ,GAAG,IAAI,CAACuF,IAAI,CAAC,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC,CAAA;;AAEtD;AACA;AACA,MAAA,IACG,CAACxF,KAAK,IAAI,CAACC,MAAM,IAClB,OAAOD,KAAK,KAAK,QAAQ,IACzB,OAAOC,MAAM,KAAK,QAAQ,EAC1B;AACAD,QAAAA,KAAK,GAAG,IAAI,CAAC2B,IAAI,CAAC8V,WAAW,CAAA;AAC7BxX,QAAAA,MAAM,GAAG,IAAI,CAAC0B,IAAI,CAAC+V,YAAY,CAAA;AACjC,OAAA;;AAEA;AACA,MAAA,IAAI,CAAC1X,KAAK,IAAI,CAACC,MAAM,EAAE;AACrB,QAAA,MAAM,IAAI8L,KAAK,CACb,2HACF,CAAC,CAAA;AACH,OAAA;AAEA,MAAA,MAAM7C,CAAC,GAAG,IAAI,CAACoO,OAAO,EAAE,CAAA;AAExB,MAAA,MAAMK,KAAK,GAAG3X,KAAK,GAAGkJ,CAAC,CAAClJ,KAAK,CAAA;AAC7B,MAAA,MAAM4X,KAAK,GAAG3X,MAAM,GAAGiJ,CAAC,CAACjJ,MAAM,CAAA;MAC/B,MAAMsX,IAAI,GAAGtY,IAAI,CAACkL,GAAG,CAACwN,KAAK,EAAEC,KAAK,CAAC,CAAA;MAEnC,IAAIJ,KAAK,IAAI,IAAI,EAAE;AACjB,QAAA,OAAOD,IAAI,CAAA;AACb,OAAA;AAEA,MAAA,IAAIM,UAAU,GAAGN,IAAI,GAAGC,KAAK,CAAA;;AAE7B;AACA;MACA,IAAIK,UAAU,KAAKpB,QAAQ,EAAEoB,UAAU,GAAGC,MAAM,CAACC,gBAAgB,GAAG,GAAG,CAAA;MAEvExI,KAAK,GACHA,KAAK,IAAI,IAAIT,KAAK,CAAC9O,KAAK,GAAG,CAAC,GAAG2X,KAAK,GAAGzO,CAAC,CAACvI,CAAC,EAAEV,MAAM,GAAG,CAAC,GAAG2X,KAAK,GAAG1O,CAAC,CAACtI,CAAC,CAAC,CAAA;AAEvE,MAAA,MAAMV,GAAG,GAAG,IAAI2V,GAAG,CAAC3M,CAAC,CAAC,CAACgG,SAAS,CAC9B,IAAIE,MAAM,CAAC;AAAEmB,QAAAA,KAAK,EAAEsH,UAAU;AAAEvX,QAAAA,MAAM,EAAEiP,KAAAA;AAAM,OAAC,CACjD,CAAC,CAAA;AAED,MAAA,OAAO,IAAI,CAAC+H,OAAO,CAACpX,GAAG,CAAC,CAAA;AAC1B,KAAA;AACF,GAAA;AACF,CAAC,CAAC,CAAA;AAEFuE,QAAQ,CAACoR,GAAG,EAAE,KAAK,CAAC;;AC5QpB;;AAEA,MAAMmC,IAAI,SAASra,KAAK,CAAC;AACvB2H,EAAAA,WAAWA,CAAC2S,GAAG,GAAG,EAAE,EAAE,GAAG5S,IAAI,EAAE;AAC7B,IAAA,KAAK,CAAC4S,GAAG,EAAE,GAAG5S,IAAI,CAAC,CAAA;AACnB,IAAA,IAAI,OAAO4S,GAAG,KAAK,QAAQ,EAAE,OAAO,IAAI,CAAA;IACxC,IAAI,CAACrZ,MAAM,GAAG,CAAC,CAAA;AACf,IAAA,IAAI,CAACN,IAAI,CAAC,GAAG2Z,GAAG,CAAC,CAAA;AACnB,GAAA;AACF,CAAA;AAWA/S,MAAM,CAAC,CAAC8S,IAAI,CAAC,EAAE;AACbE,EAAAA,IAAIA,CAACC,cAAc,EAAE,GAAG9S,IAAI,EAAE;AAC5B,IAAA,IAAI,OAAO8S,cAAc,KAAK,UAAU,EAAE;MACxC,OAAO,IAAI,CAAC5Z,GAAG,CAAC,CAACgK,EAAE,EAAE7J,CAAC,EAAEuZ,GAAG,KAAK;QAC9B,OAAOE,cAAc,CAAC5E,IAAI,CAAChL,EAAE,EAAEA,EAAE,EAAE7J,CAAC,EAAEuZ,GAAG,CAAC,CAAA;AAC5C,OAAC,CAAC,CAAA;AACJ,KAAC,MAAM;AACL,MAAA,OAAO,IAAI,CAAC1Z,GAAG,CAAEgK,EAAE,IAAK;AACtB,QAAA,OAAOA,EAAE,CAAC4P,cAAc,CAAC,CAAC,GAAG9S,IAAI,CAAC,CAAA;AACpC,OAAC,CAAC,CAAA;AACJ,KAAA;GACD;AAEDuI,EAAAA,OAAOA,GAAG;IACR,OAAOjQ,KAAK,CAACgH,SAAS,CAACyT,MAAM,CAAC7S,KAAK,CAAC,EAAE,EAAE,IAAI,CAAC,CAAA;AAC/C,GAAA;AACF,CAAC,CAAC,CAAA;AAEF,MAAM8S,QAAQ,GAAG,CAAC,SAAS,EAAE,aAAa,EAAE,MAAM,CAAC,CAAA;AAEnDL,IAAI,CAAC9S,MAAM,GAAG,UAAU5H,OAAO,EAAE;EAC/BA,OAAO,GAAGA,OAAO,CAACgb,MAAM,CAAC,CAACC,GAAG,EAAE9a,IAAI,KAAK;AACtC;IACA,IAAI4a,QAAQ,CAACtX,QAAQ,CAACtD,IAAI,CAAC,EAAE,OAAO8a,GAAG,CAAA;;AAEvC;IACA,IAAI9a,IAAI,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE,OAAO8a,GAAG,CAAA;;AAE/B;AACA,IAAA,IAAI9a,IAAI,IAAIE,KAAK,CAACgH,SAAS,EAAE;MAC3B4T,GAAG,CAAC,GAAG,GAAG9a,IAAI,CAAC,GAAGE,KAAK,CAACgH,SAAS,CAAClH,IAAI,CAAC,CAAA;AACzC,KAAA;;AAEA;AACA8a,IAAAA,GAAG,CAAC9a,IAAI,CAAC,GAAG,UAAU,GAAG+a,KAAK,EAAE;MAC9B,OAAO,IAAI,CAACN,IAAI,CAACza,IAAI,EAAE,GAAG+a,KAAK,CAAC,CAAA;KACjC,CAAA;AACD,IAAA,OAAOD,GAAG,CAAA;GACX,EAAE,EAAE,CAAC,CAAA;AAENrT,EAAAA,MAAM,CAAC,CAAC8S,IAAI,CAAC,EAAE1a,OAAO,CAAC,CAAA;AACzB,CAAC;;ACzDc,SAASmb,QAAQA,CAACC,KAAK,EAAEhT,MAAM,EAAE;AAC9C,EAAA,OAAO,IAAIsS,IAAI,CACbzZ,GAAG,CAAC,CAACmH,MAAM,IAAItD,OAAO,CAACE,QAAQ,EAAEqW,gBAAgB,CAACD,KAAK,CAAC,EAAE,UAAU/W,IAAI,EAAE;IACxE,OAAOwC,KAAK,CAACxC,IAAI,CAAC,CAAA;AACpB,GAAC,CACH,CAAC,CAAA;AACH,CAAA;;AAEA;AACO,SAASiX,IAAIA,CAACF,KAAK,EAAE;AAC1B,EAAA,OAAOD,QAAQ,CAACC,KAAK,EAAE,IAAI,CAAC/W,IAAI,CAAC,CAAA;AACnC,CAAA;AAEO,SAASkX,OAAOA,CAACH,KAAK,EAAE;EAC7B,OAAOvU,KAAK,CAAC,IAAI,CAACxC,IAAI,CAAC8B,aAAa,CAACiV,KAAK,CAAC,CAAC,CAAA;AAC9C;;AChBA,IAAII,UAAU,GAAG,CAAC,CAAA;AACLC,MAAAA,YAAY,GAAG,GAAE;AAEvB,SAASC,SAASA,CAAC5U,QAAQ,EAAE;AAClC,EAAA,IAAI6U,CAAC,GAAG7U,QAAQ,CAAC8U,cAAc,EAAE,CAAA;;AAEjC;EACA,IAAID,CAAC,KAAK7W,OAAO,CAACC,MAAM,EAAE4W,CAAC,GAAGF,YAAY,CAAA;EAC1C,IAAI,CAACE,CAAC,CAACE,MAAM,EAAEF,CAAC,CAACE,MAAM,GAAG,EAAE,CAAA;EAC5B,OAAOF,CAAC,CAACE,MAAM,CAAA;AACjB,CAAA;AAEO,SAASC,cAAcA,CAAChV,QAAQ,EAAE;AACvC,EAAA,OAAOA,QAAQ,CAACgV,cAAc,EAAE,CAAA;AAClC,CAAA;AAEO,SAASC,WAAWA,CAACjV,QAAQ,EAAE;AACpC,EAAA,IAAI6U,CAAC,GAAG7U,QAAQ,CAAC8U,cAAc,EAAE,CAAA;EACjC,IAAID,CAAC,KAAK7W,OAAO,CAACC,MAAM,EAAE4W,CAAC,GAAGF,YAAY,CAAA;EAC1C,IAAIE,CAAC,CAACE,MAAM,EAAEF,CAAC,CAACE,MAAM,GAAG,EAAE,CAAA;AAC7B,CAAA;;AAEA;AACO,SAASG,EAAEA,CAAC3X,IAAI,EAAEwX,MAAM,EAAEI,QAAQ,EAAEC,OAAO,EAAEC,OAAO,EAAE;EAC3D,MAAMxO,CAAC,GAAGsO,QAAQ,CAACG,IAAI,CAACF,OAAO,IAAI7X,IAAI,CAAC,CAAA;AACxC,EAAA,MAAMyC,QAAQ,GAAGd,YAAY,CAAC3B,IAAI,CAAC,CAAA;AACnC,EAAA,MAAMgY,GAAG,GAAGX,SAAS,CAAC5U,QAAQ,CAAC,CAAA;AAC/B,EAAA,MAAM6U,CAAC,GAAGG,cAAc,CAAChV,QAAQ,CAAC,CAAA;;AAElC;AACA+U,EAAAA,MAAM,GAAGxb,KAAK,CAACC,OAAO,CAACub,MAAM,CAAC,GAAGA,MAAM,GAAGA,MAAM,CAAC1R,KAAK,CAACJ,SAAS,CAAC,CAAA;;AAEjE;AACA,EAAA,IAAI,CAACkS,QAAQ,CAACK,gBAAgB,EAAE;AAC9BL,IAAAA,QAAQ,CAACK,gBAAgB,GAAG,EAAEd,UAAU,CAAA;AAC1C,GAAA;AAEAK,EAAAA,MAAM,CAAC3Q,OAAO,CAAC,UAAUqR,KAAK,EAAE;IAC9B,MAAMC,EAAE,GAAGD,KAAK,CAACpS,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAA;AAC9B,IAAA,MAAMrE,EAAE,GAAGyW,KAAK,CAACpS,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,CAAA;;AAErC;IACAkS,GAAG,CAACG,EAAE,CAAC,GAAGH,GAAG,CAACG,EAAE,CAAC,IAAI,EAAE,CAAA;AACvBH,IAAAA,GAAG,CAACG,EAAE,CAAC,CAAC1W,EAAE,CAAC,GAAGuW,GAAG,CAACG,EAAE,CAAC,CAAC1W,EAAE,CAAC,IAAI,EAAE,CAAA;;AAE/B;AACAuW,IAAAA,GAAG,CAACG,EAAE,CAAC,CAAC1W,EAAE,CAAC,CAACmW,QAAQ,CAACK,gBAAgB,CAAC,GAAG3O,CAAC,CAAA;;AAE1C;IACAgO,CAAC,CAACc,gBAAgB,CAACD,EAAE,EAAE7O,CAAC,EAAEwO,OAAO,IAAI,KAAK,CAAC,CAAA;AAC7C,GAAC,CAAC,CAAA;AACJ,CAAA;;AAEA;AACO,SAASO,GAAGA,CAACrY,IAAI,EAAEwX,MAAM,EAAEI,QAAQ,EAAEE,OAAO,EAAE;AACnD,EAAA,MAAMrV,QAAQ,GAAGd,YAAY,CAAC3B,IAAI,CAAC,CAAA;AACnC,EAAA,MAAMgY,GAAG,GAAGX,SAAS,CAAC5U,QAAQ,CAAC,CAAA;AAC/B,EAAA,MAAM6U,CAAC,GAAGG,cAAc,CAAChV,QAAQ,CAAC,CAAA;;AAElC;AACA,EAAA,IAAI,OAAOmV,QAAQ,KAAK,UAAU,EAAE;IAClCA,QAAQ,GAAGA,QAAQ,CAACK,gBAAgB,CAAA;IACpC,IAAI,CAACL,QAAQ,EAAE,OAAA;AACjB,GAAA;;AAEA;AACAJ,EAAAA,MAAM,GAAGxb,KAAK,CAACC,OAAO,CAACub,MAAM,CAAC,GAAGA,MAAM,GAAG,CAACA,MAAM,IAAI,EAAE,EAAE1R,KAAK,CAACJ,SAAS,CAAC,CAAA;AAEzE8R,EAAAA,MAAM,CAAC3Q,OAAO,CAAC,UAAUqR,KAAK,EAAE;AAC9B,IAAA,MAAMC,EAAE,GAAGD,KAAK,IAAIA,KAAK,CAACpS,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAA;AACvC,IAAA,MAAMrE,EAAE,GAAGyW,KAAK,IAAIA,KAAK,CAACpS,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAA;IACvC,IAAIwS,SAAS,EAAEhP,CAAC,CAAA;AAEhB,IAAA,IAAIsO,QAAQ,EAAE;AACZ;AACA,MAAA,IAAII,GAAG,CAACG,EAAE,CAAC,IAAIH,GAAG,CAACG,EAAE,CAAC,CAAC1W,EAAE,IAAI,GAAG,CAAC,EAAE;AACjC;QACA6V,CAAC,CAACiB,mBAAmB,CACnBJ,EAAE,EACFH,GAAG,CAACG,EAAE,CAAC,CAAC1W,EAAE,IAAI,GAAG,CAAC,CAACmW,QAAQ,CAAC,EAC5BE,OAAO,IAAI,KACb,CAAC,CAAA;QAED,OAAOE,GAAG,CAACG,EAAE,CAAC,CAAC1W,EAAE,IAAI,GAAG,CAAC,CAACmW,QAAQ,CAAC,CAAA;AACrC,OAAA;AACF,KAAC,MAAM,IAAIO,EAAE,IAAI1W,EAAE,EAAE;AACnB;AACA,MAAA,IAAIuW,GAAG,CAACG,EAAE,CAAC,IAAIH,GAAG,CAACG,EAAE,CAAC,CAAC1W,EAAE,CAAC,EAAE;QAC1B,KAAK6H,CAAC,IAAI0O,GAAG,CAACG,EAAE,CAAC,CAAC1W,EAAE,CAAC,EAAE;AACrB4W,UAAAA,GAAG,CAACf,CAAC,EAAE,CAACa,EAAE,EAAE1W,EAAE,CAAC,CAACyE,IAAI,CAAC,GAAG,CAAC,EAAEoD,CAAC,CAAC,CAAA;AAC/B,SAAA;AAEA,QAAA,OAAO0O,GAAG,CAACG,EAAE,CAAC,CAAC1W,EAAE,CAAC,CAAA;AACpB,OAAA;KACD,MAAM,IAAIA,EAAE,EAAE;AACb;MACA,KAAKyW,KAAK,IAAIF,GAAG,EAAE;AACjB,QAAA,KAAKM,SAAS,IAAIN,GAAG,CAACE,KAAK,CAAC,EAAE;UAC5B,IAAIzW,EAAE,KAAK6W,SAAS,EAAE;AACpBD,YAAAA,GAAG,CAACf,CAAC,EAAE,CAACY,KAAK,EAAEzW,EAAE,CAAC,CAACyE,IAAI,CAAC,GAAG,CAAC,CAAC,CAAA;AAC/B,WAAA;AACF,SAAA;AACF,OAAA;KACD,MAAM,IAAIiS,EAAE,EAAE;AACb;AACA,MAAA,IAAIH,GAAG,CAACG,EAAE,CAAC,EAAE;AACX,QAAA,KAAKG,SAAS,IAAIN,GAAG,CAACG,EAAE,CAAC,EAAE;AACzBE,UAAAA,GAAG,CAACf,CAAC,EAAE,CAACa,EAAE,EAAEG,SAAS,CAAC,CAACpS,IAAI,CAAC,GAAG,CAAC,CAAC,CAAA;AACnC,SAAA;QAEA,OAAO8R,GAAG,CAACG,EAAE,CAAC,CAAA;AAChB,OAAA;AACF,KAAC,MAAM;AACL;MACA,KAAKD,KAAK,IAAIF,GAAG,EAAE;AACjBK,QAAAA,GAAG,CAACf,CAAC,EAAEY,KAAK,CAAC,CAAA;AACf,OAAA;MAEAR,WAAW,CAACjV,QAAQ,CAAC,CAAA;AACvB,KAAA;AACF,GAAC,CAAC,CAAA;AACJ,CAAA;AAEO,SAAS+V,QAAQA,CAACxY,IAAI,EAAEkY,KAAK,EAAExY,IAAI,EAAEoY,OAAO,EAAE;AACnD,EAAA,MAAMR,CAAC,GAAGG,cAAc,CAACzX,IAAI,CAAC,CAAA;;AAE9B;AACA,EAAA,IAAIkY,KAAK,YAAYzX,OAAO,CAACC,MAAM,CAAC+X,KAAK,EAAE;AACzCnB,IAAAA,CAAC,CAACoB,aAAa,CAACR,KAAK,CAAC,CAAA;AACxB,GAAC,MAAM;IACLA,KAAK,GAAG,IAAIzX,OAAO,CAACC,MAAM,CAACiY,WAAW,CAACT,KAAK,EAAE;AAC5CU,MAAAA,MAAM,EAAElZ,IAAI;AACZmZ,MAAAA,UAAU,EAAE,IAAI;MAChB,GAAGf,OAAAA;AACL,KAAC,CAAC,CAAA;AACFR,IAAAA,CAAC,CAACoB,aAAa,CAACR,KAAK,CAAC,CAAA;AACxB,GAAA;AACA,EAAA,OAAOA,KAAK,CAAA;AACd;;AC1Ie,MAAMY,WAAW,SAASzX,IAAI,CAAC;EAC5C+W,gBAAgBA,GAAG,EAAC;AAEpBI,EAAAA,QAAQA,CAACN,KAAK,EAAExY,IAAI,EAAEoY,OAAO,EAAE;IAC7B,OAAOU,QAAQ,CAAC,IAAI,EAAEN,KAAK,EAAExY,IAAI,EAAEoY,OAAO,CAAC,CAAA;AAC7C,GAAA;EAEAY,aAAaA,CAACR,KAAK,EAAE;IACnB,MAAMF,GAAG,GAAG,IAAI,CAACT,cAAc,EAAE,CAACC,MAAM,CAAA;AACxC,IAAA,IAAI,CAACQ,GAAG,EAAE,OAAO,IAAI,CAAA;AAErB,IAAA,MAAMR,MAAM,GAAGQ,GAAG,CAACE,KAAK,CAACa,IAAI,CAAC,CAAA;AAE9B,IAAA,KAAK,MAAMhc,CAAC,IAAIya,MAAM,EAAE;AACtB,MAAA,KAAK,MAAMwB,CAAC,IAAIxB,MAAM,CAACza,CAAC,CAAC,EAAE;QACzBya,MAAM,CAACza,CAAC,CAAC,CAACic,CAAC,CAAC,CAACd,KAAK,CAAC,CAAA;AACrB,OAAA;AACF,KAAA;IAEA,OAAO,CAACA,KAAK,CAACe,gBAAgB,CAAA;AAChC,GAAA;;AAEA;AACAC,EAAAA,IAAIA,CAAChB,KAAK,EAAExY,IAAI,EAAEoY,OAAO,EAAE;IACzB,IAAI,CAACU,QAAQ,CAACN,KAAK,EAAExY,IAAI,EAAEoY,OAAO,CAAC,CAAA;AACnC,IAAA,OAAO,IAAI,CAAA;AACb,GAAA;AAEAP,EAAAA,cAAcA,GAAG;AACf,IAAA,OAAO,IAAI,CAAA;AACb,GAAA;AAEAE,EAAAA,cAAcA,GAAG;AACf,IAAA,OAAO,IAAI,CAAA;AACb,GAAA;;AAEA;AACAY,EAAAA,GAAGA,CAACH,KAAK,EAAEN,QAAQ,EAAEE,OAAO,EAAE;IAC5BO,GAAG,CAAC,IAAI,EAAEH,KAAK,EAAEN,QAAQ,EAAEE,OAAO,CAAC,CAAA;AACnC,IAAA,OAAO,IAAI,CAAA;AACb,GAAA;;AAEA;EACAH,EAAEA,CAACO,KAAK,EAAEN,QAAQ,EAAEC,OAAO,EAAEC,OAAO,EAAE;IACpCH,EAAE,CAAC,IAAI,EAAEO,KAAK,EAAEN,QAAQ,EAAEC,OAAO,EAAEC,OAAO,CAAC,CAAA;AAC3C,IAAA,OAAO,IAAI,CAAA;AACb,GAAA;EAEAS,mBAAmBA,GAAG,EAAC;AACzB,CAAA;AAEAzV,QAAQ,CAACgW,WAAW,EAAE,aAAa,CAAC;;ACvD7B,SAASK,IAAIA,GAAG,EAAC;;AAExB;AACO,MAAMC,QAAQ,GAAG;AACtBC,EAAAA,QAAQ,EAAE,GAAG;AACbC,EAAAA,IAAI,EAAE,GAAG;AACTC,EAAAA,KAAK,EAAE,CAAA;AACT,CAAC,CAAA;;AAED;AACO,MAAM1C,KAAK,GAAG;AACnB;AACA,EAAA,cAAc,EAAE,CAAC;AACjB,EAAA,gBAAgB,EAAE,CAAC;AACnB,EAAA,cAAc,EAAE,CAAC;AACjB,EAAA,iBAAiB,EAAE,OAAO;AAC1B,EAAA,gBAAgB,EAAE,MAAM;AACxB2C,EAAAA,IAAI,EAAE,SAAS;AACfC,EAAAA,MAAM,EAAE,SAAS;AACjBC,EAAAA,OAAO,EAAE,CAAC;AAEV;AACA1a,EAAAA,CAAC,EAAE,CAAC;AACJC,EAAAA,CAAC,EAAE,CAAC;AACJkR,EAAAA,EAAE,EAAE,CAAC;AACLC,EAAAA,EAAE,EAAE,CAAC;AAEL;AACA/R,EAAAA,KAAK,EAAE,CAAC;AACRC,EAAAA,MAAM,EAAE,CAAC;AAET;AACAb,EAAAA,CAAC,EAAE,CAAC;AACJoS,EAAAA,EAAE,EAAE,CAAC;AACLE,EAAAA,EAAE,EAAE,CAAC;AAEL;AACA4J,EAAAA,MAAM,EAAE,CAAC;AACT,EAAA,cAAc,EAAE,CAAC;AACjB,EAAA,YAAY,EAAE,SAAS;AAEvB;AACA,EAAA,aAAa,EAAE,OAAA;AACjB,CAAC;;;;;;;;;ACzCc,MAAMC,QAAQ,SAAS5d,KAAK,CAAC;EAC1C2H,WAAWA,CAAC,GAAGD,IAAI,EAAE;IACnB,KAAK,CAAC,GAAGA,IAAI,CAAC,CAAA;AACd,IAAA,IAAI,CAACkG,IAAI,CAAC,GAAGlG,IAAI,CAAC,CAAA;AACpB,GAAA;AAEA0J,EAAAA,KAAKA,GAAG;AACN,IAAA,OAAO,IAAI,IAAI,CAACzJ,WAAW,CAAC,IAAI,CAAC,CAAA;AACnC,GAAA;EAEAiG,IAAIA,CAAC0M,GAAG,EAAE;AACR;AACA,IAAA,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE,OAAO,IAAI,CAAA;IACxC,IAAI,CAACrZ,MAAM,GAAG,CAAC,CAAA;IACf,IAAI,CAACN,IAAI,CAAC,GAAG,IAAI,CAAC8K,KAAK,CAAC6O,GAAG,CAAC,CAAC,CAAA;AAC7B,IAAA,OAAO,IAAI,CAAA;AACb,GAAA;;AAEA;AACA7O,EAAAA,KAAKA,CAAC5K,KAAK,GAAG,EAAE,EAAE;AAChB;AACA,IAAA,IAAIA,KAAK,YAAYb,KAAK,EAAE,OAAOa,KAAK,CAAA;AAExC,IAAA,OAAOA,KAAK,CAACgJ,IAAI,EAAE,CAACC,KAAK,CAACJ,SAAS,CAAC,CAAC9I,GAAG,CAAC+U,UAAU,CAAC,CAAA;AACtD,GAAA;AAEA1F,EAAAA,OAAOA,GAAG;IACR,OAAOjQ,KAAK,CAACgH,SAAS,CAACyT,MAAM,CAAC7S,KAAK,CAAC,EAAE,EAAE,IAAI,CAAC,CAAA;AAC/C,GAAA;AAEAiW,EAAAA,KAAKA,GAAG;AACN,IAAA,OAAO,IAAIpd,GAAG,CAAC,IAAI,CAAC,CAAA;AACtB,GAAA;AAEAgM,EAAAA,QAAQA,GAAG;AACT,IAAA,OAAO,IAAI,CAACvC,IAAI,CAAC,GAAG,CAAC,CAAA;AACvB,GAAA;;AAEA;AACApG,EAAAA,OAAOA,GAAG;IACR,MAAM2G,GAAG,GAAG,EAAE,CAAA;AACdA,IAAAA,GAAG,CAAC9J,IAAI,CAAC,GAAG,IAAI,CAAC,CAAA;AACjB,IAAA,OAAO8J,GAAG,CAAA;AACZ,GAAA;AACF;;AC5CA;AACe,MAAMqT,SAAS,CAAC;AAC7B;EACAnW,WAAWA,CAAC,GAAGD,IAAI,EAAE;AACnB,IAAA,IAAI,CAACkG,IAAI,CAAC,GAAGlG,IAAI,CAAC,CAAA;AACpB,GAAA;EAEAqW,OAAOA,CAACC,IAAI,EAAE;IACZ,OAAO,IAAIF,SAAS,CAAC,IAAI,CAACG,KAAK,EAAED,IAAI,CAAC,CAAA;AACxC,GAAA;;AAEA;EACAE,MAAMA,CAACC,MAAM,EAAE;AACbA,IAAAA,MAAM,GAAG,IAAIL,SAAS,CAACK,MAAM,CAAC,CAAA;AAC9B,IAAA,OAAO,IAAIL,SAAS,CAAC,IAAI,GAAGK,MAAM,EAAE,IAAI,CAACH,IAAI,IAAIG,MAAM,CAACH,IAAI,CAAC,CAAA;AAC/D,GAAA;AAEApQ,EAAAA,IAAIA,CAACqQ,KAAK,EAAED,IAAI,EAAE;AAChBA,IAAAA,IAAI,GAAGhe,KAAK,CAACC,OAAO,CAACge,KAAK,CAAC,GAAGA,KAAK,CAAC,CAAC,CAAC,GAAGD,IAAI,CAAA;AAC7CC,IAAAA,KAAK,GAAGje,KAAK,CAACC,OAAO,CAACge,KAAK,CAAC,GAAGA,KAAK,CAAC,CAAC,CAAC,GAAGA,KAAK,CAAA;;AAE/C;IACA,IAAI,CAACA,KAAK,GAAG,CAAC,CAAA;AACd,IAAA,IAAI,CAACD,IAAI,GAAGA,IAAI,IAAI,EAAE,CAAA;;AAEtB;AACA,IAAA,IAAI,OAAOC,KAAK,KAAK,QAAQ,EAAE;AAC7B;MACA,IAAI,CAACA,KAAK,GAAGG,KAAK,CAACH,KAAK,CAAC,GACrB,CAAC,GACD,CAACxL,QAAQ,CAACwL,KAAK,CAAC,GACdA,KAAK,GAAG,CAAC,GACP,CAAC,MAAM,GACP,CAAC,MAAM,GACTA,KAAK,CAAA;AACb,KAAC,MAAM,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;AACpCD,MAAAA,IAAI,GAAGC,KAAK,CAACI,KAAK,CAACtV,aAAa,CAAC,CAAA;AAEjC,MAAA,IAAIiV,IAAI,EAAE;AACR;QACA,IAAI,CAACC,KAAK,GAAGtI,UAAU,CAACqI,IAAI,CAAC,CAAC,CAAC,CAAC,CAAA;;AAEhC;AACA,QAAA,IAAIA,IAAI,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;UACnB,IAAI,CAACC,KAAK,IAAI,GAAG,CAAA;SAClB,MAAM,IAAID,IAAI,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;UAC1B,IAAI,CAACC,KAAK,IAAI,IAAI,CAAA;AACpB,SAAA;;AAEA;AACA,QAAA,IAAI,CAACD,IAAI,GAAGA,IAAI,CAAC,CAAC,CAAC,CAAA;AACrB,OAAA;AACF,KAAC,MAAM;MACL,IAAIC,KAAK,YAAYH,SAAS,EAAE;AAC9B,QAAA,IAAI,CAACG,KAAK,GAAGA,KAAK,CAACna,OAAO,EAAE,CAAA;AAC5B,QAAA,IAAI,CAACka,IAAI,GAAGC,KAAK,CAACD,IAAI,CAAA;AACxB,OAAA;AACF,KAAA;AAEA,IAAA,OAAO,IAAI,CAAA;AACb,GAAA;;AAEA;EACAM,KAAKA,CAACH,MAAM,EAAE;AACZA,IAAAA,MAAM,GAAG,IAAIL,SAAS,CAACK,MAAM,CAAC,CAAA;AAC9B,IAAA,OAAO,IAAIL,SAAS,CAAC,IAAI,GAAGK,MAAM,EAAE,IAAI,CAACH,IAAI,IAAIG,MAAM,CAACH,IAAI,CAAC,CAAA;AAC/D,GAAA;;AAEA;EACAO,IAAIA,CAACJ,MAAM,EAAE;AACXA,IAAAA,MAAM,GAAG,IAAIL,SAAS,CAACK,MAAM,CAAC,CAAA;AAC9B,IAAA,OAAO,IAAIL,SAAS,CAAC,IAAI,GAAGK,MAAM,EAAE,IAAI,CAACH,IAAI,IAAIG,MAAM,CAACH,IAAI,CAAC,CAAA;AAC/D,GAAA;;AAEA;EACAQ,KAAKA,CAACL,MAAM,EAAE;AACZA,IAAAA,MAAM,GAAG,IAAIL,SAAS,CAACK,MAAM,CAAC,CAAA;AAC9B,IAAA,OAAO,IAAIL,SAAS,CAAC,IAAI,GAAGK,MAAM,EAAE,IAAI,CAACH,IAAI,IAAIG,MAAM,CAACH,IAAI,CAAC,CAAA;AAC/D,GAAA;AAEA/N,EAAAA,OAAOA,GAAG;IACR,OAAO,CAAC,IAAI,CAACgO,KAAK,EAAE,IAAI,CAACD,IAAI,CAAC,CAAA;AAChC,GAAA;AAEAS,EAAAA,MAAMA,GAAG;AACP,IAAA,OAAO,IAAI,CAAChS,QAAQ,EAAE,CAAA;AACxB,GAAA;AAEAA,EAAAA,QAAQA,GAAG;AACT,IAAA,OACE,CAAC,IAAI,CAACuR,IAAI,KAAK,GAAG,GACd,CAAC,EAAE,IAAI,CAACC,KAAK,GAAG,GAAG,CAAC,GAAG,GAAG,GAC1B,IAAI,CAACD,IAAI,KAAK,GAAG,GACf,IAAI,CAACC,KAAK,GAAG,GAAG,GAChB,IAAI,CAACA,KAAK,IAAI,IAAI,CAACD,IAAI,CAAA;AAEjC,GAAA;AAEAla,EAAAA,OAAOA,GAAG;IACR,OAAO,IAAI,CAACma,KAAK,CAAA;AACnB,GAAA;AACF;;ACjGA,MAAMS,eAAe,GAAG,IAAIje,GAAG,CAAC,CAC9B,MAAM,EACN,QAAQ,EACR,OAAO,EACP,SAAS,EACT,YAAY,EACZ,aAAa,EACb,gBAAgB,CACjB,CAAC,CAAA;AAEF,MAAMke,KAAK,GAAG,EAAE,CAAA;AACT,SAASC,gBAAgBA,CAACzZ,EAAE,EAAE;AACnCwZ,EAAAA,KAAK,CAAChe,IAAI,CAACwE,EAAE,CAAC,CAAA;AAChB,CAAA;;AAEA;AACe,SAAS0C,IAAIA,CAACA,IAAI,EAAE2C,GAAG,EAAE/E,EAAE,EAAE;AAC1C;EACA,IAAIoC,IAAI,IAAI,IAAI,EAAE;AAChB;IACAA,IAAI,GAAG,EAAE,CAAA;AACT2C,IAAAA,GAAG,GAAG,IAAI,CAACxG,IAAI,CAACwH,UAAU,CAAA;AAE1B,IAAA,KAAK,MAAMxH,IAAI,IAAIwG,GAAG,EAAE;MACtB3C,IAAI,CAAC7D,IAAI,CAACR,QAAQ,CAAC,GAAGgG,QAAQ,CAAC0B,IAAI,CAAClH,IAAI,CAAC6a,SAAS,CAAC,GAC/ClJ,UAAU,CAAC3R,IAAI,CAAC6a,SAAS,CAAC,GAC1B7a,IAAI,CAAC6a,SAAS,CAAA;AACpB,KAAA;AAEA,IAAA,OAAOhX,IAAI,CAAA;AACb,GAAC,MAAM,IAAIA,IAAI,YAAY7H,KAAK,EAAE;AAChC;IACA,OAAO6H,IAAI,CAAC8S,MAAM,CAAC,CAACmE,IAAI,EAAEC,IAAI,KAAK;MACjCD,IAAI,CAACC,IAAI,CAAC,GAAG,IAAI,CAAClX,IAAI,CAACkX,IAAI,CAAC,CAAA;AAC5B,MAAA,OAAOD,IAAI,CAAA;KACZ,EAAE,EAAE,CAAC,CAAA;AACR,GAAC,MAAM,IAAI,OAAOjX,IAAI,KAAK,QAAQ,IAAIA,IAAI,CAACF,WAAW,KAAKvH,MAAM,EAAE;AAClE;AACA,IAAA,KAAKoK,GAAG,IAAI3C,IAAI,EAAE,IAAI,CAACA,IAAI,CAAC2C,GAAG,EAAE3C,IAAI,CAAC2C,GAAG,CAAC,CAAC,CAAA;AAC7C,GAAC,MAAM,IAAIA,GAAG,KAAK,IAAI,EAAE;AACvB;AACA,IAAA,IAAI,CAACxG,IAAI,CAACI,eAAe,CAACyD,IAAI,CAAC,CAAA;AACjC,GAAC,MAAM,IAAI2C,GAAG,IAAI,IAAI,EAAE;AACtB;IACAA,GAAG,GAAG,IAAI,CAACxG,IAAI,CAACgb,YAAY,CAACnX,IAAI,CAAC,CAAA;IAClC,OAAO2C,GAAG,IAAI,IAAI,GACd7G,KAAQ,CAACkE,IAAI,CAAC,GACd2B,QAAQ,CAAC0B,IAAI,CAACV,GAAG,CAAC,GAChBmL,UAAU,CAACnL,GAAG,CAAC,GACfA,GAAG,CAAA;AACX,GAAC,MAAM;AACL;IACAA,GAAG,GAAGmU,KAAK,CAAChE,MAAM,CAAC,CAACsE,IAAI,EAAEC,IAAI,KAAK;AACjC,MAAA,OAAOA,IAAI,CAACrX,IAAI,EAAEoX,IAAI,EAAE,IAAI,CAAC,CAAA;KAC9B,EAAEzU,GAAG,CAAC,CAAA;;AAEP;AACA,IAAA,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE;AAC3BA,MAAAA,GAAG,GAAG,IAAIsT,SAAS,CAACtT,GAAG,CAAC,CAAA;AAC1B,KAAC,MAAM,IAAIkU,eAAe,CAACnb,GAAG,CAACsE,IAAI,CAAC,IAAI6F,KAAK,CAACG,OAAO,CAACrD,GAAG,CAAC,EAAE;AAC1D;AACAA,MAAAA,GAAG,GAAG,IAAIkD,KAAK,CAAClD,GAAG,CAAC,CAAA;AACtB,KAAC,MAAM,IAAIA,GAAG,CAAC7C,WAAW,KAAK3H,KAAK,EAAE;AACpC;AACAwK,MAAAA,GAAG,GAAG,IAAIoT,QAAQ,CAACpT,GAAG,CAAC,CAAA;AACzB,KAAA;;AAEA;IACA,IAAI3C,IAAI,KAAK,SAAS,EAAE;AACtB;MACA,IAAI,IAAI,CAACsX,OAAO,EAAE;AAChB,QAAA,IAAI,CAACA,OAAO,CAAC3U,GAAG,CAAC,CAAA;AACnB,OAAA;AACF,KAAC,MAAM;AACL;AACA,MAAA,OAAO/E,EAAE,KAAK,QAAQ,GAClB,IAAI,CAACzB,IAAI,CAACob,cAAc,CAAC3Z,EAAE,EAAEoC,IAAI,EAAE2C,GAAG,CAACiC,QAAQ,EAAE,CAAC,GAClD,IAAI,CAACzI,IAAI,CAACC,YAAY,CAAC4D,IAAI,EAAE2C,GAAG,CAACiC,QAAQ,EAAE,CAAC,CAAA;AAClD,KAAA;;AAEA;AACA,IAAA,IAAI,IAAI,CAAC4S,OAAO,KAAKxX,IAAI,KAAK,WAAW,IAAIA,IAAI,KAAK,GAAG,CAAC,EAAE;MAC1D,IAAI,CAACwX,OAAO,EAAE,CAAA;AAChB,KAAA;AACF,GAAA;AAEA,EAAA,OAAO,IAAI,CAAA;AACb;;AC5Ee,MAAMC,GAAG,SAASxC,WAAW,CAAC;AAC3CnV,EAAAA,WAAWA,CAAC3D,IAAI,EAAE6W,KAAK,EAAE;AACvB,IAAA,KAAK,EAAE,CAAA;IACP,IAAI,CAAC7W,IAAI,GAAGA,IAAI,CAAA;AAChB,IAAA,IAAI,CAAC+Y,IAAI,GAAG/Y,IAAI,CAACR,QAAQ,CAAA;AAEzB,IAAA,IAAIqX,KAAK,IAAI7W,IAAI,KAAK6W,KAAK,EAAE;AAC3B,MAAA,IAAI,CAAChT,IAAI,CAACgT,KAAK,CAAC,CAAA;AAClB,KAAA;AACF,GAAA;;AAEA;AACAvS,EAAAA,GAAGA,CAAClG,OAAO,EAAErB,CAAC,EAAE;AACdqB,IAAAA,OAAO,GAAGuD,YAAY,CAACvD,OAAO,CAAC,CAAA;;AAE/B;AACA,IAAA,IACEA,OAAO,CAACmd,eAAe,IACvB,IAAI,CAACvb,IAAI,YAAYS,OAAO,CAACC,MAAM,CAAC8a,UAAU,EAC9C;MACApd,OAAO,CAACmd,eAAe,EAAE,CAAA;AAC3B,KAAA;IAEA,IAAIxe,CAAC,IAAI,IAAI,EAAE;MACb,IAAI,CAACiD,IAAI,CAACyb,WAAW,CAACrd,OAAO,CAAC4B,IAAI,CAAC,CAAA;AACrC,KAAC,MAAM,IAAI5B,OAAO,CAAC4B,IAAI,KAAK,IAAI,CAACA,IAAI,CAAC0b,UAAU,CAAC3e,CAAC,CAAC,EAAE;AACnD,MAAA,IAAI,CAACiD,IAAI,CAAC6E,YAAY,CAACzG,OAAO,CAAC4B,IAAI,EAAE,IAAI,CAACA,IAAI,CAAC0b,UAAU,CAAC3e,CAAC,CAAC,CAAC,CAAA;AAC/D,KAAA;AAEA,IAAA,OAAO,IAAI,CAAA;AACb,GAAA;;AAEA;AACA+W,EAAAA,KAAKA,CAAC/P,MAAM,EAAEhH,CAAC,EAAE;IACf,OAAO4E,YAAY,CAACoC,MAAM,CAAC,CAAC4X,GAAG,CAAC,IAAI,EAAE5e,CAAC,CAAC,CAAA;AAC1C,GAAA;;AAEA;AACAsG,EAAAA,QAAQA,GAAG;AACT,IAAA,OAAO,IAAIgT,IAAI,CACbzZ,GAAG,CAAC,IAAI,CAACoD,IAAI,CAACqD,QAAQ,EAAE,UAAUrD,IAAI,EAAE;MACtC,OAAOwC,KAAK,CAACxC,IAAI,CAAC,CAAA;AACpB,KAAC,CACH,CAAC,CAAA;AACH,GAAA;;AAEA;AACA4b,EAAAA,KAAKA,GAAG;AACN;AACA,IAAA,OAAO,IAAI,CAAC5b,IAAI,CAAC6b,aAAa,EAAE,EAAE;MAChC,IAAI,CAAC7b,IAAI,CAACmC,WAAW,CAAC,IAAI,CAACnC,IAAI,CAAC8b,SAAS,CAAC,CAAA;AAC5C,KAAA;AAEA,IAAA,OAAO,IAAI,CAAA;AACb,GAAA;;AAEA;EACA1O,KAAKA,CAAC2O,IAAI,GAAG,IAAI,EAAEC,YAAY,GAAG,IAAI,EAAE;AACtC;IACA,IAAI,CAACvc,cAAc,EAAE,CAAA;;AAErB;IACA,IAAIwc,SAAS,GAAG,IAAI,CAACjc,IAAI,CAACkc,SAAS,CAACH,IAAI,CAAC,CAAA;AACzC,IAAA,IAAIC,YAAY,EAAE;AAChB;AACAC,MAAAA,SAAS,GAAG7Y,WAAW,CAAC6Y,SAAS,CAAC,CAAA;AACpC,KAAA;AACA,IAAA,OAAO,IAAI,IAAI,CAACtY,WAAW,CAACsY,SAAS,CAAC,CAAA;AACxC,GAAA;;AAEA;AACA1F,EAAAA,IAAIA,CAACzZ,KAAK,EAAEif,IAAI,EAAE;AAChB,IAAA,MAAM1Y,QAAQ,GAAG,IAAI,CAACA,QAAQ,EAAE,CAAA;IAChC,IAAItG,CAAC,EAAEC,EAAE,CAAA;AAET,IAAA,KAAKD,CAAC,GAAG,CAAC,EAAEC,EAAE,GAAGqG,QAAQ,CAACpG,MAAM,EAAEF,CAAC,GAAGC,EAAE,EAAED,CAAC,EAAE,EAAE;AAC7CD,MAAAA,KAAK,CAAC8G,KAAK,CAACP,QAAQ,CAACtG,CAAC,CAAC,EAAE,CAACA,CAAC,EAAEsG,QAAQ,CAAC,CAAC,CAAA;AAEvC,MAAA,IAAI0Y,IAAI,EAAE;QACR1Y,QAAQ,CAACtG,CAAC,CAAC,CAACwZ,IAAI,CAACzZ,KAAK,EAAEif,IAAI,CAAC,CAAA;AAC/B,OAAA;AACF,KAAA;AAEA,IAAA,OAAO,IAAI,CAAA;AACb,GAAA;AAEA3d,EAAAA,OAAOA,CAACoB,QAAQ,EAAEqX,KAAK,EAAE;AACvB,IAAA,OAAO,IAAI,CAAC8E,GAAG,CAAC,IAAIL,GAAG,CAAC9Z,MAAM,CAAChC,QAAQ,CAAC,EAAEqX,KAAK,CAAC,CAAC,CAAA;AACnD,GAAA;;AAEA;AACAsF,EAAAA,KAAKA,GAAG;AACN,IAAA,OAAO3Z,KAAK,CAAC,IAAI,CAACxC,IAAI,CAACkC,UAAU,CAAC,CAAA;AACpC,GAAA;;AAEA;EACAka,GAAGA,CAACrf,CAAC,EAAE;IACL,OAAOyF,KAAK,CAAC,IAAI,CAACxC,IAAI,CAAC0b,UAAU,CAAC3e,CAAC,CAAC,CAAC,CAAA;AACvC,GAAA;AAEAwa,EAAAA,cAAcA,GAAG;IACf,OAAO,IAAI,CAACvX,IAAI,CAAA;AAClB,GAAA;AAEAyX,EAAAA,cAAcA,GAAG;IACf,OAAO,IAAI,CAACzX,IAAI,CAAA;AAClB,GAAA;;AAEA;EACAT,GAAGA,CAACnB,OAAO,EAAE;AACX,IAAA,OAAO,IAAI,CAAC6F,KAAK,CAAC7F,OAAO,CAAC,IAAI,CAAC,CAAA;AACjC,GAAA;AAEAkC,EAAAA,IAAIA,CAAC+b,QAAQ,EAAEC,SAAS,EAAE;IACxB,OAAO,IAAI,CAACC,GAAG,CAACF,QAAQ,EAAEC,SAAS,EAAEhc,IAAI,CAAC,CAAA;AAC5C,GAAA;;AAEA;EACAgD,EAAEA,CAACA,EAAE,EAAE;AACL;IACA,IAAI,OAAOA,EAAE,KAAK,WAAW,IAAI,CAAC,IAAI,CAACtD,IAAI,CAACsD,EAAE,EAAE;MAC9C,IAAI,CAACtD,IAAI,CAACsD,EAAE,GAAGH,GAAG,CAAC,IAAI,CAAC4V,IAAI,CAAC,CAAA;AAC/B,KAAA;;AAEA;AACA,IAAA,OAAO,IAAI,CAAClV,IAAI,CAAC,IAAI,EAAEP,EAAE,CAAC,CAAA;AAC5B,GAAA;;AAEA;EACAW,KAAKA,CAAC7F,OAAO,EAAE;AACb,IAAA,OAAO,EAAE,CAACF,KAAK,CAAC0T,IAAI,CAAC,IAAI,CAAC5R,IAAI,CAAC0b,UAAU,CAAC,CAAC1V,OAAO,CAAC5H,OAAO,CAAC4B,IAAI,CAAC,CAAA;AAClE,GAAA;;AAEA;AACA8a,EAAAA,IAAIA,GAAG;AACL,IAAA,OAAOtY,KAAK,CAAC,IAAI,CAACxC,IAAI,CAAC8b,SAAS,CAAC,CAAA;AACnC,GAAA;;AAEA;EACAU,OAAOA,CAACC,QAAQ,EAAE;AAChB,IAAA,MAAM7V,EAAE,GAAG,IAAI,CAAC5G,IAAI,CAAA;IACpB,MAAM0c,OAAO,GACX9V,EAAE,CAAC4V,OAAO,IACV5V,EAAE,CAAC+V,eAAe,IAClB/V,EAAE,CAACgW,iBAAiB,IACpBhW,EAAE,CAACiW,kBAAkB,IACrBjW,EAAE,CAACkW,qBAAqB,IACxBlW,EAAE,CAACmW,gBAAgB,IACnB,IAAI,CAAA;IACN,OAAOL,OAAO,IAAIA,OAAO,CAAC9K,IAAI,CAAChL,EAAE,EAAE6V,QAAQ,CAAC,CAAA;AAC9C,GAAA;;AAEA;EACA1Y,MAAMA,CAACgV,IAAI,EAAE;IACX,IAAIhV,MAAM,GAAG,IAAI,CAAA;;AAEjB;IACA,IAAI,CAACA,MAAM,CAAC/D,IAAI,CAAC2T,UAAU,EAAE,OAAO,IAAI,CAAA;;AAExC;IACA5P,MAAM,GAAGvB,KAAK,CAACuB,MAAM,CAAC/D,IAAI,CAAC2T,UAAU,CAAC,CAAA;AAEtC,IAAA,IAAI,CAACoF,IAAI,EAAE,OAAOhV,MAAM,CAAA;;AAExB;IACA,GAAG;AACD,MAAA,IACE,OAAOgV,IAAI,KAAK,QAAQ,GAAGhV,MAAM,CAACyY,OAAO,CAACzD,IAAI,CAAC,GAAGhV,MAAM,YAAYgV,IAAI,EAExE,OAAOhV,MAAM,CAAA;KAChB,QAASA,MAAM,GAAGvB,KAAK,CAACuB,MAAM,CAAC/D,IAAI,CAAC2T,UAAU,CAAC,EAAA;AAEhD,IAAA,OAAO5P,MAAM,CAAA;AACf,GAAA;;AAEA;AACA4X,EAAAA,GAAGA,CAACvd,OAAO,EAAErB,CAAC,EAAE;AACdqB,IAAAA,OAAO,GAAGuD,YAAY,CAACvD,OAAO,CAAC,CAAA;AAC/B,IAAA,IAAI,CAACkG,GAAG,CAAClG,OAAO,EAAErB,CAAC,CAAC,CAAA;AACpB,IAAA,OAAOqB,OAAO,CAAA;AAChB,GAAA;;AAEA;AACA4e,EAAAA,KAAKA,CAACjZ,MAAM,EAAEhH,CAAC,EAAE;IACf,OAAO4E,YAAY,CAACoC,MAAM,CAAC,CAACO,GAAG,CAAC,IAAI,EAAEvH,CAAC,CAAC,CAAA;AAC1C,GAAA;;AAEA;AACAwH,EAAAA,MAAMA,GAAG;AACP,IAAA,IAAI,IAAI,CAACR,MAAM,EAAE,EAAE;MACjB,IAAI,CAACA,MAAM,EAAE,CAACkZ,aAAa,CAAC,IAAI,CAAC,CAAA;AACnC,KAAA;AAEA,IAAA,OAAO,IAAI,CAAA;AACb,GAAA;;AAEA;EACAA,aAAaA,CAAC7e,OAAO,EAAE;IACrB,IAAI,CAAC4B,IAAI,CAACmC,WAAW,CAAC/D,OAAO,CAAC4B,IAAI,CAAC,CAAA;AAEnC,IAAA,OAAO,IAAI,CAAA;AACb,GAAA;;AAEA;EACApC,OAAOA,CAACQ,OAAO,EAAE;AACfA,IAAAA,OAAO,GAAGuD,YAAY,CAACvD,OAAO,CAAC,CAAA;AAE/B,IAAA,IAAI,IAAI,CAAC4B,IAAI,CAAC2T,UAAU,EAAE;AACxB,MAAA,IAAI,CAAC3T,IAAI,CAAC2T,UAAU,CAACuJ,YAAY,CAAC9e,OAAO,CAAC4B,IAAI,EAAE,IAAI,CAACA,IAAI,CAAC,CAAA;AAC5D,KAAA;AAEA,IAAA,OAAO5B,OAAO,CAAA;AAChB,GAAA;EAEAiK,KAAKA,CAAC8U,SAAS,GAAG,CAAC,EAAEvgB,GAAG,GAAG,IAAI,EAAE;AAC/B,IAAA,MAAMwgB,MAAM,GAAG,EAAE,IAAID,SAAS,CAAA;AAC9B,IAAA,MAAMtG,KAAK,GAAG,IAAI,CAAChT,IAAI,CAACjH,GAAG,CAAC,CAAA;AAE5B,IAAA,KAAK,MAAMG,CAAC,IAAI8Z,KAAK,EAAE;AACrB,MAAA,IAAI,OAAOA,KAAK,CAAC9Z,CAAC,CAAC,KAAK,QAAQ,EAAE;AAChC8Z,QAAAA,KAAK,CAAC9Z,CAAC,CAAC,GAAGO,IAAI,CAAC+K,KAAK,CAACwO,KAAK,CAAC9Z,CAAC,CAAC,GAAGqgB,MAAM,CAAC,GAAGA,MAAM,CAAA;AACnD,OAAA;AACF,KAAA;AAEA,IAAA,IAAI,CAACvZ,IAAI,CAACgT,KAAK,CAAC,CAAA;AAChB,IAAA,OAAO,IAAI,CAAA;AACb,GAAA;;AAEA;AACAxW,EAAAA,GAAGA,CAACgd,OAAO,EAAEC,QAAQ,EAAE;IACrB,OAAO,IAAI,CAACf,GAAG,CAACc,OAAO,EAAEC,QAAQ,EAAEjd,GAAG,CAAC,CAAA;AACzC,GAAA;;AAEA;AACAoI,EAAAA,QAAQA,GAAG;AACT,IAAA,OAAO,IAAI,CAACnF,EAAE,EAAE,CAAA;AAClB,GAAA;EAEAia,KAAKA,CAACC,IAAI,EAAE;AACV;AACA,IAAA,IAAI,CAACxd,IAAI,CAACyd,WAAW,GAAGD,IAAI,CAAA;AAC5B,IAAA,OAAO,IAAI,CAAA;AACb,GAAA;EAEAE,IAAIA,CAAC1d,IAAI,EAAE;AACT,IAAA,MAAM+D,MAAM,GAAG,IAAI,CAACA,MAAM,EAAE,CAAA;IAE5B,IAAI,CAACA,MAAM,EAAE;AACX,MAAA,OAAO,IAAI,CAAC+P,KAAK,CAAC9T,IAAI,CAAC,CAAA;AACzB,KAAA;AAEA,IAAA,MAAMgE,QAAQ,GAAGD,MAAM,CAACE,KAAK,CAAC,IAAI,CAAC,CAAA;AACnC,IAAA,OAAOF,MAAM,CAAC4X,GAAG,CAAC3b,IAAI,EAAEgE,QAAQ,CAAC,CAAC2X,GAAG,CAAC,IAAI,CAAC,CAAA;AAC7C,GAAA;;AAEA;AACAlc,EAAAA,cAAcA,GAAG;AACf;IACA,IAAI,CAAC8W,IAAI,CAAC,YAAY;MACpB,IAAI,CAAC9W,cAAc,EAAE,CAAA;AACvB,KAAC,CAAC,CAAA;AAEF,IAAA,OAAO,IAAI,CAAA;AACb,GAAA;;AAEA;AACA8c,EAAAA,GAAGA,CAACoB,OAAO,EAAEC,QAAQ,EAAEnc,EAAE,EAAE;AACzB,IAAA,IAAI,OAAOkc,OAAO,KAAK,SAAS,EAAE;AAChClc,MAAAA,EAAE,GAAGmc,QAAQ,CAAA;AACbA,MAAAA,QAAQ,GAAGD,OAAO,CAAA;AAClBA,MAAAA,OAAO,GAAG,IAAI,CAAA;AAChB,KAAA;;AAEA;IACA,IAAIA,OAAO,IAAI,IAAI,IAAI,OAAOA,OAAO,KAAK,UAAU,EAAE;AACpD;AACAC,MAAAA,QAAQ,GAAGA,QAAQ,IAAI,IAAI,GAAG,IAAI,GAAGA,QAAQ,CAAA;;AAE7C;MACA,IAAI,CAACne,cAAc,EAAE,CAAA;MACrB,IAAIqT,OAAO,GAAG,IAAI,CAAA;;AAElB;MACA,IAAI6K,OAAO,IAAI,IAAI,EAAE;QACnB7K,OAAO,GAAGtQ,KAAK,CAACsQ,OAAO,CAAC9S,IAAI,CAACkc,SAAS,CAAC,IAAI,CAAC,CAAC,CAAA;;AAE7C;AACA,QAAA,IAAI0B,QAAQ,EAAE;AACZ,UAAA,MAAM1gB,MAAM,GAAGygB,OAAO,CAAC7K,OAAO,CAAC,CAAA;UAC/BA,OAAO,GAAG5V,MAAM,IAAI4V,OAAO,CAAA;;AAE3B;AACA,UAAA,IAAI5V,MAAM,KAAK,KAAK,EAAE,OAAO,EAAE,CAAA;AACjC,SAAA;;AAEA;QACA4V,OAAO,CAACyD,IAAI,CAAC,YAAY;AACvB,UAAA,MAAMrZ,MAAM,GAAGygB,OAAO,CAAC,IAAI,CAAC,CAAA;AAC5B,UAAA,MAAME,KAAK,GAAG3gB,MAAM,IAAI,IAAI,CAAA;;AAE5B;UACA,IAAIA,MAAM,KAAK,KAAK,EAAE;YACpB,IAAI,CAACqH,MAAM,EAAE,CAAA;;AAEb;AACF,WAAC,MAAM,IAAIrH,MAAM,IAAI,IAAI,KAAK2gB,KAAK,EAAE;AACnC,YAAA,IAAI,CAACjgB,OAAO,CAACigB,KAAK,CAAC,CAAA;AACrB,WAAA;SACD,EAAE,IAAI,CAAC,CAAA;AACV,OAAA;;AAEA;AACA,MAAA,OAAOD,QAAQ,GAAG9K,OAAO,CAAC9S,IAAI,CAACsc,SAAS,GAAGxJ,OAAO,CAAC9S,IAAI,CAACiC,SAAS,CAAA;AACnE,KAAA;;AAEA;;AAEA;AACA2b,IAAAA,QAAQ,GAAGA,QAAQ,IAAI,IAAI,GAAG,KAAK,GAAGA,QAAQ,CAAA;;AAE9C;AACA,IAAA,MAAME,IAAI,GAAGtc,MAAM,CAAC,SAAS,EAAEC,EAAE,CAAC,CAAA;IAClC,MAAMsc,QAAQ,GAAGtd,OAAO,CAACE,QAAQ,CAACqd,sBAAsB,EAAE,CAAA;;AAE1D;IACAF,IAAI,CAAC7b,SAAS,GAAG0b,OAAO,CAAA;;AAExB;IACA,KAAK,IAAIM,GAAG,GAAGH,IAAI,CAACza,QAAQ,CAACpG,MAAM,EAAEghB,GAAG,EAAE,GAAI;AAC5CF,MAAAA,QAAQ,CAACtC,WAAW,CAACqC,IAAI,CAACI,iBAAiB,CAAC,CAAA;AAC9C,KAAA;AAEA,IAAA,MAAMna,MAAM,GAAG,IAAI,CAACA,MAAM,EAAE,CAAA;;AAE5B;AACA,IAAA,OAAO6Z,QAAQ,GAAG,IAAI,CAAChgB,OAAO,CAACmgB,QAAQ,CAAC,IAAIha,MAAM,GAAG,IAAI,CAACO,GAAG,CAACyZ,QAAQ,CAAC,CAAA;AACzE,GAAA;AACF,CAAA;AAEAxa,MAAM,CAAC+X,GAAG,EAAE;EAAEzX,IAAI;EAAEoT,IAAI;AAAEC,EAAAA,OAAAA;AAAQ,CAAC,CAAC,CAAA;AACpCpU,QAAQ,CAACwY,GAAG,EAAE,KAAK,CAAC;;ACpVL,MAAM7J,OAAO,SAAS6J,GAAG,CAAC;AACvC3X,EAAAA,WAAWA,CAAC3D,IAAI,EAAE6W,KAAK,EAAE;AACvB,IAAA,KAAK,CAAC7W,IAAI,EAAE6W,KAAK,CAAC,CAAA;;AAElB;AACA,IAAA,IAAI,CAACsH,GAAG,GAAG,EAAE,CAAA;;AAEb;AACA,IAAA,IAAI,CAACne,IAAI,CAACyC,QAAQ,GAAG,IAAI,CAAA;AAEzB,IAAA,IAAIzC,IAAI,CAACoe,YAAY,CAAC,YAAY,CAAC,IAAIpe,IAAI,CAACoe,YAAY,CAAC,YAAY,CAAC,EAAE;AACtE;AACA,MAAA,IAAI,CAACC,OAAO,CACVne,IAAI,CAACuH,KAAK,CAACzH,IAAI,CAACgb,YAAY,CAAC,YAAY,CAAC,CAAC,IACzC9a,IAAI,CAACuH,KAAK,CAACzH,IAAI,CAACgb,YAAY,CAAC,YAAY,CAAC,CAAC,IAC3C,EACJ,CAAC,CAAA;AACH,KAAA;AACF,GAAA;;AAEA;AACAsD,EAAAA,MAAMA,CAACtf,CAAC,EAAEC,CAAC,EAAE;IACX,OAAO,IAAI,CAACkR,EAAE,CAACnR,CAAC,CAAC,CAACoR,EAAE,CAACnR,CAAC,CAAC,CAAA;AACzB,GAAA;;AAEA;EACAkR,EAAEA,CAACnR,CAAC,EAAE;AACJ,IAAA,OAAOA,CAAC,IAAI,IAAI,GACZ,IAAI,CAACA,CAAC,EAAE,GAAG,IAAI,CAACX,KAAK,EAAE,GAAG,CAAC,GAC3B,IAAI,CAACW,CAAC,CAACA,CAAC,GAAG,IAAI,CAACX,KAAK,EAAE,GAAG,CAAC,CAAC,CAAA;AAClC,GAAA;;AAEA;EACA+R,EAAEA,CAACnR,CAAC,EAAE;AACJ,IAAA,OAAOA,CAAC,IAAI,IAAI,GACZ,IAAI,CAACA,CAAC,EAAE,GAAG,IAAI,CAACX,MAAM,EAAE,GAAG,CAAC,GAC5B,IAAI,CAACW,CAAC,CAACA,CAAC,GAAG,IAAI,CAACX,MAAM,EAAE,GAAG,CAAC,CAAC,CAAA;AACnC,GAAA;;AAEA;AACAigB,EAAAA,IAAIA,GAAG;AACL,IAAA,MAAMhd,IAAI,GAAG,IAAI,CAACA,IAAI,EAAE,CAAA;AACxB,IAAA,OAAOA,IAAI,IAAIA,IAAI,CAACgd,IAAI,EAAE,CAAA;AAC5B,GAAA;;AAEA;AACAC,EAAAA,KAAKA,CAACxf,CAAC,EAAEC,CAAC,EAAE;IACV,OAAO,IAAI,CAACsR,EAAE,CAACvR,CAAC,CAAC,CAACwR,EAAE,CAACvR,CAAC,CAAC,CAAA;AACzB,GAAA;;AAEA;AACAsR,EAAAA,EAAEA,CAACvR,CAAC,GAAG,CAAC,EAAE;AACR,IAAA,OAAO,IAAI,CAACA,CAAC,CAAC,IAAI8a,SAAS,CAAC9a,CAAC,CAAC,CAACub,IAAI,CAAC,IAAI,CAACvb,CAAC,EAAE,CAAC,CAAC,CAAA;AAChD,GAAA;;AAEA;AACAwR,EAAAA,EAAEA,CAACvR,CAAC,GAAG,CAAC,EAAE;AACR,IAAA,OAAO,IAAI,CAACA,CAAC,CAAC,IAAI6a,SAAS,CAAC7a,CAAC,CAAC,CAACsb,IAAI,CAAC,IAAI,CAACtb,CAAC,EAAE,CAAC,CAAC,CAAA;AAChD,GAAA;AAEAsY,EAAAA,cAAcA,GAAG;AACf,IAAA,OAAO,IAAI,CAAA;AACb,GAAA;;AAEA;EACAjZ,MAAMA,CAACA,MAAM,EAAE;AACb,IAAA,OAAO,IAAI,CAACuF,IAAI,CAAC,QAAQ,EAAEvF,MAAM,CAAC,CAAA;AACpC,GAAA;;AAEA;AACAmgB,EAAAA,IAAIA,CAACzf,CAAC,EAAEC,CAAC,EAAE;IACT,OAAO,IAAI,CAACD,CAAC,CAACA,CAAC,CAAC,CAACC,CAAC,CAACA,CAAC,CAAC,CAAA;AACvB,GAAA;;AAEA;EACAyf,OAAOA,CAACC,KAAK,GAAG,IAAI,CAACpd,IAAI,EAAE,EAAE;AAC3B,IAAA,MAAMqd,UAAU,GAAG,OAAOD,KAAK,KAAK,QAAQ,CAAA;IAC5C,IAAI,CAACC,UAAU,EAAE;AACfD,MAAAA,KAAK,GAAGhd,YAAY,CAACgd,KAAK,CAAC,CAAA;AAC7B,KAAA;AACA,IAAA,MAAMD,OAAO,GAAG,IAAIrI,IAAI,EAAE,CAAA;IAC1B,IAAItS,MAAM,GAAG,IAAI,CAAA;IAEjB,OACE,CAACA,MAAM,GAAGA,MAAM,CAACA,MAAM,EAAE,KACzBA,MAAM,CAAC/D,IAAI,KAAKS,OAAO,CAACE,QAAQ,IAChCoD,MAAM,CAACvE,QAAQ,KAAK,oBAAoB,EACxC;AACAkf,MAAAA,OAAO,CAAC/hB,IAAI,CAACoH,MAAM,CAAC,CAAA;MAEpB,IAAI,CAAC6a,UAAU,IAAI7a,MAAM,CAAC/D,IAAI,KAAK2e,KAAK,CAAC3e,IAAI,EAAE;AAC7C,QAAA,MAAA;AACF,OAAA;MACA,IAAI4e,UAAU,IAAI7a,MAAM,CAACyY,OAAO,CAACmC,KAAK,CAAC,EAAE;AACvC,QAAA,MAAA;AACF,OAAA;MACA,IAAI5a,MAAM,CAAC/D,IAAI,KAAK,IAAI,CAACuB,IAAI,EAAE,CAACvB,IAAI,EAAE;AACpC;AACA,QAAA,OAAO,IAAI,CAAA;AACb,OAAA;AACF,KAAA;AAEA,IAAA,OAAO0e,OAAO,CAAA;AAChB,GAAA;;AAEA;EACAxZ,SAASA,CAACrB,IAAI,EAAE;AACdA,IAAAA,IAAI,GAAG,IAAI,CAACA,IAAI,CAACA,IAAI,CAAC,CAAA;AACtB,IAAA,IAAI,CAACA,IAAI,EAAE,OAAO,IAAI,CAAA;IAEtB,MAAM9H,CAAC,GAAG,CAAC8H,IAAI,GAAG,EAAE,EAAEwW,KAAK,CAACnV,SAAS,CAAC,CAAA;IACtC,OAAOnJ,CAAC,GAAG4F,YAAY,CAAC5F,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAA;AACtC,GAAA;;AAEA;AACAwF,EAAAA,IAAIA,GAAG;IACL,MAAM8C,CAAC,GAAG,IAAI,CAACN,MAAM,CAACd,QAAQ,CAAC1B,IAAI,CAAC,CAAC,CAAA;AACrC,IAAA,OAAO8C,CAAC,IAAIA,CAAC,CAAC9C,IAAI,EAAE,CAAA;AACtB,GAAA;;AAEA;EACA8c,OAAOA,CAAC3f,CAAC,EAAE;IACT,IAAI,CAACyf,GAAG,GAAGzf,CAAC,CAAA;AACZ,IAAA,OAAO,IAAI,CAAA;AACb,GAAA;;AAEA;AACA+U,EAAAA,IAAIA,CAACpV,KAAK,EAAEC,MAAM,EAAE;IAClB,MAAM+F,CAAC,GAAGlG,gBAAgB,CAAC,IAAI,EAAEE,KAAK,EAAEC,MAAM,CAAC,CAAA;IAE/C,OAAO,IAAI,CAACD,KAAK,CAAC,IAAIyb,SAAS,CAACzV,CAAC,CAAChG,KAAK,CAAC,CAAC,CAACC,MAAM,CAAC,IAAIwb,SAAS,CAACzV,CAAC,CAAC/F,MAAM,CAAC,CAAC,CAAA;AAC3E,GAAA;;AAEA;EACAD,KAAKA,CAACA,KAAK,EAAE;AACX,IAAA,OAAO,IAAI,CAACwF,IAAI,CAAC,OAAO,EAAExF,KAAK,CAAC,CAAA;AAClC,GAAA;;AAEA;AACAoB,EAAAA,cAAcA,GAAG;AACfA,IAAAA,cAAc,CAAC,IAAI,EAAE,IAAI,CAAC0e,GAAG,CAAC,CAAA;AAC9B,IAAA,OAAO,KAAK,CAAC1e,cAAc,EAAE,CAAA;AAC/B,GAAA;;AAEA;EACAT,CAACA,CAACA,CAAC,EAAE;AACH,IAAA,OAAO,IAAI,CAAC6E,IAAI,CAAC,GAAG,EAAE7E,CAAC,CAAC,CAAA;AAC1B,GAAA;;AAEA;EACAC,CAACA,CAACA,CAAC,EAAE;AACH,IAAA,OAAO,IAAI,CAAC4E,IAAI,CAAC,GAAG,EAAE5E,CAAC,CAAC,CAAA;AAC1B,GAAA;AACF,CAAA;AAEAsE,MAAM,CAACkO,OAAO,EAAE;EACdjT,IAAI;EACJ+W,IAAI;EACJG,MAAM;EACN9H,KAAK;EACLoF,GAAG;AACHnF,EAAAA,SAAAA;AACF,CAAC,CAAC,CAAA;AAEF/K,QAAQ,CAAC2O,OAAO,EAAE,SAAS,CAAC;;AC9K5B;AACA,MAAMoN,KAAK,GAAG;AACZpF,EAAAA,MAAM,EAAE,CACN,OAAO,EACP,OAAO,EACP,SAAS,EACT,SAAS,EACT,UAAU,EACV,YAAY,EACZ,WAAW,EACX,YAAY,CACb;AACDD,EAAAA,IAAI,EAAE,CAAC,OAAO,EAAE,SAAS,EAAE,MAAM,CAAC;AAClCsF,EAAAA,MAAM,EAAE,UAAUhY,CAAC,EAAEQ,CAAC,EAAE;IACtB,OAAOA,CAAC,KAAK,OAAO,GAAGR,CAAC,GAAGA,CAAC,GAAG,GAAG,GAAGQ,CAAC,CAAA;AACxC,GAAA;AACF,CAAA;;AAEA;AAAA,CAAA;AACC,CAAC,MAAM,EAAE,QAAQ,CAAC,CAACT,OAAO,CAAC,UAAU9K,CAAC,EAAE;EACvC,MAAMgjB,SAAS,GAAG,EAAE,CAAA;AACpB,EAAA,IAAIhiB,CAAC,CAAA;AAELgiB,EAAAA,SAAS,CAAChjB,CAAC,CAAC,GAAG,UAAU2C,CAAC,EAAE;AAC1B,IAAA,IAAI,OAAOA,CAAC,KAAK,WAAW,EAAE;AAC5B,MAAA,OAAO,IAAI,CAACmF,IAAI,CAAC9H,CAAC,CAAC,CAAA;AACrB,KAAA;AACA,IAAA,IACE,OAAO2C,CAAC,KAAK,QAAQ,IACrBA,CAAC,YAAYgL,KAAK,IAClBA,KAAK,CAACpE,KAAK,CAAC5G,CAAC,CAAC,IACdA,CAAC,YAAY+S,OAAO,EACpB;AACA,MAAA,IAAI,CAAC5N,IAAI,CAAC9H,CAAC,EAAE2C,CAAC,CAAC,CAAA;AACjB,KAAC,MAAM;AACL;AACA,MAAA,KAAK3B,CAAC,GAAG8hB,KAAK,CAAC9iB,CAAC,CAAC,CAACkB,MAAM,GAAG,CAAC,EAAEF,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;AACzC,QAAA,IAAI2B,CAAC,CAACmgB,KAAK,CAAC9iB,CAAC,CAAC,CAACgB,CAAC,CAAC,CAAC,IAAI,IAAI,EAAE;AAC1B,UAAA,IAAI,CAAC8G,IAAI,CAACgb,KAAK,CAACC,MAAM,CAAC/iB,CAAC,EAAE8iB,KAAK,CAAC9iB,CAAC,CAAC,CAACgB,CAAC,CAAC,CAAC,EAAE2B,CAAC,CAACmgB,KAAK,CAAC9iB,CAAC,CAAC,CAACgB,CAAC,CAAC,CAAC,CAAC,CAAA;AACzD,SAAA;AACF,OAAA;AACF,KAAA;AAEA,IAAA,OAAO,IAAI,CAAA;GACZ,CAAA;EAEDlB,eAAe,CAAC,CAAC,SAAS,EAAE,QAAQ,CAAC,EAAEkjB,SAAS,CAAC,CAAA;AACnD,CAAC,CAAC,CAAA;AAEFljB,eAAe,CAAC,CAAC,SAAS,EAAE,QAAQ,CAAC,EAAE;AACrC;AACAwU,EAAAA,MAAM,EAAE,UAAU2O,GAAG,EAAElW,CAAC,EAAE1C,CAAC,EAAE/I,CAAC,EAAEqK,CAAC,EAAEiG,CAAC,EAAE;AACpC;IACA,IAAIqR,GAAG,IAAI,IAAI,EAAE;AACf,MAAA,OAAO,IAAIvR,MAAM,CAAC,IAAI,CAAC,CAAA;AACzB,KAAA;;AAEA;IACA,OAAO,IAAI,CAAC5J,IAAI,CAAC,WAAW,EAAE,IAAI4J,MAAM,CAACuR,GAAG,EAAElW,CAAC,EAAE1C,CAAC,EAAE/I,CAAC,EAAEqK,CAAC,EAAEiG,CAAC,CAAC,CAAC,CAAA;GAC9D;AAED;EACAqB,MAAM,EAAE,UAAUiQ,KAAK,EAAE9O,EAAE,EAAEC,EAAE,EAAE;IAC/B,OAAO,IAAI,CAAC7C,SAAS,CAAC;AAAEyB,MAAAA,MAAM,EAAEiQ,KAAK;AAAErgB,MAAAA,EAAE,EAAEuR,EAAE;AAAErR,MAAAA,EAAE,EAAEsR,EAAAA;KAAI,EAAE,IAAI,CAAC,CAAA;GAC/D;AAED;EACA5B,IAAI,EAAE,UAAUxP,CAAC,EAAEC,CAAC,EAAEkR,EAAE,EAAEC,EAAE,EAAE;AAC5B,IAAA,OAAO1J,SAAS,CAACzJ,MAAM,KAAK,CAAC,IAAIyJ,SAAS,CAACzJ,MAAM,KAAK,CAAC,GACnD,IAAI,CAACsQ,SAAS,CAAC;AAAEiB,MAAAA,IAAI,EAAExP,CAAC;AAAEJ,MAAAA,EAAE,EAAEK,CAAC;AAAEH,MAAAA,EAAE,EAAEqR,EAAAA;AAAG,KAAC,EAAE,IAAI,CAAC,GAChD,IAAI,CAAC5C,SAAS,CAAC;AAAEiB,MAAAA,IAAI,EAAE,CAACxP,CAAC,EAAEC,CAAC,CAAC;AAAEL,MAAAA,EAAE,EAAEuR,EAAE;AAAErR,MAAAA,EAAE,EAAEsR,EAAAA;KAAI,EAAE,IAAI,CAAC,CAAA;GAC3D;EAEDtB,KAAK,EAAE,UAAUmC,GAAG,EAAEd,EAAE,EAAEC,EAAE,EAAE;IAC5B,OAAO,IAAI,CAAC7C,SAAS,CAAC;AAAEuB,MAAAA,KAAK,EAAEmC,GAAG;AAAErS,MAAAA,EAAE,EAAEuR,EAAE;AAAErR,MAAAA,EAAE,EAAEsR,EAAAA;KAAI,EAAE,IAAI,CAAC,CAAA;GAC5D;AAED;EACAxB,KAAK,EAAE,UAAU5P,CAAC,EAAEC,CAAC,EAAEkR,EAAE,EAAEC,EAAE,EAAE;AAC7B,IAAA,OAAO1J,SAAS,CAACzJ,MAAM,KAAK,CAAC,IAAIyJ,SAAS,CAACzJ,MAAM,KAAK,CAAC,GACnD,IAAI,CAACsQ,SAAS,CAAC;AAAEqB,MAAAA,KAAK,EAAE5P,CAAC;AAAEJ,MAAAA,EAAE,EAAEK,CAAC;AAAEH,MAAAA,EAAE,EAAEqR,EAAAA;AAAG,KAAC,EAAE,IAAI,CAAC,GACjD,IAAI,CAAC5C,SAAS,CAAC;AAAEqB,MAAAA,KAAK,EAAE,CAAC5P,CAAC,EAAEC,CAAC,CAAC;AAAEL,MAAAA,EAAE,EAAEuR,EAAE;AAAErR,MAAAA,EAAE,EAAEsR,EAAAA;KAAI,EAAE,IAAI,CAAC,CAAA;GAC5D;AAED;AACAb,EAAAA,SAAS,EAAE,UAAUvQ,CAAC,EAAEC,CAAC,EAAE;IACzB,OAAO,IAAI,CAACsO,SAAS,CAAC;AAAEgC,MAAAA,SAAS,EAAE,CAACvQ,CAAC,EAAEC,CAAC,CAAA;KAAG,EAAE,IAAI,CAAC,CAAA;GACnD;AAED;AACA2Q,EAAAA,QAAQ,EAAE,UAAU5Q,CAAC,EAAEC,CAAC,EAAE;IACxB,OAAO,IAAI,CAACsO,SAAS,CAAC;AAAEqC,MAAAA,QAAQ,EAAE,CAAC5Q,CAAC,EAAEC,CAAC,CAAA;KAAG,EAAE,IAAI,CAAC,CAAA;GAClD;AAED;EACAmP,IAAI,EAAE,UAAU8Q,SAAS,GAAG,MAAM,EAAEvgB,MAAM,GAAG,QAAQ,EAAE;IACrD,IAAI,YAAY,CAACqH,OAAO,CAACkZ,SAAS,CAAC,KAAK,CAAC,CAAC,EAAE;AAC1CvgB,MAAAA,MAAM,GAAGugB,SAAS,CAAA;AAClBA,MAAAA,SAAS,GAAG,MAAM,CAAA;AACpB,KAAA;IAEA,OAAO,IAAI,CAAC3R,SAAS,CAAC;AAAEa,MAAAA,IAAI,EAAE8Q,SAAS;AAAEvgB,MAAAA,MAAM,EAAEA,MAAAA;KAAQ,EAAE,IAAI,CAAC,CAAA;GACjE;AAED;AACA+a,EAAAA,OAAO,EAAE,UAAUO,KAAK,EAAE;AACxB,IAAA,OAAO,IAAI,CAACpW,IAAI,CAAC,SAAS,EAAEoW,KAAK,CAAC,CAAA;AACpC,GAAA;AACF,CAAC,CAAC,CAAA;AAEFpe,eAAe,CAAC,QAAQ,EAAE;AACxB;EACAsjB,MAAM,EAAE,UAAUngB,CAAC,EAAEC,CAAC,GAAGD,CAAC,EAAE;IAC1B,MAAM+Z,IAAI,GAAG,CAAC,IAAI,CAACqG,QAAQ,IAAI,IAAI,EAAErG,IAAI,CAAA;IACzC,OAAOA,IAAI,KAAK,gBAAgB,GAC5B,IAAI,CAAClV,IAAI,CAAC,GAAG,EAAE,IAAIiW,SAAS,CAAC9a,CAAC,CAAC,CAAC,GAChC,IAAI,CAAC6Q,EAAE,CAAC7Q,CAAC,CAAC,CAAC+Q,EAAE,CAAC9Q,CAAC,CAAC,CAAA;AACtB,GAAA;AACF,CAAC,CAAC,CAAA;AAEFpD,eAAe,CAAC,MAAM,EAAE;AACtB;EACAoB,MAAM,EAAE,YAAY;AAClB,IAAA,OAAO,IAAI,CAAC+C,IAAI,CAACqf,cAAc,EAAE,CAAA;GAClC;AACD;AACAC,EAAAA,OAAO,EAAE,UAAUriB,MAAM,EAAE;IACzB,OAAO,IAAIkQ,KAAK,CAAC,IAAI,CAACnN,IAAI,CAACuf,gBAAgB,CAACtiB,MAAM,CAAC,CAAC,CAAA;AACtD,GAAA;AACF,CAAC,CAAC,CAAA;AAEFpB,eAAe,CAAC,CAAC,SAAS,EAAE,QAAQ,CAAC,EAAE;AACrC;AACA2jB,EAAAA,IAAI,EAAE,UAAUlY,CAAC,EAAEC,CAAC,EAAE;AACpB,IAAA,IAAI,OAAOD,CAAC,KAAK,QAAQ,EAAE;AACzB,MAAA,KAAKC,CAAC,IAAID,CAAC,EAAE,IAAI,CAACkY,IAAI,CAACjY,CAAC,EAAED,CAAC,CAACC,CAAC,CAAC,CAAC,CAAA;AAC/B,MAAA,OAAO,IAAI,CAAA;AACb,KAAA;AAEA,IAAA,OAAOD,CAAC,KAAK,SAAS,GAClB,IAAI,CAAC6T,OAAO,CAAC5T,CAAC,CAAC,GACfD,CAAC,KAAK,QAAQ,GACZ,IAAI,CAACzD,IAAI,CAAC,aAAa,EAAE0D,CAAC,CAAC,GAC3BD,CAAC,KAAK,MAAM,IACVA,CAAC,KAAK,QAAQ,IACdA,CAAC,KAAK,QAAQ,IACdA,CAAC,KAAK,SAAS,IACfA,CAAC,KAAK,SAAS,IACfA,CAAC,KAAK,OAAO,GACb,IAAI,CAACzD,IAAI,CAAC,OAAO,GAAGyD,CAAC,EAAEC,CAAC,CAAC,GACzB,IAAI,CAAC1D,IAAI,CAACyD,CAAC,EAAEC,CAAC,CAAC,CAAA;AACzB,GAAA;AACF,CAAC,CAAC,CAAA;;AAEF;AACA,MAAM5L,OAAO,GAAG,CACd,OAAO,EACP,UAAU,EACV,WAAW,EACX,SAAS,EACT,WAAW,EACX,UAAU,EACV,WAAW,EACX,YAAY,EACZ,YAAY,EACZ,YAAY,EACZ,WAAW,EACX,YAAY,EACZ,UAAU,EACV,aAAa,EACb,aAAa,EACb,OAAO,EACP,aAAa,EACb,aAAa,EACb,WAAW,EACX,cAAc,EACd,eAAe,CAChB,CAACgb,MAAM,CAAC,UAAUmE,IAAI,EAAE5C,KAAK,EAAE;AAC9B;AACA,EAAA,MAAM/W,EAAE,GAAG,UAAUwM,CAAC,EAAE;IACtB,IAAIA,CAAC,KAAK,IAAI,EAAE;AACd,MAAA,IAAI,CAAC0K,GAAG,CAACH,KAAK,CAAC,CAAA;AACjB,KAAC,MAAM;AACL,MAAA,IAAI,CAACP,EAAE,CAACO,KAAK,EAAEvK,CAAC,CAAC,CAAA;AACnB,KAAA;AACA,IAAA,OAAO,IAAI,CAAA;GACZ,CAAA;AAEDmN,EAAAA,IAAI,CAAC5C,KAAK,CAAC,GAAG/W,EAAE,CAAA;AAChB,EAAA,OAAO2Z,IAAI,CAAA;AACb,CAAC,EAAE,EAAE,CAAC,CAAA;AAENjf,eAAe,CAAC,SAAS,EAAEF,OAAO,CAAC;;AClMnC;AACO,SAAS8jB,WAAWA,GAAG;AAC5B,EAAA,OAAO,IAAI,CAAC5b,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,CAAA;AACrC,CAAA;;AAEA;AACO,SAAS6N,SAASA,GAAG;EAC1B,MAAMrB,MAAM,GAAG,CAAC,IAAI,CAACxM,IAAI,CAAC,WAAW,CAAC,IAAI,EAAA;AACxC;AAAA,IACCiC,KAAK,CAACX,UAAU,CAAC,CACjBjH,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CACZtB,GAAG,CAAC,UAAU8iB,GAAG,EAAE;AAClB;IACA,MAAMC,EAAE,GAAGD,GAAG,CAAC7Z,IAAI,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC,CAAA;IAChC,OAAO,CACL6Z,EAAE,CAAC,CAAC,CAAC,EACLA,EAAE,CAAC,CAAC,CAAC,CAAC7Z,KAAK,CAACJ,SAAS,CAAC,CAAC9I,GAAG,CAAC,UAAU8iB,GAAG,EAAE;MACxC,OAAO/N,UAAU,CAAC+N,GAAG,CAAC,CAAA;AACxB,KAAC,CAAC,CACH,CAAA;GACF,CAAC,CACDE,OAAO,EAAC;AACT;AAAA,GACCjJ,MAAM,CAAC,UAAUtG,MAAM,EAAE9C,SAAS,EAAE;AACnC,IAAA,IAAIA,SAAS,CAAC,CAAC,CAAC,KAAK,QAAQ,EAAE;AAC7B,MAAA,OAAO8C,MAAM,CAACgC,SAAS,CAAC5E,MAAM,CAACwC,SAAS,CAAC1C,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;AACzD,KAAA;AACA,IAAA,OAAO8C,MAAM,CAAC9C,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC3J,KAAK,CAACyM,MAAM,EAAE9C,SAAS,CAAC,CAAC,CAAC,CAAC,CAAA;AACzD,GAAC,EAAE,IAAIE,MAAM,EAAE,CAAC,CAAA;AAElB,EAAA,OAAO4C,MAAM,CAAA;AACf,CAAA;;AAEA;AACO,SAASwP,QAAQA,CAAC9b,MAAM,EAAEhH,CAAC,EAAE;AAClC,EAAA,IAAI,IAAI,KAAKgH,MAAM,EAAE,OAAO,IAAI,CAAA;AAEhC,EAAA,IAAIzE,aAAa,CAAC,IAAI,CAACU,IAAI,CAAC,EAAE,OAAO,IAAI,CAAC8T,KAAK,CAAC/P,MAAM,EAAEhH,CAAC,CAAC,CAAA;AAE1D,EAAA,MAAMiW,GAAG,GAAG,IAAI,CAACnF,SAAS,EAAE,CAAA;EAC5B,MAAMiS,IAAI,GAAG/b,MAAM,CAAC8J,SAAS,EAAE,CAACgE,OAAO,EAAE,CAAA;EAEzC,IAAI,CAACiC,KAAK,CAAC/P,MAAM,EAAEhH,CAAC,CAAC,CAAC0iB,WAAW,EAAE,CAAClS,SAAS,CAACuS,IAAI,CAACxN,QAAQ,CAACU,GAAG,CAAC,CAAC,CAAA;AAEjE,EAAA,OAAO,IAAI,CAAA;AACb,CAAA;;AAEA;AACO,SAAS+M,MAAMA,CAAChjB,CAAC,EAAE;EACxB,OAAO,IAAI,CAAC8iB,QAAQ,CAAC,IAAI,CAACte,IAAI,EAAE,EAAExE,CAAC,CAAC,CAAA;AACtC,CAAA;;AAEA;AACO,SAASwQ,SAASA,CAAC7O,CAAC,EAAEkR,QAAQ,EAAE;AACrC;EACA,IAAIlR,CAAC,IAAI,IAAI,IAAI,OAAOA,CAAC,KAAK,QAAQ,EAAE;IACtC,MAAMshB,UAAU,GAAG,IAAIvS,MAAM,CAAC,IAAI,CAAC,CAACkD,SAAS,EAAE,CAAA;IAC/C,OAAOjS,CAAC,IAAI,IAAI,GAAGshB,UAAU,GAAGA,UAAU,CAACthB,CAAC,CAAC,CAAA;AAC/C,GAAA;AAEA,EAAA,IAAI,CAAC+O,MAAM,CAACC,YAAY,CAAChP,CAAC,CAAC,EAAE;AAC3B;AACAA,IAAAA,CAAC,GAAG;AAAE,MAAA,GAAGA,CAAC;AAAEC,MAAAA,MAAM,EAAEF,SAAS,CAACC,CAAC,EAAE,IAAI,CAAA;KAAG,CAAA;AAC1C,GAAA;;AAEA;EACA,MAAMuhB,aAAa,GAAGrQ,QAAQ,KAAK,IAAI,GAAG,IAAI,GAAGA,QAAQ,IAAI,KAAK,CAAA;EAClE,MAAM1S,MAAM,GAAG,IAAIuQ,MAAM,CAACwS,aAAa,CAAC,CAAC1S,SAAS,CAAC7O,CAAC,CAAC,CAAA;AACrD,EAAA,OAAO,IAAI,CAACmF,IAAI,CAAC,WAAW,EAAE3G,MAAM,CAAC,CAAA;AACvC,CAAA;AAEArB,eAAe,CAAC,SAAS,EAAE;EACzB4jB,WAAW;EACX/N,SAAS;EACTmO,QAAQ;EACRE,MAAM;AACNxS,EAAAA,SAAAA;AACF,CAAC,CAAC;;AC/Ea,MAAM2S,SAAS,SAASzO,OAAO,CAAC;AAC7C0O,EAAAA,OAAOA,GAAG;IACR,IAAI,CAAC5J,IAAI,CAAC,YAAY;MACpB,IAAI,IAAI,YAAY2J,SAAS,EAAE;QAC7B,OAAO,IAAI,CAACC,OAAO,EAAE,CAACC,OAAO,EAAE,CAAA;AACjC,OAAA;AACF,KAAC,CAAC,CAAA;AAEF,IAAA,OAAO,IAAI,CAAA;AACb,GAAA;AAEAA,EAAAA,OAAOA,CAACrc,MAAM,GAAG,IAAI,CAACA,MAAM,EAAE,EAAEE,KAAK,GAAGF,MAAM,CAACE,KAAK,CAAC,IAAI,CAAC,EAAE;AAC1D;AACAA,IAAAA,KAAK,GAAGA,KAAK,KAAK,CAAC,CAAC,GAAGF,MAAM,CAACV,QAAQ,EAAE,CAACpG,MAAM,GAAGgH,KAAK,CAAA;AAEvD,IAAA,IAAI,CAACsS,IAAI,CAAC,UAAUxZ,CAAC,EAAEsG,QAAQ,EAAE;AAC/B;AACA,MAAA,OAAOA,QAAQ,CAACA,QAAQ,CAACpG,MAAM,GAAGF,CAAC,GAAG,CAAC,CAAC,CAAC8iB,QAAQ,CAAC9b,MAAM,EAAEE,KAAK,CAAC,CAAA;AAClE,KAAC,CAAC,CAAA;AAEF,IAAA,OAAO,IAAI,CAACM,MAAM,EAAE,CAAA;AACtB,GAAA;AACF,CAAA;AAEAzB,QAAQ,CAACod,SAAS,EAAE,WAAW,CAAC;;ACxBjB,MAAMG,IAAI,SAASH,SAAS,CAAC;AAC1Cvc,EAAAA,WAAWA,CAAC3D,IAAI,EAAE6W,KAAK,GAAG7W,IAAI,EAAE;IAC9B,KAAK,CAACoC,SAAS,CAAC,MAAM,EAAEpC,IAAI,CAAC,EAAE6W,KAAK,CAAC,CAAA;AACvC,GAAA;AAEAsJ,EAAAA,OAAOA,GAAG;AACR,IAAA,OAAO,IAAI,CAAA;AACb,GAAA;AAEAC,EAAAA,OAAOA,GAAG;AACR,IAAA,OAAO,IAAI,CAAA;AACb,GAAA;AACF,CAAA;AAEAtd,QAAQ,CAACud,IAAI,EAAE,MAAM,CAAC;;ACdP,MAAMC,KAAK,SAAS7O,OAAO,CAAC,EAAA;AAE3C3O,QAAQ,CAACwd,KAAK,EAAE,OAAO,CAAC;;ACHxB;AACO,SAASzQ,EAAEA,CAACA,EAAE,EAAE;AACrB,EAAA,OAAO,IAAI,CAAChM,IAAI,CAAC,IAAI,EAAEgM,EAAE,CAAC,CAAA;AAC5B,CAAA;;AAEA;AACO,SAASE,EAAEA,CAACA,EAAE,EAAE;AACrB,EAAA,OAAO,IAAI,CAAClM,IAAI,CAAC,IAAI,EAAEkM,EAAE,CAAC,CAAA;AAC5B,CAAA;;AAEA;AACO,SAAS/Q,GAACA,CAACA,CAAC,EAAE;EACnB,OAAOA,CAAC,IAAI,IAAI,GAAG,IAAI,CAACmR,EAAE,EAAE,GAAG,IAAI,CAACN,EAAE,EAAE,GAAG,IAAI,CAACM,EAAE,CAACnR,CAAC,GAAG,IAAI,CAAC6Q,EAAE,EAAE,CAAC,CAAA;AACnE,CAAA;;AAEA;AACO,SAAS5Q,GAACA,CAACA,CAAC,EAAE;EACnB,OAAOA,CAAC,IAAI,IAAI,GAAG,IAAI,CAACmR,EAAE,EAAE,GAAG,IAAI,CAACL,EAAE,EAAE,GAAG,IAAI,CAACK,EAAE,CAACnR,CAAC,GAAG,IAAI,CAAC8Q,EAAE,EAAE,CAAC,CAAA;AACnE,CAAA;;AAEA;AACO,SAASI,IAAEA,CAACnR,CAAC,EAAE;AACpB,EAAA,OAAO,IAAI,CAAC6E,IAAI,CAAC,IAAI,EAAE7E,CAAC,CAAC,CAAA;AAC3B,CAAA;;AAEA;AACO,SAASoR,IAAEA,CAACnR,CAAC,EAAE;AACpB,EAAA,OAAO,IAAI,CAAC4E,IAAI,CAAC,IAAI,EAAE5E,CAAC,CAAC,CAAA;AAC3B,CAAA;;AAEA;AACO,SAASZ,OAAKA,CAACA,KAAK,EAAE;EAC3B,OAAOA,KAAK,IAAI,IAAI,GAAG,IAAI,CAACwR,EAAE,EAAE,GAAG,CAAC,GAAG,IAAI,CAACA,EAAE,CAAC,IAAIiK,SAAS,CAACzb,KAAK,CAAC,CAAC6b,MAAM,CAAC,CAAC,CAAC,CAAC,CAAA;AAChF,CAAA;;AAEA;AACO,SAAS5b,QAAMA,CAACA,MAAM,EAAE;EAC7B,OAAOA,MAAM,IAAI,IAAI,GACjB,IAAI,CAACyR,EAAE,EAAE,GAAG,CAAC,GACb,IAAI,CAACA,EAAE,CAAC,IAAI+J,SAAS,CAACxb,MAAM,CAAC,CAAC4b,MAAM,CAAC,CAAC,CAAC,CAAC,CAAA;AAC9C;;;;;;;;;;;;;;AC9Be,MAAMqG,OAAO,SAASD,KAAK,CAAC;AACzC3c,EAAAA,WAAWA,CAAC3D,IAAI,EAAE6W,KAAK,GAAG7W,IAAI,EAAE;IAC9B,KAAK,CAACoC,SAAS,CAAC,SAAS,EAAEpC,IAAI,CAAC,EAAE6W,KAAK,CAAC,CAAA;AAC1C,GAAA;AAEApD,EAAAA,IAAIA,CAACpV,KAAK,EAAEC,MAAM,EAAE;IAClB,MAAM+F,CAAC,GAAGlG,gBAAgB,CAAC,IAAI,EAAEE,KAAK,EAAEC,MAAM,CAAC,CAAA;AAE/C,IAAA,OAAO,IAAI,CAACuR,EAAE,CAAC,IAAIiK,SAAS,CAACzV,CAAC,CAAChG,KAAK,CAAC,CAAC6b,MAAM,CAAC,CAAC,CAAC,CAAC,CAACnK,EAAE,CACjD,IAAI+J,SAAS,CAACzV,CAAC,CAAC/F,MAAM,CAAC,CAAC4b,MAAM,CAAC,CAAC,CAClC,CAAC,CAAA;AACH,GAAA;AACF,CAAA;AAEA3W,MAAM,CAACgd,OAAO,EAAEC,OAAO,CAAC,CAAA;AAExB3kB,eAAe,CAAC,WAAW,EAAE;AAC3B;EACA4kB,OAAO,EAAEhd,iBAAiB,CAAC,UAAUpF,KAAK,GAAG,CAAC,EAAEC,MAAM,GAAGD,KAAK,EAAE;IAC9D,OAAO,IAAI,CAACsd,GAAG,CAAC,IAAI4E,OAAO,EAAE,CAAC,CAAC9M,IAAI,CAACpV,KAAK,EAAEC,MAAM,CAAC,CAACmgB,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAAA;GAC9D,CAAA;AACH,CAAC,CAAC,CAAA;AAEF3b,QAAQ,CAACyd,OAAO,EAAE,SAAS,CAAC;;AC/B5B,MAAM7d,QAAQ,SAAS4Y,GAAG,CAAC;EACzB3X,WAAWA,CAAC3D,IAAI,GAAGS,OAAO,CAACE,QAAQ,CAACqd,sBAAsB,EAAE,EAAE;IAC5D,KAAK,CAAChe,IAAI,CAAC,CAAA;AACb,GAAA;;AAEA;AACAuc,EAAAA,GAAGA,CAACoB,OAAO,EAAEC,QAAQ,EAAEnc,EAAE,EAAE;AACzB,IAAA,IAAI,OAAOkc,OAAO,KAAK,SAAS,EAAE;AAChClc,MAAAA,EAAE,GAAGmc,QAAQ,CAAA;AACbA,MAAAA,QAAQ,GAAGD,OAAO,CAAA;AAClBA,MAAAA,OAAO,GAAG,IAAI,CAAA;AAChB,KAAA;;AAEA;AACA;IACA,IAAIA,OAAO,IAAI,IAAI,IAAI,OAAOA,OAAO,KAAK,UAAU,EAAE;MACpD,MAAM5b,OAAO,GAAG,IAAIuZ,GAAG,CAAC9Z,MAAM,CAAC,SAAS,EAAEC,EAAE,CAAC,CAAC,CAAA;MAC9CM,OAAO,CAACuC,GAAG,CAAC,IAAI,CAACtE,IAAI,CAACkc,SAAS,CAAC,IAAI,CAAC,CAAC,CAAA;AAEtC,MAAA,OAAOna,OAAO,CAACwa,GAAG,CAAC,KAAK,EAAE9a,EAAE,CAAC,CAAA;AAC/B,KAAA;;AAEA;IACA,OAAO,KAAK,CAAC8a,GAAG,CAACoB,OAAO,EAAE,KAAK,EAAElc,EAAE,CAAC,CAAA;AACtC,GAAA;AACF,CAAA;AAEAqB,QAAQ,CAACJ,QAAQ,EAAE,UAAU,CAAC;;AC7BvB,SAASge,IAAIA,CAAC1hB,CAAC,EAAEC,CAAC,EAAE;AACzB,EAAA,OAAO,CAAC,IAAI,CAACmgB,QAAQ,IAAI,IAAI,EAAErG,IAAI,KAAK,gBAAgB,GACpD,IAAI,CAAClV,IAAI,CAAC;AAAE8c,IAAAA,EAAE,EAAE,IAAI7G,SAAS,CAAC9a,CAAC,CAAC;AAAE4hB,IAAAA,EAAE,EAAE,IAAI9G,SAAS,CAAC7a,CAAC,CAAA;AAAE,GAAC,CAAC,GACzD,IAAI,CAAC4E,IAAI,CAAC;AAAEgd,IAAAA,EAAE,EAAE,IAAI/G,SAAS,CAAC9a,CAAC,CAAC;AAAE8hB,IAAAA,EAAE,EAAE,IAAIhH,SAAS,CAAC7a,CAAC,CAAA;AAAE,GAAC,CAAC,CAAA;AAC/D,CAAA;AAEO,SAAS8hB,EAAEA,CAAC/hB,CAAC,EAAEC,CAAC,EAAE;AACvB,EAAA,OAAO,CAAC,IAAI,CAACmgB,QAAQ,IAAI,IAAI,EAAErG,IAAI,KAAK,gBAAgB,GACpD,IAAI,CAAClV,IAAI,CAAC;AAAEsM,IAAAA,EAAE,EAAE,IAAI2J,SAAS,CAAC9a,CAAC,CAAC;AAAEoR,IAAAA,EAAE,EAAE,IAAI0J,SAAS,CAAC7a,CAAC,CAAA;AAAE,GAAC,CAAC,GACzD,IAAI,CAAC4E,IAAI,CAAC;AAAE4Q,IAAAA,EAAE,EAAE,IAAIqF,SAAS,CAAC9a,CAAC,CAAC;AAAE0V,IAAAA,EAAE,EAAE,IAAIoF,SAAS,CAAC7a,CAAC,CAAA;AAAE,GAAC,CAAC,CAAA;AAC/D;;;;;;;;ACAe,MAAM+hB,QAAQ,SAASd,SAAS,CAAC;AAC9Cvc,EAAAA,WAAWA,CAACoV,IAAI,EAAElC,KAAK,EAAE;AACvB,IAAA,KAAK,CACHzU,SAAS,CAAC2W,IAAI,GAAG,UAAU,EAAE,OAAOA,IAAI,KAAK,QAAQ,GAAG,IAAI,GAAGA,IAAI,CAAC,EACpElC,KACF,CAAC,CAAA;AACH,GAAA;;AAEA;AACAhT,EAAAA,IAAIA,CAACyD,CAAC,EAAEwB,CAAC,EAAE1C,CAAC,EAAE;AACZ,IAAA,IAAIkB,CAAC,KAAK,WAAW,EAAEA,CAAC,GAAG,mBAAmB,CAAA;IAC9C,OAAO,KAAK,CAACzD,IAAI,CAACyD,CAAC,EAAEwB,CAAC,EAAE1C,CAAC,CAAC,CAAA;AAC5B,GAAA;AAEA5H,EAAAA,IAAIA,GAAG;IACL,OAAO,IAAI0V,GAAG,EAAE,CAAA;AAClB,GAAA;AAEA+M,EAAAA,OAAOA,GAAG;IACR,OAAOnK,QAAQ,CAAC,aAAa,GAAG,IAAI,CAACxT,EAAE,EAAE,GAAG,GAAG,CAAC,CAAA;AAClD,GAAA;;AAEA;AACAmF,EAAAA,QAAQA,GAAG;AACT,IAAA,OAAO,IAAI,CAACyY,GAAG,EAAE,CAAA;AACnB,GAAA;;AAEA;EACAC,MAAMA,CAACrkB,KAAK,EAAE;AACZ;IACA,IAAI,CAAC8e,KAAK,EAAE,CAAA;;AAEZ;AACA,IAAA,IAAI,OAAO9e,KAAK,KAAK,UAAU,EAAE;AAC/BA,MAAAA,KAAK,CAAC8U,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,CAAA;AACxB,KAAA;AAEA,IAAA,OAAO,IAAI,CAAA;AACb,GAAA;;AAEA;AACAsP,EAAAA,GAAGA,GAAG;IACJ,OAAO,OAAO,GAAG,IAAI,CAAC5d,EAAE,EAAE,GAAG,GAAG,CAAA;AAClC,GAAA;AACF,CAAA;AAEAC,MAAM,CAACyd,QAAQ,EAAEI,UAAU,CAAC,CAAA;AAE5BvlB,eAAe,CAAC;AACdqkB,EAAAA,SAAS,EAAE;AACT;IACAmB,QAAQA,CAAC,GAAG3d,IAAI,EAAE;MAChB,OAAO,IAAI,CAAC6a,IAAI,EAAE,CAAC8C,QAAQ,CAAC,GAAG3d,IAAI,CAAC,CAAA;AACtC,KAAA;GACD;AACD;AACA2c,EAAAA,IAAI,EAAE;AACJgB,IAAAA,QAAQ,EAAE5d,iBAAiB,CAAC,UAAUsV,IAAI,EAAEjc,KAAK,EAAE;AACjD,MAAA,OAAO,IAAI,CAAC6e,GAAG,CAAC,IAAIqF,QAAQ,CAACjI,IAAI,CAAC,CAAC,CAACoI,MAAM,CAACrkB,KAAK,CAAC,CAAA;KAClD,CAAA;AACH,GAAA;AACF,CAAC,CAAC,CAAA;AAEFgG,QAAQ,CAACke,QAAQ,EAAE,UAAU,CAAC;;ACrEf,MAAMM,OAAO,SAASpB,SAAS,CAAC;AAC7C;AACAvc,EAAAA,WAAWA,CAAC3D,IAAI,EAAE6W,KAAK,GAAG7W,IAAI,EAAE;IAC9B,KAAK,CAACoC,SAAS,CAAC,SAAS,EAAEpC,IAAI,CAAC,EAAE6W,KAAK,CAAC,CAAA;AAC1C,GAAA;;AAEA;AACAhT,EAAAA,IAAIA,CAACyD,CAAC,EAAEwB,CAAC,EAAE1C,CAAC,EAAE;AACZ,IAAA,IAAIkB,CAAC,KAAK,WAAW,EAAEA,CAAC,GAAG,kBAAkB,CAAA;IAC7C,OAAO,KAAK,CAACzD,IAAI,CAACyD,CAAC,EAAEwB,CAAC,EAAE1C,CAAC,CAAC,CAAA;AAC5B,GAAA;AAEA5H,EAAAA,IAAIA,GAAG;IACL,OAAO,IAAI0V,GAAG,EAAE,CAAA;AAClB,GAAA;AAEA+M,EAAAA,OAAOA,GAAG;IACR,OAAOnK,QAAQ,CAAC,aAAa,GAAG,IAAI,CAACxT,EAAE,EAAE,GAAG,GAAG,CAAC,CAAA;AAClD,GAAA;;AAEA;AACAmF,EAAAA,QAAQA,GAAG;AACT,IAAA,OAAO,IAAI,CAACyY,GAAG,EAAE,CAAA;AACnB,GAAA;;AAEA;EACAC,MAAMA,CAACrkB,KAAK,EAAE;AACZ;IACA,IAAI,CAAC8e,KAAK,EAAE,CAAA;;AAEZ;AACA,IAAA,IAAI,OAAO9e,KAAK,KAAK,UAAU,EAAE;AAC/BA,MAAAA,KAAK,CAAC8U,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,CAAA;AACxB,KAAA;AAEA,IAAA,OAAO,IAAI,CAAA;AACb,GAAA;;AAEA;AACAsP,EAAAA,GAAGA,GAAG;IACJ,OAAO,OAAO,GAAG,IAAI,CAAC5d,EAAE,EAAE,GAAG,GAAG,CAAA;AAClC,GAAA;AACF,CAAA;AAEAzH,eAAe,CAAC;AACdqkB,EAAAA,SAAS,EAAE;AACT;IACAqB,OAAOA,CAAC,GAAG7d,IAAI,EAAE;MACf,OAAO,IAAI,CAAC6a,IAAI,EAAE,CAACgD,OAAO,CAAC,GAAG7d,IAAI,CAAC,CAAA;AACrC,KAAA;GACD;AACD2c,EAAAA,IAAI,EAAE;IACJkB,OAAO,EAAE9d,iBAAiB,CAAC,UAAUpF,KAAK,EAAEC,MAAM,EAAExB,KAAK,EAAE;AACzD,MAAA,OAAO,IAAI,CAAC6e,GAAG,CAAC,IAAI2F,OAAO,EAAE,CAAC,CAACH,MAAM,CAACrkB,KAAK,CAAC,CAAC+G,IAAI,CAAC;AAChD7E,QAAAA,CAAC,EAAE,CAAC;AACJC,QAAAA,CAAC,EAAE,CAAC;AACJZ,QAAAA,KAAK,EAAEA,KAAK;AACZC,QAAAA,MAAM,EAAEA,MAAM;AACdkjB,QAAAA,YAAY,EAAE,gBAAA;AAChB,OAAC,CAAC,CAAA;KACH,CAAA;AACH,GAAA;AACF,CAAC,CAAC,CAAA;AAEF1e,QAAQ,CAACwe,OAAO,EAAE,SAAS,CAAC;;AC5Db,MAAMG,KAAK,SAASnB,KAAK,CAAC;AACvC3c,EAAAA,WAAWA,CAAC3D,IAAI,EAAE6W,KAAK,GAAG7W,IAAI,EAAE;IAC9B,KAAK,CAACoC,SAAS,CAAC,OAAO,EAAEpC,IAAI,CAAC,EAAE6W,KAAK,CAAC,CAAA;AACxC,GAAA;;AAEA;AACA6K,EAAAA,IAAIA,CAACR,GAAG,EAAES,QAAQ,EAAE;AAClB,IAAA,IAAI,CAACT,GAAG,EAAE,OAAO,IAAI,CAAA;IAErB,MAAMU,GAAG,GAAG,IAAInhB,OAAO,CAACC,MAAM,CAAC+gB,KAAK,EAAE,CAAA;AAEtC9J,IAAAA,EAAE,CACAiK,GAAG,EACH,MAAM,EACN,UAAUla,CAAC,EAAE;AACX,MAAA,MAAMrD,CAAC,GAAG,IAAI,CAACN,MAAM,CAACud,OAAO,CAAC,CAAA;;AAE9B;AACA,MAAA,IAAI,IAAI,CAACjjB,KAAK,EAAE,KAAK,CAAC,IAAI,IAAI,CAACC,MAAM,EAAE,KAAK,CAAC,EAAE;QAC7C,IAAI,CAACmV,IAAI,CAACmO,GAAG,CAACvjB,KAAK,EAAEujB,GAAG,CAACtjB,MAAM,CAAC,CAAA;AAClC,OAAA;MAEA,IAAI+F,CAAC,YAAYid,OAAO,EAAE;AACxB;AACA,QAAA,IAAIjd,CAAC,CAAChG,KAAK,EAAE,KAAK,CAAC,IAAIgG,CAAC,CAAC/F,MAAM,EAAE,KAAK,CAAC,EAAE;AACvC+F,UAAAA,CAAC,CAACoP,IAAI,CAAC,IAAI,CAACpV,KAAK,EAAE,EAAE,IAAI,CAACC,MAAM,EAAE,CAAC,CAAA;AACrC,SAAA;AACF,OAAA;AAEA,MAAA,IAAI,OAAOqjB,QAAQ,KAAK,UAAU,EAAE;AAClCA,QAAAA,QAAQ,CAAC/P,IAAI,CAAC,IAAI,EAAElK,CAAC,CAAC,CAAA;AACxB,OAAA;KACD,EACD,IACF,CAAC,CAAA;AAEDiQ,IAAAA,EAAE,CAACiK,GAAG,EAAE,YAAY,EAAE,YAAY;AAChC;MACAvJ,GAAG,CAACuJ,GAAG,CAAC,CAAA;AACV,KAAC,CAAC,CAAA;AAEF,IAAA,OAAO,IAAI,CAAC/d,IAAI,CAAC,MAAM,EAAG+d,GAAG,CAACC,GAAG,GAAGX,GAAG,EAAG1gB,KAAK,CAAC,CAAA;AAClD,GAAA;AACF,CAAA;AAEAoa,gBAAgB,CAAC,UAAU/W,IAAI,EAAE2C,GAAG,EAAEqX,KAAK,EAAE;AAC3C;AACA,EAAA,IAAIha,IAAI,KAAK,MAAM,IAAIA,IAAI,KAAK,QAAQ,EAAE;AACxC,IAAA,IAAI4B,OAAO,CAACyB,IAAI,CAACV,GAAG,CAAC,EAAE;AACrBA,MAAAA,GAAG,GAAGqX,KAAK,CAACtc,IAAI,EAAE,CAACgd,IAAI,EAAE,CAACuD,KAAK,CAACtb,GAAG,CAAC,CAAA;AACtC,KAAA;AACF,GAAA;EAEA,IAAIA,GAAG,YAAYib,KAAK,EAAE;AACxBjb,IAAAA,GAAG,GAAGqX,KAAK,CACRtc,IAAI,EAAE,CACNgd,IAAI,EAAE,CACNgD,OAAO,CAAC,CAAC,EAAE,CAAC,EAAGA,OAAO,IAAK;AAC1BA,MAAAA,OAAO,CAACjd,GAAG,CAACkC,GAAG,CAAC,CAAA;AAClB,KAAC,CAAC,CAAA;AACN,GAAA;AAEA,EAAA,OAAOA,GAAG,CAAA;AACZ,CAAC,CAAC,CAAA;AAEF3K,eAAe,CAAC;AACdqkB,EAAAA,SAAS,EAAE;AACT;AACA4B,IAAAA,KAAK,EAAEre,iBAAiB,CAAC,UAAU6J,MAAM,EAAEqU,QAAQ,EAAE;MACnD,OAAO,IAAI,CAAChG,GAAG,CAAC,IAAI8F,KAAK,EAAE,CAAC,CAAChO,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAACiO,IAAI,CAACpU,MAAM,EAAEqU,QAAQ,CAAC,CAAA;KAC/D,CAAA;AACH,GAAA;AACF,CAAC,CAAC,CAAA;AAEF7e,QAAQ,CAAC2e,KAAK,EAAE,OAAO,CAAC;;AC/ET,MAAMM,UAAU,SAASnI,QAAQ,CAAC;AAC/C;AACApb,EAAAA,IAAIA,GAAG;IACL,IAAIwjB,IAAI,GAAG,CAAClN,QAAQ,CAAA;IACpB,IAAImN,IAAI,GAAG,CAACnN,QAAQ,CAAA;IACpB,IAAIoN,IAAI,GAAGpN,QAAQ,CAAA;IACnB,IAAIqN,IAAI,GAAGrN,QAAQ,CAAA;AACnB,IAAA,IAAI,CAACjO,OAAO,CAAC,UAAUD,EAAE,EAAE;MACzBob,IAAI,GAAG1kB,IAAI,CAACiL,GAAG,CAAC3B,EAAE,CAAC,CAAC,CAAC,EAAEob,IAAI,CAAC,CAAA;MAC5BC,IAAI,GAAG3kB,IAAI,CAACiL,GAAG,CAAC3B,EAAE,CAAC,CAAC,CAAC,EAAEqb,IAAI,CAAC,CAAA;MAC5BC,IAAI,GAAG5kB,IAAI,CAACkL,GAAG,CAAC5B,EAAE,CAAC,CAAC,CAAC,EAAEsb,IAAI,CAAC,CAAA;MAC5BC,IAAI,GAAG7kB,IAAI,CAACkL,GAAG,CAAC5B,EAAE,CAAC,CAAC,CAAC,EAAEub,IAAI,CAAC,CAAA;AAC9B,KAAC,CAAC,CAAA;AACF,IAAA,OAAO,IAAIjO,GAAG,CAACgO,IAAI,EAAEC,IAAI,EAAEH,IAAI,GAAGE,IAAI,EAAED,IAAI,GAAGE,IAAI,CAAC,CAAA;AACtD,GAAA;;AAEA;AACA1D,EAAAA,IAAIA,CAACzf,CAAC,EAAEC,CAAC,EAAE;AACT,IAAA,MAAMV,GAAG,GAAG,IAAI,CAACC,IAAI,EAAE,CAAA;;AAEvB;IACAQ,CAAC,IAAIT,GAAG,CAACS,CAAC,CAAA;IACVC,CAAC,IAAIV,GAAG,CAACU,CAAC,CAAA;;AAEV;IACA,IAAI,CAACmb,KAAK,CAACpb,CAAC,CAAC,IAAI,CAACob,KAAK,CAACnb,CAAC,CAAC,EAAE;AAC1B,MAAA,KAAK,IAAIlC,CAAC,GAAG,IAAI,CAACE,MAAM,GAAG,CAAC,EAAEF,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;QACzC,IAAI,CAACA,CAAC,CAAC,GAAG,CAAC,IAAI,CAACA,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGiC,CAAC,EAAE,IAAI,CAACjC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGkC,CAAC,CAAC,CAAA;AAC5C,OAAA;AACF,KAAA;AAEA,IAAA,OAAO,IAAI,CAAA;AACb,GAAA;;AAEA;EACAwI,KAAKA,CAAC5K,KAAK,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;IACpB,MAAMulB,MAAM,GAAG,EAAE,CAAA;;AAEjB;IACA,IAAIvlB,KAAK,YAAYb,KAAK,EAAE;AAC1Ba,MAAAA,KAAK,GAAGb,KAAK,CAACgH,SAAS,CAACyT,MAAM,CAAC7S,KAAK,CAAC,EAAE,EAAE/G,KAAK,CAAC,CAAA;AACjD,KAAC,MAAM;AACL;AACA;AACAA,MAAAA,KAAK,GAAGA,KAAK,CAACgJ,IAAI,EAAE,CAACC,KAAK,CAACJ,SAAS,CAAC,CAAC9I,GAAG,CAAC+U,UAAU,CAAC,CAAA;AACvD,KAAA;;AAEA;AACA;AACA,IAAA,IAAI9U,KAAK,CAACI,MAAM,GAAG,CAAC,KAAK,CAAC,EAAEJ,KAAK,CAACwlB,GAAG,EAAE,CAAA;;AAEvC;IACA,KAAK,IAAItlB,CAAC,GAAG,CAAC,EAAEkhB,GAAG,GAAGphB,KAAK,CAACI,MAAM,EAAEF,CAAC,GAAGkhB,GAAG,EAAElhB,CAAC,GAAGA,CAAC,GAAG,CAAC,EAAE;AACtDqlB,MAAAA,MAAM,CAACzlB,IAAI,CAAC,CAACE,KAAK,CAACE,CAAC,CAAC,EAAEF,KAAK,CAACE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAA;AACvC,KAAA;AAEA,IAAA,OAAOqlB,MAAM,CAAA;AACf,GAAA;;AAEA;AACA3O,EAAAA,IAAIA,CAACpV,KAAK,EAAEC,MAAM,EAAE;AAClB,IAAA,IAAIvB,CAAC,CAAA;AACL,IAAA,MAAMwB,GAAG,GAAG,IAAI,CAACC,IAAI,EAAE,CAAA;;AAEvB;AACA,IAAA,KAAKzB,CAAC,GAAG,IAAI,CAACE,MAAM,GAAG,CAAC,EAAEF,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;AACrC,MAAA,IAAIwB,GAAG,CAACF,KAAK,EACX,IAAI,CAACtB,CAAC,CAAC,CAAC,CAAC,CAAC,GAAI,CAAC,IAAI,CAACA,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGwB,GAAG,CAACS,CAAC,IAAIX,KAAK,GAAIE,GAAG,CAACF,KAAK,GAAGE,GAAG,CAACS,CAAC,CAAA;AACjE,MAAA,IAAIT,GAAG,CAACD,MAAM,EACZ,IAAI,CAACvB,CAAC,CAAC,CAAC,CAAC,CAAC,GAAI,CAAC,IAAI,CAACA,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGwB,GAAG,CAACU,CAAC,IAAIX,MAAM,GAAIC,GAAG,CAACD,MAAM,GAAGC,GAAG,CAACU,CAAC,CAAA;AACrE,KAAA;AAEA,IAAA,OAAO,IAAI,CAAA;AACb,GAAA;;AAEA;AACAqjB,EAAAA,MAAMA,GAAG;IACP,OAAO;AACLzB,MAAAA,EAAE,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACdC,MAAAA,EAAE,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACdrM,MAAAA,EAAE,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACdC,MAAAA,EAAE,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;KACd,CAAA;AACH,GAAA;;AAEA;AACAjM,EAAAA,QAAQA,GAAG;IACT,MAAM5L,KAAK,GAAG,EAAE,CAAA;AAChB;AACA,IAAA,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEC,EAAE,GAAG,IAAI,CAACC,MAAM,EAAEF,CAAC,GAAGC,EAAE,EAAED,CAAC,EAAE,EAAE;AAC7CF,MAAAA,KAAK,CAACF,IAAI,CAAC,IAAI,CAACI,CAAC,CAAC,CAACmJ,IAAI,CAAC,GAAG,CAAC,CAAC,CAAA;AAC/B,KAAA;AAEA,IAAA,OAAOrJ,KAAK,CAACqJ,IAAI,CAAC,GAAG,CAAC,CAAA;AACxB,GAAA;EAEAqH,SAASA,CAACxR,CAAC,EAAE;IACX,OAAO,IAAI,CAACqR,KAAK,EAAE,CAACI,UAAU,CAACzR,CAAC,CAAC,CAAA;AACnC,GAAA;;AAEA;EACAyR,UAAUA,CAACzR,CAAC,EAAE;AACZ,IAAA,IAAI,CAAC0R,MAAM,CAACC,YAAY,CAAC3R,CAAC,CAAC,EAAE;AAC3BA,MAAAA,CAAC,GAAG,IAAI0R,MAAM,CAAC1R,CAAC,CAAC,CAAA;AACnB,KAAA;IAEA,KAAK,IAAIgB,CAAC,GAAG,IAAI,CAACE,MAAM,EAAEF,CAAC,EAAE,GAAI;AAC/B;MACA,MAAM,CAACiC,CAAC,EAAEC,CAAC,CAAC,GAAG,IAAI,CAAClC,CAAC,CAAC,CAAA;MACtB,IAAI,CAACA,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGhB,CAAC,CAACuL,CAAC,GAAGtI,CAAC,GAAGjD,CAAC,CAACqK,CAAC,GAAGnH,CAAC,GAAGlD,CAAC,CAAC2L,CAAC,CAAA;MACpC,IAAI,CAAC3K,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGhB,CAAC,CAAC+M,CAAC,GAAG9J,CAAC,GAAGjD,CAAC,CAACsB,CAAC,GAAG4B,CAAC,GAAGlD,CAAC,CAAC4R,CAAC,CAAA;AACtC,KAAA;AAEA,IAAA,OAAO,IAAI,CAAA;AACb,GAAA;AACF;;ACtHO,MAAM4U,UAAU,GAAGR,UAAU,CAAA;;AAEpC;AACO,SAAS/iB,GAACA,CAACA,CAAC,EAAE;EACnB,OAAOA,CAAC,IAAI,IAAI,GAAG,IAAI,CAACR,IAAI,EAAE,CAACQ,CAAC,GAAG,IAAI,CAACyf,IAAI,CAACzf,CAAC,EAAE,IAAI,CAACR,IAAI,EAAE,CAACS,CAAC,CAAC,CAAA;AAChE,CAAA;;AAEA;AACO,SAASA,GAACA,CAACA,CAAC,EAAE;EACnB,OAAOA,CAAC,IAAI,IAAI,GAAG,IAAI,CAACT,IAAI,EAAE,CAACS,CAAC,GAAG,IAAI,CAACwf,IAAI,CAAC,IAAI,CAACjgB,IAAI,EAAE,CAACQ,CAAC,EAAEC,CAAC,CAAC,CAAA;AAChE,CAAA;;AAEA;AACO,SAASZ,OAAKA,CAACA,KAAK,EAAE;AAC3B,EAAA,MAAMyK,CAAC,GAAG,IAAI,CAACtK,IAAI,EAAE,CAAA;AACrB,EAAA,OAAOH,KAAK,IAAI,IAAI,GAAGyK,CAAC,CAACzK,KAAK,GAAG,IAAI,CAACoV,IAAI,CAACpV,KAAK,EAAEyK,CAAC,CAACxK,MAAM,CAAC,CAAA;AAC7D,CAAA;;AAEA;AACO,SAASA,QAAMA,CAACA,MAAM,EAAE;AAC7B,EAAA,MAAMwK,CAAC,GAAG,IAAI,CAACtK,IAAI,EAAE,CAAA;AACrB,EAAA,OAAOF,MAAM,IAAI,IAAI,GAAGwK,CAAC,CAACxK,MAAM,GAAG,IAAI,CAACmV,IAAI,CAAC3K,CAAC,CAACzK,KAAK,EAAEC,MAAM,CAAC,CAAA;AAC/D;;;;;;;;;;;ACZe,MAAMkkB,IAAI,SAASlC,KAAK,CAAC;AACtC;AACA3c,EAAAA,WAAWA,CAAC3D,IAAI,EAAE6W,KAAK,GAAG7W,IAAI,EAAE;IAC9B,KAAK,CAACoC,SAAS,CAAC,MAAM,EAAEpC,IAAI,CAAC,EAAE6W,KAAK,CAAC,CAAA;AACvC,GAAA;;AAEA;AACAha,EAAAA,KAAKA,GAAG;AACN,IAAA,OAAO,IAAIklB,UAAU,CAAC,CACpB,CAAC,IAAI,CAACle,IAAI,CAAC,IAAI,CAAC,EAAE,IAAI,CAACA,IAAI,CAAC,IAAI,CAAC,CAAC,EAClC,CAAC,IAAI,CAACA,IAAI,CAAC,IAAI,CAAC,EAAE,IAAI,CAACA,IAAI,CAAC,IAAI,CAAC,CAAC,CACnC,CAAC,CAAA;AACJ,GAAA;;AAEA;AACA4a,EAAAA,IAAIA,CAACzf,CAAC,EAAEC,CAAC,EAAE;IACT,OAAO,IAAI,CAAC4E,IAAI,CAAC,IAAI,CAAChH,KAAK,EAAE,CAAC4hB,IAAI,CAACzf,CAAC,EAAEC,CAAC,CAAC,CAACqjB,MAAM,EAAE,CAAC,CAAA;AACpD,GAAA;;AAEA;EACAG,IAAIA,CAAC5B,EAAE,EAAEC,EAAE,EAAErM,EAAE,EAAEC,EAAE,EAAE;IACnB,IAAImM,EAAE,IAAI,IAAI,EAAE;AACd,MAAA,OAAO,IAAI,CAAChkB,KAAK,EAAE,CAAA;AACrB,KAAC,MAAM,IAAI,OAAOikB,EAAE,KAAK,WAAW,EAAE;AACpCD,MAAAA,EAAE,GAAG;QAAEA,EAAE;QAAEC,EAAE;QAAErM,EAAE;AAAEC,QAAAA,EAAAA;OAAI,CAAA;AACzB,KAAC,MAAM;MACLmM,EAAE,GAAG,IAAIkB,UAAU,CAAClB,EAAE,CAAC,CAACyB,MAAM,EAAE,CAAA;AAClC,KAAA;AAEA,IAAA,OAAO,IAAI,CAACze,IAAI,CAACgd,EAAE,CAAC,CAAA;AACtB,GAAA;;AAEA;AACApN,EAAAA,IAAIA,CAACpV,KAAK,EAAEC,MAAM,EAAE;IAClB,MAAM+F,CAAC,GAAGlG,gBAAgB,CAAC,IAAI,EAAEE,KAAK,EAAEC,MAAM,CAAC,CAAA;IAC/C,OAAO,IAAI,CAACuF,IAAI,CAAC,IAAI,CAAChH,KAAK,EAAE,CAAC4W,IAAI,CAACpP,CAAC,CAAChG,KAAK,EAAEgG,CAAC,CAAC/F,MAAM,CAAC,CAACgkB,MAAM,EAAE,CAAC,CAAA;AACjE,GAAA;AACF,CAAA;AAEA/e,MAAM,CAACif,IAAI,EAAEE,OAAO,CAAC,CAAA;AAErB7mB,eAAe,CAAC;AACdqkB,EAAAA,SAAS,EAAE;AACT;AACAyC,IAAAA,IAAI,EAAElf,iBAAiB,CAAC,UAAU,GAAGC,IAAI,EAAE;AACzC;AACA;AACA,MAAA,OAAO8e,IAAI,CAACxf,SAAS,CAACyf,IAAI,CAAC7e,KAAK,CAC9B,IAAI,CAAC+X,GAAG,CAAC,IAAI6G,IAAI,EAAE,CAAC,EACpB9e,IAAI,CAAC,CAAC,CAAC,IAAI,IAAI,GAAGA,IAAI,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CACtC,CAAC,CAAA;KACF,CAAA;AACH,GAAA;AACF,CAAC,CAAC,CAAA;AAEFZ,QAAQ,CAAC0f,IAAI,EAAE,MAAM,CAAC;;AC/DP,MAAMI,MAAM,SAAS1C,SAAS,CAAC;AAC5C;AACAvc,EAAAA,WAAWA,CAAC3D,IAAI,EAAE6W,KAAK,GAAG7W,IAAI,EAAE;IAC9B,KAAK,CAACoC,SAAS,CAAC,QAAQ,EAAEpC,IAAI,CAAC,EAAE6W,KAAK,CAAC,CAAA;AACzC,GAAA;;AAEA;EACAvY,MAAMA,CAACA,MAAM,EAAE;AACb,IAAA,OAAO,IAAI,CAACuF,IAAI,CAAC,cAAc,EAAEvF,MAAM,CAAC,CAAA;AAC1C,GAAA;EAEAukB,MAAMA,CAACA,MAAM,EAAE;AACb,IAAA,OAAO,IAAI,CAAChf,IAAI,CAAC,QAAQ,EAAEgf,MAAM,CAAC,CAAA;AACpC,GAAA;;AAEA;AACAC,EAAAA,GAAGA,CAAC9jB,CAAC,EAAEC,CAAC,EAAE;AACR,IAAA,OAAO,IAAI,CAAC4E,IAAI,CAAC,MAAM,EAAE7E,CAAC,CAAC,CAAC6E,IAAI,CAAC,MAAM,EAAE5E,CAAC,CAAC,CAAA;AAC7C,GAAA;;AAEA;AACAwJ,EAAAA,QAAQA,GAAG;IACT,OAAO,OAAO,GAAG,IAAI,CAACnF,EAAE,EAAE,GAAG,GAAG,CAAA;AAClC,GAAA;;AAEA;EACA6d,MAAMA,CAACrkB,KAAK,EAAE;AACZ;IACA,IAAI,CAAC8e,KAAK,EAAE,CAAA;;AAEZ;AACA,IAAA,IAAI,OAAO9e,KAAK,KAAK,UAAU,EAAE;AAC/BA,MAAAA,KAAK,CAAC8U,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,CAAA;AACxB,KAAA;AAEA,IAAA,OAAO,IAAI,CAAA;AACb,GAAA;;AAEA;EACAvT,KAAKA,CAACA,KAAK,EAAE;AACX,IAAA,OAAO,IAAI,CAACwF,IAAI,CAAC,aAAa,EAAExF,KAAK,CAAC,CAAA;AACxC,GAAA;AACF,CAAA;AAEAxC,eAAe,CAAC;AACdqkB,EAAAA,SAAS,EAAE;IACT6C,MAAMA,CAAC,GAAGrf,IAAI,EAAE;AACd;MACA,OAAO,IAAI,CAAC6a,IAAI,EAAE,CAACwE,MAAM,CAAC,GAAGrf,IAAI,CAAC,CAAA;AACpC,KAAA;GACD;AACD2c,EAAAA,IAAI,EAAE;AACJ;IACA0C,MAAM,EAAEtf,iBAAiB,CAAC,UAAUpF,KAAK,EAAEC,MAAM,EAAExB,KAAK,EAAE;AACxD;MACA,OAAO,IAAI,CAAC6e,GAAG,CAAC,IAAIiH,MAAM,EAAE,CAAC,CAC1BnP,IAAI,CAACpV,KAAK,EAAEC,MAAM,CAAC,CACnBwkB,GAAG,CAACzkB,KAAK,GAAG,CAAC,EAAEC,MAAM,GAAG,CAAC,CAAC,CAC1BqX,OAAO,CAAC,CAAC,EAAE,CAAC,EAAEtX,KAAK,EAAEC,MAAM,CAAC,CAC5BuF,IAAI,CAAC,QAAQ,EAAE,MAAM,CAAC,CACtBsd,MAAM,CAACrkB,KAAK,CAAC,CAAA;KACjB,CAAA;GACF;AACDimB,EAAAA,MAAM,EAAE;AACN;IACAA,MAAMA,CAACA,MAAM,EAAE1kB,KAAK,EAAEC,MAAM,EAAExB,KAAK,EAAE;AACnC,MAAA,IAAI+G,IAAI,GAAG,CAAC,QAAQ,CAAC,CAAA;;AAErB;MACA,IAAIkf,MAAM,KAAK,KAAK,EAAElf,IAAI,CAAClH,IAAI,CAAComB,MAAM,CAAC,CAAA;AACvClf,MAAAA,IAAI,GAAGA,IAAI,CAACqC,IAAI,CAAC,GAAG,CAAC,CAAA;;AAErB;MACA6c,MAAM,GACJrc,SAAS,CAAC,CAAC,CAAC,YAAYkc,MAAM,GAC1Blc,SAAS,CAAC,CAAC,CAAC,GACZ,IAAI,CAAC6X,IAAI,EAAE,CAACwE,MAAM,CAAC1kB,KAAK,EAAEC,MAAM,EAAExB,KAAK,CAAC,CAAA;AAE9C,MAAA,OAAO,IAAI,CAAC+G,IAAI,CAACA,IAAI,EAAEkf,MAAM,CAAC,CAAA;AAChC,KAAA;AACF,GAAA;AACF,CAAC,CAAC,CAAA;AAEFjgB,QAAQ,CAAC8f,MAAM,EAAE,QAAQ,CAAC;;ACpF1B;AACA;AACA;AACA;AACA;;AAEA,SAASI,gBAAgBA,CAACpb,CAAC,EAAE+F,CAAC,EAAE;EAC9B,OAAO,UAAUpG,CAAC,EAAE;IAClB,IAAIA,CAAC,IAAI,IAAI,EAAE,OAAO,IAAI,CAACK,CAAC,CAAC,CAAA;AAC7B,IAAA,IAAI,CAACA,CAAC,CAAC,GAAGL,CAAC,CAAA;AACX,IAAA,IAAIoG,CAAC,EAAEA,CAAC,CAACiE,IAAI,CAAC,IAAI,CAAC,CAAA;AACnB,IAAA,OAAO,IAAI,CAAA;GACZ,CAAA;AACH,CAAA;AAEO,MAAMqR,MAAM,GAAG;AACpB,EAAA,GAAG,EAAE,UAAUC,GAAG,EAAE;AAClB,IAAA,OAAOA,GAAG,CAAA;GACX;AACD,EAAA,IAAI,EAAE,UAAUA,GAAG,EAAE;AACnB,IAAA,OAAO,CAAC5lB,IAAI,CAAC+N,GAAG,CAAC6X,GAAG,GAAG5lB,IAAI,CAACC,EAAE,CAAC,GAAG,CAAC,GAAG,GAAG,CAAA;GAC1C;AACD,EAAA,GAAG,EAAE,UAAU2lB,GAAG,EAAE;IAClB,OAAO5lB,IAAI,CAAC2M,GAAG,CAAEiZ,GAAG,GAAG5lB,IAAI,CAACC,EAAE,GAAI,CAAC,CAAC,CAAA;GACrC;AACD,EAAA,GAAG,EAAE,UAAU2lB,GAAG,EAAE;AAClB,IAAA,OAAO,CAAC5lB,IAAI,CAAC+N,GAAG,CAAE6X,GAAG,GAAG5lB,IAAI,CAACC,EAAE,GAAI,CAAC,CAAC,GAAG,CAAC,CAAA;GAC1C;EACD4lB,MAAM,EAAE,UAAUtC,EAAE,EAAEC,EAAE,EAAErM,EAAE,EAAEC,EAAE,EAAE;AAChC;IACA,OAAO,UAAU5N,CAAC,EAAE;MAClB,IAAIA,CAAC,GAAG,CAAC,EAAE;QACT,IAAI+Z,EAAE,GAAG,CAAC,EAAE;AACV,UAAA,OAAQC,EAAE,GAAGD,EAAE,GAAI/Z,CAAC,CAAA;AACtB,SAAC,MAAM,IAAI2N,EAAE,GAAG,CAAC,EAAE;AACjB,UAAA,OAAQC,EAAE,GAAGD,EAAE,GAAI3N,CAAC,CAAA;AACtB,SAAC,MAAM;AACL,UAAA,OAAO,CAAC,CAAA;AACV,SAAA;AACF,OAAC,MAAM,IAAIA,CAAC,GAAG,CAAC,EAAE;QAChB,IAAI2N,EAAE,GAAG,CAAC,EAAE;UACV,OAAQ,CAAC,CAAC,GAAGC,EAAE,KAAK,CAAC,GAAGD,EAAE,CAAC,GAAI3N,CAAC,GAAG,CAAC4N,EAAE,GAAGD,EAAE,KAAK,CAAC,GAAGA,EAAE,CAAC,CAAA;AACzD,SAAC,MAAM,IAAIoM,EAAE,GAAG,CAAC,EAAE;UACjB,OAAQ,CAAC,CAAC,GAAGC,EAAE,KAAK,CAAC,GAAGD,EAAE,CAAC,GAAI/Z,CAAC,GAAG,CAACga,EAAE,GAAGD,EAAE,KAAK,CAAC,GAAGA,EAAE,CAAC,CAAA;AACzD,SAAC,MAAM;AACL,UAAA,OAAO,CAAC,CAAA;AACV,SAAA;AACF,OAAC,MAAM;AACL,QAAA,OAAO,CAAC,GAAG/Z,CAAC,GAAG,CAAC,CAAC,GAAGA,CAAC,KAAK,CAAC,GAAGga,EAAE,GAAG,CAAC,GAAGha,CAAC,IAAI,CAAC,IAAI,CAAC,GAAGA,CAAC,CAAC,GAAG4N,EAAE,GAAG5N,CAAC,IAAI,CAAC,CAAA;AACvE,OAAA;KACD,CAAA;GACF;AACD;EACAsc,KAAK,EAAE,UAAUA,KAAK,EAAEC,YAAY,GAAG,KAAK,EAAE;AAC5C;AACAA,IAAAA,YAAY,GAAGA,YAAY,CAACvd,KAAK,CAAC,GAAG,CAAC,CAAC8Z,OAAO,EAAE,CAAC,CAAC,CAAC,CAAA;IAEnD,IAAI0D,KAAK,GAAGF,KAAK,CAAA;IACjB,IAAIC,YAAY,KAAK,MAAM,EAAE;AAC3B,MAAA,EAAEC,KAAK,CAAA;AACT,KAAC,MAAM,IAAID,YAAY,KAAK,MAAM,EAAE;AAClC,MAAA,EAAEC,KAAK,CAAA;AACT,KAAA;;AAEA;AACA,IAAA,OAAO,CAACxc,CAAC,EAAEyc,UAAU,GAAG,KAAK,KAAK;AAChC;MACA,IAAIC,IAAI,GAAGlmB,IAAI,CAACmmB,KAAK,CAAC3c,CAAC,GAAGsc,KAAK,CAAC,CAAA;MAChC,MAAMM,OAAO,GAAI5c,CAAC,GAAG0c,IAAI,GAAI,CAAC,KAAK,CAAC,CAAA;AAEpC,MAAA,IAAIH,YAAY,KAAK,OAAO,IAAIA,YAAY,KAAK,MAAM,EAAE;AACvD,QAAA,EAAEG,IAAI,CAAA;AACR,OAAA;MAEA,IAAID,UAAU,IAAIG,OAAO,EAAE;AACzB,QAAA,EAAEF,IAAI,CAAA;AACR,OAAA;AAEA,MAAA,IAAI1c,CAAC,IAAI,CAAC,IAAI0c,IAAI,GAAG,CAAC,EAAE;AACtBA,QAAAA,IAAI,GAAG,CAAC,CAAA;AACV,OAAA;AAEA,MAAA,IAAI1c,CAAC,IAAI,CAAC,IAAI0c,IAAI,GAAGF,KAAK,EAAE;AAC1BE,QAAAA,IAAI,GAAGF,KAAK,CAAA;AACd,OAAA;MAEA,OAAOE,IAAI,GAAGF,KAAK,CAAA;KACpB,CAAA;AACH,GAAA;AACF,EAAC;AAEM,MAAMK,OAAO,CAAC;AACnBC,EAAAA,IAAIA,GAAG;AACL,IAAA,OAAO,KAAK,CAAA;AACd,GAAA;AACF,CAAA;;AAEA;AACA;AACA;AACA;;AAEO,MAAMC,IAAI,SAASF,OAAO,CAAC;AAChChgB,EAAAA,WAAWA,CAACxC,EAAE,GAAGiY,QAAQ,CAACE,IAAI,EAAE;AAC9B,IAAA,KAAK,EAAE,CAAA;IACP,IAAI,CAACA,IAAI,GAAG2J,MAAM,CAAC9hB,EAAE,CAAC,IAAIA,EAAE,CAAA;AAC9B,GAAA;AAEAqiB,EAAAA,IAAIA,CAAC9C,IAAI,EAAEK,EAAE,EAAEmC,GAAG,EAAE;AAClB,IAAA,IAAI,OAAOxC,IAAI,KAAK,QAAQ,EAAE;AAC5B,MAAA,OAAOwC,GAAG,GAAG,CAAC,GAAGxC,IAAI,GAAGK,EAAE,CAAA;AAC5B,KAAA;AACA,IAAA,OAAOL,IAAI,GAAG,CAACK,EAAE,GAAGL,IAAI,IAAI,IAAI,CAACpH,IAAI,CAAC4J,GAAG,CAAC,CAAA;AAC5C,GAAA;AACF,CAAA;;AAEA;AACA;AACA;AACA;;AAEO,MAAMY,UAAU,SAASH,OAAO,CAAC;EACtChgB,WAAWA,CAACxC,EAAE,EAAE;AACd,IAAA,KAAK,EAAE,CAAA;IACP,IAAI,CAAC4iB,OAAO,GAAG5iB,EAAE,CAAA;AACnB,GAAA;EAEAyiB,IAAIA,CAACxd,CAAC,EAAE;IACN,OAAOA,CAAC,CAACwd,IAAI,CAAA;AACf,GAAA;EAEAJ,IAAIA,CAAC1Q,OAAO,EAAEkR,MAAM,EAAEC,EAAE,EAAE7d,CAAC,EAAE;IAC3B,OAAO,IAAI,CAAC2d,OAAO,CAACjR,OAAO,EAAEkR,MAAM,EAAEC,EAAE,EAAE7d,CAAC,CAAC,CAAA;AAC7C,GAAA;AACF,CAAA;AAEA,SAAS8d,WAAWA,GAAG;AACrB;EACA,MAAM7K,QAAQ,GAAG,CAAC,IAAI,CAAC8K,SAAS,IAAI,GAAG,IAAI,IAAI,CAAA;AAC/C,EAAA,MAAMC,SAAS,GAAG,IAAI,CAACC,UAAU,IAAI,CAAC,CAAA;;AAEtC;EACA,MAAMC,GAAG,GAAG,KAAK,CAAA;AACjB,EAAA,MAAMpa,EAAE,GAAG5M,IAAI,CAACC,EAAE,CAAA;EAClB,MAAMgnB,EAAE,GAAGjnB,IAAI,CAACknB,GAAG,CAACJ,SAAS,GAAG,GAAG,GAAGE,GAAG,CAAC,CAAA;AAC1C,EAAA,MAAMG,IAAI,GAAG,CAACF,EAAE,GAAGjnB,IAAI,CAAC4N,IAAI,CAAChB,EAAE,GAAGA,EAAE,GAAGqa,EAAE,GAAGA,EAAE,CAAC,CAAA;AAC/C,EAAA,MAAMG,EAAE,GAAG,GAAG,IAAID,IAAI,GAAGpL,QAAQ,CAAC,CAAA;;AAElC;AACA,EAAA,IAAI,CAAChc,CAAC,GAAG,CAAC,GAAGonB,IAAI,GAAGC,EAAE,CAAA;AACtB,EAAA,IAAI,CAAC9c,CAAC,GAAG8c,EAAE,GAAGA,EAAE,CAAA;AAClB,CAAA;AAEO,MAAMC,MAAM,SAASb,UAAU,CAAC;EACrCngB,WAAWA,CAAC0V,QAAQ,GAAG,GAAG,EAAE+K,SAAS,GAAG,CAAC,EAAE;AACzC,IAAA,KAAK,EAAE,CAAA;IACP,IAAI,CAAC/K,QAAQ,CAACA,QAAQ,CAAC,CAAC+K,SAAS,CAACA,SAAS,CAAC,CAAA;AAC9C,GAAA;EAEAZ,IAAIA,CAAC1Q,OAAO,EAAEkR,MAAM,EAAEC,EAAE,EAAE7d,CAAC,EAAE;AAC3B,IAAA,IAAI,OAAO0M,OAAO,KAAK,QAAQ,EAAE,OAAOA,OAAO,CAAA;AAC/C1M,IAAAA,CAAC,CAACwd,IAAI,GAAGK,EAAE,KAAKnP,QAAQ,CAAA;AACxB,IAAA,IAAImP,EAAE,KAAKnP,QAAQ,EAAE,OAAOkP,MAAM,CAAA;AAClC,IAAA,IAAIC,EAAE,KAAK,CAAC,EAAE,OAAOnR,OAAO,CAAA;AAE5B,IAAA,IAAImR,EAAE,GAAG,GAAG,EAAEA,EAAE,GAAG,EAAE,CAAA;AAErBA,IAAAA,EAAE,IAAI,IAAI,CAAA;;AAEV;AACA,IAAA,MAAMW,QAAQ,GAAGxe,CAAC,CAACwe,QAAQ,IAAI,CAAC,CAAA;;AAEhC;AACA,IAAA,MAAMC,YAAY,GAAG,CAAC,IAAI,CAACxnB,CAAC,GAAGunB,QAAQ,GAAG,IAAI,CAAChd,CAAC,IAAIkL,OAAO,GAAGkR,MAAM,CAAC,CAAA;AACrE,IAAA,MAAMc,WAAW,GAAGhS,OAAO,GAAG8R,QAAQ,GAAGX,EAAE,GAAIY,YAAY,GAAGZ,EAAE,GAAGA,EAAE,GAAI,CAAC,CAAA;;AAE1E;AACA7d,IAAAA,CAAC,CAACwe,QAAQ,GAAGA,QAAQ,GAAGC,YAAY,GAAGZ,EAAE,CAAA;;AAEzC;AACA7d,IAAAA,CAAC,CAACwd,IAAI,GAAGtmB,IAAI,CAAC2Q,GAAG,CAAC+V,MAAM,GAAGc,WAAW,CAAC,GAAGxnB,IAAI,CAAC2Q,GAAG,CAAC2W,QAAQ,CAAC,GAAG,KAAK,CAAA;AACpE,IAAA,OAAOxe,CAAC,CAACwd,IAAI,GAAGI,MAAM,GAAGc,WAAW,CAAA;AACtC,GAAA;AACF,CAAA;AAEAvhB,MAAM,CAACohB,MAAM,EAAE;AACbtL,EAAAA,QAAQ,EAAE2J,gBAAgB,CAAC,WAAW,EAAEkB,WAAW,CAAC;AACpDE,EAAAA,SAAS,EAAEpB,gBAAgB,CAAC,YAAY,EAAEkB,WAAW,CAAA;AACvD,CAAC,CAAC,CAAA;AAEK,MAAMa,GAAG,SAASjB,UAAU,CAAC;AAClCngB,EAAAA,WAAWA,CAACU,CAAC,GAAG,GAAG,EAAEtH,CAAC,GAAG,IAAI,EAAEM,CAAC,GAAG,CAAC,EAAE2nB,MAAM,GAAG,IAAI,EAAE;AACnD,IAAA,KAAK,EAAE,CAAA;AACP,IAAA,IAAI,CAAC3gB,CAAC,CAACA,CAAC,CAAC,CAACtH,CAAC,CAACA,CAAC,CAAC,CAACM,CAAC,CAACA,CAAC,CAAC,CAAC2nB,MAAM,CAACA,MAAM,CAAC,CAAA;AACpC,GAAA;EAEAxB,IAAIA,CAAC1Q,OAAO,EAAEkR,MAAM,EAAEC,EAAE,EAAE7d,CAAC,EAAE;AAC3B,IAAA,IAAI,OAAO0M,OAAO,KAAK,QAAQ,EAAE,OAAOA,OAAO,CAAA;AAC/C1M,IAAAA,CAAC,CAACwd,IAAI,GAAGK,EAAE,KAAKnP,QAAQ,CAAA;AAExB,IAAA,IAAImP,EAAE,KAAKnP,QAAQ,EAAE,OAAOkP,MAAM,CAAA;AAClC,IAAA,IAAIC,EAAE,KAAK,CAAC,EAAE,OAAOnR,OAAO,CAAA;AAE5B,IAAA,MAAMzO,CAAC,GAAG2f,MAAM,GAAGlR,OAAO,CAAA;IAC1B,IAAI/V,CAAC,GAAG,CAACqJ,CAAC,CAAC6e,QAAQ,IAAI,CAAC,IAAI5gB,CAAC,GAAG4f,EAAE,CAAA;AAClC,IAAA,MAAM5mB,CAAC,GAAG,CAACgH,CAAC,IAAI+B,CAAC,CAAC8e,KAAK,IAAI,CAAC,CAAC,IAAIjB,EAAE,CAAA;AACnC,IAAA,MAAMe,MAAM,GAAG,IAAI,CAACG,OAAO,CAAA;;AAE3B;IACA,IAAIH,MAAM,KAAK,KAAK,EAAE;AACpBjoB,MAAAA,CAAC,GAAGO,IAAI,CAACiL,GAAG,CAAC,CAACyc,MAAM,EAAE1nB,IAAI,CAACkL,GAAG,CAACzL,CAAC,EAAEioB,MAAM,CAAC,CAAC,CAAA;AAC5C,KAAA;IAEA5e,CAAC,CAAC8e,KAAK,GAAG7gB,CAAC,CAAA;IACX+B,CAAC,CAAC6e,QAAQ,GAAGloB,CAAC,CAAA;IAEdqJ,CAAC,CAACwd,IAAI,GAAGtmB,IAAI,CAAC2Q,GAAG,CAAC5J,CAAC,CAAC,GAAG,KAAK,CAAA;IAE5B,OAAO+B,CAAC,CAACwd,IAAI,GAAGI,MAAM,GAAGlR,OAAO,IAAI,IAAI,CAACsS,CAAC,GAAG/gB,CAAC,GAAG,IAAI,CAACghB,CAAC,GAAGtoB,CAAC,GAAG,IAAI,CAACuoB,CAAC,GAAGjoB,CAAC,CAAC,CAAA;AAC3E,GAAA;AACF,CAAA;AAEAkG,MAAM,CAACwhB,GAAG,EAAE;AACVC,EAAAA,MAAM,EAAEhC,gBAAgB,CAAC,SAAS,CAAC;AACnC3e,EAAAA,CAAC,EAAE2e,gBAAgB,CAAC,GAAG,CAAC;AACxBjmB,EAAAA,CAAC,EAAEimB,gBAAgB,CAAC,GAAG,CAAC;EACxB3lB,CAAC,EAAE2lB,gBAAgB,CAAC,GAAG,CAAA;AACzB,CAAC,CAAC;;ACnOF,MAAMuC,iBAAiB,GAAG;AACxBC,EAAAA,CAAC,EAAE,CAAC;AACJC,EAAAA,CAAC,EAAE,CAAC;AACJC,EAAAA,CAAC,EAAE,CAAC;AACJC,EAAAA,CAAC,EAAE,CAAC;AACJC,EAAAA,CAAC,EAAE,CAAC;AACJC,EAAAA,CAAC,EAAE,CAAC;AACJC,EAAAA,CAAC,EAAE,CAAC;AACJC,EAAAA,CAAC,EAAE,CAAC;AACJC,EAAAA,CAAC,EAAE,CAAC;AACJC,EAAAA,CAAC,EAAE,CAAA;AACL,CAAC,CAAA;AAED,MAAMC,YAAY,GAAG;EACnBV,CAAC,EAAE,UAAUpf,CAAC,EAAE/B,CAAC,EAAE8hB,EAAE,EAAE;IACrB9hB,CAAC,CAACrF,CAAC,GAAGmnB,EAAE,CAACnnB,CAAC,GAAGoH,CAAC,CAAC,CAAC,CAAC,CAAA;IACjB/B,CAAC,CAACpF,CAAC,GAAGknB,EAAE,CAAClnB,CAAC,GAAGmH,CAAC,CAAC,CAAC,CAAC,CAAA;IAEjB,OAAO,CAAC,GAAG,EAAE/B,CAAC,CAACrF,CAAC,EAAEqF,CAAC,CAACpF,CAAC,CAAC,CAAA;GACvB;AACDwmB,EAAAA,CAAC,EAAE,UAAUrf,CAAC,EAAE/B,CAAC,EAAE;AACjBA,IAAAA,CAAC,CAACrF,CAAC,GAAGoH,CAAC,CAAC,CAAC,CAAC,CAAA;AACV/B,IAAAA,CAAC,CAACpF,CAAC,GAAGmH,CAAC,CAAC,CAAC,CAAC,CAAA;AACV,IAAA,OAAO,CAAC,GAAG,EAAEA,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;GACzB;AACDsf,EAAAA,CAAC,EAAE,UAAUtf,CAAC,EAAE/B,CAAC,EAAE;AACjBA,IAAAA,CAAC,CAACrF,CAAC,GAAGoH,CAAC,CAAC,CAAC,CAAC,CAAA;AACV,IAAA,OAAO,CAAC,GAAG,EAAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;GACnB;AACDuf,EAAAA,CAAC,EAAE,UAAUvf,CAAC,EAAE/B,CAAC,EAAE;AACjBA,IAAAA,CAAC,CAACpF,CAAC,GAAGmH,CAAC,CAAC,CAAC,CAAC,CAAA;AACV,IAAA,OAAO,CAAC,GAAG,EAAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;GACnB;AACDwf,EAAAA,CAAC,EAAE,UAAUxf,CAAC,EAAE/B,CAAC,EAAE;AACjBA,IAAAA,CAAC,CAACrF,CAAC,GAAGoH,CAAC,CAAC,CAAC,CAAC,CAAA;AACV/B,IAAAA,CAAC,CAACpF,CAAC,GAAGmH,CAAC,CAAC,CAAC,CAAC,CAAA;AACV,IAAA,OAAO,CAAC,GAAG,EAAEA,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;GACjD;AACDyf,EAAAA,CAAC,EAAE,UAAUzf,CAAC,EAAE/B,CAAC,EAAE;AACjBA,IAAAA,CAAC,CAACrF,CAAC,GAAGoH,CAAC,CAAC,CAAC,CAAC,CAAA;AACV/B,IAAAA,CAAC,CAACpF,CAAC,GAAGmH,CAAC,CAAC,CAAC,CAAC,CAAA;IACV,OAAO,CAAC,GAAG,EAAEA,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;GACrC;AACD0f,EAAAA,CAAC,EAAE,UAAU1f,CAAC,EAAE/B,CAAC,EAAE;AACjBA,IAAAA,CAAC,CAACrF,CAAC,GAAGoH,CAAC,CAAC,CAAC,CAAC,CAAA;AACV/B,IAAAA,CAAC,CAACpF,CAAC,GAAGmH,CAAC,CAAC,CAAC,CAAC,CAAA;IACV,OAAO,CAAC,GAAG,EAAEA,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;GACrC;AACD2f,EAAAA,CAAC,EAAE,UAAU3f,CAAC,EAAE/B,CAAC,EAAE;AACjBA,IAAAA,CAAC,CAACrF,CAAC,GAAGoH,CAAC,CAAC,CAAC,CAAC,CAAA;AACV/B,IAAAA,CAAC,CAACpF,CAAC,GAAGmH,CAAC,CAAC,CAAC,CAAC,CAAA;AACV,IAAA,OAAO,CAAC,GAAG,EAAEA,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;GACzB;EACD6f,CAAC,EAAE,UAAU7f,CAAC,EAAE/B,CAAC,EAAE8hB,EAAE,EAAE;AACrB9hB,IAAAA,CAAC,CAACrF,CAAC,GAAGmnB,EAAE,CAACnnB,CAAC,CAAA;AACVqF,IAAAA,CAAC,CAACpF,CAAC,GAAGknB,EAAE,CAAClnB,CAAC,CAAA;IACV,OAAO,CAAC,GAAG,CAAC,CAAA;GACb;AACD+mB,EAAAA,CAAC,EAAE,UAAU5f,CAAC,EAAE/B,CAAC,EAAE;AACjBA,IAAAA,CAAC,CAACrF,CAAC,GAAGoH,CAAC,CAAC,CAAC,CAAC,CAAA;AACV/B,IAAAA,CAAC,CAACpF,CAAC,GAAGmH,CAAC,CAAC,CAAC,CAAC,CAAA;AACV,IAAA,OAAO,CAAC,GAAG,EAAEA,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;AACxD,GAAA;AACF,CAAC,CAAA;AAED,MAAMggB,UAAU,GAAG,YAAY,CAACtgB,KAAK,CAAC,EAAE,CAAC,CAAA;AAEzC,KAAK,IAAI/I,CAAC,GAAG,CAAC,EAAEC,EAAE,GAAGopB,UAAU,CAACnpB,MAAM,EAAEF,CAAC,GAAGC,EAAE,EAAE,EAAED,CAAC,EAAE;EACnDmpB,YAAY,CAACE,UAAU,CAACrpB,CAAC,CAAC,CAAC,GAAI,UAAUA,CAAC,EAAE;AAC1C,IAAA,OAAO,UAAUqJ,CAAC,EAAE/B,CAAC,EAAE8hB,EAAE,EAAE;AACzB,MAAA,IAAIppB,CAAC,KAAK,GAAG,EAAEqJ,CAAC,CAAC,CAAC,CAAC,GAAGA,CAAC,CAAC,CAAC,CAAC,GAAG/B,CAAC,CAACrF,CAAC,MAC3B,IAAIjC,CAAC,KAAK,GAAG,EAAEqJ,CAAC,CAAC,CAAC,CAAC,GAAGA,CAAC,CAAC,CAAC,CAAC,GAAG/B,CAAC,CAACpF,CAAC,CAAA,KAChC,IAAIlC,CAAC,KAAK,GAAG,EAAE;QAClBqJ,CAAC,CAAC,CAAC,CAAC,GAAGA,CAAC,CAAC,CAAC,CAAC,GAAG/B,CAAC,CAACrF,CAAC,CAAA;QACjBoH,CAAC,CAAC,CAAC,CAAC,GAAGA,CAAC,CAAC,CAAC,CAAC,GAAG/B,CAAC,CAACpF,CAAC,CAAA;AACnB,OAAC,MAAM;AACL,QAAA,KAAK,IAAI+Z,CAAC,GAAG,CAAC,EAAEqN,EAAE,GAAGjgB,CAAC,CAACnJ,MAAM,EAAE+b,CAAC,GAAGqN,EAAE,EAAE,EAAErN,CAAC,EAAE;UAC1C5S,CAAC,CAAC4S,CAAC,CAAC,GAAG5S,CAAC,CAAC4S,CAAC,CAAC,IAAIA,CAAC,GAAG,CAAC,GAAG3U,CAAC,CAACpF,CAAC,GAAGoF,CAAC,CAACrF,CAAC,CAAC,CAAA;AACnC,SAAA;AACF,OAAA;MAEA,OAAOknB,YAAY,CAACnpB,CAAC,CAAC,CAACqJ,CAAC,EAAE/B,CAAC,EAAE8hB,EAAE,CAAC,CAAA;KACjC,CAAA;GACF,CAAEC,UAAU,CAACrpB,CAAC,CAAC,CAACkB,WAAW,EAAE,CAAC,CAAA;AACjC,CAAA;AAEA,SAASqoB,WAAWA,CAAC/S,MAAM,EAAE;AAC3B,EAAA,MAAMgT,OAAO,GAAGhT,MAAM,CAACiT,OAAO,CAAC,CAAC,CAAC,CAAA;EACjC,OAAON,YAAY,CAACK,OAAO,CAAC,CAAChT,MAAM,CAACiT,OAAO,CAACtoB,KAAK,CAAC,CAAC,CAAC,EAAEqV,MAAM,CAAClP,CAAC,EAAEkP,MAAM,CAAC4S,EAAE,CAAC,CAAA;AAC5E,CAAA;AAEA,SAASM,eAAeA,CAAClT,MAAM,EAAE;EAC/B,OACEA,MAAM,CAACiT,OAAO,CAACvpB,MAAM,IACrBsW,MAAM,CAACiT,OAAO,CAACvpB,MAAM,GAAG,CAAC,KACvBsoB,iBAAiB,CAAChS,MAAM,CAACiT,OAAO,CAAC,CAAC,CAAC,CAACvoB,WAAW,EAAE,CAAC,CAAA;AAExD,CAAA;AAEA,SAASyoB,eAAeA,CAACnT,MAAM,EAAEoT,KAAK,EAAE;EACtCpT,MAAM,CAACqT,QAAQ,IAAIC,cAAc,CAACtT,MAAM,EAAE,KAAK,CAAC,CAAA;AAChD,EAAA,MAAMuT,UAAU,GAAGnhB,YAAY,CAACuB,IAAI,CAACyf,KAAK,CAAC,CAAA;AAE3C,EAAA,IAAIG,UAAU,EAAE;AACdvT,IAAAA,MAAM,CAACiT,OAAO,GAAG,CAACG,KAAK,CAAC,CAAA;AAC1B,GAAC,MAAM;AACL,IAAA,MAAMI,WAAW,GAAGxT,MAAM,CAACwT,WAAW,CAAA;AACtC,IAAA,MAAMC,KAAK,GAAGD,WAAW,CAACjpB,WAAW,EAAE,CAAA;AACvC,IAAA,MAAMmpB,OAAO,GAAGF,WAAW,KAAKC,KAAK,CAAA;AACrCzT,IAAAA,MAAM,CAACiT,OAAO,GAAG,CAACQ,KAAK,KAAK,GAAG,GAAIC,OAAO,GAAG,GAAG,GAAG,GAAG,GAAIF,WAAW,CAAC,CAAA;AACxE,GAAA;EAEAxT,MAAM,CAAC2T,SAAS,GAAG,IAAI,CAAA;EACvB3T,MAAM,CAACwT,WAAW,GAAGxT,MAAM,CAACiT,OAAO,CAAC,CAAC,CAAC,CAAA;AAEtC,EAAA,OAAOM,UAAU,CAAA;AACnB,CAAA;AAEA,SAASD,cAAcA,CAACtT,MAAM,EAAEqT,QAAQ,EAAE;EACxC,IAAI,CAACrT,MAAM,CAACqT,QAAQ,EAAE,MAAM,IAAIxc,KAAK,CAAC,cAAc,CAAC,CAAA;AACrDmJ,EAAAA,MAAM,CAAC4G,MAAM,IAAI5G,MAAM,CAACiT,OAAO,CAAC7pB,IAAI,CAACgV,UAAU,CAAC4B,MAAM,CAAC4G,MAAM,CAAC,CAAC,CAAA;EAC/D5G,MAAM,CAACqT,QAAQ,GAAGA,QAAQ,CAAA;EAC1BrT,MAAM,CAAC4G,MAAM,GAAG,EAAE,CAAA;EAClB5G,MAAM,CAAC4T,SAAS,GAAG,KAAK,CAAA;EACxB5T,MAAM,CAAC6T,WAAW,GAAG,KAAK,CAAA;AAE1B,EAAA,IAAIX,eAAe,CAAClT,MAAM,CAAC,EAAE;IAC3B8T,eAAe,CAAC9T,MAAM,CAAC,CAAA;AACzB,GAAA;AACF,CAAA;AAEA,SAAS8T,eAAeA,CAAC9T,MAAM,EAAE;EAC/BA,MAAM,CAAC2T,SAAS,GAAG,KAAK,CAAA;EACxB,IAAI3T,MAAM,CAAC+T,QAAQ,EAAE;AACnB/T,IAAAA,MAAM,CAACiT,OAAO,GAAGF,WAAW,CAAC/S,MAAM,CAAC,CAAA;AACtC,GAAA;EACAA,MAAM,CAACgU,QAAQ,CAAC5qB,IAAI,CAAC4W,MAAM,CAACiT,OAAO,CAAC,CAAA;AACtC,CAAA;AAEA,SAASgB,SAASA,CAACjU,MAAM,EAAE;EACzB,IAAI,CAACA,MAAM,CAACiT,OAAO,CAACvpB,MAAM,EAAE,OAAO,KAAK,CAAA;AACxC,EAAA,MAAMwqB,KAAK,GAAGlU,MAAM,CAACiT,OAAO,CAAC,CAAC,CAAC,CAACvoB,WAAW,EAAE,KAAK,GAAG,CAAA;AACrD,EAAA,MAAMhB,MAAM,GAAGsW,MAAM,CAACiT,OAAO,CAACvpB,MAAM,CAAA;EAEpC,OAAOwqB,KAAK,KAAKxqB,MAAM,KAAK,CAAC,IAAIA,MAAM,KAAK,CAAC,CAAC,CAAA;AAChD,CAAA;AAEA,SAASyqB,aAAaA,CAACnU,MAAM,EAAE;EAC7B,OAAOA,MAAM,CAACoU,SAAS,CAAC1pB,WAAW,EAAE,KAAK,GAAG,CAAA;AAC/C,CAAA;AAEA,MAAM2pB,cAAc,GAAG,IAAInrB,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,CAAA;AAC3D,SAASorB,UAAUA,CAACxqB,CAAC,EAAEyqB,UAAU,GAAG,IAAI,EAAE;EAC/C,IAAI7jB,KAAK,GAAG,CAAC,CAAA;EACb,IAAI0iB,KAAK,GAAG,EAAE,CAAA;AACd,EAAA,MAAMpT,MAAM,GAAG;AACbiT,IAAAA,OAAO,EAAE,EAAE;AACXI,IAAAA,QAAQ,EAAE,KAAK;AACfzM,IAAAA,MAAM,EAAE,EAAE;AACVwN,IAAAA,SAAS,EAAE,EAAE;AACbT,IAAAA,SAAS,EAAE,KAAK;AAChBK,IAAAA,QAAQ,EAAE,EAAE;AACZJ,IAAAA,SAAS,EAAE,KAAK;AAChBC,IAAAA,WAAW,EAAE,KAAK;AAClBE,IAAAA,QAAQ,EAAEQ,UAAU;AACpB3B,IAAAA,EAAE,EAAE,IAAIhZ,KAAK,EAAE;IACf9I,CAAC,EAAE,IAAI8I,KAAK,EAAC;GACd,CAAA;AAED,EAAA,OAASoG,MAAM,CAACoU,SAAS,GAAGhB,KAAK,EAAIA,KAAK,GAAGtpB,CAAC,CAACW,MAAM,CAACiG,KAAK,EAAE,CAAE,EAAG;AAChE,IAAA,IAAI,CAACsP,MAAM,CAAC2T,SAAS,EAAE;AACrB,MAAA,IAAIR,eAAe,CAACnT,MAAM,EAAEoT,KAAK,CAAC,EAAE;AAClC,QAAA,SAAA;AACF,OAAA;AACF,KAAA;IAEA,IAAIA,KAAK,KAAK,GAAG,EAAE;AACjB,MAAA,IAAIpT,MAAM,CAAC4T,SAAS,IAAI5T,MAAM,CAAC6T,WAAW,EAAE;AAC1CP,QAAAA,cAAc,CAACtT,MAAM,EAAE,KAAK,CAAC,CAAA;AAC7B,QAAA,EAAEtP,KAAK,CAAA;AACP,QAAA,SAAA;AACF,OAAA;MACAsP,MAAM,CAACqT,QAAQ,GAAG,IAAI,CAAA;MACtBrT,MAAM,CAAC4T,SAAS,GAAG,IAAI,CAAA;MACvB5T,MAAM,CAAC4G,MAAM,IAAIwM,KAAK,CAAA;AACtB,MAAA,SAAA;AACF,KAAA;IAEA,IAAI,CAACvM,KAAK,CAACxP,QAAQ,CAAC+b,KAAK,CAAC,CAAC,EAAE;MAC3B,IAAIpT,MAAM,CAAC4G,MAAM,KAAK,GAAG,IAAIqN,SAAS,CAACjU,MAAM,CAAC,EAAE;QAC9CA,MAAM,CAACqT,QAAQ,GAAG,IAAI,CAAA;QACtBrT,MAAM,CAAC4G,MAAM,GAAGwM,KAAK,CAAA;AACrBE,QAAAA,cAAc,CAACtT,MAAM,EAAE,IAAI,CAAC,CAAA;AAC5B,QAAA,SAAA;AACF,OAAA;MAEAA,MAAM,CAACqT,QAAQ,GAAG,IAAI,CAAA;MACtBrT,MAAM,CAAC4G,MAAM,IAAIwM,KAAK,CAAA;AACtB,MAAA,SAAA;AACF,KAAA;AAEA,IAAA,IAAIiB,cAAc,CAACroB,GAAG,CAAConB,KAAK,CAAC,EAAE;MAC7B,IAAIpT,MAAM,CAACqT,QAAQ,EAAE;AACnBC,QAAAA,cAAc,CAACtT,MAAM,EAAE,KAAK,CAAC,CAAA;AAC/B,OAAA;AACA,MAAA,SAAA;AACF,KAAA;AAEA,IAAA,IAAIoT,KAAK,KAAK,GAAG,IAAIA,KAAK,KAAK,GAAG,EAAE;MAClC,IAAIpT,MAAM,CAACqT,QAAQ,IAAI,CAACc,aAAa,CAACnU,MAAM,CAAC,EAAE;AAC7CsT,QAAAA,cAAc,CAACtT,MAAM,EAAE,KAAK,CAAC,CAAA;AAC7B,QAAA,EAAEtP,KAAK,CAAA;AACP,QAAA,SAAA;AACF,OAAA;MACAsP,MAAM,CAAC4G,MAAM,IAAIwM,KAAK,CAAA;MACtBpT,MAAM,CAACqT,QAAQ,GAAG,IAAI,CAAA;AACtB,MAAA,SAAA;AACF,KAAA;AAEA,IAAA,IAAID,KAAK,CAAC1oB,WAAW,EAAE,KAAK,GAAG,EAAE;MAC/BsV,MAAM,CAAC4G,MAAM,IAAIwM,KAAK,CAAA;MACtBpT,MAAM,CAAC6T,WAAW,GAAG,IAAI,CAAA;AACzB,MAAA,SAAA;AACF,KAAA;AAEA,IAAA,IAAIzhB,YAAY,CAACuB,IAAI,CAACyf,KAAK,CAAC,EAAE;MAC5B,IAAIpT,MAAM,CAACqT,QAAQ,EAAE;AACnBC,QAAAA,cAAc,CAACtT,MAAM,EAAE,KAAK,CAAC,CAAA;AAC/B,OAAC,MAAM,IAAI,CAACkT,eAAe,CAAClT,MAAM,CAAC,EAAE;AACnC,QAAA,MAAM,IAAInJ,KAAK,CAAC,cAAc,CAAC,CAAA;AACjC,OAAC,MAAM;QACLid,eAAe,CAAC9T,MAAM,CAAC,CAAA;AACzB,OAAA;AACA,MAAA,EAAEtP,KAAK,CAAA;AACT,KAAA;AACF,GAAA;EAEA,IAAIsP,MAAM,CAACqT,QAAQ,EAAE;AACnBC,IAAAA,cAAc,CAACtT,MAAM,EAAE,KAAK,CAAC,CAAA;AAC/B,GAAA;EAEA,IAAIA,MAAM,CAAC2T,SAAS,IAAIT,eAAe,CAAClT,MAAM,CAAC,EAAE;IAC/C8T,eAAe,CAAC9T,MAAM,CAAC,CAAA;AACzB,GAAA;EAEA,OAAOA,MAAM,CAACgU,QAAQ,CAAA;AACxB;;ACpPA,SAASQ,aAAaA,CAACzgB,CAAC,EAAE;EACxB,IAAI3J,CAAC,GAAG,EAAE,CAAA;AACV,EAAA,KAAK,IAAIZ,CAAC,GAAG,CAAC,EAAEC,EAAE,GAAGsK,CAAC,CAACrK,MAAM,EAAEF,CAAC,GAAGC,EAAE,EAAED,CAAC,EAAE,EAAE;AAC1CY,IAAAA,CAAC,IAAI2J,CAAC,CAACvK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;IAEZ,IAAIuK,CAAC,CAACvK,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI,EAAE;AACnBY,MAAAA,CAAC,IAAI2J,CAAC,CAACvK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;MAEZ,IAAIuK,CAAC,CAACvK,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI,EAAE;AACnBY,QAAAA,CAAC,IAAI,GAAG,CAAA;AACRA,QAAAA,CAAC,IAAI2J,CAAC,CAACvK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;QAEZ,IAAIuK,CAAC,CAACvK,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI,EAAE;AACnBY,UAAAA,CAAC,IAAI,GAAG,CAAA;AACRA,UAAAA,CAAC,IAAI2J,CAAC,CAACvK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;AACZY,UAAAA,CAAC,IAAI,GAAG,CAAA;AACRA,UAAAA,CAAC,IAAI2J,CAAC,CAACvK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;UAEZ,IAAIuK,CAAC,CAACvK,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI,EAAE;AACnBY,YAAAA,CAAC,IAAI,GAAG,CAAA;AACRA,YAAAA,CAAC,IAAI2J,CAAC,CAACvK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;AACZY,YAAAA,CAAC,IAAI,GAAG,CAAA;AACRA,YAAAA,CAAC,IAAI2J,CAAC,CAACvK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;YAEZ,IAAIuK,CAAC,CAACvK,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI,EAAE;AACnBY,cAAAA,CAAC,IAAI,GAAG,CAAA;AACRA,cAAAA,CAAC,IAAI2J,CAAC,CAACvK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;AACd,aAAA;AACF,WAAA;AACF,SAAA;AACF,OAAA;AACF,KAAA;AACF,GAAA;EAEA,OAAOY,CAAC,GAAG,GAAG,CAAA;AAChB,CAAA;AAEe,MAAMqqB,SAAS,SAASpO,QAAQ,CAAC;AAC9C;AACApb,EAAAA,IAAIA,GAAG;AACL+U,IAAAA,MAAM,EAAE,CAACG,IAAI,CAACzT,YAAY,CAAC,GAAG,EAAE,IAAI,CAACwI,QAAQ,EAAE,CAAC,CAAA;AAChD,IAAA,OAAO,IAAIyL,GAAG,CAACX,MAAM,CAACC,KAAK,CAACE,IAAI,CAAC4B,OAAO,EAAE,CAAC,CAAA;AAC7C,GAAA;;AAEA;AACAmJ,EAAAA,IAAIA,CAACzf,CAAC,EAAEC,CAAC,EAAE;AACT;AACA,IAAA,MAAMV,GAAG,GAAG,IAAI,CAACC,IAAI,EAAE,CAAA;;AAEvB;IACAQ,CAAC,IAAIT,GAAG,CAACS,CAAC,CAAA;IACVC,CAAC,IAAIV,GAAG,CAACU,CAAC,CAAA;IAEV,IAAI,CAACmb,KAAK,CAACpb,CAAC,CAAC,IAAI,CAACob,KAAK,CAACnb,CAAC,CAAC,EAAE;AAC1B;AACA,MAAA,KAAK,IAAIqK,CAAC,EAAEvM,CAAC,GAAG,IAAI,CAACE,MAAM,GAAG,CAAC,EAAEF,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;AAC5CuM,QAAAA,CAAC,GAAG,IAAI,CAACvM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;QAEd,IAAIuM,CAAC,KAAK,GAAG,IAAIA,CAAC,KAAK,GAAG,IAAIA,CAAC,KAAK,GAAG,EAAE;AACvC,UAAA,IAAI,CAACvM,CAAC,CAAC,CAAC,CAAC,CAAC,IAAIiC,CAAC,CAAA;AACf,UAAA,IAAI,CAACjC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAIkC,CAAC,CAAA;AACjB,SAAC,MAAM,IAAIqK,CAAC,KAAK,GAAG,EAAE;AACpB,UAAA,IAAI,CAACvM,CAAC,CAAC,CAAC,CAAC,CAAC,IAAIiC,CAAC,CAAA;AACjB,SAAC,MAAM,IAAIsK,CAAC,KAAK,GAAG,EAAE;AACpB,UAAA,IAAI,CAACvM,CAAC,CAAC,CAAC,CAAC,CAAC,IAAIkC,CAAC,CAAA;AACjB,SAAC,MAAM,IAAIqK,CAAC,KAAK,GAAG,IAAIA,CAAC,KAAK,GAAG,IAAIA,CAAC,KAAK,GAAG,EAAE;AAC9C,UAAA,IAAI,CAACvM,CAAC,CAAC,CAAC,CAAC,CAAC,IAAIiC,CAAC,CAAA;AACf,UAAA,IAAI,CAACjC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAIkC,CAAC,CAAA;AACf,UAAA,IAAI,CAAClC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAIiC,CAAC,CAAA;AACf,UAAA,IAAI,CAACjC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAIkC,CAAC,CAAA;UAEf,IAAIqK,CAAC,KAAK,GAAG,EAAE;AACb,YAAA,IAAI,CAACvM,CAAC,CAAC,CAAC,CAAC,CAAC,IAAIiC,CAAC,CAAA;AACf,YAAA,IAAI,CAACjC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAIkC,CAAC,CAAA;AACjB,WAAA;AACF,SAAC,MAAM,IAAIqK,CAAC,KAAK,GAAG,EAAE;AACpB,UAAA,IAAI,CAACvM,CAAC,CAAC,CAAC,CAAC,CAAC,IAAIiC,CAAC,CAAA;AACf,UAAA,IAAI,CAACjC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAIkC,CAAC,CAAA;AACjB,SAAA;AACF,OAAA;AACF,KAAA;AAEA,IAAA,OAAO,IAAI,CAAA;AACb,GAAA;;AAEA;AACAwI,EAAAA,KAAKA,CAACpK,CAAC,GAAG,MAAM,EAAE;AAChB,IAAA,IAAIrB,KAAK,CAACC,OAAO,CAACoB,CAAC,CAAC,EAAE;AACpBA,MAAAA,CAAC,GAAGrB,KAAK,CAACgH,SAAS,CAACyT,MAAM,CAAC7S,KAAK,CAAC,EAAE,EAAEvG,CAAC,CAAC,CAACoL,QAAQ,EAAE,CAAA;AACpD,KAAA;IAEA,OAAOof,UAAU,CAACxqB,CAAC,CAAC,CAAA;AACtB,GAAA;;AAEA;AACAoW,EAAAA,IAAIA,CAACpV,KAAK,EAAEC,MAAM,EAAE;AAClB;AACA,IAAA,MAAMC,GAAG,GAAG,IAAI,CAACC,IAAI,EAAE,CAAA;IACvB,IAAIzB,CAAC,EAAEuM,CAAC,CAAA;;AAER;AACA;AACA/K,IAAAA,GAAG,CAACF,KAAK,GAAGE,GAAG,CAACF,KAAK,KAAK,CAAC,GAAG,CAAC,GAAGE,GAAG,CAACF,KAAK,CAAA;AAC3CE,IAAAA,GAAG,CAACD,MAAM,GAAGC,GAAG,CAACD,MAAM,KAAK,CAAC,GAAG,CAAC,GAAGC,GAAG,CAACD,MAAM,CAAA;;AAE9C;AACA,IAAA,KAAKvB,CAAC,GAAG,IAAI,CAACE,MAAM,GAAG,CAAC,EAAEF,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;AACrCuM,MAAAA,CAAC,GAAG,IAAI,CAACvM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;MAEd,IAAIuM,CAAC,KAAK,GAAG,IAAIA,CAAC,KAAK,GAAG,IAAIA,CAAC,KAAK,GAAG,EAAE;AACvC,QAAA,IAAI,CAACvM,CAAC,CAAC,CAAC,CAAC,CAAC,GAAI,CAAC,IAAI,CAACA,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGwB,GAAG,CAACS,CAAC,IAAIX,KAAK,GAAIE,GAAG,CAACF,KAAK,GAAGE,GAAG,CAACS,CAAC,CAAA;AAC/D,QAAA,IAAI,CAACjC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAI,CAAC,IAAI,CAACA,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGwB,GAAG,CAACU,CAAC,IAAIX,MAAM,GAAIC,GAAG,CAACD,MAAM,GAAGC,GAAG,CAACU,CAAC,CAAA;AACnE,OAAC,MAAM,IAAIqK,CAAC,KAAK,GAAG,EAAE;AACpB,QAAA,IAAI,CAACvM,CAAC,CAAC,CAAC,CAAC,CAAC,GAAI,CAAC,IAAI,CAACA,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGwB,GAAG,CAACS,CAAC,IAAIX,KAAK,GAAIE,GAAG,CAACF,KAAK,GAAGE,GAAG,CAACS,CAAC,CAAA;AACjE,OAAC,MAAM,IAAIsK,CAAC,KAAK,GAAG,EAAE;AACpB,QAAA,IAAI,CAACvM,CAAC,CAAC,CAAC,CAAC,CAAC,GAAI,CAAC,IAAI,CAACA,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGwB,GAAG,CAACU,CAAC,IAAIX,MAAM,GAAIC,GAAG,CAACD,MAAM,GAAGC,GAAG,CAACU,CAAC,CAAA;AACnE,OAAC,MAAM,IAAIqK,CAAC,KAAK,GAAG,IAAIA,CAAC,KAAK,GAAG,IAAIA,CAAC,KAAK,GAAG,EAAE;AAC9C,QAAA,IAAI,CAACvM,CAAC,CAAC,CAAC,CAAC,CAAC,GAAI,CAAC,IAAI,CAACA,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGwB,GAAG,CAACS,CAAC,IAAIX,KAAK,GAAIE,GAAG,CAACF,KAAK,GAAGE,GAAG,CAACS,CAAC,CAAA;AAC/D,QAAA,IAAI,CAACjC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAI,CAAC,IAAI,CAACA,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGwB,GAAG,CAACU,CAAC,IAAIX,MAAM,GAAIC,GAAG,CAACD,MAAM,GAAGC,GAAG,CAACU,CAAC,CAAA;AACjE,QAAA,IAAI,CAAClC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAI,CAAC,IAAI,CAACA,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGwB,GAAG,CAACS,CAAC,IAAIX,KAAK,GAAIE,GAAG,CAACF,KAAK,GAAGE,GAAG,CAACS,CAAC,CAAA;AAC/D,QAAA,IAAI,CAACjC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAI,CAAC,IAAI,CAACA,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGwB,GAAG,CAACU,CAAC,IAAIX,MAAM,GAAIC,GAAG,CAACD,MAAM,GAAGC,GAAG,CAACU,CAAC,CAAA;QAEjE,IAAIqK,CAAC,KAAK,GAAG,EAAE;AACb,UAAA,IAAI,CAACvM,CAAC,CAAC,CAAC,CAAC,CAAC,GAAI,CAAC,IAAI,CAACA,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGwB,GAAG,CAACS,CAAC,IAAIX,KAAK,GAAIE,GAAG,CAACF,KAAK,GAAGE,GAAG,CAACS,CAAC,CAAA;AAC/D,UAAA,IAAI,CAACjC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAI,CAAC,IAAI,CAACA,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGwB,GAAG,CAACU,CAAC,IAAIX,MAAM,GAAIC,GAAG,CAACD,MAAM,GAAGC,GAAG,CAACU,CAAC,CAAA;AACnE,SAAA;AACF,OAAC,MAAM,IAAIqK,CAAC,KAAK,GAAG,EAAE;AACpB;AACA,QAAA,IAAI,CAACvM,CAAC,CAAC,CAAC,CAAC,CAAC,GAAI,IAAI,CAACA,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGsB,KAAK,GAAIE,GAAG,CAACF,KAAK,CAAA;AAC7C,QAAA,IAAI,CAACtB,CAAC,CAAC,CAAC,CAAC,CAAC,GAAI,IAAI,CAACA,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGuB,MAAM,GAAIC,GAAG,CAACD,MAAM,CAAA;;AAE/C;AACA,QAAA,IAAI,CAACvB,CAAC,CAAC,CAAC,CAAC,CAAC,GAAI,CAAC,IAAI,CAACA,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGwB,GAAG,CAACS,CAAC,IAAIX,KAAK,GAAIE,GAAG,CAACF,KAAK,GAAGE,GAAG,CAACS,CAAC,CAAA;AAC/D,QAAA,IAAI,CAACjC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAI,CAAC,IAAI,CAACA,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGwB,GAAG,CAACU,CAAC,IAAIX,MAAM,GAAIC,GAAG,CAACD,MAAM,GAAGC,GAAG,CAACU,CAAC,CAAA;AACnE,OAAA;AACF,KAAA;AAEA,IAAA,OAAO,IAAI,CAAA;AACb,GAAA;;AAEA;AACAwJ,EAAAA,QAAQA,GAAG;IACT,OAAOsf,aAAa,CAAC,IAAI,CAAC,CAAA;AAC5B,GAAA;AACF;;ACzIA,MAAME,eAAe,GAAIhO,KAAK,IAAK;EACjC,MAAMlB,IAAI,GAAG,OAAOkB,KAAK,CAAA;EAEzB,IAAIlB,IAAI,KAAK,QAAQ,EAAE;AACrB,IAAA,OAAOe,SAAS,CAAA;AAClB,GAAC,MAAM,IAAIf,IAAI,KAAK,QAAQ,EAAE;AAC5B,IAAA,IAAIrP,KAAK,CAACG,OAAO,CAACoQ,KAAK,CAAC,EAAE;AACxB,MAAA,OAAOvQ,KAAK,CAAA;KACb,MAAM,IAAIhE,SAAS,CAACwB,IAAI,CAAC+S,KAAK,CAAC,EAAE;MAChC,OAAOtU,YAAY,CAACuB,IAAI,CAAC+S,KAAK,CAAC,GAAG+N,SAAS,GAAGpO,QAAQ,CAAA;KACvD,MAAM,IAAI7U,aAAa,CAACmC,IAAI,CAAC+S,KAAK,CAAC,EAAE;AACpC,MAAA,OAAOH,SAAS,CAAA;AAClB,KAAC,MAAM;AACL,MAAA,OAAOoO,YAAY,CAAA;AACrB,KAAA;AACF,GAAC,MAAM,IAAIC,cAAc,CAACniB,OAAO,CAACiU,KAAK,CAACtW,WAAW,CAAC,GAAG,CAAC,CAAC,EAAE;IACzD,OAAOsW,KAAK,CAACtW,WAAW,CAAA;GACzB,MAAM,IAAI3H,KAAK,CAACC,OAAO,CAACge,KAAK,CAAC,EAAE;AAC/B,IAAA,OAAOL,QAAQ,CAAA;AACjB,GAAC,MAAM,IAAIb,IAAI,KAAK,QAAQ,EAAE;AAC5B,IAAA,OAAOqP,SAAS,CAAA;AAClB,GAAC,MAAM;AACL,IAAA,OAAOF,YAAY,CAAA;AACrB,GAAA;AACF,CAAC,CAAA;AAEc,MAAMG,SAAS,CAAC;EAC7B1kB,WAAWA,CAACogB,OAAO,EAAE;IACnB,IAAI,CAACuE,QAAQ,GAAGvE,OAAO,IAAI,IAAIF,IAAI,CAAC,GAAG,CAAC,CAAA;IAExC,IAAI,CAAC0E,KAAK,GAAG,IAAI,CAAA;IACjB,IAAI,CAACC,GAAG,GAAG,IAAI,CAAA;IACf,IAAI,CAACC,KAAK,GAAG,IAAI,CAAA;IACjB,IAAI,CAACC,QAAQ,GAAG,IAAI,CAAA;IACpB,IAAI,CAACC,SAAS,GAAG,IAAI,CAAA;AACvB,GAAA;EAEAC,EAAEA,CAAC1F,GAAG,EAAE;IACN,OAAO,IAAI,CAACyF,SAAS,CAACE,KAAK,CACzB,IAAI,CAACN,KAAK,EACV,IAAI,CAACC,GAAG,EACRtF,GAAG,EACH,IAAI,CAACoF,QAAQ,EACb,IAAI,CAACI,QACP,CAAC,CAAA;AACH,GAAA;AAEA9E,EAAAA,IAAIA,GAAG;IACL,MAAMkF,QAAQ,GAAG,IAAI,CAACJ,QAAQ,CAAC9rB,GAAG,CAAC,IAAI,CAAC0rB,QAAQ,CAAC1E,IAAI,CAAC,CAACjN,MAAM,CAAC,UAC5DmE,IAAI,EACJC,IAAI,EACJ;MACA,OAAOD,IAAI,IAAIC,IAAI,CAAA;KACpB,EAAE,IAAI,CAAC,CAAA;AACR,IAAA,OAAO+N,QAAQ,CAAA;AACjB,GAAA;EAEApI,IAAIA,CAACla,GAAG,EAAE;IACR,IAAIA,GAAG,IAAI,IAAI,EAAE;MACf,OAAO,IAAI,CAAC+hB,KAAK,CAAA;AACnB,KAAA;IAEA,IAAI,CAACA,KAAK,GAAG,IAAI,CAACQ,IAAI,CAACviB,GAAG,CAAC,CAAA;AAC3B,IAAA,OAAO,IAAI,CAAA;AACb,GAAA;EAEAud,OAAOA,CAACA,OAAO,EAAE;AACf,IAAA,IAAIA,OAAO,IAAI,IAAI,EAAE,OAAO,IAAI,CAACuE,QAAQ,CAAA;IACzC,IAAI,CAACA,QAAQ,GAAGvE,OAAO,CAAA;AACvB,IAAA,OAAO,IAAI,CAAA;AACb,GAAA;EAEAhD,EAAEA,CAACva,GAAG,EAAE;IACN,IAAIA,GAAG,IAAI,IAAI,EAAE;MACf,OAAO,IAAI,CAACgiB,GAAG,CAAA;AACjB,KAAA;IAEA,IAAI,CAACA,GAAG,GAAG,IAAI,CAACO,IAAI,CAACviB,GAAG,CAAC,CAAA;AACzB,IAAA,OAAO,IAAI,CAAA;AACb,GAAA;EAEAuS,IAAIA,CAACA,IAAI,EAAE;AACT;IACA,IAAIA,IAAI,IAAI,IAAI,EAAE;MAChB,OAAO,IAAI,CAAC0P,KAAK,CAAA;AACnB,KAAA;;AAEA;IACA,IAAI,CAACA,KAAK,GAAG1P,IAAI,CAAA;AACjB,IAAA,OAAO,IAAI,CAAA;AACb,GAAA;EAEAgQ,IAAIA,CAAC9O,KAAK,EAAE;AACV,IAAA,IAAI,CAAC,IAAI,CAACwO,KAAK,EAAE;AACf,MAAA,IAAI,CAAC1P,IAAI,CAACkP,eAAe,CAAChO,KAAK,CAAC,CAAC,CAAA;AACnC,KAAA;IAEA,IAAI/c,MAAM,GAAG,IAAI,IAAI,CAACurB,KAAK,CAACxO,KAAK,CAAC,CAAA;AAClC,IAAA,IAAI,IAAI,CAACwO,KAAK,KAAK/e,KAAK,EAAE;AACxBxM,MAAAA,MAAM,GAAG,IAAI,CAACsrB,GAAG,GACbtrB,MAAM,CAAC,IAAI,CAACsrB,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,GACrB,IAAI,CAACD,KAAK,GACRrrB,MAAM,CAAC,IAAI,CAACqrB,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,GACvBrrB,MAAM,CAAA;AACd,KAAA;AAEA,IAAA,IAAI,IAAI,CAACurB,KAAK,KAAKL,SAAS,EAAE;MAC5BlrB,MAAM,GAAG,IAAI,CAACsrB,GAAG,GACbtrB,MAAM,CAAC8rB,KAAK,CAAC,IAAI,CAACR,GAAG,CAAC,GACtB,IAAI,CAACD,KAAK,GACRrrB,MAAM,CAAC8rB,KAAK,CAAC,IAAI,CAACT,KAAK,CAAC,GACxBrrB,MAAM,CAAA;AACd,KAAA;AAEAA,IAAAA,MAAM,GAAGA,MAAM,CAAC+rB,YAAY,EAAE,CAAA;AAE9B,IAAA,IAAI,CAACN,SAAS,GAAG,IAAI,CAACA,SAAS,IAAI,IAAI,IAAI,CAACF,KAAK,EAAE,CAAA;AACnD,IAAA,IAAI,CAACC,QAAQ,GACX,IAAI,CAACA,QAAQ,IACb1sB,KAAK,CAAC4H,KAAK,CAAC,IAAI,EAAE5H,KAAK,CAACkB,MAAM,CAACD,MAAM,CAAC,CAAC,CACpCL,GAAG,CAACR,MAAM,CAAC,CACXQ,GAAG,CAAC,UAAU8B,CAAC,EAAE;MAChBA,CAAC,CAACklB,IAAI,GAAG,IAAI,CAAA;AACb,MAAA,OAAOllB,CAAC,CAAA;AACV,KAAC,CAAC,CAAA;AACN,IAAA,OAAOxB,MAAM,CAAA;AACf,GAAA;AACF,CAAA;AAEO,MAAMgrB,YAAY,CAAC;EACxBvkB,WAAWA,CAAC,GAAGD,IAAI,EAAE;AACnB,IAAA,IAAI,CAACkG,IAAI,CAAC,GAAGlG,IAAI,CAAC,CAAA;AACpB,GAAA;EAEAkG,IAAIA,CAACpD,GAAG,EAAE;AACRA,IAAAA,GAAG,GAAGxK,KAAK,CAACC,OAAO,CAACuK,GAAG,CAAC,GAAGA,GAAG,CAAC,CAAC,CAAC,GAAGA,GAAG,CAAA;IACvC,IAAI,CAACyT,KAAK,GAAGzT,GAAG,CAAA;AAChB,IAAA,OAAO,IAAI,CAAA;AACb,GAAA;AAEAyF,EAAAA,OAAOA,GAAG;AACR,IAAA,OAAO,CAAC,IAAI,CAACgO,KAAK,CAAC,CAAA;AACrB,GAAA;AAEAna,EAAAA,OAAOA,GAAG;IACR,OAAO,IAAI,CAACma,KAAK,CAAA;AACnB,GAAA;AACF,CAAA;AAEO,MAAMiP,YAAY,CAAC;EACxBvlB,WAAWA,CAAC,GAAGD,IAAI,EAAE;AACnB,IAAA,IAAI,CAACkG,IAAI,CAAC,GAAGlG,IAAI,CAAC,CAAA;AACpB,GAAA;EAEAkG,IAAIA,CAACgN,GAAG,EAAE;AACR,IAAA,IAAI5a,KAAK,CAACC,OAAO,CAAC2a,GAAG,CAAC,EAAE;AACtBA,MAAAA,GAAG,GAAG;AACJjI,QAAAA,MAAM,EAAEiI,GAAG,CAAC,CAAC,CAAC;AACd/H,QAAAA,MAAM,EAAE+H,GAAG,CAAC,CAAC,CAAC;AACd9H,QAAAA,KAAK,EAAE8H,GAAG,CAAC,CAAC,CAAC;AACb5H,QAAAA,MAAM,EAAE4H,GAAG,CAAC,CAAC,CAAC;AACdnH,QAAAA,UAAU,EAAEmH,GAAG,CAAC,CAAC,CAAC;AAClBjH,QAAAA,UAAU,EAAEiH,GAAG,CAAC,CAAC,CAAC;AAClB/X,QAAAA,OAAO,EAAE+X,GAAG,CAAC,CAAC,CAAC;QACf7X,OAAO,EAAE6X,GAAG,CAAC,CAAC,CAAA;OACf,CAAA;AACH,KAAA;IAEAxa,MAAM,CAACE,MAAM,CAAC,IAAI,EAAE4sB,YAAY,CAACvpB,QAAQ,EAAEiX,GAAG,CAAC,CAAA;AAC/C,IAAA,OAAO,IAAI,CAAA;AACb,GAAA;AAEA3K,EAAAA,OAAOA,GAAG;IACR,MAAM1E,CAAC,GAAG,IAAI,CAAA;AAEd,IAAA,OAAO,CACLA,CAAC,CAACoH,MAAM,EACRpH,CAAC,CAACsH,MAAM,EACRtH,CAAC,CAACuH,KAAK,EACPvH,CAAC,CAACyH,MAAM,EACRzH,CAAC,CAACkI,UAAU,EACZlI,CAAC,CAACoI,UAAU,EACZpI,CAAC,CAAC1I,OAAO,EACT0I,CAAC,CAACxI,OAAO,CACV,CAAA;AACH,GAAA;AACF,CAAA;AAEAmqB,YAAY,CAACvpB,QAAQ,GAAG;AACtBgP,EAAAA,MAAM,EAAE,CAAC;AACTE,EAAAA,MAAM,EAAE,CAAC;AACTC,EAAAA,KAAK,EAAE,CAAC;AACRE,EAAAA,MAAM,EAAE,CAAC;AACTS,EAAAA,UAAU,EAAE,CAAC;AACbE,EAAAA,UAAU,EAAE,CAAC;AACb9Q,EAAAA,OAAO,EAAE,CAAC;AACVE,EAAAA,OAAO,EAAE,CAAA;AACX,CAAC,CAAA;AAED,MAAMoqB,SAAS,GAAGA,CAAC7hB,CAAC,EAAEwB,CAAC,KAAK;EAC1B,OAAOxB,CAAC,CAAC,CAAC,CAAC,GAAGwB,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAGxB,CAAC,CAAC,CAAC,CAAC,GAAGwB,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAA;AAC/C,CAAC,CAAA;AAEM,MAAMsf,SAAS,CAAC;EACrBzkB,WAAWA,CAAC,GAAGD,IAAI,EAAE;AACnB,IAAA,IAAI,CAACkG,IAAI,CAAC,GAAGlG,IAAI,CAAC,CAAA;AACpB,GAAA;EAEAslB,KAAKA,CAAC5X,KAAK,EAAE;AACX,IAAA,MAAM3G,MAAM,GAAG,IAAI,CAACA,MAAM,CAAA;AAC1B,IAAA,KAAK,IAAI1N,CAAC,GAAG,CAAC,EAAEC,EAAE,GAAGyN,MAAM,CAACxN,MAAM,EAAEF,CAAC,GAAGC,EAAE,EAAE,EAAED,CAAC,EAAE;AAC/C;AACA,MAAA,IAAI0N,MAAM,CAAC1N,CAAC,GAAG,CAAC,CAAC,KAAKqU,KAAK,CAACrU,CAAC,GAAG,CAAC,CAAC,EAAE;QAClC,IAAI0N,MAAM,CAAC1N,CAAC,GAAG,CAAC,CAAC,KAAK2M,KAAK,IAAI0H,KAAK,CAACrU,CAAC,GAAG,CAAC,CAAC,KAAK0N,MAAM,CAAC1N,CAAC,GAAG,CAAC,CAAC,EAAE;AAC7D,UAAA,MAAM6L,KAAK,GAAGwI,KAAK,CAACrU,CAAC,GAAG,CAAC,CAAC,CAAA;UAC1B,MAAM+M,KAAK,GAAG,IAAIJ,KAAK,CAAC,IAAI,CAACe,MAAM,CAAC2e,MAAM,CAACrsB,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,CAClD6L,KAAK,CAAC,EAAE,CACRqD,OAAO,EAAE,CAAA;AACZ,UAAA,IAAI,CAACxB,MAAM,CAAC2e,MAAM,CAACrsB,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,GAAG+M,KAAK,CAAC,CAAA;AACxC,SAAA;QAEA/M,CAAC,IAAI0N,MAAM,CAAC1N,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAA;AACtB,QAAA,SAAA;AACF,OAAA;AAEA,MAAA,IAAI,CAACqU,KAAK,CAACrU,CAAC,GAAG,CAAC,CAAC,EAAE;AACjB,QAAA,OAAO,IAAI,CAAA;AACb,OAAA;;AAEA;AACA;AACA,MAAA,MAAMssB,aAAa,GAAG,IAAIjY,KAAK,CAACrU,CAAC,GAAG,CAAC,CAAC,EAAE,CAACkP,OAAO,EAAE,CAAA;;AAElD;MACA,MAAMqd,QAAQ,GAAG7e,MAAM,CAAC1N,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAA;AAElC0N,MAAAA,MAAM,CAAC2e,MAAM,CACXrsB,CAAC,EACDusB,QAAQ,EACRlY,KAAK,CAACrU,CAAC,CAAC,EACRqU,KAAK,CAACrU,CAAC,GAAG,CAAC,CAAC,EACZqU,KAAK,CAACrU,CAAC,GAAG,CAAC,CAAC,EACZ,GAAGssB,aACL,CAAC,CAAA;MAEDtsB,CAAC,IAAI0N,MAAM,CAAC1N,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAA;AACxB,KAAA;AACA,IAAA,OAAO,IAAI,CAAA;AACb,GAAA;EAEA6M,IAAIA,CAAC2f,QAAQ,EAAE;IACb,IAAI,CAAC9e,MAAM,GAAG,EAAE,CAAA;AAEhB,IAAA,IAAIzO,KAAK,CAACC,OAAO,CAACstB,QAAQ,CAAC,EAAE;AAC3B,MAAA,IAAI,CAAC9e,MAAM,GAAG8e,QAAQ,CAACrrB,KAAK,EAAE,CAAA;AAC9B,MAAA,OAAA;AACF,KAAA;AAEAqrB,IAAAA,QAAQ,GAAGA,QAAQ,IAAI,EAAE,CAAA;IACzB,MAAMC,OAAO,GAAG,EAAE,CAAA;AAElB,IAAA,KAAK,MAAMzsB,CAAC,IAAIwsB,QAAQ,EAAE;MACxB,MAAME,IAAI,GAAGxB,eAAe,CAACsB,QAAQ,CAACxsB,CAAC,CAAC,CAAC,CAAA;AACzC,MAAA,MAAMyJ,GAAG,GAAG,IAAIijB,IAAI,CAACF,QAAQ,CAACxsB,CAAC,CAAC,CAAC,CAACkP,OAAO,EAAE,CAAA;AAC3Cud,MAAAA,OAAO,CAAC7sB,IAAI,CAAC,CAACI,CAAC,EAAE0sB,IAAI,EAAEjjB,GAAG,CAACvJ,MAAM,EAAE,GAAGuJ,GAAG,CAAC,CAAC,CAAA;AAC7C,KAAA;AAEAgjB,IAAAA,OAAO,CAACE,IAAI,CAACP,SAAS,CAAC,CAAA;IAEvB,IAAI,CAAC1e,MAAM,GAAG+e,OAAO,CAAC7S,MAAM,CAAC,CAACmE,IAAI,EAAEC,IAAI,KAAKD,IAAI,CAACrE,MAAM,CAACsE,IAAI,CAAC,EAAE,EAAE,CAAC,CAAA;AACnE,IAAA,OAAO,IAAI,CAAA;AACb,GAAA;AAEA9O,EAAAA,OAAOA,GAAG;IACR,OAAO,IAAI,CAACxB,MAAM,CAAA;AACpB,GAAA;AAEA3K,EAAAA,OAAOA,GAAG;IACR,MAAM8W,GAAG,GAAG,EAAE,CAAA;AACd,IAAA,MAAMN,GAAG,GAAG,IAAI,CAAC7L,MAAM,CAAA;;AAEvB;IACA,OAAO6L,GAAG,CAACrZ,MAAM,EAAE;AACjB,MAAA,MAAM4C,GAAG,GAAGyW,GAAG,CAACqT,KAAK,EAAE,CAAA;AACvB,MAAA,MAAMF,IAAI,GAAGnT,GAAG,CAACqT,KAAK,EAAE,CAAA;AACxB,MAAA,MAAMC,GAAG,GAAGtT,GAAG,CAACqT,KAAK,EAAE,CAAA;MACvB,MAAMlf,MAAM,GAAG6L,GAAG,CAAC8S,MAAM,CAAC,CAAC,EAAEQ,GAAG,CAAC,CAAA;MACjChT,GAAG,CAAC/W,GAAG,CAAC,GAAG,IAAI4pB,IAAI,CAAChf,MAAM,CAAC,CAAC;AAC9B,KAAA;AAEA,IAAA,OAAOmM,GAAG,CAAA;AACZ,GAAA;AACF,CAAA;AAEA,MAAMuR,cAAc,GAAG,CAACD,YAAY,EAAEgB,YAAY,EAAEd,SAAS,CAAC,CAAA;AAEvD,SAASyB,qBAAqBA,CAAC9Q,IAAI,GAAG,EAAE,EAAE;EAC/CoP,cAAc,CAACxrB,IAAI,CAAC,GAAG,EAAE,CAAC8Z,MAAM,CAACsC,IAAI,CAAC,CAAC,CAAA;AACzC,CAAA;AAEO,SAAS+Q,aAAaA,GAAG;EAC9BvmB,MAAM,CAAC4kB,cAAc,EAAE;IACrBpH,EAAEA,CAACva,GAAG,EAAE;MACN,OAAO,IAAI6hB,SAAS,EAAE,CACnBtP,IAAI,CAAC,IAAI,CAACpV,WAAW,CAAC,CACtB+c,IAAI,CAAC,IAAI,CAACzU,OAAO,EAAE,CAAC;OACpB8U,EAAE,CAACva,GAAG,CAAC,CAAA;KACX;IACDyJ,SAASA,CAACqG,GAAG,EAAE;AACb,MAAA,IAAI,CAAC1M,IAAI,CAAC0M,GAAG,CAAC,CAAA;AACd,MAAA,OAAO,IAAI,CAAA;KACZ;AACD2S,IAAAA,YAAYA,GAAG;AACb,MAAA,OAAO,IAAI,CAAChd,OAAO,EAAE,CAAA;KACtB;IACD4c,KAAKA,CAACnI,IAAI,EAAEK,EAAE,EAAEmC,GAAG,EAAEa,OAAO,EAAEgG,OAAO,EAAE;AACrC,MAAA,MAAMC,MAAM,GAAG,UAAUjtB,CAAC,EAAEkH,KAAK,EAAE;AACjC,QAAA,OAAO8f,OAAO,CAACP,IAAI,CAACzmB,CAAC,EAAEgkB,EAAE,CAAC9c,KAAK,CAAC,EAAEif,GAAG,EAAE6G,OAAO,CAAC9lB,KAAK,CAAC,EAAE8lB,OAAO,CAAC,CAAA;OAChE,CAAA;MAED,OAAO,IAAI,CAAC9Z,SAAS,CAACyQ,IAAI,CAAC9jB,GAAG,CAACotB,MAAM,CAAC,CAAC,CAAA;AACzC,KAAA;AACF,GAAC,CAAC,CAAA;AACJ;;ACzUe,MAAMC,IAAI,SAAS3J,KAAK,CAAC;AACtC;AACA3c,EAAAA,WAAWA,CAAC3D,IAAI,EAAE6W,KAAK,GAAG7W,IAAI,EAAE;IAC9B,KAAK,CAACoC,SAAS,CAAC,MAAM,EAAEpC,IAAI,CAAC,EAAE6W,KAAK,CAAC,CAAA;AACvC,GAAA;;AAEA;AACAha,EAAAA,KAAKA,GAAG;AACN,IAAA,OAAO,IAAI,CAACqtB,MAAM,KAAK,IAAI,CAACA,MAAM,GAAG,IAAIlC,SAAS,CAAC,IAAI,CAACnkB,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAA;AACrE,GAAA;;AAEA;AACA+X,EAAAA,KAAKA,GAAG;IACN,OAAO,IAAI,CAACsO,MAAM,CAAA;AAClB,IAAA,OAAO,IAAI,CAAA;AACb,GAAA;;AAEA;EACA5rB,MAAMA,CAACA,MAAM,EAAE;IACb,OAAOA,MAAM,IAAI,IAAI,GACjB,IAAI,CAACE,IAAI,EAAE,CAACF,MAAM,GAClB,IAAI,CAACmV,IAAI,CAAC,IAAI,CAACjV,IAAI,EAAE,CAACH,KAAK,EAAEC,MAAM,CAAC,CAAA;AAC1C,GAAA;;AAEA;AACAmgB,EAAAA,IAAIA,CAACzf,CAAC,EAAEC,CAAC,EAAE;AACT,IAAA,OAAO,IAAI,CAAC4E,IAAI,CAAC,GAAG,EAAE,IAAI,CAAChH,KAAK,EAAE,CAAC4hB,IAAI,CAACzf,CAAC,EAAEC,CAAC,CAAC,CAAC,CAAA;AAChD,GAAA;;AAEA;EACAwjB,IAAIA,CAACplB,CAAC,EAAE;AACN,IAAA,OAAOA,CAAC,IAAI,IAAI,GACZ,IAAI,CAACR,KAAK,EAAE,GACZ,IAAI,CAAC+e,KAAK,EAAE,CAAC/X,IAAI,CACf,GAAG,EACH,OAAOxG,CAAC,KAAK,QAAQ,GAAGA,CAAC,GAAI,IAAI,CAAC6sB,MAAM,GAAG,IAAIlC,SAAS,CAAC3qB,CAAC,CAC5D,CAAC,CAAA;AACP,GAAA;;AAEA;AACAoW,EAAAA,IAAIA,CAACpV,KAAK,EAAEC,MAAM,EAAE;IAClB,MAAM+F,CAAC,GAAGlG,gBAAgB,CAAC,IAAI,EAAEE,KAAK,EAAEC,MAAM,CAAC,CAAA;IAC/C,OAAO,IAAI,CAACuF,IAAI,CAAC,GAAG,EAAE,IAAI,CAAChH,KAAK,EAAE,CAAC4W,IAAI,CAACpP,CAAC,CAAChG,KAAK,EAAEgG,CAAC,CAAC/F,MAAM,CAAC,CAAC,CAAA;AAC7D,GAAA;;AAEA;EACAD,KAAKA,CAACA,KAAK,EAAE;IACX,OAAOA,KAAK,IAAI,IAAI,GAChB,IAAI,CAACG,IAAI,EAAE,CAACH,KAAK,GACjB,IAAI,CAACoV,IAAI,CAACpV,KAAK,EAAE,IAAI,CAACG,IAAI,EAAE,CAACF,MAAM,CAAC,CAAA;AAC1C,GAAA;;AAEA;EACAU,CAACA,CAACA,CAAC,EAAE;IACH,OAAOA,CAAC,IAAI,IAAI,GAAG,IAAI,CAACR,IAAI,EAAE,CAACQ,CAAC,GAAG,IAAI,CAACyf,IAAI,CAACzf,CAAC,EAAE,IAAI,CAACR,IAAI,EAAE,CAACS,CAAC,CAAC,CAAA;AAChE,GAAA;;AAEA;EACAA,CAACA,CAACA,CAAC,EAAE;IACH,OAAOA,CAAC,IAAI,IAAI,GAAG,IAAI,CAACT,IAAI,EAAE,CAACS,CAAC,GAAG,IAAI,CAACwf,IAAI,CAAC,IAAI,CAACjgB,IAAI,EAAE,CAACQ,CAAC,EAAEC,CAAC,CAAC,CAAA;AAChE,GAAA;AACF,CAAA;;AAEA;AACAgrB,IAAI,CAACjnB,SAAS,CAACuf,UAAU,GAAGyF,SAAS,CAAA;;AAErC;AACAnsB,eAAe,CAAC;AACdqkB,EAAAA,SAAS,EAAE;AACT;AACAxM,IAAAA,IAAI,EAAEjQ,iBAAiB,CAAC,UAAUpG,CAAC,EAAE;AACnC;AACA,MAAA,OAAO,IAAI,CAACse,GAAG,CAAC,IAAIsO,IAAI,EAAE,CAAC,CAACxH,IAAI,CAACplB,CAAC,IAAI,IAAI2qB,SAAS,EAAE,CAAC,CAAA;KACvD,CAAA;AACH,GAAA;AACF,CAAC,CAAC,CAAA;AAEFllB,QAAQ,CAACmnB,IAAI,EAAE,MAAM,CAAC;;AChFtB;AACO,SAASptB,KAAKA,GAAG;AACtB,EAAA,OAAO,IAAI,CAACqtB,MAAM,KAAK,IAAI,CAACA,MAAM,GAAG,IAAInI,UAAU,CAAC,IAAI,CAACle,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAA;AAC3E,CAAA;;AAEA;AACO,SAAS+X,KAAKA,GAAG;EACtB,OAAO,IAAI,CAACsO,MAAM,CAAA;AAClB,EAAA,OAAO,IAAI,CAAA;AACb,CAAA;;AAEA;AACO,SAASzL,MAAIA,CAACzf,CAAC,EAAEC,CAAC,EAAE;AACzB,EAAA,OAAO,IAAI,CAAC4E,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAChH,KAAK,EAAE,CAAC4hB,IAAI,CAACzf,CAAC,EAAEC,CAAC,CAAC,CAAC,CAAA;AACrD,CAAA;;AAEA;AACO,SAASwjB,IAAIA,CAACpe,CAAC,EAAE;AACtB,EAAA,OAAOA,CAAC,IAAI,IAAI,GACZ,IAAI,CAACxH,KAAK,EAAE,GACZ,IAAI,CAAC+e,KAAK,EAAE,CAAC/X,IAAI,CACf,QAAQ,EACR,OAAOQ,CAAC,KAAK,QAAQ,GAAGA,CAAC,GAAI,IAAI,CAAC6lB,MAAM,GAAG,IAAInI,UAAU,CAAC1d,CAAC,CAC7D,CAAC,CAAA;AACP,CAAA;;AAEA;AACO,SAASoP,MAAIA,CAACpV,KAAK,EAAEC,MAAM,EAAE;EAClC,MAAM+F,CAAC,GAAGlG,gBAAgB,CAAC,IAAI,EAAEE,KAAK,EAAEC,MAAM,CAAC,CAAA;EAC/C,OAAO,IAAI,CAACuF,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAChH,KAAK,EAAE,CAAC4W,IAAI,CAACpP,CAAC,CAAChG,KAAK,EAAEgG,CAAC,CAAC/F,MAAM,CAAC,CAAC,CAAA;AAClE;;;;;;;;;;;ACrBe,MAAM6rB,OAAO,SAAS7J,KAAK,CAAC;AACzC;AACA3c,EAAAA,WAAWA,CAAC3D,IAAI,EAAE6W,KAAK,GAAG7W,IAAI,EAAE;IAC9B,KAAK,CAACoC,SAAS,CAAC,SAAS,EAAEpC,IAAI,CAAC,EAAE6W,KAAK,CAAC,CAAA;AAC1C,GAAA;AACF,CAAA;AAEAhb,eAAe,CAAC;AACdqkB,EAAAA,SAAS,EAAE;AACT;AACAkK,IAAAA,OAAO,EAAE3mB,iBAAiB,CAAC,UAAUY,CAAC,EAAE;AACtC;AACA,MAAA,OAAO,IAAI,CAACsX,GAAG,CAAC,IAAIwO,OAAO,EAAE,CAAC,CAAC1H,IAAI,CAACpe,CAAC,IAAI,IAAI0d,UAAU,EAAE,CAAC,CAAA;KAC3D,CAAA;AACH,GAAA;AACF,CAAC,CAAC,CAAA;AAEFxe,MAAM,CAAC4mB,OAAO,EAAEzH,OAAO,CAAC,CAAA;AACxBnf,MAAM,CAAC4mB,OAAO,EAAEE,IAAI,CAAC,CAAA;AACrBvnB,QAAQ,CAACqnB,OAAO,EAAE,SAAS,CAAC;;ACnBb,MAAMG,QAAQ,SAAShK,KAAK,CAAC;AAC1C;AACA3c,EAAAA,WAAWA,CAAC3D,IAAI,EAAE6W,KAAK,GAAG7W,IAAI,EAAE;IAC9B,KAAK,CAACoC,SAAS,CAAC,UAAU,EAAEpC,IAAI,CAAC,EAAE6W,KAAK,CAAC,CAAA;AAC3C,GAAA;AACF,CAAA;AAEAhb,eAAe,CAAC;AACdqkB,EAAAA,SAAS,EAAE;AACT;AACAqK,IAAAA,QAAQ,EAAE9mB,iBAAiB,CAAC,UAAUY,CAAC,EAAE;AACvC;AACA,MAAA,OAAO,IAAI,CAACsX,GAAG,CAAC,IAAI2O,QAAQ,EAAE,CAAC,CAAC7H,IAAI,CAACpe,CAAC,IAAI,IAAI0d,UAAU,EAAE,CAAC,CAAA;KAC5D,CAAA;AACH,GAAA;AACF,CAAC,CAAC,CAAA;AAEFxe,MAAM,CAAC+mB,QAAQ,EAAE5H,OAAO,CAAC,CAAA;AACzBnf,MAAM,CAAC+mB,QAAQ,EAAED,IAAI,CAAC,CAAA;AACtBvnB,QAAQ,CAACwnB,QAAQ,EAAE,UAAU,CAAC;;ACrBf,MAAME,IAAI,SAASlK,KAAK,CAAC;AACtC;AACA3c,EAAAA,WAAWA,CAAC3D,IAAI,EAAE6W,KAAK,GAAG7W,IAAI,EAAE;IAC9B,KAAK,CAACoC,SAAS,CAAC,MAAM,EAAEpC,IAAI,CAAC,EAAE6W,KAAK,CAAC,CAAA;AACvC,GAAA;AACF,CAAA;AAEAtT,MAAM,CAACinB,IAAI,EAAE;EAAE3a,EAAE;AAAEE,EAAAA,EAAAA;AAAG,CAAC,CAAC,CAAA;AAExBlU,eAAe,CAAC;AACdqkB,EAAAA,SAAS,EAAE;AACT;AACA/M,IAAAA,IAAI,EAAE1P,iBAAiB,CAAC,UAAUpF,KAAK,EAAEC,MAAM,EAAE;AAC/C,MAAA,OAAO,IAAI,CAACqd,GAAG,CAAC,IAAI6O,IAAI,EAAE,CAAC,CAAC/W,IAAI,CAACpV,KAAK,EAAEC,MAAM,CAAC,CAAA;KAChD,CAAA;AACH,GAAA;AACF,CAAC,CAAC,CAAA;AAEFwE,QAAQ,CAAC0nB,IAAI,EAAE,MAAM,CAAC;;AC5BP,MAAMC,KAAK,CAAC;AACzB9mB,EAAAA,WAAWA,GAAG;IACZ,IAAI,CAAC+mB,MAAM,GAAG,IAAI,CAAA;IAClB,IAAI,CAACC,KAAK,GAAG,IAAI,CAAA;AACnB,GAAA;;AAEA;AACAxO,EAAAA,KAAKA,GAAG;IACN,OAAO,IAAI,CAACuO,MAAM,IAAI,IAAI,CAACA,MAAM,CAACzQ,KAAK,CAAA;AACzC,GAAA;;AAEA;AACAa,EAAAA,IAAIA,GAAG;IACL,OAAO,IAAI,CAAC6P,KAAK,IAAI,IAAI,CAACA,KAAK,CAAC1Q,KAAK,CAAA;AACvC,GAAA;EAEAtd,IAAIA,CAACsd,KAAK,EAAE;AACV;IACA,MAAM2Q,IAAI,GACR,OAAO3Q,KAAK,CAAC/V,IAAI,KAAK,WAAW,GAC7B+V,KAAK,GACL;AAAEA,MAAAA,KAAK,EAAEA,KAAK;AAAE/V,MAAAA,IAAI,EAAE,IAAI;AAAEC,MAAAA,IAAI,EAAE,IAAA;KAAM,CAAA;;AAE9C;IACA,IAAI,IAAI,CAACwmB,KAAK,EAAE;AACdC,MAAAA,IAAI,CAACzmB,IAAI,GAAG,IAAI,CAACwmB,KAAK,CAAA;AACtB,MAAA,IAAI,CAACA,KAAK,CAACzmB,IAAI,GAAG0mB,IAAI,CAAA;MACtB,IAAI,CAACD,KAAK,GAAGC,IAAI,CAAA;AACnB,KAAC,MAAM;MACL,IAAI,CAACD,KAAK,GAAGC,IAAI,CAAA;MACjB,IAAI,CAACF,MAAM,GAAGE,IAAI,CAAA;AACpB,KAAA;;AAEA;AACA,IAAA,OAAOA,IAAI,CAAA;AACb,GAAA;;AAEA;EACArmB,MAAMA,CAACqmB,IAAI,EAAE;AACX;AACA,IAAA,IAAIA,IAAI,CAACzmB,IAAI,EAAEymB,IAAI,CAACzmB,IAAI,CAACD,IAAI,GAAG0mB,IAAI,CAAC1mB,IAAI,CAAA;AACzC,IAAA,IAAI0mB,IAAI,CAAC1mB,IAAI,EAAE0mB,IAAI,CAAC1mB,IAAI,CAACC,IAAI,GAAGymB,IAAI,CAACzmB,IAAI,CAAA;AACzC,IAAA,IAAIymB,IAAI,KAAK,IAAI,CAACD,KAAK,EAAE,IAAI,CAACA,KAAK,GAAGC,IAAI,CAACzmB,IAAI,CAAA;AAC/C,IAAA,IAAIymB,IAAI,KAAK,IAAI,CAACF,MAAM,EAAE,IAAI,CAACA,MAAM,GAAGE,IAAI,CAAC1mB,IAAI,CAAA;;AAEjD;IACA0mB,IAAI,CAACzmB,IAAI,GAAG,IAAI,CAAA;IAChBymB,IAAI,CAAC1mB,IAAI,GAAG,IAAI,CAAA;AAClB,GAAA;AAEAylB,EAAAA,KAAKA,GAAG;AACN;AACA,IAAA,MAAMplB,MAAM,GAAG,IAAI,CAACmmB,MAAM,CAAA;AAC1B,IAAA,IAAI,CAACnmB,MAAM,EAAE,OAAO,IAAI,CAAA;;AAExB;AACA,IAAA,IAAI,CAACmmB,MAAM,GAAGnmB,MAAM,CAACL,IAAI,CAAA;IACzB,IAAI,IAAI,CAACwmB,MAAM,EAAE,IAAI,CAACA,MAAM,CAACvmB,IAAI,GAAG,IAAI,CAAA;IACxC,IAAI,CAACwmB,KAAK,GAAG,IAAI,CAACD,MAAM,GAAG,IAAI,CAACC,KAAK,GAAG,IAAI,CAAA;IAC5C,OAAOpmB,MAAM,CAAC0V,KAAK,CAAA;AACrB,GAAA;AACF;;AC1DA,MAAM4Q,QAAQ,GAAG;AACfC,EAAAA,QAAQ,EAAE,IAAI;AACdC,EAAAA,MAAM,EAAE,IAAIN,KAAK,EAAE;AACnBO,EAAAA,QAAQ,EAAE,IAAIP,KAAK,EAAE;AACrBQ,EAAAA,UAAU,EAAE,IAAIR,KAAK,EAAE;AACvBS,EAAAA,KAAK,EAAEA,MAAMzqB,OAAO,CAACC,MAAM,CAACyqB,WAAW,IAAI1qB,OAAO,CAACC,MAAM,CAAC0qB,IAAI;AAC9DjmB,EAAAA,UAAU,EAAE,EAAE;EAEdkmB,KAAKA,CAAClqB,EAAE,EAAE;AACR;AACA,IAAA,MAAMnB,IAAI,GAAG6qB,QAAQ,CAACE,MAAM,CAACpuB,IAAI,CAAC;AAAE2uB,MAAAA,GAAG,EAAEnqB,EAAAA;AAAG,KAAC,CAAC,CAAA;;AAE9C;AACA,IAAA,IAAI0pB,QAAQ,CAACC,QAAQ,KAAK,IAAI,EAAE;AAC9BD,MAAAA,QAAQ,CAACC,QAAQ,GAAGrqB,OAAO,CAACC,MAAM,CAAC6qB,qBAAqB,CAACV,QAAQ,CAACW,KAAK,CAAC,CAAA;AAC1E,KAAA;;AAEA;AACA,IAAA,OAAOxrB,IAAI,CAAA;GACZ;AAEDyrB,EAAAA,OAAOA,CAACtqB,EAAE,EAAEoY,KAAK,EAAE;IACjBA,KAAK,GAAGA,KAAK,IAAI,CAAC,CAAA;;AAElB;AACA,IAAA,MAAMmS,IAAI,GAAGb,QAAQ,CAACK,KAAK,EAAE,CAACS,GAAG,EAAE,GAAGpS,KAAK,CAAA;;AAE3C;AACA,IAAA,MAAMvZ,IAAI,GAAG6qB,QAAQ,CAACG,QAAQ,CAACruB,IAAI,CAAC;AAAE2uB,MAAAA,GAAG,EAAEnqB,EAAE;AAAEuqB,MAAAA,IAAI,EAAEA,IAAAA;AAAK,KAAC,CAAC,CAAA;;AAE5D;AACA,IAAA,IAAIb,QAAQ,CAACC,QAAQ,KAAK,IAAI,EAAE;AAC9BD,MAAAA,QAAQ,CAACC,QAAQ,GAAGrqB,OAAO,CAACC,MAAM,CAAC6qB,qBAAqB,CAACV,QAAQ,CAACW,KAAK,CAAC,CAAA;AAC1E,KAAA;AAEA,IAAA,OAAOxrB,IAAI,CAAA;GACZ;EAED4rB,SAASA,CAACzqB,EAAE,EAAE;AACZ;IACA,MAAMnB,IAAI,GAAG6qB,QAAQ,CAACI,UAAU,CAACtuB,IAAI,CAACwE,EAAE,CAAC,CAAA;AACzC;AACA,IAAA,IAAI0pB,QAAQ,CAACC,QAAQ,KAAK,IAAI,EAAE;AAC9BD,MAAAA,QAAQ,CAACC,QAAQ,GAAGrqB,OAAO,CAACC,MAAM,CAAC6qB,qBAAqB,CAACV,QAAQ,CAACW,KAAK,CAAC,CAAA;AAC1E,KAAA;AAEA,IAAA,OAAOxrB,IAAI,CAAA;GACZ;EAED6rB,WAAWA,CAAC7rB,IAAI,EAAE;IAChBA,IAAI,IAAI,IAAI,IAAI6qB,QAAQ,CAACE,MAAM,CAACxmB,MAAM,CAACvE,IAAI,CAAC,CAAA;GAC7C;EAED8rB,YAAYA,CAAC9rB,IAAI,EAAE;IACjBA,IAAI,IAAI,IAAI,IAAI6qB,QAAQ,CAACG,QAAQ,CAACzmB,MAAM,CAACvE,IAAI,CAAC,CAAA;GAC/C;EAED+rB,eAAeA,CAAC/rB,IAAI,EAAE;IACpBA,IAAI,IAAI,IAAI,IAAI6qB,QAAQ,CAACI,UAAU,CAAC1mB,MAAM,CAACvE,IAAI,CAAC,CAAA;GACjD;EAEDwrB,KAAKA,CAACG,GAAG,EAAE;AACT;AACA;IACA,IAAIK,WAAW,GAAG,IAAI,CAAA;IACtB,MAAMC,WAAW,GAAGpB,QAAQ,CAACG,QAAQ,CAAClQ,IAAI,EAAE,CAAA;IAC5C,OAAQkR,WAAW,GAAGnB,QAAQ,CAACG,QAAQ,CAACrB,KAAK,EAAE,EAAG;AAChD;AACA,MAAA,IAAIgC,GAAG,IAAIK,WAAW,CAACN,IAAI,EAAE;QAC3BM,WAAW,CAACV,GAAG,EAAE,CAAA;AACnB,OAAC,MAAM;AACLT,QAAAA,QAAQ,CAACG,QAAQ,CAACruB,IAAI,CAACqvB,WAAW,CAAC,CAAA;AACrC,OAAA;;AAEA;MACA,IAAIA,WAAW,KAAKC,WAAW,EAAE,MAAA;AACnC,KAAA;;AAEA;IACA,IAAIC,SAAS,GAAG,IAAI,CAAA;IACpB,MAAMC,SAAS,GAAGtB,QAAQ,CAACE,MAAM,CAACjQ,IAAI,EAAE,CAAA;AACxC,IAAA,OAAOoR,SAAS,KAAKC,SAAS,KAAKD,SAAS,GAAGrB,QAAQ,CAACE,MAAM,CAACpB,KAAK,EAAE,CAAC,EAAE;AACvEuC,MAAAA,SAAS,CAACZ,GAAG,CAACK,GAAG,CAAC,CAAA;AACpB,KAAA;IAEA,IAAIS,aAAa,GAAG,IAAI,CAAA;IACxB,OAAQA,aAAa,GAAGvB,QAAQ,CAACI,UAAU,CAACtB,KAAK,EAAE,EAAG;AACpDyC,MAAAA,aAAa,EAAE,CAAA;AACjB,KAAA;;AAEA;AACAvB,IAAAA,QAAQ,CAACC,QAAQ,GACfD,QAAQ,CAACG,QAAQ,CAAC7O,KAAK,EAAE,IAAI0O,QAAQ,CAACE,MAAM,CAAC5O,KAAK,EAAE,GAChD1b,OAAO,CAACC,MAAM,CAAC6qB,qBAAqB,CAACV,QAAQ,CAACW,KAAK,CAAC,GACpD,IAAI,CAAA;AACZ,GAAA;AACF;;AC9FA,MAAMa,YAAY,GAAG,UAAUC,UAAU,EAAE;AACzC,EAAA,MAAMC,KAAK,GAAGD,UAAU,CAACC,KAAK,CAAA;EAC9B,MAAMlT,QAAQ,GAAGiT,UAAU,CAACE,MAAM,CAACnT,QAAQ,EAAE,CAAA;AAC7C,EAAA,MAAMoT,GAAG,GAAGF,KAAK,GAAGlT,QAAQ,CAAA;EAC5B,OAAO;AACLkT,IAAAA,KAAK,EAAEA,KAAK;AACZlT,IAAAA,QAAQ,EAAEA,QAAQ;AAClBoT,IAAAA,GAAG,EAAEA,GAAG;IACRD,MAAM,EAAEF,UAAU,CAACE,MAAAA;GACpB,CAAA;AACH,CAAC,CAAA;AAED,MAAME,aAAa,GAAG,YAAY;AAChC,EAAA,MAAMlY,CAAC,GAAG/T,OAAO,CAACC,MAAM,CAAA;EACxB,OAAO,CAAC8T,CAAC,CAAC2W,WAAW,IAAI3W,CAAC,CAAC4W,IAAI,EAAEO,GAAG,EAAE,CAAA;AACxC,CAAC,CAAA;AAEc,MAAMgB,QAAQ,SAAS7T,WAAW,CAAC;AAChD;AACAnV,EAAAA,WAAWA,CAACipB,UAAU,GAAGF,aAAa,EAAE;AACtC,IAAA,KAAK,EAAE,CAAA;IAEP,IAAI,CAACG,WAAW,GAAGD,UAAU,CAAA;;AAE7B;IACA,IAAI,CAACE,SAAS,EAAE,CAAA;AAClB,GAAA;AAEAC,EAAAA,MAAMA,GAAG;AACP,IAAA,OAAO,CAAC,CAAC,IAAI,CAACC,UAAU,CAAA;AAC1B,GAAA;AAEAC,EAAAA,MAAMA,GAAG;AACP;IACA,IAAI,CAACvB,IAAI,CAAC,IAAI,CAACwB,oBAAoB,EAAE,GAAG,CAAC,CAAC,CAAA;AAC1C,IAAA,OAAO,IAAI,CAACC,KAAK,EAAE,CAAA;AACrB,GAAA;;AAEA;AACAC,EAAAA,UAAUA,GAAG;AACX,IAAA,MAAMC,cAAc,GAAG,IAAI,CAACC,iBAAiB,EAAE,CAAA;AAC/C,IAAA,MAAMC,YAAY,GAAGF,cAAc,GAAGA,cAAc,CAACb,MAAM,CAACnT,QAAQ,EAAE,GAAG,CAAC,CAAA;IAC1E,MAAMmU,aAAa,GAAGH,cAAc,GAAGA,cAAc,CAACd,KAAK,GAAG,IAAI,CAACkB,KAAK,CAAA;IACxE,OAAOD,aAAa,GAAGD,YAAY,CAAA;AACrC,GAAA;AAEAL,EAAAA,oBAAoBA,GAAG;IACrB,MAAMQ,QAAQ,GAAG,IAAI,CAACC,QAAQ,CAAC/wB,GAAG,CAAEG,CAAC,IAAKA,CAAC,CAACwvB,KAAK,GAAGxvB,CAAC,CAACyvB,MAAM,CAACnT,QAAQ,EAAE,CAAC,CAAA;IACxE,OAAO/b,IAAI,CAACiL,GAAG,CAAC,CAAC,EAAE,GAAGmlB,QAAQ,CAAC,CAAA;AACjC,GAAA;AAEAJ,EAAAA,iBAAiBA,GAAG;AAClB,IAAA,OAAO,IAAI,CAACM,iBAAiB,CAAC,IAAI,CAACC,aAAa,CAAC,CAAA;AACnD,GAAA;EAEAD,iBAAiBA,CAACtqB,EAAE,EAAE;AACpB,IAAA,OAAO,IAAI,CAACqqB,QAAQ,CAAC,IAAI,CAACG,UAAU,CAAC9nB,OAAO,CAAC1C,EAAE,CAAC,CAAC,IAAI,IAAI,CAAA;AAC3D,GAAA;AAEA6pB,EAAAA,KAAKA,GAAG;IACN,IAAI,CAACY,OAAO,GAAG,IAAI,CAAA;AACnB,IAAA,OAAO,IAAI,CAACC,SAAS,EAAE,CAAA;AACzB,GAAA;EAEAC,OAAOA,CAACC,WAAW,EAAE;AACnB,IAAA,IAAIA,WAAW,IAAI,IAAI,EAAE,OAAO,IAAI,CAACC,QAAQ,CAAA;IAC7C,IAAI,CAACA,QAAQ,GAAGD,WAAW,CAAA;AAC3B,IAAA,OAAO,IAAI,CAAA;AACb,GAAA;AAEAE,EAAAA,IAAIA,GAAG;AACL;IACA,IAAI,CAACL,OAAO,GAAG,KAAK,CAAA;IACpB,OAAO,IAAI,CAACM,UAAU,EAAE,CAACL,SAAS,EAAE,CAAA;AACtC,GAAA;EAEApO,OAAOA,CAAC0O,GAAG,EAAE;AACX,IAAA,MAAMC,YAAY,GAAG,IAAI,CAACC,KAAK,EAAE,CAAA;IACjC,IAAIF,GAAG,IAAI,IAAI,EAAE,OAAO,IAAI,CAACE,KAAK,CAAC,CAACD,YAAY,CAAC,CAAA;AAEjD,IAAA,MAAME,QAAQ,GAAGnxB,IAAI,CAAC2Q,GAAG,CAACsgB,YAAY,CAAC,CAAA;IACvC,OAAO,IAAI,CAACC,KAAK,CAACF,GAAG,GAAG,CAACG,QAAQ,GAAGA,QAAQ,CAAC,CAAA;AAC/C,GAAA;;AAEA;AACAC,EAAAA,QAAQA,CAAClC,MAAM,EAAEjT,KAAK,EAAEoV,IAAI,EAAE;IAC5B,IAAInC,MAAM,IAAI,IAAI,EAAE;AAClB,MAAA,OAAO,IAAI,CAACmB,QAAQ,CAAC/wB,GAAG,CAACyvB,YAAY,CAAC,CAAA;AACxC,KAAA;;AAEA;AACA;AACA;;IAEA,IAAIuC,iBAAiB,GAAG,CAAC,CAAA;AACzB,IAAA,MAAMC,OAAO,GAAG,IAAI,CAACzB,UAAU,EAAE,CAAA;IACjC7T,KAAK,GAAGA,KAAK,IAAI,CAAC,CAAA;;AAElB;IACA,IAAIoV,IAAI,IAAI,IAAI,IAAIA,IAAI,KAAK,MAAM,IAAIA,IAAI,KAAK,OAAO,EAAE;AACvD;AACAC,MAAAA,iBAAiB,GAAGC,OAAO,CAAA;KAC5B,MAAM,IAAIF,IAAI,KAAK,UAAU,IAAIA,IAAI,KAAK,OAAO,EAAE;AAClDC,MAAAA,iBAAiB,GAAGrV,KAAK,CAAA;AACzBA,MAAAA,KAAK,GAAG,CAAC,CAAA;AACX,KAAC,MAAM,IAAIoV,IAAI,KAAK,KAAK,EAAE;MACzBC,iBAAiB,GAAG,IAAI,CAACnB,KAAK,CAAA;AAChC,KAAC,MAAM,IAAIkB,IAAI,KAAK,UAAU,EAAE;MAC9B,MAAMrC,UAAU,GAAG,IAAI,CAACsB,iBAAiB,CAACpB,MAAM,CAAClpB,EAAE,CAAC,CAAA;AACpD,MAAA,IAAIgpB,UAAU,EAAE;AACdsC,QAAAA,iBAAiB,GAAGtC,UAAU,CAACC,KAAK,GAAGhT,KAAK,CAAA;AAC5CA,QAAAA,KAAK,GAAG,CAAC,CAAA;AACX,OAAA;AACF,KAAC,MAAM,IAAIoV,IAAI,KAAK,WAAW,EAAE;AAC/B,MAAA,MAAMtB,cAAc,GAAG,IAAI,CAACC,iBAAiB,EAAE,CAAA;MAC/C,MAAME,aAAa,GAAGH,cAAc,GAAGA,cAAc,CAACd,KAAK,GAAG,IAAI,CAACkB,KAAK,CAAA;AACxEmB,MAAAA,iBAAiB,GAAGpB,aAAa,CAAA;AACnC,KAAC,MAAM;AACL,MAAA,MAAM,IAAIpjB,KAAK,CAAC,wCAAwC,CAAC,CAAA;AAC3D,KAAA;;AAEA;IACAoiB,MAAM,CAACsC,UAAU,EAAE,CAAA;AACnBtC,IAAAA,MAAM,CAACpT,QAAQ,CAAC,IAAI,CAAC,CAAA;AAErB,IAAA,MAAM6U,OAAO,GAAGzB,MAAM,CAACyB,OAAO,EAAE,CAAA;AAChC,IAAA,MAAM3B,UAAU,GAAG;MACjB2B,OAAO,EAAEA,OAAO,KAAK,IAAI,GAAG,IAAI,CAACE,QAAQ,GAAGF,OAAO;MACnD1B,KAAK,EAAEqC,iBAAiB,GAAGrV,KAAK;AAChCiT,MAAAA,MAAAA;KACD,CAAA;AAED,IAAA,IAAI,CAACqB,aAAa,GAAGrB,MAAM,CAAClpB,EAAE,CAAA;AAE9B,IAAA,IAAI,CAACqqB,QAAQ,CAAChxB,IAAI,CAAC2vB,UAAU,CAAC,CAAA;AAC9B,IAAA,IAAI,CAACqB,QAAQ,CAACjE,IAAI,CAAC,CAACpiB,CAAC,EAAEwB,CAAC,KAAKxB,CAAC,CAACilB,KAAK,GAAGzjB,CAAC,CAACyjB,KAAK,CAAC,CAAA;AAC/C,IAAA,IAAI,CAACuB,UAAU,GAAG,IAAI,CAACH,QAAQ,CAAC/wB,GAAG,CAAEmyB,IAAI,IAAKA,IAAI,CAACvC,MAAM,CAAClpB,EAAE,CAAC,CAAA;AAE7D,IAAA,IAAI,CAAC+qB,UAAU,EAAE,CAACL,SAAS,EAAE,CAAA;AAC7B,IAAA,OAAO,IAAI,CAAA;AACb,GAAA;EAEAgB,IAAIA,CAAC/K,EAAE,EAAE;IACP,OAAO,IAAI,CAACyH,IAAI,CAAC,IAAI,CAAC+B,KAAK,GAAGxJ,EAAE,CAAC,CAAA;AACnC,GAAA;EAEA3W,MAAMA,CAACnM,EAAE,EAAE;AACT,IAAA,IAAIA,EAAE,IAAI,IAAI,EAAE,OAAO,IAAI,CAAC0rB,WAAW,CAAA;IACvC,IAAI,CAACA,WAAW,GAAG1rB,EAAE,CAAA;AACrB,IAAA,OAAO,IAAI,CAAA;AACb,GAAA;EAEAqtB,KAAKA,CAACA,KAAK,EAAE;AACX,IAAA,IAAIA,KAAK,IAAI,IAAI,EAAE,OAAO,IAAI,CAACS,MAAM,CAAA;IACrC,IAAI,CAACA,MAAM,GAAGT,KAAK,CAAA;AACnB,IAAA,OAAO,IAAI,CAAA;AACb,GAAA;AAEAU,EAAAA,IAAIA,GAAG;AACL;AACA,IAAA,IAAI,CAACxD,IAAI,CAAC,CAAC,CAAC,CAAA;AACZ,IAAA,OAAO,IAAI,CAACyB,KAAK,EAAE,CAAA;AACrB,GAAA;EAEAzB,IAAIA,CAACA,IAAI,EAAE;AACT,IAAA,IAAIA,IAAI,IAAI,IAAI,EAAE,OAAO,IAAI,CAAC+B,KAAK,CAAA;IACnC,IAAI,CAACA,KAAK,GAAG/B,IAAI,CAAA;AACjB,IAAA,OAAO,IAAI,CAACsC,SAAS,CAAC,IAAI,CAAC,CAAA;AAC7B,GAAA;;AAEA;EACAc,UAAUA,CAACtC,MAAM,EAAE;IACjB,MAAMvoB,KAAK,GAAG,IAAI,CAAC6pB,UAAU,CAAC9nB,OAAO,CAACwmB,MAAM,CAAClpB,EAAE,CAAC,CAAA;AAChD,IAAA,IAAIW,KAAK,GAAG,CAAC,EAAE,OAAO,IAAI,CAAA;IAE1B,IAAI,CAAC0pB,QAAQ,CAACvE,MAAM,CAACnlB,KAAK,EAAE,CAAC,CAAC,CAAA;IAC9B,IAAI,CAAC6pB,UAAU,CAAC1E,MAAM,CAACnlB,KAAK,EAAE,CAAC,CAAC,CAAA;AAEhCuoB,IAAAA,MAAM,CAACpT,QAAQ,CAAC,IAAI,CAAC,CAAA;AACrB,IAAA,OAAO,IAAI,CAAA;AACb,GAAA;;AAEA;AACAiV,EAAAA,UAAUA,GAAG;AACX,IAAA,IAAI,CAAC,IAAI,CAACtB,MAAM,EAAE,EAAE;AAClB,MAAA,IAAI,CAACoC,eAAe,GAAG,IAAI,CAACtC,WAAW,EAAE,CAAA;AAC3C,KAAA;AACA,IAAA,OAAO,IAAI,CAAA;AACb,GAAA;;AAEA;AACAmB,EAAAA,SAASA,CAACoB,aAAa,GAAG,KAAK,EAAE;AAC/BvE,IAAAA,QAAQ,CAACgB,WAAW,CAAC,IAAI,CAACmB,UAAU,CAAC,CAAA;IACrC,IAAI,CAACA,UAAU,GAAG,IAAI,CAAA;AAEtB,IAAA,IAAIoC,aAAa,EAAE,OAAO,IAAI,CAACC,cAAc,EAAE,CAAA;AAC/C,IAAA,IAAI,IAAI,CAACtB,OAAO,EAAE,OAAO,IAAI,CAAA;IAE7B,IAAI,CAACf,UAAU,GAAGnC,QAAQ,CAACQ,KAAK,CAAC,IAAI,CAACiE,KAAK,CAAC,CAAA;AAC5C,IAAA,OAAO,IAAI,CAAA;AACb,GAAA;AAEAC,EAAAA,OAAOA,CAACH,aAAa,GAAG,KAAK,EAAE;AAC7B;AACA,IAAA,MAAM1D,IAAI,GAAG,IAAI,CAACmB,WAAW,EAAE,CAAA;AAC/B,IAAA,IAAI2C,QAAQ,GAAG9D,IAAI,GAAG,IAAI,CAACyD,eAAe,CAAA;AAE1C,IAAA,IAAIC,aAAa,EAAEI,QAAQ,GAAG,CAAC,CAAA;AAE/B,IAAA,MAAMC,MAAM,GAAG,IAAI,CAACR,MAAM,GAAGO,QAAQ,IAAI,IAAI,CAAC/B,KAAK,GAAG,IAAI,CAACiC,aAAa,CAAC,CAAA;IACzE,IAAI,CAACP,eAAe,GAAGzD,IAAI,CAAA;;AAE3B;AACA;IACA,IAAI,CAAC0D,aAAa,EAAE;AAClB;MACA,IAAI,CAAC3B,KAAK,IAAIgC,MAAM,CAAA;AACpB,MAAA,IAAI,CAAChC,KAAK,GAAG,IAAI,CAACA,KAAK,GAAG,CAAC,GAAG,CAAC,GAAG,IAAI,CAACA,KAAK,CAAA;AAC9C,KAAA;AACA,IAAA,IAAI,CAACiC,aAAa,GAAG,IAAI,CAACjC,KAAK,CAAA;IAC/B,IAAI,CAACvU,IAAI,CAAC,MAAM,EAAE,IAAI,CAACuU,KAAK,CAAC,CAAA;;AAE7B;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;IACA,KAAK,IAAI7lB,CAAC,GAAG,IAAI,CAAC+lB,QAAQ,CAAC1wB,MAAM,EAAE2K,CAAC,EAAE,GAAI;AACxC;AACA,MAAA,MAAM0kB,UAAU,GAAG,IAAI,CAACqB,QAAQ,CAAC/lB,CAAC,CAAC,CAAA;AACnC,MAAA,MAAM4kB,MAAM,GAAGF,UAAU,CAACE,MAAM,CAAA;;AAEhC;AACA;MACA,MAAMmD,SAAS,GAAG,IAAI,CAAClC,KAAK,GAAGnB,UAAU,CAACC,KAAK,CAAA;;AAE/C;AACA;MACA,IAAIoD,SAAS,IAAI,CAAC,EAAE;QAClBnD,MAAM,CAACoD,KAAK,EAAE,CAAA;AAChB,OAAA;AACF,KAAA;;AAEA;IACA,IAAIC,WAAW,GAAG,KAAK,CAAA;AACvB,IAAA,KAAK,IAAI9yB,CAAC,GAAG,CAAC,EAAEkhB,GAAG,GAAG,IAAI,CAAC0P,QAAQ,CAAC1wB,MAAM,EAAEF,CAAC,GAAGkhB,GAAG,EAAElhB,CAAC,EAAE,EAAE;AACxD;AACA,MAAA,MAAMuvB,UAAU,GAAG,IAAI,CAACqB,QAAQ,CAAC5wB,CAAC,CAAC,CAAA;AACnC,MAAA,MAAMyvB,MAAM,GAAGF,UAAU,CAACE,MAAM,CAAA;MAChC,IAAIvI,EAAE,GAAGwL,MAAM,CAAA;;AAEf;AACA;MACA,MAAME,SAAS,GAAG,IAAI,CAAClC,KAAK,GAAGnB,UAAU,CAACC,KAAK,CAAA;;AAE/C;MACA,IAAIoD,SAAS,IAAI,CAAC,EAAE;AAClBE,QAAAA,WAAW,GAAG,IAAI,CAAA;AAClB,QAAA,SAAA;AACF,OAAC,MAAM,IAAIF,SAAS,GAAG1L,EAAE,EAAE;AACzB;AACAA,QAAAA,EAAE,GAAG0L,SAAS,CAAA;AAChB,OAAA;AAEA,MAAA,IAAI,CAACnD,MAAM,CAACO,MAAM,EAAE,EAAE,SAAA;;AAEtB;AACA;MACA,MAAM+C,QAAQ,GAAGtD,MAAM,CAAChJ,IAAI,CAACS,EAAE,CAAC,CAACL,IAAI,CAAA;MACrC,IAAI,CAACkM,QAAQ,EAAE;AACbD,QAAAA,WAAW,GAAG,IAAI,CAAA;AAClB;AACF,OAAC,MAAM,IAAIvD,UAAU,CAAC2B,OAAO,KAAK,IAAI,EAAE;AACtC;AACA,QAAA,MAAMY,OAAO,GAAGrC,MAAM,CAACnT,QAAQ,EAAE,GAAGmT,MAAM,CAACd,IAAI,EAAE,GAAG,IAAI,CAAC+B,KAAK,CAAA;QAE9D,IAAIoB,OAAO,GAAGvC,UAAU,CAAC2B,OAAO,GAAG,IAAI,CAACR,KAAK,EAAE;AAC7C;UACAjB,MAAM,CAACsC,UAAU,EAAE,CAAA;AACnB,UAAA,EAAE/xB,CAAC,CAAA;AACH,UAAA,EAAEkhB,GAAG,CAAA;AACP,SAAA;AACF,OAAA;AACF,KAAA;;AAEA;AACA;AACA,IAAA,IACG4R,WAAW,IAAI,EAAE,IAAI,CAACZ,MAAM,GAAG,CAAC,IAAI,IAAI,CAACxB,KAAK,KAAK,CAAC,CAAC,IACrD,IAAI,CAACK,UAAU,CAAC7wB,MAAM,IAAI,IAAI,CAACgyB,MAAM,GAAG,CAAC,IAAI,IAAI,CAACxB,KAAK,GAAG,CAAE,EAC7D;MACA,IAAI,CAACO,SAAS,EAAE,CAAA;AAClB,KAAC,MAAM;MACL,IAAI,CAACb,KAAK,EAAE,CAAA;AACZ,MAAA,IAAI,CAACjU,IAAI,CAAC,UAAU,CAAC,CAAA;AACvB,KAAA;AAEA,IAAA,OAAO,IAAI,CAAA;AACb,GAAA;AAEA4T,EAAAA,SAASA,GAAG;AACV;;AAEA;IACA,IAAI,CAACiD,UAAU,GAAG,CAAC,CAAA;IACnB,IAAI,CAACd,MAAM,GAAG,GAAG,CAAA;;AAEjB;IACA,IAAI,CAACd,QAAQ,GAAG,CAAC,CAAA;;AAEjB;IACA,IAAI,CAACnB,UAAU,GAAG,IAAI,CAAA;IACtB,IAAI,CAACe,OAAO,GAAG,IAAI,CAAA;IACnB,IAAI,CAACJ,QAAQ,GAAG,EAAE,CAAA;IAClB,IAAI,CAACG,UAAU,GAAG,EAAE,CAAA;AACpB,IAAA,IAAI,CAACD,aAAa,GAAG,CAAC,CAAC,CAAA;IACvB,IAAI,CAACJ,KAAK,GAAG,CAAC,CAAA;IACd,IAAI,CAAC0B,eAAe,GAAG,CAAC,CAAA;IACxB,IAAI,CAACO,aAAa,GAAG,CAAC,CAAA;;AAEtB;AACA,IAAA,IAAI,CAACJ,KAAK,GAAG,IAAI,CAACC,OAAO,CAACxX,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC,CAAA;AAC3C,IAAA,IAAI,CAACsX,cAAc,GAAG,IAAI,CAACE,OAAO,CAACxX,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,CAAA;AACrD,GAAA;AACF,CAAA;AAEAlc,eAAe,CAAC;AACd4V,EAAAA,OAAO,EAAE;AACP2H,IAAAA,QAAQ,EAAE,UAAUA,QAAQ,EAAE;MAC5B,IAAIA,QAAQ,IAAI,IAAI,EAAE;QACpB,IAAI,CAAC4W,SAAS,GAAG,IAAI,CAACA,SAAS,IAAI,IAAIrD,QAAQ,EAAE,CAAA;QACjD,OAAO,IAAI,CAACqD,SAAS,CAAA;AACvB,OAAC,MAAM;QACL,IAAI,CAACA,SAAS,GAAG5W,QAAQ,CAAA;AACzB,QAAA,OAAO,IAAI,CAAA;AACb,OAAA;AACF,KAAA;AACF,GAAA;AACF,CAAC,CAAC;;AC7Ua,MAAM6W,MAAM,SAASnX,WAAW,CAAC;EAC9CnV,WAAWA,CAACmU,OAAO,EAAE;AACnB,IAAA,KAAK,EAAE,CAAA;;AAEP;AACA,IAAA,IAAI,CAACxU,EAAE,GAAG2sB,MAAM,CAAC3sB,EAAE,EAAE,CAAA;;AAErB;IACAwU,OAAO,GAAGA,OAAO,IAAI,IAAI,GAAGsB,QAAQ,CAACC,QAAQ,GAAGvB,OAAO,CAAA;;AAEvD;AACAA,IAAAA,OAAO,GAAG,OAAOA,OAAO,KAAK,UAAU,GAAG,IAAIgM,UAAU,CAAChM,OAAO,CAAC,GAAGA,OAAO,CAAA;;AAE3E;IACA,IAAI,CAACsH,QAAQ,GAAG,IAAI,CAAA;IACpB,IAAI,CAAC4Q,SAAS,GAAG,IAAI,CAAA;IACrB,IAAI,CAACpM,IAAI,GAAG,KAAK,CAAA;IACjB,IAAI,CAACsM,MAAM,GAAG,EAAE,CAAA;;AAEhB;IACA,IAAI,CAAC/L,SAAS,GAAG,OAAOrM,OAAO,KAAK,QAAQ,IAAIA,OAAO,CAAA;AACvD,IAAA,IAAI,CAACqY,cAAc,GAAGrY,OAAO,YAAYgM,UAAU,CAAA;AACnD,IAAA,IAAI,CAACwE,QAAQ,GAAG,IAAI,CAAC6H,cAAc,GAAGrY,OAAO,GAAG,IAAI+L,IAAI,EAAE,CAAA;;AAE1D;AACA,IAAA,IAAI,CAACuM,QAAQ,GAAG,EAAE,CAAA;;AAElB;IACA,IAAI,CAACC,OAAO,GAAG,IAAI,CAAA;IACnB,IAAI,CAAC5C,KAAK,GAAG,CAAC,CAAA;IACd,IAAI,CAAC6C,SAAS,GAAG,CAAC,CAAA;;AAElB;IACA,IAAI,CAACC,QAAQ,GAAG,IAAI,CAAA;;AAEpB;AACA,IAAA,IAAI,CAACprB,UAAU,GAAG,IAAIsI,MAAM,EAAE,CAAA;IAC9B,IAAI,CAAC+iB,WAAW,GAAG,CAAC,CAAA;;AAEpB;IACA,IAAI,CAACC,aAAa,GAAG,KAAK,CAAA;IAC1B,IAAI,CAACC,QAAQ,GAAG,KAAK,CAAA;IACrB,IAAI,CAACC,UAAU,GAAG,CAAC,CAAA;IACnB,IAAI,CAACC,MAAM,GAAG,KAAK,CAAA;IACnB,IAAI,CAACC,KAAK,GAAG,CAAC,CAAA;IACd,IAAI,CAACC,MAAM,GAAG,CAAC,CAAA;IAEf,IAAI,CAACC,QAAQ,GAAG,IAAI,CAAA;;AAEpB;IACA,IAAI,CAAC5C,QAAQ,GAAG,IAAI,CAACgC,cAAc,GAAG,IAAI,GAAG,IAAI,CAAA;AACnD,GAAA;AAEA,EAAA,OAAOa,QAAQA,CAAC3X,QAAQ,EAAEE,KAAK,EAAEoV,IAAI,EAAE;AACrC;IACA,IAAInU,KAAK,GAAG,CAAC,CAAA;IACb,IAAIyW,KAAK,GAAG,KAAK,CAAA;IACjB,IAAIC,IAAI,GAAG,CAAC,CAAA;AACZ7X,IAAAA,QAAQ,GAAGA,QAAQ,IAAID,QAAQ,CAACC,QAAQ,CAAA;AACxCE,IAAAA,KAAK,GAAGA,KAAK,IAAIH,QAAQ,CAACG,KAAK,CAAA;IAC/BoV,IAAI,GAAGA,IAAI,IAAI,MAAM,CAAA;;AAErB;IACA,IAAI,OAAOtV,QAAQ,KAAK,QAAQ,IAAI,EAAEA,QAAQ,YAAYsK,OAAO,CAAC,EAAE;AAClEpK,MAAAA,KAAK,GAAGF,QAAQ,CAACE,KAAK,IAAIA,KAAK,CAAA;AAC/BoV,MAAAA,IAAI,GAAGtV,QAAQ,CAACsV,IAAI,IAAIA,IAAI,CAAA;AAC5BsC,MAAAA,KAAK,GAAG5X,QAAQ,CAAC4X,KAAK,IAAIA,KAAK,CAAA;AAC/BzW,MAAAA,KAAK,GAAGnB,QAAQ,CAACmB,KAAK,IAAIA,KAAK,CAAA;AAC/B0W,MAAAA,IAAI,GAAG7X,QAAQ,CAAC6X,IAAI,IAAIA,IAAI,CAAA;AAC5B7X,MAAAA,QAAQ,GAAGA,QAAQ,CAACA,QAAQ,IAAID,QAAQ,CAACC,QAAQ,CAAA;AACnD,KAAA;IAEA,OAAO;AACLA,MAAAA,QAAQ,EAAEA,QAAQ;AAClBE,MAAAA,KAAK,EAAEA,KAAK;AACZ0X,MAAAA,KAAK,EAAEA,KAAK;AACZzW,MAAAA,KAAK,EAAEA,KAAK;AACZ0W,MAAAA,IAAI,EAAEA,IAAI;AACVvC,MAAAA,IAAI,EAAEA,IAAAA;KACP,CAAA;AACH,GAAA;EAEA5B,MAAMA,CAACsD,OAAO,EAAE;AACd,IAAA,IAAIA,OAAO,IAAI,IAAI,EAAE,OAAO,IAAI,CAACA,OAAO,CAAA;IACxC,IAAI,CAACA,OAAO,GAAGA,OAAO,CAAA;AACtB,IAAA,OAAO,IAAI,CAAA;AACb,GAAA;;AAEA;AACF;AACA;AACA;AACA;EACEc,YAAYA,CAAC5jB,SAAS,EAAE;AACtB,IAAA,IAAI,CAACpI,UAAU,CAACuL,UAAU,CAACnD,SAAS,CAAC,CAAA;AACrC,IAAA,OAAO,IAAI,CAAA;AACb,GAAA;EAEA3I,KAAKA,CAACzD,EAAE,EAAE;AACR,IAAA,OAAO,IAAI,CAACwW,EAAE,CAAC,UAAU,EAAExW,EAAE,CAAC,CAAA;AAChC,GAAA;AAEAiwB,EAAAA,OAAOA,CAAC/X,QAAQ,EAAEE,KAAK,EAAEoV,IAAI,EAAE;IAC7B,MAAMjwB,CAAC,GAAGuxB,MAAM,CAACe,QAAQ,CAAC3X,QAAQ,EAAEE,KAAK,EAAEoV,IAAI,CAAC,CAAA;IAChD,MAAMnC,MAAM,GAAG,IAAIyD,MAAM,CAACvxB,CAAC,CAAC2a,QAAQ,CAAC,CAAA;IACrC,IAAI,IAAI,CAAC2W,SAAS,EAAExD,MAAM,CAACpT,QAAQ,CAAC,IAAI,CAAC4W,SAAS,CAAC,CAAA;IACnD,IAAI,IAAI,CAAC5Q,QAAQ,EAAEoN,MAAM,CAACpuB,OAAO,CAAC,IAAI,CAACghB,QAAQ,CAAC,CAAA;AAChD,IAAA,OAAOoN,MAAM,CAAC6E,IAAI,CAAC3yB,CAAC,CAAC,CAACgwB,QAAQ,CAAChwB,CAAC,CAAC6a,KAAK,EAAE7a,CAAC,CAACiwB,IAAI,CAAC,CAAA;AACjD,GAAA;AAEA2C,EAAAA,cAAcA,GAAG;AACf,IAAA,IAAI,CAACnsB,UAAU,GAAG,IAAIsI,MAAM,EAAE,CAAA;AAC9B,IAAA,OAAO,IAAI,CAAA;AACb,GAAA;;AAEA;AACA8jB,EAAAA,wBAAwBA,GAAG;IACzB,IACE,CAAC,IAAI,CAAC3N,IAAI,IACV,CAAC,IAAI,CAACoM,SAAS,IACf,CAAC,IAAI,CAACA,SAAS,CAAClC,UAAU,CAAC1uB,QAAQ,CAAC,IAAI,CAACkE,EAAE,CAAC,EAC5C;MACA,IAAI,CAAC4sB,MAAM,GAAG,IAAI,CAACA,MAAM,CAAC/yB,MAAM,CAAEytB,IAAI,IAAK;QACzC,OAAO,CAACA,IAAI,CAAC4G,WAAW,CAAA;AAC1B,OAAC,CAAC,CAAA;AACJ,KAAA;AACF,GAAA;EAEAjY,KAAKA,CAACA,KAAK,EAAE;AACX,IAAA,OAAO,IAAI,CAAC6X,OAAO,CAAC,CAAC,EAAE7X,KAAK,CAAC,CAAA;AAC/B,GAAA;AAEAF,EAAAA,QAAQA,GAAG;AACT,IAAA,OAAO,IAAI,CAACyX,MAAM,IAAI,IAAI,CAACD,KAAK,GAAG,IAAI,CAAC1M,SAAS,CAAC,GAAG,IAAI,CAAC0M,KAAK,CAAA;AACjE,GAAA;EAEAY,MAAMA,CAACtwB,EAAE,EAAE;AACT,IAAA,OAAO,IAAI,CAACuwB,KAAK,CAAC,IAAI,EAAEvwB,EAAE,CAAC,CAAA;AAC7B,GAAA;EAEAmY,IAAIA,CAACnY,EAAE,EAAE;AACP,IAAA,IAAI,CAACmnB,QAAQ,GAAG,IAAIzE,IAAI,CAAC1iB,EAAE,CAAC,CAAA;AAC5B,IAAA,OAAO,IAAI,CAAA;AACb,GAAA;AACA;AACF;AACA;AACA;AACA;AACA;;EAEE/C,OAAOA,CAACA,OAAO,EAAE;AACf,IAAA,IAAIA,OAAO,IAAI,IAAI,EAAE,OAAO,IAAI,CAACghB,QAAQ,CAAA;IACzC,IAAI,CAACA,QAAQ,GAAGhhB,OAAO,CAAA;IACvBA,OAAO,CAACuzB,cAAc,EAAE,CAAA;AACxB,IAAA,OAAO,IAAI,CAAA;AACb,GAAA;AAEA1E,EAAAA,MAAMA,GAAG;AACP,IAAA,OAAO,IAAI,CAACzJ,IAAI,CAAC1O,QAAQ,CAAC,CAAA;AAC5B,GAAA;AAEAuc,EAAAA,IAAIA,CAAC7W,KAAK,EAAEyW,KAAK,EAAEC,IAAI,EAAE;AACvB;AACA,IAAA,IAAI,OAAO1W,KAAK,KAAK,QAAQ,EAAE;MAC7ByW,KAAK,GAAGzW,KAAK,CAACyW,KAAK,CAAA;MACnBC,IAAI,GAAG1W,KAAK,CAAC0W,IAAI,CAAA;MACjB1W,KAAK,GAAGA,KAAK,CAACA,KAAK,CAAA;AACrB,KAAA;;AAEA;AACA,IAAA,IAAI,CAACsW,MAAM,GAAGtW,KAAK,IAAI1F,QAAQ,CAAA;AAC/B,IAAA,IAAI,CAAC8b,MAAM,GAAGK,KAAK,IAAI,KAAK,CAAA;AAC5B,IAAA,IAAI,CAACJ,KAAK,GAAGK,IAAI,IAAI,CAAC,CAAA;;AAEtB;AACA,IAAA,IAAI,IAAI,CAACJ,MAAM,KAAK,IAAI,EAAE;MACxB,IAAI,CAACA,MAAM,GAAGhc,QAAQ,CAAA;AACxB,KAAA;AAEA,IAAA,OAAO,IAAI,CAAA;AACb,GAAA;EAEA8c,KAAKA,CAACvtB,CAAC,EAAE;IACP,MAAMwtB,YAAY,GAAG,IAAI,CAAC1N,SAAS,GAAG,IAAI,CAAC0M,KAAK,CAAA;IAChD,IAAIxsB,CAAC,IAAI,IAAI,EAAE;MACb,MAAMytB,SAAS,GAAGx0B,IAAI,CAACmmB,KAAK,CAAC,IAAI,CAACgK,KAAK,GAAGoE,YAAY,CAAC,CAAA;MACvD,MAAME,YAAY,GAAG,IAAI,CAACtE,KAAK,GAAGqE,SAAS,GAAGD,YAAY,CAAA;AAC1D,MAAA,MAAM7tB,QAAQ,GAAG+tB,YAAY,GAAG,IAAI,CAAC5N,SAAS,CAAA;MAC9C,OAAO7mB,IAAI,CAACkL,GAAG,CAACspB,SAAS,GAAG9tB,QAAQ,EAAE,IAAI,CAAC8sB,MAAM,CAAC,CAAA;AACpD,KAAA;AACA,IAAA,MAAMkB,KAAK,GAAG10B,IAAI,CAACmmB,KAAK,CAACpf,CAAC,CAAC,CAAA;AAC3B,IAAA,MAAM4tB,OAAO,GAAG5tB,CAAC,GAAG,CAAC,CAAA;IACrB,MAAMqnB,IAAI,GAAGmG,YAAY,GAAGG,KAAK,GAAG,IAAI,CAAC7N,SAAS,GAAG8N,OAAO,CAAA;AAC5D,IAAA,OAAO,IAAI,CAACvG,IAAI,CAACA,IAAI,CAAC,CAAA;AACxB,GAAA;EAEAuC,OAAOA,CAACC,WAAW,EAAE;AACnB,IAAA,IAAIA,WAAW,IAAI,IAAI,EAAE,OAAO,IAAI,CAACC,QAAQ,CAAA;IAC7C,IAAI,CAACA,QAAQ,GAAGD,WAAW,CAAA;AAC3B,IAAA,OAAO,IAAI,CAAA;AACb,GAAA;EAEAlqB,QAAQA,CAACK,CAAC,EAAE;AACV;AACA,IAAA,MAAMrF,CAAC,GAAG,IAAI,CAACyuB,KAAK,CAAA;AACpB,IAAA,MAAMpwB,CAAC,GAAG,IAAI,CAAC8mB,SAAS,CAAA;AACxB,IAAA,MAAM3P,CAAC,GAAG,IAAI,CAACqc,KAAK,CAAA;AACpB,IAAA,MAAM/pB,CAAC,GAAG,IAAI,CAACgqB,MAAM,CAAA;AACrB,IAAA,MAAMnzB,CAAC,GAAG,IAAI,CAACizB,MAAM,CAAA;AACrB,IAAA,MAAMnzB,CAAC,GAAG,IAAI,CAACizB,QAAQ,CAAA;AACvB,IAAA,IAAI1sB,QAAQ,CAAA;IAEZ,IAAIK,CAAC,IAAI,IAAI,EAAE;AACb;AACN;AACA;AACA;AACA;AACA;;AAEM;AACA,MAAA,MAAMsJ,CAAC,GAAG,UAAU3O,CAAC,EAAE;QACrB,MAAMkzB,QAAQ,GAAGv0B,CAAC,GAAGL,IAAI,CAACmmB,KAAK,CAAEzkB,CAAC,IAAI,CAAC,IAAIwV,CAAC,GAAGnX,CAAC,CAAC,CAAC,IAAKmX,CAAC,GAAGnX,CAAC,CAAC,CAAC,CAAA;QAC9D,MAAM80B,SAAS,GAAID,QAAQ,IAAI,CAACz0B,CAAC,IAAM,CAACy0B,QAAQ,IAAIz0B,CAAE,CAAA;QACtD,MAAM20B,QAAQ,GACX90B,IAAI,CAACyO,GAAG,CAAC,CAAC,CAAC,EAAEomB,SAAS,CAAC,IAAInzB,CAAC,IAAIwV,CAAC,GAAGnX,CAAC,CAAC,CAAC,GAAIA,CAAC,GAAG80B,SAAS,CAAA;AAC3D,QAAA,MAAME,OAAO,GAAG/0B,IAAI,CAACiL,GAAG,CAACjL,IAAI,CAACkL,GAAG,CAAC4pB,QAAQ,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAA;AAClD,QAAA,OAAOC,OAAO,CAAA;OACf,CAAA;;AAED;MACA,MAAMxD,OAAO,GAAG/nB,CAAC,IAAI0N,CAAC,GAAGnX,CAAC,CAAC,GAAGmX,CAAC,CAAA;AAC/BxQ,MAAAA,QAAQ,GACNhF,CAAC,IAAI,CAAC,GACF1B,IAAI,CAAC+K,KAAK,CAACsF,CAAC,CAAC,IAAI,CAAC,CAAC,GACnB3O,CAAC,GAAG6vB,OAAO,GACTlhB,CAAC,CAAC3O,CAAC,CAAC,GACJ1B,IAAI,CAAC+K,KAAK,CAACsF,CAAC,CAACkhB,OAAO,GAAG,IAAI,CAAC,CAAC,CAAA;AACrC,MAAA,OAAO7qB,QAAQ,CAAA;AACjB,KAAA;;AAEA;IACA,MAAM8tB,SAAS,GAAGx0B,IAAI,CAACmmB,KAAK,CAAC,IAAI,CAACmO,KAAK,EAAE,CAAC,CAAA;IAC1C,MAAMU,YAAY,GAAG30B,CAAC,IAAIm0B,SAAS,GAAG,CAAC,KAAK,CAAC,CAAA;IAC7C,MAAMS,QAAQ,GAAID,YAAY,IAAI,CAAC70B,CAAC,IAAMA,CAAC,IAAI60B,YAAa,CAAA;IAC5DtuB,QAAQ,GAAG8tB,SAAS,IAAIS,QAAQ,GAAGluB,CAAC,GAAG,CAAC,GAAGA,CAAC,CAAC,CAAA;AAC7C,IAAA,OAAO,IAAI,CAACutB,KAAK,CAAC5tB,QAAQ,CAAC,CAAA;AAC7B,GAAA;EAEAwuB,QAAQA,CAACnuB,CAAC,EAAE;IACV,IAAIA,CAAC,IAAI,IAAI,EAAE;AACb,MAAA,OAAO/G,IAAI,CAACkL,GAAG,CAAC,CAAC,EAAE,IAAI,CAACilB,KAAK,GAAG,IAAI,CAACpU,QAAQ,EAAE,CAAC,CAAA;AAClD,KAAA;IACA,OAAO,IAAI,CAACqS,IAAI,CAACrnB,CAAC,GAAG,IAAI,CAACgV,QAAQ,EAAE,CAAC,CAAA;AACvC,GAAA;;AAEA;AACF;AACA;AACA;AACA;EACEqY,KAAKA,CAACe,MAAM,EAAEC,KAAK,EAAEC,UAAU,EAAEnB,WAAW,EAAE;AAC5C,IAAA,IAAI,CAACtB,MAAM,CAACvzB,IAAI,CAAC;MACfi2B,WAAW,EAAEH,MAAM,IAAItZ,IAAI;MAC3BqT,MAAM,EAAEkG,KAAK,IAAIvZ,IAAI;AACrB0Z,MAAAA,QAAQ,EAAEF,UAAU;AACpBnB,MAAAA,WAAW,EAAEA,WAAW;AACxBsB,MAAAA,WAAW,EAAE,KAAK;AAClBhD,MAAAA,QAAQ,EAAE,KAAA;AACZ,KAAC,CAAC,CAAA;AACF,IAAA,MAAM1W,QAAQ,GAAG,IAAI,CAACA,QAAQ,EAAE,CAAA;IAChCA,QAAQ,IAAI,IAAI,CAACA,QAAQ,EAAE,CAAC4U,SAAS,EAAE,CAAA;AACvC,IAAA,OAAO,IAAI,CAAA;AACb,GAAA;AAEA4B,EAAAA,KAAKA,GAAG;AACN,IAAA,IAAI,IAAI,CAACW,QAAQ,EAAE,OAAO,IAAI,CAAA;AAC9B,IAAA,IAAI,CAAC7E,IAAI,CAAC,CAAC,CAAC,CAAA;IACZ,IAAI,CAAC6E,QAAQ,GAAG,IAAI,CAAA;AACpB,IAAA,OAAO,IAAI,CAAA;AACb,GAAA;EAEA3Q,OAAOA,CAACA,OAAO,EAAE;AACf,IAAA,IAAI,CAAC8Q,QAAQ,GAAG9Q,OAAO,IAAI,IAAI,GAAG,CAAC,IAAI,CAAC8Q,QAAQ,GAAG9Q,OAAO,CAAA;AAC1D,IAAA,OAAO,IAAI,CAAA;AACb,GAAA;AAEA8O,EAAAA,QAAQA,CAACtV,QAAQ,EAAEG,KAAK,EAAEoV,IAAI,EAAE;AAC9B;AACA,IAAA,IAAI,EAAEvV,QAAQ,YAAYuT,QAAQ,CAAC,EAAE;AACnCgC,MAAAA,IAAI,GAAGpV,KAAK,CAAA;AACZA,MAAAA,KAAK,GAAGH,QAAQ,CAAA;AAChBA,MAAAA,QAAQ,GAAG,IAAI,CAACA,QAAQ,EAAE,CAAA;AAC5B,KAAA;;AAEA;IACA,IAAI,CAACA,QAAQ,EAAE;MACb,MAAMhP,KAAK,CAAC,6CAA6C,CAAC,CAAA;AAC5D,KAAA;;AAEA;IACAgP,QAAQ,CAACsV,QAAQ,CAAC,IAAI,EAAEnV,KAAK,EAAEoV,IAAI,CAAC,CAAA;AACpC,IAAA,OAAO,IAAI,CAAA;AACb,GAAA;EAEAnL,IAAIA,CAACS,EAAE,EAAE;AACP;AACA,IAAA,IAAI,CAAC,IAAI,CAACoM,OAAO,EAAE,OAAO,IAAI,CAAA;;AAE9B;AACApM,IAAAA,EAAE,GAAGA,EAAE,IAAI,IAAI,GAAG,EAAE,GAAGA,EAAE,CAAA;IACzB,IAAI,CAACwJ,KAAK,IAAIxJ,EAAE,CAAA;AAChB,IAAA,MAAMjgB,QAAQ,GAAG,IAAI,CAACA,QAAQ,EAAE,CAAA;;AAEhC;AACA,IAAA,MAAM+uB,OAAO,GAAG,IAAI,CAACC,aAAa,KAAKhvB,QAAQ,IAAI,IAAI,CAACypB,KAAK,IAAI,CAAC,CAAA;IAClE,IAAI,CAACuF,aAAa,GAAGhvB,QAAQ,CAAA;;AAE7B;AACA,IAAA,MAAMqV,QAAQ,GAAG,IAAI,CAACA,QAAQ,EAAE,CAAA;AAChC,IAAA,MAAM4Z,WAAW,GAAG,IAAI,CAAC3C,SAAS,IAAI,CAAC,IAAI,IAAI,CAAC7C,KAAK,GAAG,CAAC,CAAA;AACzD,IAAA,MAAMyF,YAAY,GAAG,IAAI,CAAC5C,SAAS,GAAGjX,QAAQ,IAAI,IAAI,CAACoU,KAAK,IAAIpU,QAAQ,CAAA;AAExE,IAAA,IAAI,CAACiX,SAAS,GAAG,IAAI,CAAC7C,KAAK,CAAA;AAC3B,IAAA,IAAIwF,WAAW,EAAE;AACf,MAAA,IAAI,CAAC/Z,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,CAAA;AAC1B,KAAA;;AAEA;AACA;AACA;AACA,IAAA,MAAMia,WAAW,GAAG,IAAI,CAAChD,cAAc,CAAA;AACvC,IAAA,IAAI,CAACvM,IAAI,GAAG,CAACuP,WAAW,IAAI,CAACD,YAAY,IAAI,IAAI,CAACzF,KAAK,IAAIpU,QAAQ,CAAA;;AAEnE;IACA,IAAI,CAACkX,QAAQ,GAAG,KAAK,CAAA;IAErB,IAAI6C,SAAS,GAAG,KAAK,CAAA;AACrB;IACA,IAAIL,OAAO,IAAII,WAAW,EAAE;AAC1B,MAAA,IAAI,CAACE,WAAW,CAACN,OAAO,CAAC,CAAA;;AAEzB;AACA,MAAA,IAAI,CAAC5tB,UAAU,GAAG,IAAIsI,MAAM,EAAE,CAAA;MAC9B2lB,SAAS,GAAG,IAAI,CAACE,IAAI,CAACH,WAAW,GAAGlP,EAAE,GAAGjgB,QAAQ,CAAC,CAAA;AAElD,MAAA,IAAI,CAACkV,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,CAAA;AACzB,KAAA;AACA;AACA;IACA,IAAI,CAAC0K,IAAI,GAAG,IAAI,CAACA,IAAI,IAAKwP,SAAS,IAAID,WAAY,CAAA;AACnD,IAAA,IAAID,YAAY,EAAE;AAChB,MAAA,IAAI,CAACha,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,CAAA;AAC7B,KAAA;AACA,IAAA,OAAO,IAAI,CAAA;AACb,GAAA;;AAEA;AACF;AACA;AACA;AACA;EACEwS,IAAIA,CAACA,IAAI,EAAE;IACT,IAAIA,IAAI,IAAI,IAAI,EAAE;MAChB,OAAO,IAAI,CAAC+B,KAAK,CAAA;AACnB,KAAA;AACA,IAAA,MAAMxJ,EAAE,GAAGyH,IAAI,GAAG,IAAI,CAAC+B,KAAK,CAAA;AAC5B,IAAA,IAAI,CAACjK,IAAI,CAACS,EAAE,CAAC,CAAA;AACb,IAAA,OAAO,IAAI,CAAA;AACb,GAAA;EAEA7K,QAAQA,CAACA,QAAQ,EAAE;AACjB;IACA,IAAI,OAAOA,QAAQ,KAAK,WAAW,EAAE,OAAO,IAAI,CAAC4W,SAAS,CAAA;IAC1D,IAAI,CAACA,SAAS,GAAG5W,QAAQ,CAAA;AACzB,IAAA,OAAO,IAAI,CAAA;AACb,GAAA;AAEA0V,EAAAA,UAAUA,GAAG;AACX,IAAA,MAAM1V,QAAQ,GAAG,IAAI,CAACA,QAAQ,EAAE,CAAA;AAChCA,IAAAA,QAAQ,IAAIA,QAAQ,CAAC0V,UAAU,CAAC,IAAI,CAAC,CAAA;AACrC,IAAA,OAAO,IAAI,CAAA;AACb,GAAA;;AAEA;EACAuE,WAAWA,CAACN,OAAO,EAAE;AACnB;AACA,IAAA,IAAI,CAACA,OAAO,IAAI,CAAC,IAAI,CAAC5C,cAAc,EAAE,OAAA;;AAEtC;AACA,IAAA,KAAK,IAAIpzB,CAAC,GAAG,CAAC,EAAEkhB,GAAG,GAAG,IAAI,CAACiS,MAAM,CAACjzB,MAAM,EAAEF,CAAC,GAAGkhB,GAAG,EAAE,EAAElhB,CAAC,EAAE;AACtD;AACA,MAAA,MAAM+V,OAAO,GAAG,IAAI,CAACod,MAAM,CAACnzB,CAAC,CAAC,CAAA;;AAE9B;MACA,MAAMw2B,OAAO,GAAG,IAAI,CAACpD,cAAc,IAAK,CAACrd,OAAO,CAACggB,WAAW,IAAIC,OAAQ,CAAA;AACxEA,MAAAA,OAAO,GAAG,CAACjgB,OAAO,CAACgd,QAAQ,CAAA;;AAE3B;MACA,IAAIyD,OAAO,IAAIR,OAAO,EAAE;AACtBjgB,QAAAA,OAAO,CAAC8f,WAAW,CAAChhB,IAAI,CAAC,IAAI,CAAC,CAAA;QAC9BkB,OAAO,CAACggB,WAAW,GAAG,IAAI,CAAA;AAC5B,OAAA;AACF,KAAA;AACF,GAAA;;AAEA;AACAU,EAAAA,gBAAgBA,CAACC,MAAM,EAAEC,OAAO,EAAE;AAChC,IAAA,IAAI,CAACtD,QAAQ,CAACqD,MAAM,CAAC,GAAG;AACtBC,MAAAA,OAAO,EAAEA,OAAO;MAChBC,MAAM,EAAE,IAAI,CAACzD,MAAM,CAAC,IAAI,CAACA,MAAM,CAACjzB,MAAM,GAAG,CAAC,CAAA;KAC3C,CAAA;;AAED;AACA;AACA;AACA;AACA;AACA;IACA,IAAI,IAAI,CAACkzB,cAAc,EAAE;AACvB,MAAA,MAAM/W,QAAQ,GAAG,IAAI,CAACA,QAAQ,EAAE,CAAA;AAChCA,MAAAA,QAAQ,IAAIA,QAAQ,CAACgV,IAAI,EAAE,CAAA;AAC7B,KAAA;AACF,GAAA;;AAEA;AACA;EACAkF,IAAIA,CAACM,YAAY,EAAE;AACjB;IACA,IAAIC,WAAW,GAAG,IAAI,CAAA;AACtB,IAAA,KAAK,IAAI92B,CAAC,GAAG,CAAC,EAAEkhB,GAAG,GAAG,IAAI,CAACiS,MAAM,CAACjzB,MAAM,EAAEF,CAAC,GAAGkhB,GAAG,EAAE,EAAElhB,CAAC,EAAE;AACtD;AACA,MAAA,MAAM+V,OAAO,GAAG,IAAI,CAACod,MAAM,CAACnzB,CAAC,CAAC,CAAA;;AAE9B;AACA;MACA,MAAMq2B,SAAS,GAAGtgB,OAAO,CAAC0Z,MAAM,CAAC5a,IAAI,CAAC,IAAI,EAAEgiB,YAAY,CAAC,CAAA;MACzD9gB,OAAO,CAACgd,QAAQ,GAAGhd,OAAO,CAACgd,QAAQ,IAAIsD,SAAS,KAAK,IAAI,CAAA;AACzDS,MAAAA,WAAW,GAAGA,WAAW,IAAI/gB,OAAO,CAACgd,QAAQ,CAAA;AAC/C,KAAA;;AAEA;AACA,IAAA,OAAO+D,WAAW,CAAA;AACpB,GAAA;;AAEA;AACAC,EAAAA,YAAYA,CAACL,MAAM,EAAEzP,MAAM,EAAE+P,KAAK,EAAE;AAClC,IAAA,IAAI,IAAI,CAAC3D,QAAQ,CAACqD,MAAM,CAAC,EAAE;AACzB;MACA,IAAI,CAAC,IAAI,CAACrD,QAAQ,CAACqD,MAAM,CAAC,CAACE,MAAM,CAACb,WAAW,EAAE;AAC7C,QAAA,MAAM7uB,KAAK,GAAG,IAAI,CAACisB,MAAM,CAAClqB,OAAO,CAAC,IAAI,CAACoqB,QAAQ,CAACqD,MAAM,CAAC,CAACE,MAAM,CAAC,CAAA;QAC/D,IAAI,CAACzD,MAAM,CAAC9G,MAAM,CAACnlB,KAAK,EAAE,CAAC,CAAC,CAAA;AAC5B,QAAA,OAAO,KAAK,CAAA;AACd,OAAA;;AAEA;AACA;MACA,IAAI,IAAI,CAACmsB,QAAQ,CAACqD,MAAM,CAAC,CAACE,MAAM,CAACd,QAAQ,EAAE;AACzC,QAAA,IAAI,CAACzC,QAAQ,CAACqD,MAAM,CAAC,CAACE,MAAM,CAACd,QAAQ,CAACjhB,IAAI,CAAC,IAAI,EAAEoS,MAAM,EAAE+P,KAAK,CAAC,CAAA;AAC/D;AACF,OAAC,MAAM;QACL,IAAI,CAAC3D,QAAQ,CAACqD,MAAM,CAAC,CAACC,OAAO,CAAC3S,EAAE,CAACiD,MAAM,CAAC,CAAA;AAC1C,OAAA;MAEA,IAAI,CAACoM,QAAQ,CAACqD,MAAM,CAAC,CAACE,MAAM,CAAC7D,QAAQ,GAAG,KAAK,CAAA;AAC7C,MAAA,MAAM1W,QAAQ,GAAG,IAAI,CAACA,QAAQ,EAAE,CAAA;AAChCA,MAAAA,QAAQ,IAAIA,QAAQ,CAACgV,IAAI,EAAE,CAAA;AAC3B,MAAA,OAAO,IAAI,CAAA;AACb,KAAA;AACA,IAAA,OAAO,KAAK,CAAA;AACd,GAAA;AACF,CAAA;AAEA6B,MAAM,CAAC3sB,EAAE,GAAG,CAAC,CAAA;AAEN,MAAM0wB,UAAU,CAAC;AACtBrwB,EAAAA,WAAWA,CAACwB,UAAU,GAAG,IAAIsI,MAAM,EAAE,EAAEnK,EAAE,GAAG,CAAC,CAAC,EAAEsgB,IAAI,GAAG,IAAI,EAAE;IAC3D,IAAI,CAACze,UAAU,GAAGA,UAAU,CAAA;IAC5B,IAAI,CAAC7B,EAAE,GAAGA,EAAE,CAAA;IACZ,IAAI,CAACsgB,IAAI,GAAGA,IAAI,CAAA;AAClB,GAAA;EAEA2N,wBAAwBA,GAAG,EAAC;AAC9B,CAAA;AAEAhuB,MAAM,CAAC,CAAC0sB,MAAM,EAAE+D,UAAU,CAAC,EAAE;EAC3BC,SAASA,CAACzH,MAAM,EAAE;AAChB,IAAA,OAAO,IAAIwH,UAAU,CACnBxH,MAAM,CAACrnB,UAAU,CAACkN,SAAS,CAAC,IAAI,CAAClN,UAAU,CAAC,EAC5CqnB,MAAM,CAAClpB,EACT,CAAC,CAAA;AACH,GAAA;AACF,CAAC,CAAC,CAAA;;AAEF;;AAEA,MAAM+O,SAAS,GAAGA,CAACyI,IAAI,EAAEC,IAAI,KAAKD,IAAI,CAACpK,UAAU,CAACqK,IAAI,CAAC,CAAA;AACvD,MAAMmZ,kBAAkB,GAAI1H,MAAM,IAAKA,MAAM,CAACrnB,UAAU,CAAA;AAExD,SAASgvB,eAAeA,GAAG;AACzB;AACA,EAAA,MAAMC,OAAO,GAAG,IAAI,CAACC,sBAAsB,CAACD,OAAO,CAAA;AACnD,EAAA,MAAME,YAAY,GAAGF,OAAO,CACzBx3B,GAAG,CAACs3B,kBAAkB,CAAC,CACvBvd,MAAM,CAACtE,SAAS,EAAE,IAAI5E,MAAM,EAAE,CAAC,CAAA;AAElC,EAAA,IAAI,CAACF,SAAS,CAAC+mB,YAAY,CAAC,CAAA;AAE5B,EAAA,IAAI,CAACD,sBAAsB,CAACzf,KAAK,EAAE,CAAA;EAEnC,IAAI,IAAI,CAACyf,sBAAsB,CAACp3B,MAAM,EAAE,KAAK,CAAC,EAAE;IAC9C,IAAI,CAAC8zB,QAAQ,GAAG,IAAI,CAAA;AACtB,GAAA;AACF,CAAA;AAEO,MAAMwD,WAAW,CAAC;AACvB5wB,EAAAA,WAAWA,GAAG;IACZ,IAAI,CAACywB,OAAO,GAAG,EAAE,CAAA;IACjB,IAAI,CAACI,GAAG,GAAG,EAAE,CAAA;AACf,GAAA;EAEAlwB,GAAGA,CAACkoB,MAAM,EAAE;IACV,IAAI,IAAI,CAAC4H,OAAO,CAACh1B,QAAQ,CAACotB,MAAM,CAAC,EAAE,OAAA;AACnC,IAAA,MAAMlpB,EAAE,GAAGkpB,MAAM,CAAClpB,EAAE,GAAG,CAAC,CAAA;AAExB,IAAA,IAAI,CAAC8wB,OAAO,CAACz3B,IAAI,CAAC6vB,MAAM,CAAC,CAAA;AACzB,IAAA,IAAI,CAACgI,GAAG,CAAC73B,IAAI,CAAC2G,EAAE,CAAC,CAAA;AAEjB,IAAA,OAAO,IAAI,CAAA;AACb,GAAA;EAEAmxB,WAAWA,CAACnxB,EAAE,EAAE;AACd,IAAA,MAAMoxB,SAAS,GAAG,IAAI,CAACF,GAAG,CAACxuB,OAAO,CAAC1C,EAAE,GAAG,CAAC,CAAC,IAAI,CAAC,CAAA;IAC/C,IAAI,CAACkxB,GAAG,CAACpL,MAAM,CAAC,CAAC,EAAEsL,SAAS,EAAE,CAAC,CAAC,CAAA;IAChC,IAAI,CAACN,OAAO,CACThL,MAAM,CAAC,CAAC,EAAEsL,SAAS,EAAE,IAAIV,UAAU,EAAE,CAAC,CACtCntB,OAAO,CAAEpJ,CAAC,IAAKA,CAAC,CAAC8zB,wBAAwB,EAAE,CAAC,CAAA;AAC/C,IAAA,OAAO,IAAI,CAAA;AACb,GAAA;AAEAoD,EAAAA,IAAIA,CAACrxB,EAAE,EAAEsxB,SAAS,EAAE;IAClB,MAAM3wB,KAAK,GAAG,IAAI,CAACuwB,GAAG,CAACxuB,OAAO,CAAC1C,EAAE,GAAG,CAAC,CAAC,CAAA;AACtC,IAAA,IAAI,CAACkxB,GAAG,CAACpL,MAAM,CAACnlB,KAAK,EAAE,CAAC,EAAEX,EAAE,GAAG,CAAC,CAAC,CAAA;IACjC,IAAI,CAAC8wB,OAAO,CAAChL,MAAM,CAACnlB,KAAK,EAAE,CAAC,EAAE2wB,SAAS,CAAC,CAAA;AACxC,IAAA,OAAO,IAAI,CAAA;AACb,GAAA;EAEAC,OAAOA,CAACvxB,EAAE,EAAE;AACV,IAAA,OAAO,IAAI,CAAC8wB,OAAO,CAAC,IAAI,CAACI,GAAG,CAACxuB,OAAO,CAAC1C,EAAE,GAAG,CAAC,CAAC,CAAC,CAAA;AAC/C,GAAA;AAEArG,EAAAA,MAAMA,GAAG;AACP,IAAA,OAAO,IAAI,CAACu3B,GAAG,CAACv3B,MAAM,CAAA;AACxB,GAAA;AAEA2X,EAAAA,KAAKA,GAAG;IACN,IAAIkgB,UAAU,GAAG,IAAI,CAAA;AACrB,IAAA,KAAK,IAAI/3B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACq3B,OAAO,CAACn3B,MAAM,EAAE,EAAEF,CAAC,EAAE;AAC5C,MAAA,MAAMyvB,MAAM,GAAG,IAAI,CAAC4H,OAAO,CAACr3B,CAAC,CAAC,CAAA;MAE9B,MAAMg4B,SAAS,GACbD,UAAU,IACVtI,MAAM,CAAC5I,IAAI,IACXkR,UAAU,CAAClR,IAAI;AACf;AACC,MAAA,CAAC4I,MAAM,CAACwD,SAAS,IAChB,CAACxD,MAAM,CAACwD,SAAS,CAAClC,UAAU,CAAC1uB,QAAQ,CAACotB,MAAM,CAAClpB,EAAE,CAAC,CAAC,KAClD,CAACwxB,UAAU,CAAC9E,SAAS,IACpB,CAAC8E,UAAU,CAAC9E,SAAS,CAAClC,UAAU,CAAC1uB,QAAQ,CAAC01B,UAAU,CAACxxB,EAAE,CAAC,CAAC,CAAA;AAE7D,MAAA,IAAIyxB,SAAS,EAAE;AACb;AACA,QAAA,IAAI,CAACxwB,MAAM,CAACioB,MAAM,CAAClpB,EAAE,CAAC,CAAA;AACtB,QAAA,MAAMsxB,SAAS,GAAGpI,MAAM,CAACyH,SAAS,CAACa,UAAU,CAAC,CAAA;QAC9C,IAAI,CAACH,IAAI,CAACG,UAAU,CAACxxB,EAAE,EAAEsxB,SAAS,CAAC,CAAA;AACnCE,QAAAA,UAAU,GAAGF,SAAS,CAAA;AACtB,QAAA,EAAE73B,CAAC,CAAA;AACL,OAAC,MAAM;AACL+3B,QAAAA,UAAU,GAAGtI,MAAM,CAAA;AACrB,OAAA;AACF,KAAA;AAEA,IAAA,OAAO,IAAI,CAAA;AACb,GAAA;EAEAjoB,MAAMA,CAACjB,EAAE,EAAE;IACT,MAAMW,KAAK,GAAG,IAAI,CAACuwB,GAAG,CAACxuB,OAAO,CAAC1C,EAAE,GAAG,CAAC,CAAC,CAAA;IACtC,IAAI,CAACkxB,GAAG,CAACpL,MAAM,CAACnlB,KAAK,EAAE,CAAC,CAAC,CAAA;IACzB,IAAI,CAACmwB,OAAO,CAAChL,MAAM,CAACnlB,KAAK,EAAE,CAAC,CAAC,CAAA;AAC7B,IAAA,OAAO,IAAI,CAAA;AACb,GAAA;AACF,CAAA;AAEApI,eAAe,CAAC;AACd4V,EAAAA,OAAO,EAAE;AACP2f,IAAAA,OAAOA,CAAC/X,QAAQ,EAAEE,KAAK,EAAEoV,IAAI,EAAE;MAC7B,MAAMjwB,CAAC,GAAGuxB,MAAM,CAACe,QAAQ,CAAC3X,QAAQ,EAAEE,KAAK,EAAEoV,IAAI,CAAC,CAAA;AAChD,MAAA,MAAMvV,QAAQ,GAAG,IAAI,CAACA,QAAQ,EAAE,CAAA;AAChC,MAAA,OAAO,IAAI6W,MAAM,CAACvxB,CAAC,CAAC2a,QAAQ,CAAC,CAC1BgY,IAAI,CAAC3yB,CAAC,CAAC,CACPN,OAAO,CAAC,IAAI,CAAC,CACbgb,QAAQ,CAACA,QAAQ,CAACgV,IAAI,EAAE,CAAC,CACzBM,QAAQ,CAAChwB,CAAC,CAAC6a,KAAK,EAAE7a,CAAC,CAACiwB,IAAI,CAAC,CAAA;KAC7B;AAEDpV,IAAAA,KAAKA,CAACyb,EAAE,EAAErG,IAAI,EAAE;MACd,OAAO,IAAI,CAACyC,OAAO,CAAC,CAAC,EAAE4D,EAAE,EAAErG,IAAI,CAAC,CAAA;KACjC;AAED;AACA;AACA;AACA;IACAsG,4BAA4BA,CAACC,aAAa,EAAE;MAC1C,IAAI,CAACb,sBAAsB,CAACI,WAAW,CAACS,aAAa,CAAC5xB,EAAE,CAAC,CAAA;KAC1D;IAED6xB,iBAAiBA,CAACriB,OAAO,EAAE;MACzB,OACE,IAAI,CAACuhB,sBAAsB,CAACD,OAAAA;AAC1B;AACA;AACA;OACCj3B,MAAM,CAAEqvB,MAAM,IAAKA,MAAM,CAAClpB,EAAE,IAAIwP,OAAO,CAACxP,EAAE,CAAC,CAC3C1G,GAAG,CAACs3B,kBAAkB,CAAC,CACvBvd,MAAM,CAACtE,SAAS,EAAE,IAAI5E,MAAM,EAAE,CAAC,CAAA;KAErC;IAED2nB,UAAUA,CAAC5I,MAAM,EAAE;AACjB,MAAA,IAAI,CAAC6H,sBAAsB,CAAC/vB,GAAG,CAACkoB,MAAM,CAAC,CAAA;;AAEvC;AACA;AACA;AACA3B,MAAAA,QAAQ,CAACkB,eAAe,CAAC,IAAI,CAACgF,QAAQ,CAAC,CAAA;AACvC,MAAA,IAAI,CAACA,QAAQ,GAAGlG,QAAQ,CAACe,SAAS,CAACuI,eAAe,CAACpc,IAAI,CAAC,IAAI,CAAC,CAAC,CAAA;KAC/D;AAED4Z,IAAAA,cAAcA,GAAG;AACf,MAAA,IAAI,IAAI,CAACZ,QAAQ,IAAI,IAAI,EAAE;AACzB,QAAA,IAAI,CAACsD,sBAAsB,GAAG,IAAIE,WAAW,EAAE,CAACjwB,GAAG,CACjD,IAAI0vB,UAAU,CAAC,IAAIvmB,MAAM,CAAC,IAAI,CAAC,CACjC,CAAC,CAAA;AACH,OAAA;AACF,KAAA;AACF,GAAA;AACF,CAAC,CAAC,CAAA;;AAEF;AACA,MAAM4nB,UAAU,GAAGA,CAAC/tB,CAAC,EAAEwB,CAAC,KAAKxB,CAAC,CAACnK,MAAM,CAAE6B,CAAC,IAAK,CAAC8J,CAAC,CAAC1J,QAAQ,CAACJ,CAAC,CAAC,CAAC,CAAA;AAE5DuE,MAAM,CAAC0sB,MAAM,EAAE;AACbpsB,EAAAA,IAAIA,CAACyD,CAAC,EAAEC,CAAC,EAAE;IACT,OAAO,IAAI,CAAC+tB,SAAS,CAAC,MAAM,EAAEhuB,CAAC,EAAEC,CAAC,CAAC,CAAA;GACpC;AAED;AACAjB,EAAAA,GAAGA,CAAC3I,CAAC,EAAE4J,CAAC,EAAE;IACR,OAAO,IAAI,CAAC+tB,SAAS,CAAC,KAAK,EAAE33B,CAAC,EAAE4J,CAAC,CAAC,CAAA;GACnC;AAED+tB,EAAAA,SAASA,CAACvc,IAAI,EAAEwc,WAAW,EAAE/uB,GAAG,EAAE;AAChC,IAAA,IAAI,OAAO+uB,WAAW,KAAK,QAAQ,EAAE;AACnC,MAAA,OAAO,IAAI,CAACD,SAAS,CAACvc,IAAI,EAAE;AAAE,QAAA,CAACwc,WAAW,GAAG/uB,GAAAA;AAAI,OAAC,CAAC,CAAA;AACrD,KAAA;IAEA,IAAIqQ,KAAK,GAAG0e,WAAW,CAAA;IACvB,IAAI,IAAI,CAACzB,YAAY,CAAC/a,IAAI,EAAElC,KAAK,CAAC,EAAE,OAAO,IAAI,CAAA;AAE/C,IAAA,IAAI6c,OAAO,GAAG,IAAIrL,SAAS,CAAC,IAAI,CAACC,QAAQ,CAAC,CAACvH,EAAE,CAAClK,KAAK,CAAC,CAAA;AACpD,IAAA,IAAI9W,IAAI,GAAG3D,MAAM,CAAC2D,IAAI,CAAC8W,KAAK,CAAC,CAAA;IAE7B,IAAI,CAAC6a,KAAK,CACR,YAAY;AACVgC,MAAAA,OAAO,GAAGA,OAAO,CAAChT,IAAI,CAAC,IAAI,CAACtiB,OAAO,EAAE,CAAC2a,IAAI,CAAC,CAAChZ,IAAI,CAAC,CAAC,CAAA;KACnD,EACD,UAAUmjB,GAAG,EAAE;AACb,MAAA,IAAI,CAAC9kB,OAAO,EAAE,CAAC2a,IAAI,CAAC,CAAC2a,OAAO,CAAC9K,EAAE,CAAC1F,GAAG,CAAC,CAACpjB,OAAO,EAAE,CAAC,CAAA;AAC/C,MAAA,OAAO4zB,OAAO,CAAC9P,IAAI,EAAE,CAAA;KACtB,EACD,UAAU4R,UAAU,EAAE;AACpB;AACA,MAAA,MAAMC,OAAO,GAAGr5B,MAAM,CAAC2D,IAAI,CAACy1B,UAAU,CAAC,CAAA;AACvC,MAAA,MAAME,WAAW,GAAGL,UAAU,CAACI,OAAO,EAAE11B,IAAI,CAAC,CAAA;;AAE7C;MACA,IAAI21B,WAAW,CAACz4B,MAAM,EAAE;AACtB;AACA,QAAA,MAAM04B,cAAc,GAAG,IAAI,CAACv3B,OAAO,EAAE,CAAC2a,IAAI,CAAC,CAAC2c,WAAW,CAAC,CAAA;;AAExD;AACA,QAAA,MAAME,YAAY,GAAG,IAAIxN,SAAS,CAACsL,OAAO,CAAChT,IAAI,EAAE,CAAC,CAAC5gB,OAAO,EAAE,CAAA;;AAE5D;AACA1D,QAAAA,MAAM,CAACE,MAAM,CAACs5B,YAAY,EAAED,cAAc,CAAC,CAAA;AAC3CjC,QAAAA,OAAO,CAAChT,IAAI,CAACkV,YAAY,CAAC,CAAA;AAC5B,OAAA;;AAEA;AACA,MAAA,MAAMC,UAAU,GAAG,IAAIzN,SAAS,CAACsL,OAAO,CAAC3S,EAAE,EAAE,CAAC,CAACjhB,OAAO,EAAE,CAAA;;AAExD;AACA1D,MAAAA,MAAM,CAACE,MAAM,CAACu5B,UAAU,EAAEL,UAAU,CAAC,CAAA;;AAErC;AACA9B,MAAAA,OAAO,CAAC3S,EAAE,CAAC8U,UAAU,CAAC,CAAA;;AAEtB;AACA91B,MAAAA,IAAI,GAAG01B,OAAO,CAAA;AACd5e,MAAAA,KAAK,GAAG2e,UAAU,CAAA;AACpB,KACF,CAAC,CAAA;AAED,IAAA,IAAI,CAAChC,gBAAgB,CAACza,IAAI,EAAE2a,OAAO,CAAC,CAAA;AACpC,IAAA,OAAO,IAAI,CAAA;GACZ;AAED9d,EAAAA,IAAIA,CAACC,KAAK,EAAEjI,KAAK,EAAE;AACjB,IAAA,IAAI,IAAI,CAACkmB,YAAY,CAAC,MAAM,EAAEje,KAAK,EAAEjI,KAAK,CAAC,EAAE,OAAO,IAAI,CAAA;AAExD,IAAA,IAAI8lB,OAAO,GAAG,IAAIrL,SAAS,CAAC,IAAI,CAACC,QAAQ,CAAC,CAACvH,EAAE,CAAC,IAAIjH,SAAS,CAACjE,KAAK,CAAC,CAAC,CAAA;IAEnE,IAAI,CAAC6b,KAAK,CACR,YAAY;AACVgC,MAAAA,OAAO,GAAGA,OAAO,CAAChT,IAAI,CAAC,IAAI,CAACtiB,OAAO,EAAE,CAACwX,IAAI,EAAE,CAAC,CAAA;KAC9C,EACD,UAAUsN,GAAG,EAAE;AACb,MAAA,IAAI,CAAC9kB,OAAO,EAAE,CAACwX,IAAI,CAAC8d,OAAO,CAAC9K,EAAE,CAAC1F,GAAG,CAAC,EAAEtV,KAAK,CAAC,CAAA;AAC3C,MAAA,OAAO8lB,OAAO,CAAC9P,IAAI,EAAE,CAAA;AACvB,KAAC,EACD,UAAUkS,QAAQ,EAAEC,QAAQ,EAAE;AAC5BnoB,MAAAA,KAAK,GAAGmoB,QAAQ,CAAA;AAChBrC,MAAAA,OAAO,CAAC3S,EAAE,CAAC+U,QAAQ,CAAC,CAAA;AACtB,KACF,CAAC,CAAA;AAED,IAAA,IAAI,CAACtC,gBAAgB,CAAC,MAAM,EAAEE,OAAO,CAAC,CAAA;AACtC,IAAA,OAAO,IAAI,CAAA;GACZ;AAED;AACF;AACA;;AAEE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEAnmB,EAAAA,SAASA,CAACpI,UAAU,EAAEyK,QAAQ,EAAEomB,MAAM,EAAE;AACtC;AACApmB,IAAAA,QAAQ,GAAGzK,UAAU,CAACyK,QAAQ,IAAIA,QAAQ,CAAA;AAC1C,IAAA,IACE,IAAI,CAACugB,cAAc,IACnB,CAACvgB,QAAQ,IACT,IAAI,CAACkkB,YAAY,CAAC,WAAW,EAAE3uB,UAAU,CAAC,EAC1C;AACA,MAAA,OAAO,IAAI,CAAA;AACb,KAAA;;AAEA;AACA,IAAA,MAAM8wB,QAAQ,GAAGxoB,MAAM,CAACC,YAAY,CAACvI,UAAU,CAAC,CAAA;AAChD6wB,IAAAA,MAAM,GACJ7wB,UAAU,CAAC6wB,MAAM,IAAI,IAAI,GACrB7wB,UAAU,CAAC6wB,MAAM,GACjBA,MAAM,IAAI,IAAI,GACZA,MAAM,GACN,CAACC,QAAQ,CAAA;;AAEjB;AACA,IAAA,MAAMvC,OAAO,GAAG,IAAIrL,SAAS,CAAC,IAAI,CAACC,QAAQ,CAAC,CAACvP,IAAI,CAC/Cid,MAAM,GAAG9M,YAAY,GAAGzb,MAC1B,CAAC,CAAA;AAED,IAAA,IAAI9O,MAAM,CAAA;AACV,IAAA,IAAIP,OAAO,CAAA;AACX,IAAA,IAAI0U,OAAO,CAAA;AACX,IAAA,IAAIojB,YAAY,CAAA;AAChB,IAAA,IAAIC,cAAc,CAAA;IAElB,SAASC,KAAKA,GAAG;AACf;AACAh4B,MAAAA,OAAO,GAAGA,OAAO,IAAI,IAAI,CAACA,OAAO,EAAE,CAAA;MACnCO,MAAM,GAAGA,MAAM,IAAIF,SAAS,CAAC0G,UAAU,EAAE/G,OAAO,CAAC,CAAA;MAEjD+3B,cAAc,GAAG,IAAI1oB,MAAM,CAACmC,QAAQ,GAAGymB,SAAS,GAAGj4B,OAAO,CAAC,CAAA;;AAE3D;AACAA,MAAAA,OAAO,CAACg3B,UAAU,CAAC,IAAI,CAAC,CAAA;;AAExB;MACA,IAAI,CAACxlB,QAAQ,EAAE;AACbxR,QAAAA,OAAO,CAAC62B,4BAA4B,CAAC,IAAI,CAAC,CAAA;AAC5C,OAAA;AACF,KAAA;IAEA,SAAS3J,GAAGA,CAACpI,GAAG,EAAE;AAChB;AACA;AACA,MAAA,IAAI,CAACtT,QAAQ,EAAE,IAAI,CAAC0hB,cAAc,EAAE,CAAA;MAEpC,MAAM;QAAEtyB,CAAC;AAAEC,QAAAA,CAAAA;AAAE,OAAC,GAAG,IAAIkO,KAAK,CAACxO,MAAM,CAAC,CAAC4O,SAAS,CAC1CnP,OAAO,CAAC+2B,iBAAiB,CAAC,IAAI,CAChC,CAAC,CAAA;AAED,MAAA,IAAInR,MAAM,GAAG,IAAIvW,MAAM,CAAC;AAAE,QAAA,GAAGtI,UAAU;AAAExG,QAAAA,MAAM,EAAE,CAACK,CAAC,EAAEC,CAAC,CAAA;AAAE,OAAC,CAAC,CAAA;MAC1D,IAAIstB,KAAK,GAAG,IAAI,CAAC4D,cAAc,IAAIrd,OAAO,GAAGA,OAAO,GAAGqjB,cAAc,CAAA;AAErE,MAAA,IAAIH,MAAM,EAAE;QACVhS,MAAM,GAAGA,MAAM,CAACrT,SAAS,CAAC3R,CAAC,EAAEC,CAAC,CAAC,CAAA;QAC/BstB,KAAK,GAAGA,KAAK,CAAC5b,SAAS,CAAC3R,CAAC,EAAEC,CAAC,CAAC,CAAA;;AAE7B;AACA,QAAA,MAAMq3B,OAAO,GAAGtS,MAAM,CAAChV,MAAM,CAAA;AAC7B,QAAA,MAAMunB,QAAQ,GAAGhK,KAAK,CAACvd,MAAM,CAAA;;AAE7B;AACA,QAAA,MAAMwnB,aAAa,GAAG,CAACF,OAAO,GAAG,GAAG,EAAEA,OAAO,EAAEA,OAAO,GAAG,GAAG,CAAC,CAAA;AAC7D,QAAA,MAAMG,SAAS,GAAGD,aAAa,CAAC55B,GAAG,CAAE0K,CAAC,IAAKhK,IAAI,CAAC2Q,GAAG,CAAC3G,CAAC,GAAGivB,QAAQ,CAAC,CAAC,CAAA;QAClE,MAAMG,QAAQ,GAAGp5B,IAAI,CAACkL,GAAG,CAAC,GAAGiuB,SAAS,CAAC,CAAA;AACvC,QAAA,MAAMxyB,KAAK,GAAGwyB,SAAS,CAACzwB,OAAO,CAAC0wB,QAAQ,CAAC,CAAA;AACzC1S,QAAAA,MAAM,CAAChV,MAAM,GAAGwnB,aAAa,CAACvyB,KAAK,CAAC,CAAA;AACtC,OAAA;AAEA,MAAA,IAAI2L,QAAQ,EAAE;AACZ;AACA;QACA,IAAI,CAACqmB,QAAQ,EAAE;AACbjS,UAAAA,MAAM,CAAChV,MAAM,GAAG7J,UAAU,CAAC6J,MAAM,IAAI,CAAC,CAAA;AACxC,SAAA;AACA,QAAA,IAAI,IAAI,CAACmhB,cAAc,IAAI+F,YAAY,EAAE;UACvC3J,KAAK,CAACvd,MAAM,GAAGknB,YAAY,CAAA;AAC7B,SAAA;AACF,OAAA;AAEAxC,MAAAA,OAAO,CAAChT,IAAI,CAAC6L,KAAK,CAAC,CAAA;AACnBmH,MAAAA,OAAO,CAAC3S,EAAE,CAACiD,MAAM,CAAC,CAAA;AAElB,MAAA,MAAM2S,gBAAgB,GAAGjD,OAAO,CAAC9K,EAAE,CAAC1F,GAAG,CAAC,CAAA;MACxCgT,YAAY,GAAGS,gBAAgB,CAAC3nB,MAAM,CAAA;AACtC8D,MAAAA,OAAO,GAAG,IAAIrF,MAAM,CAACkpB,gBAAgB,CAAC,CAAA;AAEtC,MAAA,IAAI,CAACxF,YAAY,CAACre,OAAO,CAAC,CAAA;AAC1B1U,MAAAA,OAAO,CAACg3B,UAAU,CAAC,IAAI,CAAC,CAAA;AACxB,MAAA,OAAO1B,OAAO,CAAC9P,IAAI,EAAE,CAAA;AACvB,KAAA;IAEA,SAASiP,QAAQA,CAAC+D,aAAa,EAAE;AAC/B;MACA,IACE,CAACA,aAAa,CAACj4B,MAAM,IAAI,QAAQ,EAAE8J,QAAQ,EAAE,KAC7C,CAACtD,UAAU,CAACxG,MAAM,IAAI,QAAQ,EAAE8J,QAAQ,EAAE,EAC1C;AACA9J,QAAAA,MAAM,GAAGF,SAAS,CAACm4B,aAAa,EAAEx4B,OAAO,CAAC,CAAA;AAC5C,OAAA;;AAEA;AACA+G,MAAAA,UAAU,GAAG;AAAE,QAAA,GAAGyxB,aAAa;AAAEj4B,QAAAA,MAAAA;OAAQ,CAAA;AAC3C,KAAA;IAEA,IAAI,CAAC+yB,KAAK,CAAC0E,KAAK,EAAE9K,GAAG,EAAEuH,QAAQ,EAAE,IAAI,CAAC,CAAA;IACtC,IAAI,CAAC1C,cAAc,IAAI,IAAI,CAACqD,gBAAgB,CAAC,WAAW,EAAEE,OAAO,CAAC,CAAA;AAClE,IAAA,OAAO,IAAI,CAAA;GACZ;AAED;EACA10B,CAACA,CAACA,CAAC,EAAE;AACH,IAAA,OAAO,IAAI,CAAC63B,YAAY,CAAC,GAAG,EAAE73B,CAAC,CAAC,CAAA;GACjC;AAED;EACAC,CAACA,CAACA,CAAC,EAAE;AACH,IAAA,OAAO,IAAI,CAAC43B,YAAY,CAAC,GAAG,EAAE53B,CAAC,CAAC,CAAA;GACjC;EAED63B,EAAEA,CAAC93B,CAAC,EAAE;AACJ,IAAA,OAAO,IAAI,CAAC63B,YAAY,CAAC,IAAI,EAAE73B,CAAC,CAAC,CAAA;GAClC;EAED+3B,EAAEA,CAAC93B,CAAC,EAAE;AACJ,IAAA,OAAO,IAAI,CAAC43B,YAAY,CAAC,IAAI,EAAE53B,CAAC,CAAC,CAAA;GAClC;AAEDsR,EAAAA,EAAEA,CAACvR,CAAC,GAAG,CAAC,EAAE;AACR,IAAA,OAAO,IAAI,CAACg4B,iBAAiB,CAAC,GAAG,EAAEh4B,CAAC,CAAC,CAAA;GACtC;AAEDwR,EAAAA,EAAEA,CAACvR,CAAC,GAAG,CAAC,EAAE;AACR,IAAA,OAAO,IAAI,CAAC+3B,iBAAiB,CAAC,GAAG,EAAE/3B,CAAC,CAAC,CAAA;GACtC;AAEDuf,EAAAA,KAAKA,CAACxf,CAAC,EAAEC,CAAC,EAAE;IACV,OAAO,IAAI,CAACsR,EAAE,CAACvR,CAAC,CAAC,CAACwR,EAAE,CAACvR,CAAC,CAAC,CAAA;GACxB;AAED+3B,EAAAA,iBAAiBA,CAACvD,MAAM,EAAE1S,EAAE,EAAE;AAC5BA,IAAAA,EAAE,GAAG,IAAIjH,SAAS,CAACiH,EAAE,CAAC,CAAA;;AAEtB;IACA,IAAI,IAAI,CAAC+S,YAAY,CAACL,MAAM,EAAE1S,EAAE,CAAC,EAAE,OAAO,IAAI,CAAA;;AAE9C;AACA,IAAA,MAAM2S,OAAO,GAAG,IAAIrL,SAAS,CAAC,IAAI,CAACC,QAAQ,CAAC,CAACvH,EAAE,CAACA,EAAE,CAAC,CAAA;IACnD,IAAIL,IAAI,GAAG,IAAI,CAAA;IACf,IAAI,CAACgR,KAAK,CACR,YAAY;MACVhR,IAAI,GAAG,IAAI,CAACtiB,OAAO,EAAE,CAACq1B,MAAM,CAAC,EAAE,CAAA;AAC/BC,MAAAA,OAAO,CAAChT,IAAI,CAACA,IAAI,CAAC,CAAA;AAClBgT,MAAAA,OAAO,CAAC3S,EAAE,CAACL,IAAI,GAAGK,EAAE,CAAC,CAAA;KACtB,EACD,UAAUmC,GAAG,EAAE;AACb,MAAA,IAAI,CAAC9kB,OAAO,EAAE,CAACq1B,MAAM,CAAC,CAACC,OAAO,CAAC9K,EAAE,CAAC1F,GAAG,CAAC,CAAC,CAAA;AACvC,MAAA,OAAOwQ,OAAO,CAAC9P,IAAI,EAAE,CAAA;KACtB,EACD,UAAUqT,KAAK,EAAE;MACfvD,OAAO,CAAC3S,EAAE,CAACL,IAAI,GAAG,IAAI5G,SAAS,CAACmd,KAAK,CAAC,CAAC,CAAA;AACzC,KACF,CAAC,CAAA;;AAED;AACA,IAAA,IAAI,CAACzD,gBAAgB,CAACC,MAAM,EAAEC,OAAO,CAAC,CAAA;AACtC,IAAA,OAAO,IAAI,CAAA;GACZ;AAEDwD,EAAAA,YAAYA,CAACzD,MAAM,EAAE1S,EAAE,EAAE;AACvB;IACA,IAAI,IAAI,CAAC+S,YAAY,CAACL,MAAM,EAAE1S,EAAE,CAAC,EAAE,OAAO,IAAI,CAAA;;AAE9C;AACA,IAAA,MAAM2S,OAAO,GAAG,IAAIrL,SAAS,CAAC,IAAI,CAACC,QAAQ,CAAC,CAACvH,EAAE,CAACA,EAAE,CAAC,CAAA;IACnD,IAAI,CAAC2Q,KAAK,CACR,YAAY;AACVgC,MAAAA,OAAO,CAAChT,IAAI,CAAC,IAAI,CAACtiB,OAAO,EAAE,CAACq1B,MAAM,CAAC,EAAE,CAAC,CAAA;KACvC,EACD,UAAUvQ,GAAG,EAAE;AACb,MAAA,IAAI,CAAC9kB,OAAO,EAAE,CAACq1B,MAAM,CAAC,CAACC,OAAO,CAAC9K,EAAE,CAAC1F,GAAG,CAAC,CAAC,CAAA;AACvC,MAAA,OAAOwQ,OAAO,CAAC9P,IAAI,EAAE,CAAA;AACvB,KACF,CAAC,CAAA;;AAED;AACA,IAAA,IAAI,CAAC4P,gBAAgB,CAACC,MAAM,EAAEC,OAAO,CAAC,CAAA;AACtC,IAAA,OAAO,IAAI,CAAA;GACZ;AAEDmD,EAAAA,YAAYA,CAACpD,MAAM,EAAExZ,KAAK,EAAE;IAC1B,OAAO,IAAI,CAACid,YAAY,CAACzD,MAAM,EAAE,IAAI3Z,SAAS,CAACG,KAAK,CAAC,CAAC,CAAA;GACvD;AAED;EACA9J,EAAEA,CAACnR,CAAC,EAAE;AACJ,IAAA,OAAO,IAAI,CAAC63B,YAAY,CAAC,IAAI,EAAE73B,CAAC,CAAC,CAAA;GAClC;AAED;EACAoR,EAAEA,CAACnR,CAAC,EAAE;AACJ,IAAA,OAAO,IAAI,CAAC43B,YAAY,CAAC,IAAI,EAAE53B,CAAC,CAAC,CAAA;GAClC;AAED;AACAwf,EAAAA,IAAIA,CAACzf,CAAC,EAAEC,CAAC,EAAE;IACT,OAAO,IAAI,CAACD,CAAC,CAACA,CAAC,CAAC,CAACC,CAAC,CAACA,CAAC,CAAC,CAAA;GACtB;AAEDk4B,EAAAA,KAAKA,CAACn4B,CAAC,EAAEC,CAAC,EAAE;IACV,OAAO,IAAI,CAAC63B,EAAE,CAAC93B,CAAC,CAAC,CAAC+3B,EAAE,CAAC93B,CAAC,CAAC,CAAA;GACxB;AAED;AACAqf,EAAAA,MAAMA,CAACtf,CAAC,EAAEC,CAAC,EAAE;IACX,OAAO,IAAI,CAACkR,EAAE,CAACnR,CAAC,CAAC,CAACoR,EAAE,CAACnR,CAAC,CAAC,CAAA;GACxB;AAED;AACAwU,EAAAA,IAAIA,CAACpV,KAAK,EAAEC,MAAM,EAAE;AAClB;AACA,IAAA,IAAIC,GAAG,CAAA;AAEP,IAAA,IAAI,CAACF,KAAK,IAAI,CAACC,MAAM,EAAE;AACrBC,MAAAA,GAAG,GAAG,IAAI,CAAC6gB,QAAQ,CAAC5gB,IAAI,EAAE,CAAA;AAC5B,KAAA;IAEA,IAAI,CAACH,KAAK,EAAE;MACVA,KAAK,GAAIE,GAAG,CAACF,KAAK,GAAGE,GAAG,CAACD,MAAM,GAAIA,MAAM,CAAA;AAC3C,KAAA;IAEA,IAAI,CAACA,MAAM,EAAE;MACXA,MAAM,GAAIC,GAAG,CAACD,MAAM,GAAGC,GAAG,CAACF,KAAK,GAAIA,KAAK,CAAA;AAC3C,KAAA;IAEA,OAAO,IAAI,CAACA,KAAK,CAACA,KAAK,CAAC,CAACC,MAAM,CAACA,MAAM,CAAC,CAAA;GACxC;AAED;EACAD,KAAKA,CAACA,KAAK,EAAE;AACX,IAAA,OAAO,IAAI,CAACw4B,YAAY,CAAC,OAAO,EAAEx4B,KAAK,CAAC,CAAA;GACzC;AAED;EACAC,MAAMA,CAACA,MAAM,EAAE;AACb,IAAA,OAAO,IAAI,CAACu4B,YAAY,CAAC,QAAQ,EAAEv4B,MAAM,CAAC,CAAA;GAC3C;AAED;EACAmkB,IAAIA,CAACnb,CAAC,EAAEwB,CAAC,EAAE1C,CAAC,EAAE/I,CAAC,EAAE;AACf;AACA,IAAA,IAAIqJ,SAAS,CAACzJ,MAAM,KAAK,CAAC,EAAE;AAC1B,MAAA,OAAO,IAAI,CAACwlB,IAAI,CAAC,CAACnb,CAAC,EAAEwB,CAAC,EAAE1C,CAAC,EAAE/I,CAAC,CAAC,CAAC,CAAA;AAChC,KAAA;IAEA,IAAI,IAAI,CAACy2B,YAAY,CAAC,MAAM,EAAExsB,CAAC,CAAC,EAAE,OAAO,IAAI,CAAA;IAE7C,MAAMosB,OAAO,GAAG,IAAIrL,SAAS,CAAC,IAAI,CAACC,QAAQ,CAAC,CACzCvP,IAAI,CAAC,IAAI,CAACqG,QAAQ,CAACmD,UAAU,CAAC,CAC9BxB,EAAE,CAACzZ,CAAC,CAAC,CAAA;IAER,IAAI,CAACoqB,KAAK,CACR,YAAY;MACVgC,OAAO,CAAChT,IAAI,CAAC,IAAI,CAACtB,QAAQ,CAACviB,KAAK,EAAE,CAAC,CAAA;KACpC,EACD,UAAUqmB,GAAG,EAAE;MACb,IAAI,CAAC9D,QAAQ,CAACqD,IAAI,CAACiR,OAAO,CAAC9K,EAAE,CAAC1F,GAAG,CAAC,CAAC,CAAA;AACnC,MAAA,OAAOwQ,OAAO,CAAC9P,IAAI,EAAE,CAAA;AACvB,KACF,CAAC,CAAA;AAED,IAAA,IAAI,CAAC4P,gBAAgB,CAAC,MAAM,EAAEE,OAAO,CAAC,CAAA;AACtC,IAAA,OAAO,IAAI,CAAA;GACZ;AAED;EACAvY,OAAOA,CAAClB,KAAK,EAAE;AACb,IAAA,OAAO,IAAI,CAAC4c,YAAY,CAAC,SAAS,EAAE5c,KAAK,CAAC,CAAA;GAC3C;AAED;EACAtE,OAAOA,CAAC3W,CAAC,EAAEC,CAAC,EAAEZ,KAAK,EAAEC,MAAM,EAAE;AAC3B,IAAA,OAAO,IAAI,CAAC44B,YAAY,CAAC,SAAS,EAAE,IAAIhjB,GAAG,CAAClV,CAAC,EAAEC,CAAC,EAAEZ,KAAK,EAAEC,MAAM,CAAC,CAAC,CAAA;GAClE;EAED6iB,MAAMA,CAACziB,CAAC,EAAE;AACR,IAAA,IAAI,OAAOA,CAAC,KAAK,QAAQ,EAAE;MACzB,OAAO,IAAI,CAACyiB,MAAM,CAAC;AACjBxH,QAAAA,MAAM,EAAEjT,SAAS,CAAC,CAAC,CAAC;AACpBoD,QAAAA,KAAK,EAAEpD,SAAS,CAAC,CAAC,CAAC;QACnBgT,OAAO,EAAEhT,SAAS,CAAC,CAAC,CAAA;AACtB,OAAC,CAAC,CAAA;AACJ,KAAA;AAEA,IAAA,IAAIhI,CAAC,CAACgb,OAAO,IAAI,IAAI,EAAE,IAAI,CAAC7V,IAAI,CAAC,cAAc,EAAEnF,CAAC,CAACgb,OAAO,CAAC,CAAA;AAC3D,IAAA,IAAIhb,CAAC,CAACoL,KAAK,IAAI,IAAI,EAAE,IAAI,CAACjG,IAAI,CAAC,YAAY,EAAEnF,CAAC,CAACoL,KAAK,CAAC,CAAA;AACrD,IAAA,IAAIpL,CAAC,CAACib,MAAM,IAAI,IAAI,EAAE,IAAI,CAAC9V,IAAI,CAAC,QAAQ,EAAEnF,CAAC,CAACib,MAAM,CAAC,CAAA;AAEnD,IAAA,OAAO,IAAI,CAAA;AACb,GAAA;AACF,CAAC,CAAC,CAAA;AAEFpW,MAAM,CAAC0sB,MAAM,EAAE;EAAEpgB,EAAE;EAAEE,EAAE;EAAE2Q,IAAI;AAAEK,EAAAA,EAAAA;AAAG,CAAC,CAAC,CAAA;AACpCje,QAAQ,CAACmtB,MAAM,EAAE,QAAQ,CAAC;;AChjCX,MAAMmH,GAAG,SAASlX,SAAS,CAAC;AACzCvc,EAAAA,WAAWA,CAAC3D,IAAI,EAAE6W,KAAK,GAAG7W,IAAI,EAAE;IAC9B,KAAK,CAACoC,SAAS,CAAC,KAAK,EAAEpC,IAAI,CAAC,EAAE6W,KAAK,CAAC,CAAA;IACpC,IAAI,CAACyB,SAAS,EAAE,CAAA;AAClB,GAAA;;AAEA;AACAiG,EAAAA,IAAIA,GAAG;AACL,IAAA,IAAI,CAAC,IAAI,CAACrL,MAAM,EAAE,EAAE,OAAO,IAAI,CAAC3R,IAAI,EAAE,CAACgd,IAAI,EAAE,CAAA;IAE7C,OAAO/b,KAAK,CAAC,IAAI,CAACxC,IAAI,CAAC8B,aAAa,CAAC,MAAM,CAAC,CAAC,IAAI,IAAI,CAAC6Z,GAAG,CAAC,IAAI0E,IAAI,EAAE,CAAC,CAAA;AACvE,GAAA;AAEAnN,EAAAA,MAAMA,GAAG;AACP,IAAA,OACE,CAAC,IAAI,CAAClT,IAAI,CAAC2T,UAAU,IACpB,EAAE,IAAI,CAAC3T,IAAI,CAAC2T,UAAU,YAAYlT,OAAO,CAACC,MAAM,CAAC8a,UAAU,CAAC,IAC3D,IAAI,CAACxb,IAAI,CAAC2T,UAAU,CAACnU,QAAQ,KAAK,oBAAqB,CAAA;AAE7D,GAAA;;AAEA;AACA8Y,EAAAA,SAASA,GAAG;AACV,IAAA,IAAI,CAAC,IAAI,CAACpF,MAAM,EAAE,EAAE,OAAO,IAAI,CAAC3R,IAAI,EAAE,CAAC+W,SAAS,EAAE,CAAA;IAClD,OAAO,IAAI,CAACzU,IAAI,CAAC;AAAEtD,MAAAA,KAAK,EAAEF,GAAG;AAAEg3B,MAAAA,OAAO,EAAE,KAAA;KAAO,CAAC,CAACxzB,IAAI,CACnD,aAAa,EACbrD,KAAK,EACLD,KACF,CAAC,CAAA;AACH,GAAA;AAEAgb,EAAAA,eAAeA,GAAG;IAChB,OAAO,IAAI,CAAC1X,IAAI,CAAC;AAAEtD,MAAAA,KAAK,EAAE,IAAI;AAAE82B,MAAAA,OAAO,EAAE,IAAA;AAAK,KAAC,CAAC,CAC7CxzB,IAAI,CAAC,aAAa,EAAE,IAAI,EAAEtD,KAAK,CAAC,CAChCsD,IAAI,CAAC,aAAa,EAAE,IAAI,EAAEtD,KAAK,CAAC,CAAA;AACrC,GAAA;;AAEA;AACA;AACAgB,EAAAA,IAAIA,GAAG;AACL,IAAA,IAAI,IAAI,CAAC2R,MAAM,EAAE,EAAE,OAAO,IAAI,CAAA;AAC9B,IAAA,OAAO,KAAK,CAAC3R,IAAI,EAAE,CAAA;AACrB,GAAA;AACF,CAAA;AAEA1F,eAAe,CAAC;AACdqkB,EAAAA,SAAS,EAAE;AACT;IACAoX,MAAM,EAAE7zB,iBAAiB,CAAC,YAAY;MACpC,OAAO,IAAI,CAACkY,GAAG,CAAC,IAAIyb,GAAG,EAAE,CAAC,CAAA;KAC3B,CAAA;AACH,GAAA;AACF,CAAC,CAAC,CAAA;AAEFt0B,QAAQ,CAACs0B,GAAG,EAAE,KAAK,EAAE,IAAI,CAAC;;AC9DX,MAAMG,MAAM,SAASrX,SAAS,CAAC;AAC5C;AACAvc,EAAAA,WAAWA,CAAC3D,IAAI,EAAE6W,KAAK,GAAG7W,IAAI,EAAE;IAC9B,KAAK,CAACoC,SAAS,CAAC,QAAQ,EAAEpC,IAAI,CAAC,EAAE6W,KAAK,CAAC,CAAA;AACzC,GAAA;AACF,CAAA;AAEAhb,eAAe,CAAC;AACdqkB,EAAAA,SAAS,EAAE;IACTsX,MAAM,EAAE/zB,iBAAiB,CAAC,YAAY;MACpC,OAAO,IAAI,CAACkY,GAAG,CAAC,IAAI4b,MAAM,EAAE,CAAC,CAAA;KAC9B,CAAA;AACH,GAAA;AACF,CAAC,CAAC,CAAA;AAEFz0B,QAAQ,CAACy0B,MAAM,EAAE,QAAQ,CAAC;;ACjB1B;AACO,SAASE,KAAKA,CAACja,IAAI,EAAE;AAC1B;AACA,EAAA,IAAI,IAAI,CAACka,MAAM,KAAK,KAAK,EAAE;IACzB,IAAI,CAAC9b,KAAK,EAAE,CAAA;AACd,GAAA;;AAEA;AACA,EAAA,IAAI,CAAC5b,IAAI,CAACyb,WAAW,CAAChb,OAAO,CAACE,QAAQ,CAACg3B,cAAc,CAACna,IAAI,CAAC,CAAC,CAAA;AAE5D,EAAA,OAAO,IAAI,CAAA;AACb,CAAA;;AAEA;AACO,SAASvgB,MAAMA,GAAG;AACvB,EAAA,OAAO,IAAI,CAAC+C,IAAI,CAAC43B,qBAAqB,EAAE,CAAA;AAC1C,CAAA;;AAEA;AACA;AACA;AACO,SAAS54B,GAACA,CAACA,CAAC,EAAET,GAAG,GAAG,IAAI,CAACC,IAAI,EAAE,EAAE;EACtC,IAAIQ,CAAC,IAAI,IAAI,EAAE;IACb,OAAOT,GAAG,CAACS,CAAC,CAAA;AACd,GAAA;AAEA,EAAA,OAAO,IAAI,CAAC6E,IAAI,CAAC,GAAG,EAAE,IAAI,CAACA,IAAI,CAAC,GAAG,CAAC,GAAG7E,CAAC,GAAGT,GAAG,CAACS,CAAC,CAAC,CAAA;AACnD,CAAA;;AAEA;AACO,SAASC,GAACA,CAACA,CAAC,EAAEV,GAAG,GAAG,IAAI,CAACC,IAAI,EAAE,EAAE;EACtC,IAAIS,CAAC,IAAI,IAAI,EAAE;IACb,OAAOV,GAAG,CAACU,CAAC,CAAA;AACd,GAAA;AAEA,EAAA,OAAO,IAAI,CAAC4E,IAAI,CAAC,GAAG,EAAE,IAAI,CAACA,IAAI,CAAC,GAAG,CAAC,GAAG5E,CAAC,GAAGV,GAAG,CAACU,CAAC,CAAC,CAAA;AACnD,CAAA;AAEO,SAASwf,MAAIA,CAACzf,CAAC,EAAEC,CAAC,EAAEV,GAAG,GAAG,IAAI,CAACC,IAAI,EAAE,EAAE;AAC5C,EAAA,OAAO,IAAI,CAACQ,CAAC,CAACA,CAAC,EAAET,GAAG,CAAC,CAACU,CAAC,CAACA,CAAC,EAAEV,GAAG,CAAC,CAAA;AACjC,CAAA;;AAEA;AACO,SAAS4R,EAAEA,CAACnR,CAAC,EAAET,GAAG,GAAG,IAAI,CAACC,IAAI,EAAE,EAAE;EACvC,IAAIQ,CAAC,IAAI,IAAI,EAAE;IACb,OAAOT,GAAG,CAAC4R,EAAE,CAAA;AACf,GAAA;AAEA,EAAA,OAAO,IAAI,CAACtM,IAAI,CAAC,GAAG,EAAE,IAAI,CAACA,IAAI,CAAC,GAAG,CAAC,GAAG7E,CAAC,GAAGT,GAAG,CAAC4R,EAAE,CAAC,CAAA;AACpD,CAAA;;AAEA;AACO,SAASC,EAAEA,CAACnR,CAAC,EAAEV,GAAG,GAAG,IAAI,CAACC,IAAI,EAAE,EAAE;EACvC,IAAIS,CAAC,IAAI,IAAI,EAAE;IACb,OAAOV,GAAG,CAAC6R,EAAE,CAAA;AACf,GAAA;AAEA,EAAA,OAAO,IAAI,CAACvM,IAAI,CAAC,GAAG,EAAE,IAAI,CAACA,IAAI,CAAC,GAAG,CAAC,GAAG5E,CAAC,GAAGV,GAAG,CAAC6R,EAAE,CAAC,CAAA;AACpD,CAAA;AAEO,SAASkO,MAAMA,CAACtf,CAAC,EAAEC,CAAC,EAAEV,GAAG,GAAG,IAAI,CAACC,IAAI,EAAE,EAAE;AAC9C,EAAA,OAAO,IAAI,CAAC2R,EAAE,CAACnR,CAAC,EAAET,GAAG,CAAC,CAAC6R,EAAE,CAACnR,CAAC,EAAEV,GAAG,CAAC,CAAA;AACnC,CAAA;AAEO,SAASu4B,EAAEA,CAAC93B,CAAC,EAAE;AACpB,EAAA,OAAO,IAAI,CAAC6E,IAAI,CAAC,GAAG,EAAE7E,CAAC,CAAC,CAAA;AAC1B,CAAA;AAEO,SAAS+3B,EAAEA,CAAC93B,CAAC,EAAE;AACpB,EAAA,OAAO,IAAI,CAAC4E,IAAI,CAAC,GAAG,EAAE5E,CAAC,CAAC,CAAA;AAC1B,CAAA;AAEO,SAASk4B,KAAKA,CAACn4B,CAAC,EAAEC,CAAC,EAAE;EAC1B,OAAO,IAAI,CAAC63B,EAAE,CAAC93B,CAAC,CAAC,CAAC+3B,EAAE,CAAC93B,CAAC,CAAC,CAAA;AACzB,CAAA;;AAEA;AACO,SAAS44B,KAAKA,CAACA,KAAK,EAAE;AAC3B,EAAA,IAAI,CAACH,MAAM,GAAG,CAAC,CAACG,KAAK,CAAA;AACrB,EAAA,OAAO,IAAI,CAAA;AACb;;;;;;;;;;;;;;;;;;ACpEe,MAAMC,IAAI,SAASxX,KAAK,CAAC;AACtC;AACA3c,EAAAA,WAAWA,CAAC3D,IAAI,EAAE6W,KAAK,GAAG7W,IAAI,EAAE;IAC9B,KAAK,CAACoC,SAAS,CAAC,MAAM,EAAEpC,IAAI,CAAC,EAAE6W,KAAK,CAAC,CAAA;AAErC,IAAA,IAAI,CAACsH,GAAG,CAAChD,OAAO,GAAG,IAAI,CAACgD,GAAG,CAAChD,OAAO,IAAI,IAAIrB,SAAS,CAAC,GAAG,CAAC,CAAC;AAC1D,IAAA,IAAI,CAACie,QAAQ,GAAG,IAAI,CAAC;AACrB,IAAA,IAAI,CAACL,MAAM,GAAG,KAAK,CAAC;AACtB,GAAA;;AAEA;EACAvc,OAAOA,CAAClB,KAAK,EAAE;AACb;IACA,IAAIA,KAAK,IAAI,IAAI,EAAE;AACjB,MAAA,OAAO,IAAI,CAACkE,GAAG,CAAChD,OAAO,CAAA;AACzB,KAAA;;AAEA;IACA,IAAI,CAACgD,GAAG,CAAChD,OAAO,GAAG,IAAIrB,SAAS,CAACG,KAAK,CAAC,CAAA;AAEvC,IAAA,OAAO,IAAI,CAACoB,OAAO,EAAE,CAAA;AACvB,GAAA;;AAEA;EACAA,OAAOA,CAACA,OAAO,EAAE;AACf;AACA,IAAA,IAAI,OAAOA,OAAO,KAAK,SAAS,EAAE;MAChC,IAAI,CAAC0c,QAAQ,GAAG1c,OAAO,CAAA;AACzB,KAAA;;AAEA;IACA,IAAI,IAAI,CAAC0c,QAAQ,EAAE;MACjB,MAAMC,IAAI,GAAG,IAAI,CAAA;MACjB,IAAIC,eAAe,GAAG,CAAC,CAAA;AACvB,MAAA,MAAM9c,OAAO,GAAG,IAAI,CAACgD,GAAG,CAAChD,OAAO,CAAA;AAEhC,MAAA,IAAI,CAAC5E,IAAI,CAAC,UAAUxZ,CAAC,EAAE;AACrB,QAAA,IAAIuC,aAAa,CAAC,IAAI,CAACU,IAAI,CAAC,EAAE,OAAA;AAE9B,QAAA,MAAMk4B,QAAQ,GAAGz3B,OAAO,CAACC,MAAM,CAC5By3B,gBAAgB,CAAC,IAAI,CAACn4B,IAAI,CAAC,CAC3BgH,gBAAgB,CAAC,WAAW,CAAC,CAAA;QAEhC,MAAMwJ,EAAE,GAAG2K,OAAO,GAAG,IAAIrB,SAAS,CAACoe,QAAQ,CAAC,CAAA;AAE5C,QAAA,IAAI,IAAI,CAAC/Z,GAAG,CAACia,QAAQ,EAAE;UACrB,IAAI,CAACv0B,IAAI,CAAC,GAAG,EAAEm0B,IAAI,CAACn0B,IAAI,CAAC,GAAG,CAAC,CAAC,CAAA;AAE9B,UAAA,IAAI,IAAI,CAAC2Z,IAAI,EAAE,KAAK,IAAI,EAAE;AACxBya,YAAAA,eAAe,IAAIznB,EAAE,CAAA;AACvB,WAAC,MAAM;AACL,YAAA,IAAI,CAAC3M,IAAI,CAAC,IAAI,EAAE9G,CAAC,GAAGyT,EAAE,GAAGynB,eAAe,GAAG,CAAC,CAAC,CAAA;AAC7CA,YAAAA,eAAe,GAAG,CAAC,CAAA;AACrB,WAAA;AACF,SAAA;AACF,OAAC,CAAC,CAAA;AAEF,MAAA,IAAI,CAAC/e,IAAI,CAAC,SAAS,CAAC,CAAA;AACtB,KAAA;AAEA,IAAA,OAAO,IAAI,CAAA;AACb,GAAA;;AAEA;EACAmF,OAAOA,CAAC3f,CAAC,EAAE;IACT,IAAI,CAACyf,GAAG,GAAGzf,CAAC,CAAA;AACZ,IAAA,IAAI,CAACyf,GAAG,CAAChD,OAAO,GAAG,IAAIrB,SAAS,CAACpb,CAAC,CAACyc,OAAO,IAAI,GAAG,CAAC,CAAA;AAClD,IAAA,OAAO,IAAI,CAAA;AACb,GAAA;AAEA1b,EAAAA,cAAcA,GAAG;AACfA,IAAAA,cAAc,CAAC,IAAI,EAAE,IAAI,CAAC0e,GAAG,EAAE;AAAEhD,MAAAA,OAAO,EAAE,GAAA;AAAI,KAAC,CAAC,CAAA;AAChD,IAAA,OAAO,IAAI,CAAA;AACb,GAAA;;AAEA;EACAqC,IAAIA,CAACA,IAAI,EAAE;AACT;IACA,IAAIA,IAAI,KAAK6Y,SAAS,EAAE;AACtB,MAAA,MAAMhzB,QAAQ,GAAG,IAAI,CAACrD,IAAI,CAAC0b,UAAU,CAAA;MACrC,IAAI2c,SAAS,GAAG,CAAC,CAAA;AACjB7a,MAAAA,IAAI,GAAG,EAAE,CAAA;AAET,MAAA,KAAK,IAAIzgB,CAAC,GAAG,CAAC,EAAEkhB,GAAG,GAAG5a,QAAQ,CAACpG,MAAM,EAAEF,CAAC,GAAGkhB,GAAG,EAAE,EAAElhB,CAAC,EAAE;AACnD;AACA,QAAA,IAAIsG,QAAQ,CAACtG,CAAC,CAAC,CAACyC,QAAQ,KAAK,UAAU,IAAIF,aAAa,CAAC+D,QAAQ,CAACtG,CAAC,CAAC,CAAC,EAAE;UACrE,IAAIA,CAAC,KAAK,CAAC,EAAEs7B,SAAS,GAAGt7B,CAAC,GAAG,CAAC,CAAA;AAC9B,UAAA,SAAA;AACF,SAAA;;AAEA;QACA,IACEA,CAAC,KAAKs7B,SAAS,IACfh1B,QAAQ,CAACtG,CAAC,CAAC,CAACu7B,QAAQ,KAAK,CAAC,IAC1B91B,KAAK,CAACa,QAAQ,CAACtG,CAAC,CAAC,CAAC,CAACohB,GAAG,CAACia,QAAQ,KAAK,IAAI,EACxC;AACA5a,UAAAA,IAAI,IAAI,IAAI,CAAA;AACd,SAAA;;AAEA;AACAA,QAAAA,IAAI,IAAIna,QAAQ,CAACtG,CAAC,CAAC,CAAC0gB,WAAW,CAAA;AACjC,OAAA;AAEA,MAAA,OAAOD,IAAI,CAAA;AACb,KAAA;;AAEA;IACA,IAAI,CAAC5B,KAAK,EAAE,CAACic,KAAK,CAAC,IAAI,CAAC,CAAA;AAExB,IAAA,IAAI,OAAOra,IAAI,KAAK,UAAU,EAAE;AAC9B;AACAA,MAAAA,IAAI,CAAC5L,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,CAAA;AACvB,KAAC,MAAM;AACL;MACA4L,IAAI,GAAG,CAACA,IAAI,GAAG,EAAE,EAAE1X,KAAK,CAAC,IAAI,CAAC,CAAA;;AAE9B;AACA,MAAA,KAAK,IAAIkT,CAAC,GAAG,CAAC,EAAEqN,EAAE,GAAG7I,IAAI,CAACvgB,MAAM,EAAE+b,CAAC,GAAGqN,EAAE,EAAErN,CAAC,EAAE,EAAE;AAC7C,QAAA,IAAI,CAACuf,OAAO,CAAC/a,IAAI,CAACxE,CAAC,CAAC,CAAC,CAAA;AACvB,OAAA;AACF,KAAA;;AAEA;IACA,OAAO,IAAI,CAAC6e,KAAK,CAAC,KAAK,CAAC,CAACxc,OAAO,EAAE,CAAA;AACpC,GAAA;AACF,CAAA;AAEA9X,MAAM,CAACu0B,IAAI,EAAEU,QAAQ,CAAC,CAAA;AAEtB38B,eAAe,CAAC;AACdqkB,EAAAA,SAAS,EAAE;AACT;AACA1C,IAAAA,IAAI,EAAE/Z,iBAAiB,CAAC,UAAU+Z,IAAI,GAAG,EAAE,EAAE;AAC3C,MAAA,OAAO,IAAI,CAAC7B,GAAG,CAAC,IAAImc,IAAI,EAAE,CAAC,CAACta,IAAI,CAACA,IAAI,CAAC,CAAA;AACxC,KAAC,CAAC;AAEF;AACAia,IAAAA,KAAK,EAAEh0B,iBAAiB,CAAC,UAAU+Z,IAAI,GAAG,EAAE,EAAE;AAC5C,MAAA,OAAO,IAAI,CAAC7B,GAAG,CAAC,IAAImc,IAAI,EAAE,CAAC,CAACL,KAAK,CAACja,IAAI,CAAC,CAAA;KACxC,CAAA;AACH,GAAA;AACF,CAAC,CAAC,CAAA;AAEF1a,QAAQ,CAACg1B,IAAI,EAAE,MAAM,CAAC;;AChJP,MAAMW,KAAK,SAASnY,KAAK,CAAC;AACvC;AACA3c,EAAAA,WAAWA,CAAC3D,IAAI,EAAE6W,KAAK,GAAG7W,IAAI,EAAE;IAC9B,KAAK,CAACoC,SAAS,CAAC,OAAO,EAAEpC,IAAI,CAAC,EAAE6W,KAAK,CAAC,CAAA;AACtC,IAAA,IAAI,CAAC6gB,MAAM,GAAG,KAAK,CAAC;AACtB,GAAA;;AAEA;EACAnnB,EAAEA,CAACA,EAAE,EAAE;AACL,IAAA,OAAO,IAAI,CAAC1M,IAAI,CAAC,IAAI,EAAE0M,EAAE,CAAC,CAAA;AAC5B,GAAA;;AAEA;EACAC,EAAEA,CAACA,EAAE,EAAE;AACL,IAAA,OAAO,IAAI,CAAC3M,IAAI,CAAC,IAAI,EAAE2M,EAAE,CAAC,CAAA;AAC5B,GAAA;;AAEA;AACA+nB,EAAAA,OAAOA,GAAG;AACR;AACA,IAAA,IAAI,CAACpa,GAAG,CAACia,QAAQ,GAAG,IAAI,CAAA;;AAExB;AACA,IAAA,MAAM5a,IAAI,GAAG,IAAI,CAACzZ,MAAM,EAAE,CAAA;;AAE1B;AACA,IAAA,IAAI,EAAEyZ,IAAI,YAAYsa,IAAI,CAAC,EAAE;AAC3B,MAAA,OAAO,IAAI,CAAA;AACb,KAAA;AAEA,IAAA,MAAM/6B,CAAC,GAAGygB,IAAI,CAACvZ,KAAK,CAAC,IAAI,CAAC,CAAA;AAE1B,IAAA,MAAMi0B,QAAQ,GAAGz3B,OAAO,CAACC,MAAM,CAC5By3B,gBAAgB,CAAC,IAAI,CAACn4B,IAAI,CAAC,CAC3BgH,gBAAgB,CAAC,WAAW,CAAC,CAAA;AAChC,IAAA,MAAMwJ,EAAE,GAAGgN,IAAI,CAACW,GAAG,CAAChD,OAAO,GAAG,IAAIrB,SAAS,CAACoe,QAAQ,CAAC,CAAA;;AAErD;IACA,OAAO,IAAI,CAAC1nB,EAAE,CAACzT,CAAC,GAAGyT,EAAE,GAAG,CAAC,CAAC,CAAC3M,IAAI,CAAC,GAAG,EAAE2Z,IAAI,CAACxe,CAAC,EAAE,CAAC,CAAA;AAChD,GAAA;;AAEA;EACAwe,IAAIA,CAACA,IAAI,EAAE;IACT,IAAIA,IAAI,IAAI,IAAI,EACd,OAAO,IAAI,CAACxd,IAAI,CAACyd,WAAW,IAAI,IAAI,CAACU,GAAG,CAACia,QAAQ,GAAG,IAAI,GAAG,EAAE,CAAC,CAAA;AAEhE,IAAA,IAAI,OAAO5a,IAAI,KAAK,UAAU,EAAE;MAC9B,IAAI,CAAC5B,KAAK,EAAE,CAACic,KAAK,CAAC,IAAI,CAAC,CAAA;AACxBra,MAAAA,IAAI,CAAC5L,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,CAAA;AACrB,MAAA,IAAI,CAACimB,KAAK,CAAC,KAAK,CAAC,CAAA;AACnB,KAAC,MAAM;AACL,MAAA,IAAI,CAACJ,KAAK,CAACja,IAAI,CAAC,CAAA;AAClB,KAAA;AAEA,IAAA,OAAO,IAAI,CAAA;AACb,GAAA;AACF,CAAA;AAEAja,MAAM,CAACk1B,KAAK,EAAED,QAAQ,CAAC,CAAA;AAEvB38B,eAAe,CAAC;AACd48B,EAAAA,KAAK,EAAE;AACLC,IAAAA,KAAK,EAAEj1B,iBAAiB,CAAC,UAAU+Z,IAAI,GAAG,EAAE,EAAE;AAC5C,MAAA,MAAMkb,KAAK,GAAG,IAAID,KAAK,EAAE,CAAA;;AAEzB;AACA,MAAA,IAAI,CAAC,IAAI,CAACf,MAAM,EAAE;QAChB,IAAI,CAAC9b,KAAK,EAAE,CAAA;AACd,OAAA;;AAEA;MACA,OAAO,IAAI,CAACD,GAAG,CAAC+c,KAAK,CAAC,CAAClb,IAAI,CAACA,IAAI,CAAC,CAAA;KAClC,CAAA;GACF;AACDsa,EAAAA,IAAI,EAAE;AACJS,IAAAA,OAAO,EAAE,UAAU/a,IAAI,GAAG,EAAE,EAAE;MAC5B,OAAO,IAAI,CAACkb,KAAK,CAAClb,IAAI,CAAC,CAAC+a,OAAO,EAAE,CAAA;AACnC,KAAA;AACF,GAAA;AACF,CAAC,CAAC,CAAA;AAEFz1B,QAAQ,CAAC21B,KAAK,EAAE,OAAO,CAAC;;ACnFT,MAAME,MAAM,SAASrY,KAAK,CAAC;AACxC3c,EAAAA,WAAWA,CAAC3D,IAAI,EAAE6W,KAAK,GAAG7W,IAAI,EAAE;IAC9B,KAAK,CAACoC,SAAS,CAAC,QAAQ,EAAEpC,IAAI,CAAC,EAAE6W,KAAK,CAAC,CAAA;AACzC,GAAA;EAEAsI,MAAMA,CAAC1hB,CAAC,EAAE;AACR,IAAA,OAAO,IAAI,CAACoG,IAAI,CAAC,GAAG,EAAEpG,CAAC,CAAC,CAAA;AAC1B,GAAA;;AAEA;EACAoS,EAAEA,CAACA,EAAE,EAAE;AACL,IAAA,OAAO,IAAI,CAAChM,IAAI,CAAC,GAAG,EAAEgM,EAAE,CAAC,CAAA;AAC3B,GAAA;;AAEA;EACAE,EAAEA,CAACA,EAAE,EAAE;AACL,IAAA,OAAO,IAAI,CAACF,EAAE,CAACE,EAAE,CAAC,CAAA;AACpB,GAAA;EAEA0D,IAAIA,CAACA,IAAI,EAAE;AACT,IAAA,OAAO,IAAI,CAAC0L,MAAM,CAAC,IAAIrF,SAAS,CAACrG,IAAI,CAAC,CAACyG,MAAM,CAAC,CAAC,CAAC,CAAC,CAAA;AACnD,GAAA;AACF,CAAA;AAEA3W,MAAM,CAACo1B,MAAM,EAAE;KAAE35B,GAAC;KAAEC,GAAC;MAAEkR,IAAE;MAAEC,IAAE;SAAE/R,OAAK;AAAEC,UAAAA,QAAAA;AAAO,CAAC,CAAC,CAAA;AAE/CzC,eAAe,CAAC;AACdqkB,EAAAA,SAAS,EAAE;AACT;AACA0Y,IAAAA,MAAM,EAAEn1B,iBAAiB,CAAC,UAAUgQ,IAAI,GAAG,CAAC,EAAE;MAC5C,OAAO,IAAI,CAACkI,GAAG,CAAC,IAAIgd,MAAM,EAAE,CAAC,CAACllB,IAAI,CAACA,IAAI,CAAC,CAACgL,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAAA;KACpD,CAAA;AACH,GAAA;AACF,CAAC,CAAC,CAAA;AAEF3b,QAAQ,CAAC61B,MAAM,EAAE,QAAQ,CAAC;;ACzCX,MAAME,QAAQ,SAAS3Y,SAAS,CAAC;AAC9Cvc,EAAAA,WAAWA,CAAC3D,IAAI,EAAE6W,KAAK,GAAG7W,IAAI,EAAE;IAC9B,KAAK,CAACoC,SAAS,CAAC,UAAU,EAAEpC,IAAI,CAAC,EAAE6W,KAAK,CAAC,CAAA;AAC3C,GAAA;;AAEA;AACAtS,EAAAA,MAAMA,GAAG;AACP;IACA,IAAI,CAAC0c,OAAO,EAAE,CAACpa,OAAO,CAAC,UAAUD,EAAE,EAAE;MACnCA,EAAE,CAACkyB,MAAM,EAAE,CAAA;AACb,KAAC,CAAC,CAAA;;AAEF;AACA,IAAA,OAAO,KAAK,CAACv0B,MAAM,EAAE,CAAA;AACvB,GAAA;AAEA0c,EAAAA,OAAOA,GAAG;IACR,OAAOnK,QAAQ,CAAC,kBAAkB,GAAG,IAAI,CAACxT,EAAE,EAAE,GAAG,GAAG,CAAC,CAAA;AACvD,GAAA;AACF,CAAA;AAEAzH,eAAe,CAAC;AACdqkB,EAAAA,SAAS,EAAE;AACT;IACA6Y,IAAI,EAAEt1B,iBAAiB,CAAC,YAAY;AAClC,MAAA,OAAO,IAAI,CAAC8a,IAAI,EAAE,CAAC5C,GAAG,CAAC,IAAIkd,QAAQ,EAAE,CAAC,CAAA;KACvC,CAAA;GACF;AACDpnB,EAAAA,OAAO,EAAE;AACP;AACAunB,IAAAA,OAAOA,GAAG;AACR,MAAA,OAAO,IAAI,CAAC9zB,SAAS,CAAC,WAAW,CAAC,CAAA;KACnC;IAED+zB,QAAQA,CAAC76B,OAAO,EAAE;AAChB;MACA,MAAM46B,OAAO,GACX56B,OAAO,YAAYy6B,QAAQ,GACvBz6B,OAAO,GACP,IAAI,CAAC2F,MAAM,EAAE,CAACg1B,IAAI,EAAE,CAACz0B,GAAG,CAAClG,OAAO,CAAC,CAAA;;AAEvC;AACA,MAAA,OAAO,IAAI,CAACyF,IAAI,CAAC,WAAW,EAAE,OAAO,GAAGm1B,OAAO,CAAC11B,EAAE,EAAE,GAAG,GAAG,CAAC,CAAA;KAC5D;AAED;AACAw1B,IAAAA,MAAMA,GAAG;AACP,MAAA,OAAO,IAAI,CAACj1B,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,CAAA;AACrC,KAAA;AACF,GAAA;AACF,CAAC,CAAC,CAAA;AAEFf,QAAQ,CAAC+1B,QAAQ,EAAE,UAAU,CAAC;;ACrDf,MAAMK,aAAa,SAASznB,OAAO,CAAC;AACjD9N,EAAAA,WAAWA,CAAC3D,IAAI,EAAE6W,KAAK,GAAG7W,IAAI,EAAE;IAC9B,KAAK,CAACoC,SAAS,CAAC,eAAe,EAAEpC,IAAI,CAAC,EAAE6W,KAAK,CAAC,CAAA;AAChD,GAAA;AACF,CAAA;AAEAhb,eAAe,CAAC;AACdqkB,EAAAA,SAAS,EAAE;AACTiZ,IAAAA,aAAa,EAAE11B,iBAAiB,CAAC,UAAUpF,KAAK,EAAEC,MAAM,EAAE;AACxD,MAAA,OAAO,IAAI,CAACqd,GAAG,CAAC,IAAIud,aAAa,EAAE,CAAC,CAACzlB,IAAI,CAACpV,KAAK,EAAEC,MAAM,CAAC,CAAA;KACzD,CAAA;AACH,GAAA;AACF,CAAC,CAAC,CAAA;AAEFwE,QAAQ,CAACo2B,aAAa,EAAE,eAAe,CAAC;;ACZjC,SAAS1a,KAAKA,CAACjO,EAAE,EAAEC,EAAE,EAAE;EAC5B,IAAI,CAACnN,QAAQ,EAAE,CAACwD,OAAO,CAAEuyB,KAAK,IAAK;AACjC,IAAA,IAAI56B,IAAI,CAAA;;AAER;AACA;IACA,IAAI;AACF;AACA;AACA;AACA;AACA;AACA;AACAA,MAAAA,IAAI,GACF46B,KAAK,CAACp5B,IAAI,YAAYoB,SAAS,EAAE,CAACi4B,aAAa,GAC3C,IAAInlB,GAAG,CAACklB,KAAK,CAACv1B,IAAI,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC,CAAC,GAClDu1B,KAAK,CAAC56B,IAAI,EAAE,CAAA;KACnB,CAAC,OAAOkJ,CAAC,EAAE;AACV,MAAA,OAAA;AACF,KAAA;;AAEA;AACA,IAAA,MAAM3L,CAAC,GAAG,IAAI0R,MAAM,CAAC2rB,KAAK,CAAC,CAAA;AAC3B;AACA;AACA,IAAA,MAAM/oB,MAAM,GAAGtU,CAAC,CAACwT,SAAS,CAACgB,EAAE,EAAEC,EAAE,CAAC,CAACjD,SAAS,CAACxR,CAAC,CAAC8V,OAAO,EAAE,CAAC,CAAA;AACzD;AACA,IAAA,MAAMxN,CAAC,GAAG,IAAI8I,KAAK,CAAC3O,IAAI,CAACQ,CAAC,EAAER,IAAI,CAACS,CAAC,CAAC,CAACsO,SAAS,CAAC8C,MAAM,CAAC,CAAA;AACrD;IACA+oB,KAAK,CAAC3a,IAAI,CAACpa,CAAC,CAACrF,CAAC,EAAEqF,CAAC,CAACpF,CAAC,CAAC,CAAA;AACtB,GAAC,CAAC,CAAA;AAEF,EAAA,OAAO,IAAI,CAAA;AACb,CAAA;AAEO,SAASsR,EAAEA,CAACA,EAAE,EAAE;AACrB,EAAA,OAAO,IAAI,CAACiO,KAAK,CAACjO,EAAE,EAAE,CAAC,CAAC,CAAA;AAC1B,CAAA;AAEO,SAASC,EAAEA,CAACA,EAAE,EAAE;AACrB,EAAA,OAAO,IAAI,CAACgO,KAAK,CAAC,CAAC,EAAEhO,EAAE,CAAC,CAAA;AAC1B,CAAA;AAEO,SAASlS,MAAMA,CAACA,MAAM,EAAEC,GAAG,GAAG,IAAI,CAACC,IAAI,EAAE,EAAE;AAChD,EAAA,IAAIF,MAAM,IAAI,IAAI,EAAE,OAAOC,GAAG,CAACD,MAAM,CAAA;EACrC,OAAO,IAAI,CAACmV,IAAI,CAAClV,GAAG,CAACF,KAAK,EAAEC,MAAM,EAAEC,GAAG,CAAC,CAAA;AAC1C,CAAA;AAEO,SAASkgB,IAAIA,CAACzf,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAG,CAAC,EAAEV,GAAG,GAAG,IAAI,CAACC,IAAI,EAAE,EAAE;AACpD,EAAA,MAAM+R,EAAE,GAAGvR,CAAC,GAAGT,GAAG,CAACS,CAAC,CAAA;AACpB,EAAA,MAAMwR,EAAE,GAAGvR,CAAC,GAAGV,GAAG,CAACU,CAAC,CAAA;AAEpB,EAAA,OAAO,IAAI,CAACuf,KAAK,CAACjO,EAAE,EAAEC,EAAE,CAAC,CAAA;AAC3B,CAAA;AAEO,SAASiD,IAAIA,CAACpV,KAAK,EAAEC,MAAM,EAAEC,GAAG,GAAG,IAAI,CAACC,IAAI,EAAE,EAAE;EACrD,MAAM6F,CAAC,GAAGlG,gBAAgB,CAAC,IAAI,EAAEE,KAAK,EAAEC,MAAM,EAAEC,GAAG,CAAC,CAAA;EACpD,MAAMoQ,MAAM,GAAGtK,CAAC,CAAChG,KAAK,GAAGE,GAAG,CAACF,KAAK,CAAA;EAClC,MAAMwQ,MAAM,GAAGxK,CAAC,CAAC/F,MAAM,GAAGC,GAAG,CAACD,MAAM,CAAA;EAEpC,IAAI,CAAC+E,QAAQ,EAAE,CAACwD,OAAO,CAAEuyB,KAAK,IAAK;AACjC,IAAA,MAAM16B,CAAC,GAAG,IAAIyO,KAAK,CAAC5O,GAAG,CAAC,CAACgP,SAAS,CAAC,IAAIE,MAAM,CAAC2rB,KAAK,CAAC,CAACvnB,OAAO,EAAE,CAAC,CAAA;AAC/DunB,IAAAA,KAAK,CAACxqB,KAAK,CAACD,MAAM,EAAEE,MAAM,EAAEnQ,CAAC,CAACM,CAAC,EAAEN,CAAC,CAACO,CAAC,CAAC,CAAA;AACvC,GAAC,CAAC,CAAA;AAEF,EAAA,OAAO,IAAI,CAAA;AACb,CAAA;AAEO,SAASZ,KAAKA,CAACA,KAAK,EAAEE,GAAG,GAAG,IAAI,CAACC,IAAI,EAAE,EAAE;AAC9C,EAAA,IAAIH,KAAK,IAAI,IAAI,EAAE,OAAOE,GAAG,CAACF,KAAK,CAAA;EACnC,OAAO,IAAI,CAACoV,IAAI,CAACpV,KAAK,EAAEE,GAAG,CAACD,MAAM,EAAEC,GAAG,CAAC,CAAA;AAC1C,CAAA;AAEO,SAASS,CAACA,CAACA,CAAC,EAAET,GAAG,GAAG,IAAI,CAACC,IAAI,EAAE,EAAE;AACtC,EAAA,IAAIQ,CAAC,IAAI,IAAI,EAAE,OAAOT,GAAG,CAACS,CAAC,CAAA;EAC3B,OAAO,IAAI,CAACyf,IAAI,CAACzf,CAAC,EAAET,GAAG,CAACU,CAAC,EAAEV,GAAG,CAAC,CAAA;AACjC,CAAA;AAEO,SAASU,CAACA,CAACA,CAAC,EAAEV,GAAG,GAAG,IAAI,CAACC,IAAI,EAAE,EAAE;AACtC,EAAA,IAAIS,CAAC,IAAI,IAAI,EAAE,OAAOV,GAAG,CAACU,CAAC,CAAA;EAC3B,OAAO,IAAI,CAACwf,IAAI,CAAClgB,GAAG,CAACS,CAAC,EAAEC,CAAC,EAAEV,GAAG,CAAC,CAAA;AACjC;;;;;;;;;;;;;;;AC7Ee,MAAM+6B,CAAC,SAASpZ,SAAS,CAAC;AACvCvc,EAAAA,WAAWA,CAAC3D,IAAI,EAAE6W,KAAK,GAAG7W,IAAI,EAAE;IAC9B,KAAK,CAACoC,SAAS,CAAC,GAAG,EAAEpC,IAAI,CAAC,EAAE6W,KAAK,CAAC,CAAA;AACpC,GAAA;AACF,CAAA;AAEAtT,MAAM,CAAC+1B,CAAC,EAAEC,iBAAiB,CAAC,CAAA;AAE5B19B,eAAe,CAAC;AACdqkB,EAAAA,SAAS,EAAE;AACT;IACAsZ,KAAK,EAAE/1B,iBAAiB,CAAC,YAAY;MACnC,OAAO,IAAI,CAACkY,GAAG,CAAC,IAAI2d,CAAC,EAAE,CAAC,CAAA;KACzB,CAAA;AACH,GAAA;AACF,CAAC,CAAC,CAAA;AAEFx2B,QAAQ,CAACw2B,CAAC,EAAE,GAAG,CAAC;;AChBD,MAAMtT,CAAC,SAAS9F,SAAS,CAAC;AACvCvc,EAAAA,WAAWA,CAAC3D,IAAI,EAAE6W,KAAK,GAAG7W,IAAI,EAAE;IAC9B,KAAK,CAACoC,SAAS,CAAC,GAAG,EAAEpC,IAAI,CAAC,EAAE6W,KAAK,CAAC,CAAA;AACpC,GAAA;;AAEA;EACAmN,MAAMA,CAACA,MAAM,EAAE;AACb,IAAA,OAAO,IAAI,CAACngB,IAAI,CAAC,QAAQ,EAAEmgB,MAAM,CAAC,CAAA;AACpC,GAAA;;AAEA;EACAjD,EAAEA,CAACG,GAAG,EAAE;IACN,OAAO,IAAI,CAACrd,IAAI,CAAC,MAAM,EAAEqd,GAAG,EAAE1gB,KAAK,CAAC,CAAA;AACtC,GAAA;AACF,CAAA;AAEA+C,MAAM,CAACyiB,CAAC,EAAEuT,iBAAiB,CAAC,CAAA;AAE5B19B,eAAe,CAAC;AACdqkB,EAAAA,SAAS,EAAE;AACT;AACAuZ,IAAAA,IAAI,EAAEh2B,iBAAiB,CAAC,UAAUyd,GAAG,EAAE;AACrC,MAAA,OAAO,IAAI,CAACvF,GAAG,CAAC,IAAIqK,CAAC,EAAE,CAAC,CAACjF,EAAE,CAACG,GAAG,CAAC,CAAA;KACjC,CAAA;GACF;AACDzP,EAAAA,OAAO,EAAE;AACPioB,IAAAA,MAAMA,GAAG;AACP,MAAA,MAAMD,IAAI,GAAG,IAAI,CAACE,MAAM,EAAE,CAAA;AAE1B,MAAA,IAAI,CAACF,IAAI,EAAE,OAAO,IAAI,CAAA;AAEtB,MAAA,MAAM11B,MAAM,GAAG01B,IAAI,CAAC11B,MAAM,EAAE,CAAA;MAE5B,IAAI,CAACA,MAAM,EAAE;AACX,QAAA,OAAO,IAAI,CAACQ,MAAM,EAAE,CAAA;AACtB,OAAA;AAEA,MAAA,MAAMN,KAAK,GAAGF,MAAM,CAACE,KAAK,CAACw1B,IAAI,CAAC,CAAA;AAChC11B,MAAAA,MAAM,CAACO,GAAG,CAAC,IAAI,EAAEL,KAAK,CAAC,CAAA;MAEvBw1B,IAAI,CAACl1B,MAAM,EAAE,CAAA;AACb,MAAA,OAAO,IAAI,CAAA;KACZ;IACDq1B,MAAMA,CAAC1Y,GAAG,EAAE;AACV;AACA,MAAA,IAAIuY,IAAI,GAAG,IAAI,CAACE,MAAM,EAAE,CAAA;MAExB,IAAI,CAACF,IAAI,EAAE;AACTA,QAAAA,IAAI,GAAG,IAAIzT,CAAC,EAAE,CAAA;AACd,QAAA,IAAI,CAACtI,IAAI,CAAC+b,IAAI,CAAC,CAAA;AACjB,OAAA;AAEA,MAAA,IAAI,OAAOvY,GAAG,KAAK,UAAU,EAAE;AAC7BA,QAAAA,GAAG,CAACtP,IAAI,CAAC6nB,IAAI,EAAEA,IAAI,CAAC,CAAA;AACtB,OAAC,MAAM;AACLA,QAAAA,IAAI,CAAC1Y,EAAE,CAACG,GAAG,CAAC,CAAA;AACd,OAAA;AAEA,MAAA,OAAO,IAAI,CAAA;KACZ;AACDyY,IAAAA,MAAMA,GAAG;AACP,MAAA,MAAMF,IAAI,GAAG,IAAI,CAAC11B,MAAM,EAAE,CAAA;AAC1B,MAAA,IAAI01B,IAAI,IAAIA,IAAI,CAACz5B,IAAI,CAACR,QAAQ,CAAC1B,WAAW,EAAE,KAAK,GAAG,EAAE;AACpD,QAAA,OAAO27B,IAAI,CAAA;AACb,OAAA;AAEA,MAAA,OAAO,IAAI,CAAA;AACb,KAAA;AACF,GAAA;AACF,CAAC,CAAC,CAAA;AAEF32B,QAAQ,CAACkjB,CAAC,EAAE,GAAG,CAAC;;AC7ED,MAAM6T,IAAI,SAAS3Z,SAAS,CAAC;AAC1C;AACAvc,EAAAA,WAAWA,CAAC3D,IAAI,EAAE6W,KAAK,GAAG7W,IAAI,EAAE;IAC9B,KAAK,CAACoC,SAAS,CAAC,MAAM,EAAEpC,IAAI,CAAC,EAAE6W,KAAK,CAAC,CAAA;AACvC,GAAA;;AAEA;AACAtS,EAAAA,MAAMA,GAAG;AACP;IACA,IAAI,CAAC0c,OAAO,EAAE,CAACpa,OAAO,CAAC,UAAUD,EAAE,EAAE;MACnCA,EAAE,CAACkzB,MAAM,EAAE,CAAA;AACb,KAAC,CAAC,CAAA;;AAEF;AACA,IAAA,OAAO,KAAK,CAACv1B,MAAM,EAAE,CAAA;AACvB,GAAA;AAEA0c,EAAAA,OAAOA,GAAG;IACR,OAAOnK,QAAQ,CAAC,aAAa,GAAG,IAAI,CAACxT,EAAE,EAAE,GAAG,GAAG,CAAC,CAAA;AAClD,GAAA;AACF,CAAA;AAEAzH,eAAe,CAAC;AACdqkB,EAAAA,SAAS,EAAE;IACT6Z,IAAI,EAAEt2B,iBAAiB,CAAC,YAAY;AAClC,MAAA,OAAO,IAAI,CAAC8a,IAAI,EAAE,CAAC5C,GAAG,CAAC,IAAIke,IAAI,EAAE,CAAC,CAAA;KACnC,CAAA;GACF;AACDpoB,EAAAA,OAAO,EAAE;AACP;AACAuoB,IAAAA,MAAMA,GAAG;AACP,MAAA,OAAO,IAAI,CAAC90B,SAAS,CAAC,MAAM,CAAC,CAAA;KAC9B;IAED+0B,QAAQA,CAAC77B,OAAO,EAAE;AAChB;MACA,MAAM47B,MAAM,GACV57B,OAAO,YAAYy7B,IAAI,GAAGz7B,OAAO,GAAG,IAAI,CAAC2F,MAAM,EAAE,CAACg2B,IAAI,EAAE,CAACz1B,GAAG,CAAClG,OAAO,CAAC,CAAA;;AAEvE;AACA,MAAA,OAAO,IAAI,CAACyF,IAAI,CAAC,MAAM,EAAE,OAAO,GAAGm2B,MAAM,CAAC12B,EAAE,EAAE,GAAG,GAAG,CAAC,CAAA;KACtD;AAED;AACAw2B,IAAAA,MAAMA,GAAG;AACP,MAAA,OAAO,IAAI,CAACj2B,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,CAAA;AAChC,KAAA;AACF,GAAA;AACF,CAAC,CAAC,CAAA;AAEFf,QAAQ,CAAC+2B,IAAI,EAAE,MAAM,CAAC;;AClDP,MAAMK,IAAI,SAASzoB,OAAO,CAAC;AACxC9N,EAAAA,WAAWA,CAAC3D,IAAI,EAAE6W,KAAK,GAAG7W,IAAI,EAAE;IAC9B,KAAK,CAACoC,SAAS,CAAC,MAAM,EAAEpC,IAAI,CAAC,EAAE6W,KAAK,CAAC,CAAA;AACvC,GAAA;;AAEA;EACAsK,MAAMA,CAACziB,CAAC,EAAE;IACR,IAAI,OAAOA,CAAC,KAAK,QAAQ,IAAIA,CAAC,YAAYob,SAAS,EAAE;AACnDpb,MAAAA,CAAC,GAAG;AACFib,QAAAA,MAAM,EAAEjT,SAAS,CAAC,CAAC,CAAC;AACpBoD,QAAAA,KAAK,EAAEpD,SAAS,CAAC,CAAC,CAAC;QACnBgT,OAAO,EAAEhT,SAAS,CAAC,CAAC,CAAA;OACrB,CAAA;AACH,KAAA;;AAEA;AACA,IAAA,IAAIhI,CAAC,CAACgb,OAAO,IAAI,IAAI,EAAE,IAAI,CAAC7V,IAAI,CAAC,cAAc,EAAEnF,CAAC,CAACgb,OAAO,CAAC,CAAA;AAC3D,IAAA,IAAIhb,CAAC,CAACoL,KAAK,IAAI,IAAI,EAAE,IAAI,CAACjG,IAAI,CAAC,YAAY,EAAEnF,CAAC,CAACoL,KAAK,CAAC,CAAA;AACrD,IAAA,IAAIpL,CAAC,CAACib,MAAM,IAAI,IAAI,EAAE,IAAI,CAAC9V,IAAI,CAAC,QAAQ,EAAE,IAAIiW,SAAS,CAACpb,CAAC,CAACib,MAAM,CAAC,CAAC,CAAA;AAElE,IAAA,OAAO,IAAI,CAAA;AACb,GAAA;AACF,CAAA;AAEA9d,eAAe,CAAC;AACdmlB,EAAAA,QAAQ,EAAE;AACR;IACAkO,IAAI,EAAE,UAAUvV,MAAM,EAAE7P,KAAK,EAAE4P,OAAO,EAAE;AACtC,MAAA,OAAO,IAAI,CAACiC,GAAG,CAAC,IAAIue,IAAI,EAAE,CAAC,CAAC/Y,MAAM,CAACxH,MAAM,EAAE7P,KAAK,EAAE4P,OAAO,CAAC,CAAA;AAC5D,KAAA;AACF,GAAA;AACF,CAAC,CAAC,CAAA;AAEF5W,QAAQ,CAACo3B,IAAI,EAAE,MAAM,CAAC;;ACjCtB,SAASC,OAAOA,CAAC1d,QAAQ,EAAE2d,IAAI,EAAE;AAC/B,EAAA,IAAI,CAAC3d,QAAQ,EAAE,OAAO,EAAE,CAAA;AACxB,EAAA,IAAI,CAAC2d,IAAI,EAAE,OAAO3d,QAAQ,CAAA;AAE1B,EAAA,IAAIhW,GAAG,GAAGgW,QAAQ,GAAG,GAAG,CAAA;AAExB,EAAA,KAAK,MAAM1f,CAAC,IAAIq9B,IAAI,EAAE;AACpB3zB,IAAAA,GAAG,IAAI/I,WAAW,CAACX,CAAC,CAAC,GAAG,GAAG,GAAGq9B,IAAI,CAACr9B,CAAC,CAAC,GAAG,GAAG,CAAA;AAC7C,GAAA;AAEA0J,EAAAA,GAAG,IAAI,GAAG,CAAA;AAEV,EAAA,OAAOA,GAAG,CAAA;AACZ,CAAA;AAEe,MAAM4zB,KAAK,SAAS5oB,OAAO,CAAC;AACzC9N,EAAAA,WAAWA,CAAC3D,IAAI,EAAE6W,KAAK,GAAG7W,IAAI,EAAE;IAC9B,KAAK,CAACoC,SAAS,CAAC,OAAO,EAAEpC,IAAI,CAAC,EAAE6W,KAAK,CAAC,CAAA;AACxC,GAAA;AAEAyjB,EAAAA,OAAOA,CAAC9lB,CAAC,GAAG,EAAE,EAAE;AACd,IAAA,IAAI,CAACxU,IAAI,CAACyd,WAAW,IAAIjJ,CAAC,CAAA;AAC1B,IAAA,OAAO,IAAI,CAAA;AACb,GAAA;EAEAgL,IAAIA,CAAC1jB,IAAI,EAAE+lB,GAAG,EAAE9Y,MAAM,GAAG,EAAE,EAAE;AAC3B,IAAA,OAAO,IAAI,CAACqxB,IAAI,CAAC,YAAY,EAAE;AAC7BG,MAAAA,UAAU,EAAEz+B,IAAI;AAChB+lB,MAAAA,GAAG,EAAEA,GAAG;MACR,GAAG9Y,MAAAA;AACL,KAAC,CAAC,CAAA;AACJ,GAAA;AAEAqxB,EAAAA,IAAIA,CAAC3d,QAAQ,EAAE7F,GAAG,EAAE;IAClB,OAAO,IAAI,CAAC0jB,OAAO,CAACH,OAAO,CAAC1d,QAAQ,EAAE7F,GAAG,CAAC,CAAC,CAAA;AAC7C,GAAA;AACF,CAAA;AAEA/a,eAAe,CAAC,KAAK,EAAE;AACrB0K,EAAAA,KAAKA,CAACkW,QAAQ,EAAE7F,GAAG,EAAE;AACnB,IAAA,OAAO,IAAI,CAAC+E,GAAG,CAAC,IAAI0e,KAAK,EAAE,CAAC,CAACD,IAAI,CAAC3d,QAAQ,EAAE7F,GAAG,CAAC,CAAA;GACjD;AACD4jB,EAAAA,QAAQA,CAAC1+B,IAAI,EAAE+lB,GAAG,EAAE9Y,MAAM,EAAE;AAC1B,IAAA,OAAO,IAAI,CAAC4S,GAAG,CAAC,IAAI0e,KAAK,EAAE,CAAC,CAAC7a,IAAI,CAAC1jB,IAAI,EAAE+lB,GAAG,EAAE9Y,MAAM,CAAC,CAAA;AACtD,GAAA;AACF,CAAC,CAAC,CAAA;AAEFjG,QAAQ,CAACu3B,KAAK,EAAE,OAAO,CAAC;;AC5CT,MAAMI,QAAQ,SAAS3C,IAAI,CAAC;AACzC;AACAn0B,EAAAA,WAAWA,CAAC3D,IAAI,EAAE6W,KAAK,GAAG7W,IAAI,EAAE;IAC9B,KAAK,CAACoC,SAAS,CAAC,UAAU,EAAEpC,IAAI,CAAC,EAAE6W,KAAK,CAAC,CAAA;AAC3C,GAAA;;AAEA;AACAha,EAAAA,KAAKA,GAAG;AACN,IAAA,MAAM69B,KAAK,GAAG,IAAI,CAACA,KAAK,EAAE,CAAA;IAE1B,OAAOA,KAAK,GAAGA,KAAK,CAAC79B,KAAK,EAAE,GAAG,IAAI,CAAA;AACrC,GAAA;;AAEA;EACA4lB,IAAIA,CAACplB,CAAC,EAAE;AACN,IAAA,MAAMq9B,KAAK,GAAG,IAAI,CAACA,KAAK,EAAE,CAAA;IAC1B,IAAIC,SAAS,GAAG,IAAI,CAAA;AAEpB,IAAA,IAAID,KAAK,EAAE;AACTC,MAAAA,SAAS,GAAGD,KAAK,CAACjY,IAAI,CAACplB,CAAC,CAAC,CAAA;AAC3B,KAAA;AAEA,IAAA,OAAOA,CAAC,IAAI,IAAI,GAAGs9B,SAAS,GAAG,IAAI,CAAA;AACrC,GAAA;;AAEA;AACAD,EAAAA,KAAKA,GAAG;AACN,IAAA,OAAO,IAAI,CAACx1B,SAAS,CAAC,MAAM,CAAC,CAAA;AAC/B,GAAA;AACF,CAAA;AAEArJ,eAAe,CAAC;AACdqkB,EAAAA,SAAS,EAAE;AACT0a,IAAAA,QAAQ,EAAEn3B,iBAAiB,CAAC,UAAU+Z,IAAI,EAAE9J,IAAI,EAAE;AAChD;AACA,MAAA,IAAI,EAAE8J,IAAI,YAAYsa,IAAI,CAAC,EAAE;AAC3Bta,QAAAA,IAAI,GAAG,IAAI,CAACA,IAAI,CAACA,IAAI,CAAC,CAAA;AACxB,OAAA;AAEA,MAAA,OAAOA,IAAI,CAAC9J,IAAI,CAACA,IAAI,CAAC,CAAA;KACvB,CAAA;GACF;AACDokB,EAAAA,IAAI,EAAE;AACJ;IACApkB,IAAI,EAAEjQ,iBAAiB,CAAC,UAAUi3B,KAAK,EAAEG,WAAW,GAAG,IAAI,EAAE;AAC3D,MAAA,MAAMD,QAAQ,GAAG,IAAIH,QAAQ,EAAE,CAAA;;AAE/B;AACA,MAAA,IAAI,EAAEC,KAAK,YAAYzQ,IAAI,CAAC,EAAE;AAC5B;QACAyQ,KAAK,GAAG,IAAI,CAACnc,IAAI,EAAE,CAAC7K,IAAI,CAACgnB,KAAK,CAAC,CAAA;AACjC,OAAA;;AAEA;MACAE,QAAQ,CAAC/2B,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG62B,KAAK,EAAEl6B,KAAK,CAAC,CAAA;;AAEzC;AACA,MAAA,IAAIR,IAAI,CAAA;AACR,MAAA,IAAI66B,WAAW,EAAE;AACf,QAAA,OAAQ76B,IAAI,GAAG,IAAI,CAACA,IAAI,CAACkC,UAAU,EAAG;AACpC04B,UAAAA,QAAQ,CAAC56B,IAAI,CAACyb,WAAW,CAACzb,IAAI,CAAC,CAAA;AACjC,SAAA;AACF,OAAA;;AAEA;AACA,MAAA,OAAO,IAAI,CAAC2b,GAAG,CAACif,QAAQ,CAAC,CAAA;AAC3B,KAAC,CAAC;AAEF;AACAA,IAAAA,QAAQA,GAAG;AACT,MAAA,OAAO,IAAI,CAAC1jB,OAAO,CAAC,UAAU,CAAC,CAAA;AACjC,KAAA;GACD;AACD+S,EAAAA,IAAI,EAAE;AACJ;AACAzM,IAAAA,IAAI,EAAE/Z,iBAAiB,CAAC,UAAU+Z,IAAI,EAAE;AACtC;AACA,MAAA,IAAI,EAAEA,IAAI,YAAYsa,IAAI,CAAC,EAAE;AAC3Bta,QAAAA,IAAI,GAAG,IAAIsa,IAAI,EAAE,CAAChkB,KAAK,CAAC,IAAI,CAAC/P,MAAM,EAAE,CAAC,CAACyZ,IAAI,CAACA,IAAI,CAAC,CAAA;AACnD,OAAA;;AAEA;AACA,MAAA,OAAOA,IAAI,CAAC9J,IAAI,CAAC,IAAI,CAAC,CAAA;AACxB,KAAC,CAAC;AAEFuN,IAAAA,OAAOA,GAAG;MACR,OAAOnK,QAAQ,CAAC,cAAc,CAAC,CAAC3Z,MAAM,CAAE6C,IAAI,IAAK;AAC/C,QAAA,OAAO,CAACA,IAAI,CAAC6D,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,EAAEzE,QAAQ,CAAC,IAAI,CAACkE,EAAE,EAAE,CAAC,CAAA;AACtD,OAAC,CAAC,CAAA;;AAEF;AACA;AACF,KAAA;AACF,GAAA;AACF,CAAC,CAAC,CAAA;AAEFm3B,QAAQ,CAACz3B,SAAS,CAACuf,UAAU,GAAGyF,SAAS,CAAA;AACzCllB,QAAQ,CAAC23B,QAAQ,EAAE,UAAU,CAAC;;ACpGf,MAAMK,GAAG,SAASxa,KAAK,CAAC;AACrC3c,EAAAA,WAAWA,CAAC3D,IAAI,EAAE6W,KAAK,GAAG7W,IAAI,EAAE;IAC9B,KAAK,CAACoC,SAAS,CAAC,KAAK,EAAEpC,IAAI,CAAC,EAAE6W,KAAK,CAAC,CAAA;AACtC,GAAA;;AAEA;AACAkkB,EAAAA,GAAGA,CAAC38B,OAAO,EAAE48B,IAAI,EAAE;AACjB;AACA,IAAA,OAAO,IAAI,CAACn3B,IAAI,CAAC,MAAM,EAAE,CAACm3B,IAAI,IAAI,EAAE,IAAI,GAAG,GAAG58B,OAAO,EAAEoC,KAAK,CAAC,CAAA;AAC/D,GAAA;AACF,CAAA;AAEA3E,eAAe,CAAC;AACdqkB,EAAAA,SAAS,EAAE;AACT;AACA6a,IAAAA,GAAG,EAAEt3B,iBAAiB,CAAC,UAAUrF,OAAO,EAAE48B,IAAI,EAAE;AAC9C,MAAA,OAAO,IAAI,CAACrf,GAAG,CAAC,IAAImf,GAAG,EAAE,CAAC,CAACC,GAAG,CAAC38B,OAAO,EAAE48B,IAAI,CAAC,CAAA;KAC9C,CAAA;AACH,GAAA;AACF,CAAC,CAAC,CAAA;AAEFl4B,QAAQ,CAACg4B,GAAG,EAAE,KAAK,CAAC;;AC1BpB;AAgEO,MAAMG,GAAG,GAAGt5B,aAAY;AAsE/B4B,MAAM,CAAC,CAAC6zB,GAAG,EAAEG,MAAM,EAAE9V,KAAK,EAAEH,OAAO,EAAEsB,MAAM,CAAC,EAAErmB,aAAa,CAAC,SAAS,CAAC,CAAC,CAAA;AAEvEgH,MAAM,CAAC,CAACif,IAAI,EAAE8H,QAAQ,EAAEH,OAAO,EAAEF,IAAI,CAAC,EAAE1tB,aAAa,CAAC,QAAQ,CAAC,CAAC,CAAA;AAEhEgH,MAAM,CAACu0B,IAAI,EAAEv7B,aAAa,CAAC,MAAM,CAAC,CAAC,CAAA;AACnCgH,MAAM,CAAC0mB,IAAI,EAAE1tB,aAAa,CAAC,MAAM,CAAC,CAAC,CAAA;AAEnCgH,MAAM,CAAC8c,IAAI,EAAE9jB,aAAa,CAAC,MAAM,CAAC,CAAC,CAAA;AAEnCgH,MAAM,CAAC,CAACu0B,IAAI,EAAEW,KAAK,CAAC,EAAEl8B,aAAa,CAAC,OAAO,CAAC,CAAC,CAAA;AAE7CgH,MAAM,CAAC,CAACinB,IAAI,EAAEjK,OAAO,EAAES,QAAQ,EAAEiP,MAAM,CAAC,EAAE1zB,aAAa,CAAC,QAAQ,CAAC,CAAC,CAAA;AAElEgH,MAAM,CAACuV,WAAW,EAAEvc,aAAa,CAAC,aAAa,CAAC,CAAC,CAAA;AACjDgH,MAAM,CAAC+X,GAAG,EAAE/e,aAAa,CAAC,KAAK,CAAC,CAAC,CAAA;AACjCgH,MAAM,CAACkO,OAAO,EAAElV,aAAa,CAAC,SAAS,CAAC,CAAC,CAAA;AACzCgH,MAAM,CAAC+c,KAAK,EAAE/jB,aAAa,CAAC,OAAO,CAAC,CAAC,CAAA;AACrCgH,MAAM,CAAC,CAAC2c,SAAS,EAAExd,QAAQ,CAAC,EAAEnG,aAAa,CAAC,WAAW,CAAC,CAAC,CAAA;AACzDgH,MAAM,CAACyd,QAAQ,EAAEzkB,aAAa,CAAC,UAAU,CAAC,CAAC,CAAA;AAE3CgH,MAAM,CAAC0sB,MAAM,EAAE1zB,aAAa,CAAC,QAAQ,CAAC,CAAC,CAAA;AAEvC8Z,IAAI,CAAC9S,MAAM,CAAC/G,cAAc,EAAE,CAAC,CAAA;AAE7BqtB,qBAAqB,CAAC,CACpB/P,SAAS,EACTpQ,KAAK,EACLwK,GAAG,EACHzG,MAAM,EACNmM,QAAQ,EACRmI,UAAU,EACViG,SAAS,EACT7a,KAAK,CACN,CAAC,CAAA;AAEF2c,aAAa,EAAE;;;;"}
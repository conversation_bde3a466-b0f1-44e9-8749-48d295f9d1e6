{"version": 3, "file": "svg.min.js", "sources": ["../src/utils/methods.js", "../src/utils/utils.js", "../src/modules/core/namespaces.js", "../src/utils/window.js", "../src/types/Base.js", "../src/utils/adopter.js", "../src/modules/optional/arrange.js", "../src/modules/core/regex.js", "../src/types/Color.js", "../src/modules/optional/class.js", "../src/modules/optional/css.js", "../src/modules/optional/data.js", "../src/modules/optional/memory.js", "../src/types/Point.js", "../src/types/Matrix.js", "../src/modules/core/parser.js", "../src/types/Box.js", "../src/types/List.js", "../src/modules/core/selector.js", "../src/modules/core/event.js", "../src/types/EventTarget.js", "../src/modules/core/defaults.js", "../src/types/SVGArray.js", "../src/types/SVGNumber.js", "../src/modules/core/attr.js", "../src/elements/Dom.js", "../src/elements/Element.js", "../src/modules/optional/sugar.js", "../src/modules/optional/transform.js", "../src/elements/Container.js", "../src/elements/Defs.js", "../src/elements/Shape.js", "../src/modules/core/circled.js", "../src/elements/Ellipse.js", "../src/elements/Fragment.js", "../src/modules/core/gradiented.js", "../src/elements/Gradient.js", "../src/elements/Pattern.js", "../src/elements/Image.js", "../src/types/PointArray.js", "../src/modules/core/pointed.js", "../src/elements/Line.js", "../src/elements/Marker.js", "../src/animation/Controller.js", "../src/utils/pathParser.js", "../src/types/PathArray.js", "../src/animation/Morphable.js", "../src/elements/Path.js", "../src/modules/core/poly.js", "../src/elements/Polygon.js", "../src/elements/Polyline.js", "../src/elements/Rect.js", "../src/animation/Queue.js", "../src/animation/Animator.js", "../src/animation/Timeline.js", "../src/animation/Runner.js", "../src/elements/Svg.js", "../src/elements/Symbol.js", "../src/modules/core/textable.js", "../src/elements/Text.js", "../src/elements/Tspan.js", "../src/elements/Circle.js", "../src/elements/ClipPath.js", "../src/elements/ForeignObject.js", "../src/modules/core/containerGeometry.js", "../src/elements/G.js", "../src/elements/A.js", "../src/elements/Mask.js", "../src/elements/Stop.js", "../src/elements/Style.js", "../src/elements/TextPath.js", "../src/elements/Use.js", "../src/main.js", "../src/svg.js"], "sourcesContent": ["const methods = {}\nconst names = []\n\nexport function registerMethods(name, m) {\n  if (Array.isArray(name)) {\n    for (const _name of name) {\n      registerMethods(_name, m)\n    }\n    return\n  }\n\n  if (typeof name === 'object') {\n    for (const _name in name) {\n      registerMethods(_name, name[_name])\n    }\n    return\n  }\n\n  addMethodNames(Object.getOwnPropertyNames(m))\n  methods[name] = Object.assign(methods[name] || {}, m)\n}\n\nexport function getMethodsFor(name) {\n  return methods[name] || {}\n}\n\nexport function getMethodNames() {\n  return [...new Set(names)]\n}\n\nexport function addMethodNames(_names) {\n  names.push(..._names)\n}\n", "// Map function\nexport function map(array, block) {\n  let i\n  const il = array.length\n  const result = []\n\n  for (i = 0; i < il; i++) {\n    result.push(block(array[i]))\n  }\n\n  return result\n}\n\n// Filter function\nexport function filter(array, block) {\n  let i\n  const il = array.length\n  const result = []\n\n  for (i = 0; i < il; i++) {\n    if (block(array[i])) {\n      result.push(array[i])\n    }\n  }\n\n  return result\n}\n\n// Degrees to radians\nexport function radians(d) {\n  return ((d % 360) * Math.PI) / 180\n}\n\n// Radians to degrees\nexport function degrees(r) {\n  return ((r * 180) / Math.PI) % 360\n}\n\n// Convert camel cased string to dash separated\nexport function unCamelCase(s) {\n  return s.replace(/([A-Z])/g, function (m, g) {\n    return '-' + g.toLowerCase()\n  })\n}\n\n// Capitalize first letter of a string\nexport function capitalize(s) {\n  return s.charAt(0).toUpperCase() + s.slice(1)\n}\n\n// Calculate proportional width and height values when necessary\nexport function proportionalSize(element, width, height, box) {\n  if (width == null || height == null) {\n    box = box || element.bbox()\n\n    if (width == null) {\n      width = (box.width / box.height) * height\n    } else if (height == null) {\n      height = (box.height / box.width) * width\n    }\n  }\n\n  return {\n    width: width,\n    height: height\n  }\n}\n\n/**\n * This function adds support for string origins.\n * It searches for an origin in o.origin o.ox and o.originX.\n * This way, origin: {x: 'center', y: 50} can be passed as well as ox: 'center', oy: 50\n **/\nexport function getOrigin(o, element) {\n  const origin = o.origin\n  // First check if origin is in ox or originX\n  let ox = o.ox != null ? o.ox : o.originX != null ? o.originX : 'center'\n  let oy = o.oy != null ? o.oy : o.originY != null ? o.originY : 'center'\n\n  // Then check if origin was used and overwrite in that case\n  if (origin != null) {\n    ;[ox, oy] = Array.isArray(origin)\n      ? origin\n      : typeof origin === 'object'\n        ? [origin.x, origin.y]\n        : [origin, origin]\n  }\n\n  // Make sure to only call bbox when actually needed\n  const condX = typeof ox === 'string'\n  const condY = typeof oy === 'string'\n  if (condX || condY) {\n    const { height, width, x, y } = element.bbox()\n\n    // And only overwrite if string was passed for this specific axis\n    if (condX) {\n      ox = ox.includes('left')\n        ? x\n        : ox.includes('right')\n          ? x + width\n          : x + width / 2\n    }\n\n    if (condY) {\n      oy = oy.includes('top')\n        ? y\n        : oy.includes('bottom')\n          ? y + height\n          : y + height / 2\n    }\n  }\n\n  // Return the origin as it is if it wasn't a string\n  return [ox, oy]\n}\n\nconst descriptiveElements = new Set(['desc', 'metadata', 'title'])\nexport const isDescriptive = (element) =>\n  descriptiveElements.has(element.nodeName)\n\nexport const writeDataToDom = (element, data, defaults = {}) => {\n  const cloned = { ...data }\n\n  for (const key in cloned) {\n    if (cloned[key].valueOf() === defaults[key]) {\n      delete cloned[key]\n    }\n  }\n\n  if (Object.keys(cloned).length) {\n    element.node.setAttribute('data-svgjs', JSON.stringify(cloned)) // see #428\n  } else {\n    element.node.removeAttribute('data-svgjs')\n    element.node.removeAttribute('svgjs:data')\n  }\n}\n", "// Default namespaces\nexport const svg = 'http://www.w3.org/2000/svg'\nexport const html = 'http://www.w3.org/1999/xhtml'\nexport const xmlns = 'http://www.w3.org/2000/xmlns/'\nexport const xlink = 'http://www.w3.org/1999/xlink'\n", "export const globals = {\n  window: typeof window === 'undefined' ? null : window,\n  document: typeof document === 'undefined' ? null : document\n}\n\nexport function registerWindow(win = null, doc = null) {\n  globals.window = win\n  globals.document = doc\n}\n\nconst save = {}\n\nexport function saveWindow() {\n  save.window = globals.window\n  save.document = globals.document\n}\n\nexport function restoreWindow() {\n  globals.window = save.window\n  globals.document = save.document\n}\n\nexport function withWindow(win, fn) {\n  saveWindow()\n  registerWindow(win, win.document)\n  fn(win, win.document)\n  restoreWindow()\n}\n\nexport function getWindow() {\n  return globals.window\n}\n", "export default class Base {\n  // constructor (node/*, {extensions = []} */) {\n  //   // this.tags = []\n  //   //\n  //   // for (let extension of extensions) {\n  //   //   extension.setup.call(this, node)\n  //   //   this.tags.push(extension.name)\n  //   // }\n  // }\n}\n", "import { addMethodNames } from './methods.js'\nimport { capitalize } from './utils.js'\nimport { svg } from '../modules/core/namespaces.js'\nimport { globals } from '../utils/window.js'\nimport Base from '../types/Base.js'\n\nconst elements = {}\nexport const root = '___SYMBOL___ROOT___'\n\n// Method for element creation\nexport function create(name, ns = svg) {\n  // create element\n  return globals.document.createElementNS(ns, name)\n}\n\nexport function makeInstance(element, isHTML = false) {\n  if (element instanceof Base) return element\n\n  if (typeof element === 'object') {\n    return adopter(element)\n  }\n\n  if (element == null) {\n    return new elements[root]()\n  }\n\n  if (typeof element === 'string' && element.charAt(0) !== '<') {\n    return adopter(globals.document.querySelector(element))\n  }\n\n  // Make sure, that HTML elements are created with the correct namespace\n  const wrapper = isHTML ? globals.document.createElement('div') : create('svg')\n  wrapper.innerHTML = element\n\n  // We can use firstChild here because we know,\n  // that the first char is < and thus an element\n  element = adopter(wrapper.firstChild)\n\n  // make sure, that element doesn't have its wrapper attached\n  wrapper.removeChild(wrapper.firstChild)\n  return element\n}\n\nexport function nodeOrNew(name, node) {\n  return node &&\n    (node instanceof globals.window.Node ||\n      (node.ownerDocument &&\n        node instanceof node.ownerDocument.defaultView.Node))\n    ? node\n    : create(name)\n}\n\n// Adopt existing svg elements\nexport function adopt(node) {\n  // check for presence of node\n  if (!node) return null\n\n  // make sure a node isn't already adopted\n  if (node.instance instanceof Base) return node.instance\n\n  if (node.nodeName === '#document-fragment') {\n    return new elements.Fragment(node)\n  }\n\n  // initialize variables\n  let className = capitalize(node.nodeName || 'Dom')\n\n  // Make sure that gradients are adopted correctly\n  if (className === 'LinearGradient' || className === 'RadialGradient') {\n    className = 'Gradient'\n\n    // Fallback to Dom if element is not known\n  } else if (!elements[className]) {\n    className = 'Dom'\n  }\n\n  return new elements[className](node)\n}\n\nlet adopter = adopt\n\nexport function mockAdopt(mock = adopt) {\n  adopter = mock\n}\n\nexport function register(element, name = element.name, asRoot = false) {\n  elements[name] = element\n  if (asRoot) elements[root] = element\n\n  addMethodNames(Object.getOwnPropertyNames(element.prototype))\n\n  return element\n}\n\nexport function getClass(name) {\n  return elements[name]\n}\n\n// Element id sequence\nlet did = 1000\n\n// Get next named element id\nexport function eid(name) {\n  return 'Svgjs' + capitalize(name) + did++\n}\n\n// Deep new id assignment\nexport function assignNewId(node) {\n  // do the same for SVG child nodes as well\n  for (let i = node.children.length - 1; i >= 0; i--) {\n    assignNewId(node.children[i])\n  }\n\n  if (node.id) {\n    node.id = eid(node.nodeName)\n    return node\n  }\n\n  return node\n}\n\n// Method for extending objects\nexport function extend(modules, methods) {\n  let key, i\n\n  modules = Array.isArray(modules) ? modules : [modules]\n\n  for (i = modules.length - 1; i >= 0; i--) {\n    for (key in methods) {\n      modules[i].prototype[key] = methods[key]\n    }\n  }\n}\n\nexport function wrapWithAttrCheck(fn) {\n  return function (...args) {\n    const o = args[args.length - 1]\n\n    if (o && o.constructor === Object && !(o instanceof Array)) {\n      return fn.apply(this, args.slice(0, -1)).attr(o)\n    } else {\n      return fn.apply(this, args)\n    }\n  }\n}\n", "import { makeInstance } from '../../utils/adopter.js'\nimport { registerMethods } from '../../utils/methods.js'\n\n// Get all siblings, including myself\nexport function siblings() {\n  return this.parent().children()\n}\n\n// Get the current position siblings\nexport function position() {\n  return this.parent().index(this)\n}\n\n// Get the next element (will return null if there is none)\nexport function next() {\n  return this.siblings()[this.position() + 1]\n}\n\n// Get the next element (will return null if there is none)\nexport function prev() {\n  return this.siblings()[this.position() - 1]\n}\n\n// Send given element one step forward\nexport function forward() {\n  const i = this.position()\n  const p = this.parent()\n\n  // move node one step forward\n  p.add(this.remove(), i + 1)\n\n  return this\n}\n\n// Send given element one step backward\nexport function backward() {\n  const i = this.position()\n  const p = this.parent()\n\n  p.add(this.remove(), i ? i - 1 : 0)\n\n  return this\n}\n\n// Send given element all the way to the front\nexport function front() {\n  const p = this.parent()\n\n  // Move node forward\n  p.add(this.remove())\n\n  return this\n}\n\n// Send given element all the way to the back\nexport function back() {\n  const p = this.parent()\n\n  // Move node back\n  p.add(this.remove(), 0)\n\n  return this\n}\n\n// Inserts a given element before the targeted element\nexport function before(element) {\n  element = makeInstance(element)\n  element.remove()\n\n  const i = this.position()\n\n  this.parent().add(element, i)\n\n  return this\n}\n\n// Inserts a given element after the targeted element\nexport function after(element) {\n  element = makeInstance(element)\n  element.remove()\n\n  const i = this.position()\n\n  this.parent().add(element, i + 1)\n\n  return this\n}\n\nexport function insertBefore(element) {\n  element = makeInstance(element)\n  element.before(this)\n  return this\n}\n\nexport function insertAfter(element) {\n  element = makeInstance(element)\n  element.after(this)\n  return this\n}\n\nregisterMethods('Dom', {\n  siblings,\n  position,\n  next,\n  prev,\n  forward,\n  backward,\n  front,\n  back,\n  before,\n  after,\n  insertBefore,\n  insertAfter\n})\n", "// Parse unit value\nexport const numberAndUnit =\n  /^([+-]?(\\d+(\\.\\d*)?|\\.\\d+)(e[+-]?\\d+)?)([a-z%]*)$/i\n\n// Parse hex value\nexport const hex = /^#?([a-f\\d]{2})([a-f\\d]{2})([a-f\\d]{2})$/i\n\n// Parse rgb value\nexport const rgb = /rgb\\((\\d+),(\\d+),(\\d+)\\)/\n\n// Parse reference id\nexport const reference = /(#[a-z_][a-z0-9\\-_]*)/i\n\n// splits a transformation chain\nexport const transforms = /\\)\\s*,?\\s*/\n\n// Whitespace\nexport const whitespace = /\\s/g\n\n// Test hex value\nexport const isHex = /^#[a-f0-9]{3}$|^#[a-f0-9]{6}$/i\n\n// Test rgb value\nexport const isRgb = /^rgb\\(/\n\n// Test for blank string\nexport const isBlank = /^(\\s+)?$/\n\n// Test for numeric string\nexport const isNumber = /^[+-]?(\\d+(\\.\\d*)?|\\.\\d+)(e[+-]?\\d+)?$/i\n\n// Test for image url\nexport const isImage = /\\.(jpg|jpeg|png|gif|svg)(\\?[^=]+.*)?/i\n\n// split at whitespace and comma\nexport const delimiter = /[\\s,]+/\n\n// Test for path letter\nexport const isPathLetter = /[MLHVCSQTAZ]/i\n", "import { hex, isHex, isRgb, rgb, whitespace } from '../modules/core/regex.js'\n\nfunction sixDigitHex(hex) {\n  return hex.length === 4\n    ? [\n        '#',\n        hex.substring(1, 2),\n        hex.substring(1, 2),\n        hex.substring(2, 3),\n        hex.substring(2, 3),\n        hex.substring(3, 4),\n        hex.substring(3, 4)\n      ].join('')\n    : hex\n}\n\nfunction componentHex(component) {\n  const integer = Math.round(component)\n  const bounded = Math.max(0, Math.min(255, integer))\n  const hex = bounded.toString(16)\n  return hex.length === 1 ? '0' + hex : hex\n}\n\nfunction is(object, space) {\n  for (let i = space.length; i--; ) {\n    if (object[space[i]] == null) {\n      return false\n    }\n  }\n  return true\n}\n\nfunction getParameters(a, b) {\n  const params = is(a, 'rgb')\n    ? { _a: a.r, _b: a.g, _c: a.b, _d: 0, space: 'rgb' }\n    : is(a, 'xyz')\n      ? { _a: a.x, _b: a.y, _c: a.z, _d: 0, space: 'xyz' }\n      : is(a, 'hsl')\n        ? { _a: a.h, _b: a.s, _c: a.l, _d: 0, space: 'hsl' }\n        : is(a, 'lab')\n          ? { _a: a.l, _b: a.a, _c: a.b, _d: 0, space: 'lab' }\n          : is(a, 'lch')\n            ? { _a: a.l, _b: a.c, _c: a.h, _d: 0, space: 'lch' }\n            : is(a, 'cmyk')\n              ? { _a: a.c, _b: a.m, _c: a.y, _d: a.k, space: 'cmyk' }\n              : { _a: 0, _b: 0, _c: 0, space: 'rgb' }\n\n  params.space = b || params.space\n  return params\n}\n\nfunction cieSpace(space) {\n  if (space === 'lab' || space === 'xyz' || space === 'lch') {\n    return true\n  } else {\n    return false\n  }\n}\n\nfunction hueToRgb(p, q, t) {\n  if (t < 0) t += 1\n  if (t > 1) t -= 1\n  if (t < 1 / 6) return p + (q - p) * 6 * t\n  if (t < 1 / 2) return q\n  if (t < 2 / 3) return p + (q - p) * (2 / 3 - t) * 6\n  return p\n}\n\nexport default class Color {\n  constructor(...inputs) {\n    this.init(...inputs)\n  }\n\n  // Test if given value is a color\n  static isColor(color) {\n    return (\n      color && (color instanceof Color || this.isRgb(color) || this.test(color))\n    )\n  }\n\n  // Test if given value is an rgb object\n  static isRgb(color) {\n    return (\n      color &&\n      typeof color.r === 'number' &&\n      typeof color.g === 'number' &&\n      typeof color.b === 'number'\n    )\n  }\n\n  /*\n  Generating random colors\n  */\n  static random(mode = 'vibrant', t) {\n    // Get the math modules\n    const { random, round, sin, PI: pi } = Math\n\n    // Run the correct generator\n    if (mode === 'vibrant') {\n      const l = (81 - 57) * random() + 57\n      const c = (83 - 45) * random() + 45\n      const h = 360 * random()\n      const color = new Color(l, c, h, 'lch')\n      return color\n    } else if (mode === 'sine') {\n      t = t == null ? random() : t\n      const r = round(80 * sin((2 * pi * t) / 0.5 + 0.01) + 150)\n      const g = round(50 * sin((2 * pi * t) / 0.5 + 4.6) + 200)\n      const b = round(100 * sin((2 * pi * t) / 0.5 + 2.3) + 150)\n      const color = new Color(r, g, b)\n      return color\n    } else if (mode === 'pastel') {\n      const l = (94 - 86) * random() + 86\n      const c = (26 - 9) * random() + 9\n      const h = 360 * random()\n      const color = new Color(l, c, h, 'lch')\n      return color\n    } else if (mode === 'dark') {\n      const l = 10 + 10 * random()\n      const c = (125 - 75) * random() + 86\n      const h = 360 * random()\n      const color = new Color(l, c, h, 'lch')\n      return color\n    } else if (mode === 'rgb') {\n      const r = 255 * random()\n      const g = 255 * random()\n      const b = 255 * random()\n      const color = new Color(r, g, b)\n      return color\n    } else if (mode === 'lab') {\n      const l = 100 * random()\n      const a = 256 * random() - 128\n      const b = 256 * random() - 128\n      const color = new Color(l, a, b, 'lab')\n      return color\n    } else if (mode === 'grey') {\n      const grey = 255 * random()\n      const color = new Color(grey, grey, grey)\n      return color\n    } else {\n      throw new Error('Unsupported random color mode')\n    }\n  }\n\n  // Test if given value is a color string\n  static test(color) {\n    return typeof color === 'string' && (isHex.test(color) || isRgb.test(color))\n  }\n\n  cmyk() {\n    // Get the rgb values for the current color\n    const { _a, _b, _c } = this.rgb()\n    const [r, g, b] = [_a, _b, _c].map((v) => v / 255)\n\n    // Get the cmyk values in an unbounded format\n    const k = Math.min(1 - r, 1 - g, 1 - b)\n\n    if (k === 1) {\n      // Catch the black case\n      return new Color(0, 0, 0, 1, 'cmyk')\n    }\n\n    const c = (1 - r - k) / (1 - k)\n    const m = (1 - g - k) / (1 - k)\n    const y = (1 - b - k) / (1 - k)\n\n    // Construct the new color\n    const color = new Color(c, m, y, k, 'cmyk')\n    return color\n  }\n\n  hsl() {\n    // Get the rgb values\n    const { _a, _b, _c } = this.rgb()\n    const [r, g, b] = [_a, _b, _c].map((v) => v / 255)\n\n    // Find the maximum and minimum values to get the lightness\n    const max = Math.max(r, g, b)\n    const min = Math.min(r, g, b)\n    const l = (max + min) / 2\n\n    // If the r, g, v values are identical then we are grey\n    const isGrey = max === min\n\n    // Calculate the hue and saturation\n    const delta = max - min\n    const s = isGrey\n      ? 0\n      : l > 0.5\n        ? delta / (2 - max - min)\n        : delta / (max + min)\n    const h = isGrey\n      ? 0\n      : max === r\n        ? ((g - b) / delta + (g < b ? 6 : 0)) / 6\n        : max === g\n          ? ((b - r) / delta + 2) / 6\n          : max === b\n            ? ((r - g) / delta + 4) / 6\n            : 0\n\n    // Construct and return the new color\n    const color = new Color(360 * h, 100 * s, 100 * l, 'hsl')\n    return color\n  }\n\n  init(a = 0, b = 0, c = 0, d = 0, space = 'rgb') {\n    // This catches the case when a falsy value is passed like ''\n    a = !a ? 0 : a\n\n    // Reset all values in case the init function is rerun with new color space\n    if (this.space) {\n      for (const component in this.space) {\n        delete this[this.space[component]]\n      }\n    }\n\n    if (typeof a === 'number') {\n      // Allow for the case that we don't need d...\n      space = typeof d === 'string' ? d : space\n      d = typeof d === 'string' ? 0 : d\n\n      // Assign the values straight to the color\n      Object.assign(this, { _a: a, _b: b, _c: c, _d: d, space })\n      // If the user gave us an array, make the color from it\n    } else if (a instanceof Array) {\n      this.space = b || (typeof a[3] === 'string' ? a[3] : a[4]) || 'rgb'\n      Object.assign(this, { _a: a[0], _b: a[1], _c: a[2], _d: a[3] || 0 })\n    } else if (a instanceof Object) {\n      // Set the object up and assign its values directly\n      const values = getParameters(a, b)\n      Object.assign(this, values)\n    } else if (typeof a === 'string') {\n      if (isRgb.test(a)) {\n        const noWhitespace = a.replace(whitespace, '')\n        const [_a, _b, _c] = rgb\n          .exec(noWhitespace)\n          .slice(1, 4)\n          .map((v) => parseInt(v))\n        Object.assign(this, { _a, _b, _c, _d: 0, space: 'rgb' })\n      } else if (isHex.test(a)) {\n        const hexParse = (v) => parseInt(v, 16)\n        const [, _a, _b, _c] = hex.exec(sixDigitHex(a)).map(hexParse)\n        Object.assign(this, { _a, _b, _c, _d: 0, space: 'rgb' })\n      } else throw Error(\"Unsupported string format, can't construct Color\")\n    }\n\n    // Now add the components as a convenience\n    const { _a, _b, _c, _d } = this\n    const components =\n      this.space === 'rgb'\n        ? { r: _a, g: _b, b: _c }\n        : this.space === 'xyz'\n          ? { x: _a, y: _b, z: _c }\n          : this.space === 'hsl'\n            ? { h: _a, s: _b, l: _c }\n            : this.space === 'lab'\n              ? { l: _a, a: _b, b: _c }\n              : this.space === 'lch'\n                ? { l: _a, c: _b, h: _c }\n                : this.space === 'cmyk'\n                  ? { c: _a, m: _b, y: _c, k: _d }\n                  : {}\n    Object.assign(this, components)\n  }\n\n  lab() {\n    // Get the xyz color\n    const { x, y, z } = this.xyz()\n\n    // Get the lab components\n    const l = 116 * y - 16\n    const a = 500 * (x - y)\n    const b = 200 * (y - z)\n\n    // Construct and return a new color\n    const color = new Color(l, a, b, 'lab')\n    return color\n  }\n\n  lch() {\n    // Get the lab color directly\n    const { l, a, b } = this.lab()\n\n    // Get the chromaticity and the hue using polar coordinates\n    const c = Math.sqrt(a ** 2 + b ** 2)\n    let h = (180 * Math.atan2(b, a)) / Math.PI\n    if (h < 0) {\n      h *= -1\n      h = 360 - h\n    }\n\n    // Make a new color and return it\n    const color = new Color(l, c, h, 'lch')\n    return color\n  }\n  /*\n  Conversion Methods\n  */\n\n  rgb() {\n    if (this.space === 'rgb') {\n      return this\n    } else if (cieSpace(this.space)) {\n      // Convert to the xyz color space\n      let { x, y, z } = this\n      if (this.space === 'lab' || this.space === 'lch') {\n        // Get the values in the lab space\n        let { l, a, b } = this\n        if (this.space === 'lch') {\n          const { c, h } = this\n          const dToR = Math.PI / 180\n          a = c * Math.cos(dToR * h)\n          b = c * Math.sin(dToR * h)\n        }\n\n        // Undo the nonlinear function\n        const yL = (l + 16) / 116\n        const xL = a / 500 + yL\n        const zL = yL - b / 200\n\n        // Get the xyz values\n        const ct = 16 / 116\n        const mx = 0.008856\n        const nm = 7.787\n        x = 0.95047 * (xL ** 3 > mx ? xL ** 3 : (xL - ct) / nm)\n        y = 1.0 * (yL ** 3 > mx ? yL ** 3 : (yL - ct) / nm)\n        z = 1.08883 * (zL ** 3 > mx ? zL ** 3 : (zL - ct) / nm)\n      }\n\n      // Convert xyz to unbounded rgb values\n      const rU = x * 3.2406 + y * -1.5372 + z * -0.4986\n      const gU = x * -0.9689 + y * 1.8758 + z * 0.0415\n      const bU = x * 0.0557 + y * -0.204 + z * 1.057\n\n      // Convert the values to true rgb values\n      const pow = Math.pow\n      const bd = 0.0031308\n      const r = rU > bd ? 1.055 * pow(rU, 1 / 2.4) - 0.055 : 12.92 * rU\n      const g = gU > bd ? 1.055 * pow(gU, 1 / 2.4) - 0.055 : 12.92 * gU\n      const b = bU > bd ? 1.055 * pow(bU, 1 / 2.4) - 0.055 : 12.92 * bU\n\n      // Make and return the color\n      const color = new Color(255 * r, 255 * g, 255 * b)\n      return color\n    } else if (this.space === 'hsl') {\n      // https://bgrins.github.io/TinyColor/docs/tinycolor.html\n      // Get the current hsl values\n      let { h, s, l } = this\n      h /= 360\n      s /= 100\n      l /= 100\n\n      // If we are grey, then just make the color directly\n      if (s === 0) {\n        l *= 255\n        const color = new Color(l, l, l)\n        return color\n      }\n\n      // TODO I have no idea what this does :D If you figure it out, tell me!\n      const q = l < 0.5 ? l * (1 + s) : l + s - l * s\n      const p = 2 * l - q\n\n      // Get the rgb values\n      const r = 255 * hueToRgb(p, q, h + 1 / 3)\n      const g = 255 * hueToRgb(p, q, h)\n      const b = 255 * hueToRgb(p, q, h - 1 / 3)\n\n      // Make a new color\n      const color = new Color(r, g, b)\n      return color\n    } else if (this.space === 'cmyk') {\n      // https://gist.github.com/felipesabino/5066336\n      // Get the normalised cmyk values\n      const { c, m, y, k } = this\n\n      // Get the rgb values\n      const r = 255 * (1 - Math.min(1, c * (1 - k) + k))\n      const g = 255 * (1 - Math.min(1, m * (1 - k) + k))\n      const b = 255 * (1 - Math.min(1, y * (1 - k) + k))\n\n      // Form the color and return it\n      const color = new Color(r, g, b)\n      return color\n    } else {\n      return this\n    }\n  }\n\n  toArray() {\n    const { _a, _b, _c, _d, space } = this\n    return [_a, _b, _c, _d, space]\n  }\n\n  toHex() {\n    const [r, g, b] = this._clamped().map(componentHex)\n    return `#${r}${g}${b}`\n  }\n\n  toRgb() {\n    const [rV, gV, bV] = this._clamped()\n    const string = `rgb(${rV},${gV},${bV})`\n    return string\n  }\n\n  toString() {\n    return this.toHex()\n  }\n\n  xyz() {\n    // Normalise the red, green and blue values\n    const { _a: r255, _b: g255, _c: b255 } = this.rgb()\n    const [r, g, b] = [r255, g255, b255].map((v) => v / 255)\n\n    // Convert to the lab rgb space\n    const rL = r > 0.04045 ? Math.pow((r + 0.055) / 1.055, 2.4) : r / 12.92\n    const gL = g > 0.04045 ? Math.pow((g + 0.055) / 1.055, 2.4) : g / 12.92\n    const bL = b > 0.04045 ? Math.pow((b + 0.055) / 1.055, 2.4) : b / 12.92\n\n    // Convert to the xyz color space without bounding the values\n    const xU = (rL * 0.4124 + gL * 0.3576 + bL * 0.1805) / 0.95047\n    const yU = (rL * 0.2126 + gL * 0.7152 + bL * 0.0722) / 1.0\n    const zU = (rL * 0.0193 + gL * 0.1192 + bL * 0.9505) / 1.08883\n\n    // Get the proper xyz values by applying the bounding\n    const x = xU > 0.008856 ? Math.pow(xU, 1 / 3) : 7.787 * xU + 16 / 116\n    const y = yU > 0.008856 ? Math.pow(yU, 1 / 3) : 7.787 * yU + 16 / 116\n    const z = zU > 0.008856 ? Math.pow(zU, 1 / 3) : 7.787 * zU + 16 / 116\n\n    // Make and return the color\n    const color = new Color(x, y, z, 'xyz')\n    return color\n  }\n\n  /*\n  Input and Output methods\n  */\n\n  _clamped() {\n    const { _a, _b, _c } = this.rgb()\n    const { max, min, round } = Math\n    const format = (v) => max(0, min(round(v), 255))\n    return [_a, _b, _c].map(format)\n  }\n\n  /*\n  Constructing colors\n  */\n}\n", "import { delimiter } from '../core/regex.js'\nimport { registerMethods } from '../../utils/methods.js'\n\n// Return array of classes on the node\nexport function classes() {\n  const attr = this.attr('class')\n  return attr == null ? [] : attr.trim().split(delimiter)\n}\n\n// Return true if class exists on the node, false otherwise\nexport function hasClass(name) {\n  return this.classes().indexOf(name) !== -1\n}\n\n// Add class to the node\nexport function addClass(name) {\n  if (!this.hasClass(name)) {\n    const array = this.classes()\n    array.push(name)\n    this.attr('class', array.join(' '))\n  }\n\n  return this\n}\n\n// Remove class from the node\nexport function removeClass(name) {\n  if (this.hasClass(name)) {\n    this.attr(\n      'class',\n      this.classes()\n        .filter(function (c) {\n          return c !== name\n        })\n        .join(' ')\n    )\n  }\n\n  return this\n}\n\n// Toggle the presence of a class on the node\nexport function toggleClass(name) {\n  return this.hasClass(name) ? this.removeClass(name) : this.addClass(name)\n}\n\nregisterMethods('Dom', {\n  classes,\n  hasClass,\n  addClass,\n  removeClass,\n  toggleClass\n})\n", "import { isBlank } from '../core/regex.js'\nimport { registerMethods } from '../../utils/methods.js'\n\n// Dynamic style generator\nexport function css(style, val) {\n  const ret = {}\n  if (arguments.length === 0) {\n    // get full style as object\n    this.node.style.cssText\n      .split(/\\s*;\\s*/)\n      .filter(function (el) {\n        return !!el.length\n      })\n      .forEach(function (el) {\n        const t = el.split(/\\s*:\\s*/)\n        ret[t[0]] = t[1]\n      })\n    return ret\n  }\n\n  if (arguments.length < 2) {\n    // get style properties as array\n    if (Array.isArray(style)) {\n      for (const name of style) {\n        const cased = name\n        ret[name] = this.node.style.getPropertyValue(cased)\n      }\n      return ret\n    }\n\n    // get style for property\n    if (typeof style === 'string') {\n      return this.node.style.getPropertyValue(style)\n    }\n\n    // set styles in object\n    if (typeof style === 'object') {\n      for (const name in style) {\n        // set empty string if null/undefined/'' was given\n        this.node.style.setProperty(\n          name,\n          style[name] == null || isBlank.test(style[name]) ? '' : style[name]\n        )\n      }\n    }\n  }\n\n  // set style for property\n  if (arguments.length === 2) {\n    this.node.style.setProperty(\n      style,\n      val == null || isBlank.test(val) ? '' : val\n    )\n  }\n\n  return this\n}\n\n// Show element\nexport function show() {\n  return this.css('display', '')\n}\n\n// Hide element\nexport function hide() {\n  return this.css('display', 'none')\n}\n\n// Is element visible?\nexport function visible() {\n  return this.css('display') !== 'none'\n}\n\nregisterMethods('Dom', {\n  css,\n  show,\n  hide,\n  visible\n})\n", "import { registerMethods } from '../../utils/methods.js'\nimport { filter, map } from '../../utils/utils.js'\n\n// Store data values on svg nodes\nexport function data(a, v, r) {\n  if (a == null) {\n    // get an object of attributes\n    return this.data(\n      map(\n        filter(\n          this.node.attributes,\n          (el) => el.nodeName.indexOf('data-') === 0\n        ),\n        (el) => el.nodeName.slice(5)\n      )\n    )\n  } else if (a instanceof Array) {\n    const data = {}\n    for (const key of a) {\n      data[key] = this.data(key)\n    }\n    return data\n  } else if (typeof a === 'object') {\n    for (v in a) {\n      this.data(v, a[v])\n    }\n  } else if (arguments.length < 2) {\n    try {\n      return JSON.parse(this.attr('data-' + a))\n    } catch (e) {\n      return this.attr('data-' + a)\n    }\n  } else {\n    this.attr(\n      'data-' + a,\n      v === null\n        ? null\n        : r === true || typeof v === 'string' || typeof v === 'number'\n          ? v\n          : JSON.stringify(v)\n    )\n  }\n\n  return this\n}\n\nregisterMethods('Dom', { data })\n", "import { registerMethods } from '../../utils/methods.js'\n\n// Remember arbitrary data\nexport function remember(k, v) {\n  // remember every item in an object individually\n  if (typeof arguments[0] === 'object') {\n    for (const key in k) {\n      this.remember(key, k[key])\n    }\n  } else if (arguments.length === 1) {\n    // retrieve memory\n    return this.memory()[k]\n  } else {\n    // store memory\n    this.memory()[k] = v\n  }\n\n  return this\n}\n\n// Erase a given memory\nexport function forget() {\n  if (arguments.length === 0) {\n    this._memory = {}\n  } else {\n    for (let i = arguments.length - 1; i >= 0; i--) {\n      delete this.memory()[arguments[i]]\n    }\n  }\n  return this\n}\n\n// This triggers creation of a new hidden class which is not performant\n// However, this function is not rarely used so it will not happen frequently\n// Return local memory object\nexport function memory() {\n  return (this._memory = this._memory || {})\n}\n\nregisterMethods('Dom', { remember, forget, memory })\n", "import Matrix from './Matrix.js'\n\nexport default class Point {\n  // Initialize\n  constructor(...args) {\n    this.init(...args)\n  }\n\n  // Clone point\n  clone() {\n    return new Point(this)\n  }\n\n  init(x, y) {\n    const base = { x: 0, y: 0 }\n\n    // ensure source as object\n    const source = Array.isArray(x)\n      ? { x: x[0], y: x[1] }\n      : typeof x === 'object'\n        ? { x: x.x, y: x.y }\n        : { x: x, y: y }\n\n    // merge source\n    this.x = source.x == null ? base.x : source.x\n    this.y = source.y == null ? base.y : source.y\n\n    return this\n  }\n\n  toArray() {\n    return [this.x, this.y]\n  }\n\n  transform(m) {\n    return this.clone().transformO(m)\n  }\n\n  // Transform point with matrix\n  transformO(m) {\n    if (!Matrix.isMatrixLike(m)) {\n      m = new Matrix(m)\n    }\n\n    const { x, y } = this\n\n    // Perform the matrix multiplication\n    this.x = m.a * x + m.c * y + m.e\n    this.y = m.b * x + m.d * y + m.f\n\n    return this\n  }\n}\n\nexport function point(x, y) {\n  return new Point(x, y).transformO(this.screenCTM().inverseO())\n}\n", "import { delimiter } from '../modules/core/regex.js'\nimport { radians } from '../utils/utils.js'\nimport { register } from '../utils/adopter.js'\nimport Element from '../elements/Element.js'\nimport Point from './Point.js'\n\nfunction closeEnough(a, b, threshold) {\n  return Math.abs(b - a) < (threshold || 1e-6)\n}\n\nexport default class Matrix {\n  constructor(...args) {\n    this.init(...args)\n  }\n\n  static formatTransforms(o) {\n    // Get all of the parameters required to form the matrix\n    const flipBoth = o.flip === 'both' || o.flip === true\n    const flipX = o.flip && (flipBoth || o.flip === 'x') ? -1 : 1\n    const flipY = o.flip && (flipBoth || o.flip === 'y') ? -1 : 1\n    const skewX =\n      o.skew && o.skew.length\n        ? o.skew[0]\n        : isFinite(o.skew)\n          ? o.skew\n          : isFinite(o.skewX)\n            ? o.skewX\n            : 0\n    const skewY =\n      o.skew && o.skew.length\n        ? o.skew[1]\n        : isFinite(o.skew)\n          ? o.skew\n          : isFinite(o.skewY)\n            ? o.skewY\n            : 0\n    const scaleX =\n      o.scale && o.scale.length\n        ? o.scale[0] * flipX\n        : isFinite(o.scale)\n          ? o.scale * flipX\n          : isFinite(o.scaleX)\n            ? o.scaleX * flipX\n            : flipX\n    const scaleY =\n      o.scale && o.scale.length\n        ? o.scale[1] * flipY\n        : isFinite(o.scale)\n          ? o.scale * flipY\n          : isFinite(o.scaleY)\n            ? o.scaleY * flipY\n            : flipY\n    const shear = o.shear || 0\n    const theta = o.rotate || o.theta || 0\n    const origin = new Point(\n      o.origin || o.around || o.ox || o.originX,\n      o.oy || o.originY\n    )\n    const ox = origin.x\n    const oy = origin.y\n    // We need Point to be invalid if nothing was passed because we cannot default to 0 here. That is why NaN\n    const position = new Point(\n      o.position || o.px || o.positionX || NaN,\n      o.py || o.positionY || NaN\n    )\n    const px = position.x\n    const py = position.y\n    const translate = new Point(\n      o.translate || o.tx || o.translateX,\n      o.ty || o.translateY\n    )\n    const tx = translate.x\n    const ty = translate.y\n    const relative = new Point(\n      o.relative || o.rx || o.relativeX,\n      o.ry || o.relativeY\n    )\n    const rx = relative.x\n    const ry = relative.y\n\n    // Populate all of the values\n    return {\n      scaleX,\n      scaleY,\n      skewX,\n      skewY,\n      shear,\n      theta,\n      rx,\n      ry,\n      tx,\n      ty,\n      ox,\n      oy,\n      px,\n      py\n    }\n  }\n\n  static fromArray(a) {\n    return { a: a[0], b: a[1], c: a[2], d: a[3], e: a[4], f: a[5] }\n  }\n\n  static isMatrixLike(o) {\n    return (\n      o.a != null ||\n      o.b != null ||\n      o.c != null ||\n      o.d != null ||\n      o.e != null ||\n      o.f != null\n    )\n  }\n\n  // left matrix, right matrix, target matrix which is overwritten\n  static matrixMultiply(l, r, o) {\n    // Work out the product directly\n    const a = l.a * r.a + l.c * r.b\n    const b = l.b * r.a + l.d * r.b\n    const c = l.a * r.c + l.c * r.d\n    const d = l.b * r.c + l.d * r.d\n    const e = l.e + l.a * r.e + l.c * r.f\n    const f = l.f + l.b * r.e + l.d * r.f\n\n    // make sure to use local variables because l/r and o could be the same\n    o.a = a\n    o.b = b\n    o.c = c\n    o.d = d\n    o.e = e\n    o.f = f\n\n    return o\n  }\n\n  around(cx, cy, matrix) {\n    return this.clone().aroundO(cx, cy, matrix)\n  }\n\n  // Transform around a center point\n  aroundO(cx, cy, matrix) {\n    const dx = cx || 0\n    const dy = cy || 0\n    return this.translateO(-dx, -dy).lmultiplyO(matrix).translateO(dx, dy)\n  }\n\n  // Clones this matrix\n  clone() {\n    return new Matrix(this)\n  }\n\n  // Decomposes this matrix into its affine parameters\n  decompose(cx = 0, cy = 0) {\n    // Get the parameters from the matrix\n    const a = this.a\n    const b = this.b\n    const c = this.c\n    const d = this.d\n    const e = this.e\n    const f = this.f\n\n    // Figure out if the winding direction is clockwise or counterclockwise\n    const determinant = a * d - b * c\n    const ccw = determinant > 0 ? 1 : -1\n\n    // Since we only shear in x, we can use the x basis to get the x scale\n    // and the rotation of the resulting matrix\n    const sx = ccw * Math.sqrt(a * a + b * b)\n    const thetaRad = Math.atan2(ccw * b, ccw * a)\n    const theta = (180 / Math.PI) * thetaRad\n    const ct = Math.cos(thetaRad)\n    const st = Math.sin(thetaRad)\n\n    // We can then solve the y basis vector simultaneously to get the other\n    // two affine parameters directly from these parameters\n    const lam = (a * c + b * d) / determinant\n    const sy = (c * sx) / (lam * a - b) || (d * sx) / (lam * b + a)\n\n    // Use the translations\n    const tx = e - cx + cx * ct * sx + cy * (lam * ct * sx - st * sy)\n    const ty = f - cy + cx * st * sx + cy * (lam * st * sx + ct * sy)\n\n    // Construct the decomposition and return it\n    return {\n      // Return the affine parameters\n      scaleX: sx,\n      scaleY: sy,\n      shear: lam,\n      rotate: theta,\n      translateX: tx,\n      translateY: ty,\n      originX: cx,\n      originY: cy,\n\n      // Return the matrix parameters\n      a: this.a,\n      b: this.b,\n      c: this.c,\n      d: this.d,\n      e: this.e,\n      f: this.f\n    }\n  }\n\n  // Check if two matrices are equal\n  equals(other) {\n    if (other === this) return true\n    const comp = new Matrix(other)\n    return (\n      closeEnough(this.a, comp.a) &&\n      closeEnough(this.b, comp.b) &&\n      closeEnough(this.c, comp.c) &&\n      closeEnough(this.d, comp.d) &&\n      closeEnough(this.e, comp.e) &&\n      closeEnough(this.f, comp.f)\n    )\n  }\n\n  // Flip matrix on x or y, at a given offset\n  flip(axis, around) {\n    return this.clone().flipO(axis, around)\n  }\n\n  flipO(axis, around) {\n    return axis === 'x'\n      ? this.scaleO(-1, 1, around, 0)\n      : axis === 'y'\n        ? this.scaleO(1, -1, 0, around)\n        : this.scaleO(-1, -1, axis, around || axis) // Define an x, y flip point\n  }\n\n  // Initialize\n  init(source) {\n    const base = Matrix.fromArray([1, 0, 0, 1, 0, 0])\n\n    // ensure source as object\n    source =\n      source instanceof Element\n        ? source.matrixify()\n        : typeof source === 'string'\n          ? Matrix.fromArray(source.split(delimiter).map(parseFloat))\n          : Array.isArray(source)\n            ? Matrix.fromArray(source)\n            : typeof source === 'object' && Matrix.isMatrixLike(source)\n              ? source\n              : typeof source === 'object'\n                ? new Matrix().transform(source)\n                : arguments.length === 6\n                  ? Matrix.fromArray([].slice.call(arguments))\n                  : base\n\n    // Merge the source matrix with the base matrix\n    this.a = source.a != null ? source.a : base.a\n    this.b = source.b != null ? source.b : base.b\n    this.c = source.c != null ? source.c : base.c\n    this.d = source.d != null ? source.d : base.d\n    this.e = source.e != null ? source.e : base.e\n    this.f = source.f != null ? source.f : base.f\n\n    return this\n  }\n\n  inverse() {\n    return this.clone().inverseO()\n  }\n\n  // Inverses matrix\n  inverseO() {\n    // Get the current parameters out of the matrix\n    const a = this.a\n    const b = this.b\n    const c = this.c\n    const d = this.d\n    const e = this.e\n    const f = this.f\n\n    // Invert the 2x2 matrix in the top left\n    const det = a * d - b * c\n    if (!det) throw new Error('Cannot invert ' + this)\n\n    // Calculate the top 2x2 matrix\n    const na = d / det\n    const nb = -b / det\n    const nc = -c / det\n    const nd = a / det\n\n    // Apply the inverted matrix to the top right\n    const ne = -(na * e + nc * f)\n    const nf = -(nb * e + nd * f)\n\n    // Construct the inverted matrix\n    this.a = na\n    this.b = nb\n    this.c = nc\n    this.d = nd\n    this.e = ne\n    this.f = nf\n\n    return this\n  }\n\n  lmultiply(matrix) {\n    return this.clone().lmultiplyO(matrix)\n  }\n\n  lmultiplyO(matrix) {\n    const r = this\n    const l = matrix instanceof Matrix ? matrix : new Matrix(matrix)\n\n    return Matrix.matrixMultiply(l, r, this)\n  }\n\n  // Left multiplies by the given matrix\n  multiply(matrix) {\n    return this.clone().multiplyO(matrix)\n  }\n\n  multiplyO(matrix) {\n    // Get the matrices\n    const l = this\n    const r = matrix instanceof Matrix ? matrix : new Matrix(matrix)\n\n    return Matrix.matrixMultiply(l, r, this)\n  }\n\n  // Rotate matrix\n  rotate(r, cx, cy) {\n    return this.clone().rotateO(r, cx, cy)\n  }\n\n  rotateO(r, cx = 0, cy = 0) {\n    // Convert degrees to radians\n    r = radians(r)\n\n    const cos = Math.cos(r)\n    const sin = Math.sin(r)\n\n    const { a, b, c, d, e, f } = this\n\n    this.a = a * cos - b * sin\n    this.b = b * cos + a * sin\n    this.c = c * cos - d * sin\n    this.d = d * cos + c * sin\n    this.e = e * cos - f * sin + cy * sin - cx * cos + cx\n    this.f = f * cos + e * sin - cx * sin - cy * cos + cy\n\n    return this\n  }\n\n  // Scale matrix\n  scale() {\n    return this.clone().scaleO(...arguments)\n  }\n\n  scaleO(x, y = x, cx = 0, cy = 0) {\n    // Support uniform scaling\n    if (arguments.length === 3) {\n      cy = cx\n      cx = y\n      y = x\n    }\n\n    const { a, b, c, d, e, f } = this\n\n    this.a = a * x\n    this.b = b * y\n    this.c = c * x\n    this.d = d * y\n    this.e = e * x - cx * x + cx\n    this.f = f * y - cy * y + cy\n\n    return this\n  }\n\n  // Shear matrix\n  shear(a, cx, cy) {\n    return this.clone().shearO(a, cx, cy)\n  }\n\n  // eslint-disable-next-line no-unused-vars\n  shearO(lx, cx = 0, cy = 0) {\n    const { a, b, c, d, e, f } = this\n\n    this.a = a + b * lx\n    this.c = c + d * lx\n    this.e = e + f * lx - cy * lx\n\n    return this\n  }\n\n  // Skew Matrix\n  skew() {\n    return this.clone().skewO(...arguments)\n  }\n\n  skewO(x, y = x, cx = 0, cy = 0) {\n    // support uniformal skew\n    if (arguments.length === 3) {\n      cy = cx\n      cx = y\n      y = x\n    }\n\n    // Convert degrees to radians\n    x = radians(x)\n    y = radians(y)\n\n    const lx = Math.tan(x)\n    const ly = Math.tan(y)\n\n    const { a, b, c, d, e, f } = this\n\n    this.a = a + b * lx\n    this.b = b + a * ly\n    this.c = c + d * lx\n    this.d = d + c * ly\n    this.e = e + f * lx - cy * lx\n    this.f = f + e * ly - cx * ly\n\n    return this\n  }\n\n  // SkewX\n  skewX(x, cx, cy) {\n    return this.skew(x, 0, cx, cy)\n  }\n\n  // SkewY\n  skewY(y, cx, cy) {\n    return this.skew(0, y, cx, cy)\n  }\n\n  toArray() {\n    return [this.a, this.b, this.c, this.d, this.e, this.f]\n  }\n\n  // Convert matrix to string\n  toString() {\n    return (\n      'matrix(' +\n      this.a +\n      ',' +\n      this.b +\n      ',' +\n      this.c +\n      ',' +\n      this.d +\n      ',' +\n      this.e +\n      ',' +\n      this.f +\n      ')'\n    )\n  }\n\n  // Transform a matrix into another matrix by manipulating the space\n  transform(o) {\n    // Check if o is a matrix and then left multiply it directly\n    if (Matrix.isMatrixLike(o)) {\n      const matrix = new Matrix(o)\n      return matrix.multiplyO(this)\n    }\n\n    // Get the proposed transformations and the current transformations\n    const t = Matrix.formatTransforms(o)\n    const current = this\n    const { x: ox, y: oy } = new Point(t.ox, t.oy).transform(current)\n\n    // Construct the resulting matrix\n    const transformer = new Matrix()\n      .translateO(t.rx, t.ry)\n      .lmultiplyO(current)\n      .translateO(-ox, -oy)\n      .scaleO(t.scaleX, t.scaleY)\n      .skewO(t.skewX, t.skewY)\n      .shearO(t.shear)\n      .rotateO(t.theta)\n      .translateO(ox, oy)\n\n    // If we want the origin at a particular place, we force it there\n    if (isFinite(t.px) || isFinite(t.py)) {\n      const origin = new Point(ox, oy).transform(transformer)\n      // TODO: Replace t.px with isFinite(t.px)\n      // Doesn't work because t.px is also 0 if it wasn't passed\n      const dx = isFinite(t.px) ? t.px - origin.x : 0\n      const dy = isFinite(t.py) ? t.py - origin.y : 0\n      transformer.translateO(dx, dy)\n    }\n\n    // Translate now after positioning\n    transformer.translateO(t.tx, t.ty)\n    return transformer\n  }\n\n  // Translate matrix\n  translate(x, y) {\n    return this.clone().translateO(x, y)\n  }\n\n  translateO(x, y) {\n    this.e += x || 0\n    this.f += y || 0\n    return this\n  }\n\n  valueOf() {\n    return {\n      a: this.a,\n      b: this.b,\n      c: this.c,\n      d: this.d,\n      e: this.e,\n      f: this.f\n    }\n  }\n}\n\nexport function ctm() {\n  return new Matrix(this.node.getCTM())\n}\n\nexport function screenCTM() {\n  try {\n    /* https://bugzilla.mozilla.org/show_bug.cgi?id=1344537\n       This is needed because FF does not return the transformation matrix\n       for the inner coordinate system when getScreenCTM() is called on nested svgs.\n       However all other Browsers do that */\n    if (typeof this.isRoot === 'function' && !this.isRoot()) {\n      const rect = this.rect(1, 1)\n      const m = rect.node.getScreenCTM()\n      rect.remove()\n      return new Matrix(m)\n    }\n    return new Matrix(this.node.getScreenCTM())\n  } catch (e) {\n    console.warn(\n      `Cannot get CTM from SVG node ${this.node.nodeName}. Is the element rendered?`\n    )\n    return new Matrix()\n  }\n}\n\nregister(Matrix, 'Matrix')\n", "import { globals } from '../../utils/window.js'\nimport { makeInstance } from '../../utils/adopter.js'\n\nexport default function parser() {\n  // Reuse cached element if possible\n  if (!parser.nodes) {\n    const svg = makeInstance().size(2, 0)\n    svg.node.style.cssText = [\n      'opacity: 0',\n      'position: absolute',\n      'left: -100%',\n      'top: -100%',\n      'overflow: hidden'\n    ].join(';')\n\n    svg.attr('focusable', 'false')\n    svg.attr('aria-hidden', 'true')\n\n    const path = svg.path().node\n\n    parser.nodes = { svg, path }\n  }\n\n  if (!parser.nodes.svg.node.parentNode) {\n    const b = globals.document.body || globals.document.documentElement\n    parser.nodes.svg.addTo(b)\n  }\n\n  return parser.nodes\n}\n", "import { delimiter } from '../modules/core/regex.js'\nimport { globals } from '../utils/window.js'\nimport { register } from '../utils/adopter.js'\nimport { registerMethods } from '../utils/methods.js'\nimport Matrix from './Matrix.js'\nimport Point from './Point.js'\nimport parser from '../modules/core/parser.js'\n\nexport function isNulledBox(box) {\n  return !box.width && !box.height && !box.x && !box.y\n}\n\nexport function domContains(node) {\n  return (\n    node === globals.document ||\n    (\n      globals.document.documentElement.contains ||\n      function (node) {\n        // This is IE - it does not support contains() for top-level SVGs\n        while (node.parentNode) {\n          node = node.parentNode\n        }\n        return node === globals.document\n      }\n    ).call(globals.document.documentElement, node)\n  )\n}\n\nexport default class Box {\n  constructor(...args) {\n    this.init(...args)\n  }\n\n  addOffset() {\n    // offset by window scroll position, because getBoundingClientRect changes when window is scrolled\n    this.x += globals.window.pageXOffset\n    this.y += globals.window.pageYOffset\n    return new Box(this)\n  }\n\n  init(source) {\n    const base = [0, 0, 0, 0]\n    source =\n      typeof source === 'string'\n        ? source.split(delimiter).map(parseFloat)\n        : Array.isArray(source)\n          ? source\n          : typeof source === 'object'\n            ? [\n                source.left != null ? source.left : source.x,\n                source.top != null ? source.top : source.y,\n                source.width,\n                source.height\n              ]\n            : arguments.length === 4\n              ? [].slice.call(arguments)\n              : base\n\n    this.x = source[0] || 0\n    this.y = source[1] || 0\n    this.width = this.w = source[2] || 0\n    this.height = this.h = source[3] || 0\n\n    // Add more bounding box properties\n    this.x2 = this.x + this.w\n    this.y2 = this.y + this.h\n    this.cx = this.x + this.w / 2\n    this.cy = this.y + this.h / 2\n\n    return this\n  }\n\n  isNulled() {\n    return isNulledBox(this)\n  }\n\n  // Merge rect box with another, return a new instance\n  merge(box) {\n    const x = Math.min(this.x, box.x)\n    const y = Math.min(this.y, box.y)\n    const width = Math.max(this.x + this.width, box.x + box.width) - x\n    const height = Math.max(this.y + this.height, box.y + box.height) - y\n\n    return new Box(x, y, width, height)\n  }\n\n  toArray() {\n    return [this.x, this.y, this.width, this.height]\n  }\n\n  toString() {\n    return this.x + ' ' + this.y + ' ' + this.width + ' ' + this.height\n  }\n\n  transform(m) {\n    if (!(m instanceof Matrix)) {\n      m = new Matrix(m)\n    }\n\n    let xMin = Infinity\n    let xMax = -Infinity\n    let yMin = Infinity\n    let yMax = -Infinity\n\n    const pts = [\n      new Point(this.x, this.y),\n      new Point(this.x2, this.y),\n      new Point(this.x, this.y2),\n      new Point(this.x2, this.y2)\n    ]\n\n    pts.forEach(function (p) {\n      p = p.transform(m)\n      xMin = Math.min(xMin, p.x)\n      xMax = Math.max(xMax, p.x)\n      yMin = Math.min(yMin, p.y)\n      yMax = Math.max(yMax, p.y)\n    })\n\n    return new Box(xMin, yMin, xMax - xMin, yMax - yMin)\n  }\n}\n\nfunction getBox(el, getBBoxFn, retry) {\n  let box\n\n  try {\n    // Try to get the box with the provided function\n    box = getBBoxFn(el.node)\n\n    // If the box is worthless and not even in the dom, retry\n    // by throwing an error here...\n    if (isNulledBox(box) && !domContains(el.node)) {\n      throw new Error('Element not in the dom')\n    }\n  } catch (e) {\n    // ... and calling the retry handler here\n    box = retry(el)\n  }\n\n  return box\n}\n\nexport function bbox() {\n  // Function to get bbox is getBBox()\n  const getBBox = (node) => node.getBBox()\n\n  // Take all measures so that a stupid browser renders the element\n  // so we can get the bbox from it when we try again\n  const retry = (el) => {\n    try {\n      const clone = el.clone().addTo(parser().svg).show()\n      const box = clone.node.getBBox()\n      clone.remove()\n      return box\n    } catch (e) {\n      // We give up...\n      throw new Error(\n        `Getting bbox of element \"${\n          el.node.nodeName\n        }\" is not possible: ${e.toString()}`\n      )\n    }\n  }\n\n  const box = getBox(this, getBBox, retry)\n  const bbox = new Box(box)\n\n  return bbox\n}\n\nexport function rbox(el) {\n  const getRBox = (node) => node.getBoundingClientRect()\n  const retry = (el) => {\n    // There is no point in trying tricks here because if we insert the element into the dom ourselves\n    // it obviously will be at the wrong position\n    throw new Error(\n      `Getting rbox of element \"${el.node.nodeName}\" is not possible`\n    )\n  }\n\n  const box = getBox(this, getRBox, retry)\n  const rbox = new Box(box)\n\n  // If an element was passed, we want the bbox in the coordinate system of that element\n  if (el) {\n    return rbox.transform(el.screenCTM().inverseO())\n  }\n\n  // Else we want it in absolute screen coordinates\n  // Therefore we need to add the scrollOffset\n  return rbox.addOffset()\n}\n\n// Checks whether the given point is inside the bounding box\nexport function inside(x, y) {\n  const box = this.bbox()\n\n  return (\n    x > box.x && y > box.y && x < box.x + box.width && y < box.y + box.height\n  )\n}\n\nregisterMethods({\n  viewbox: {\n    viewbox(x, y, width, height) {\n      // act as getter\n      if (x == null) return new Box(this.attr('viewBox'))\n\n      // act as setter\n      return this.attr('viewBox', new Box(x, y, width, height))\n    },\n\n    zoom(level, point) {\n      // Its best to rely on the attributes here and here is why:\n      // clientXYZ: Doesn't work on non-root svgs because they dont have a CSSBox (silly!)\n      // getBoundingClientRect: Doesn't work because Chrome just ignores width and height of nested svgs completely\n      //                        that means, their clientRect is always as big as the content.\n      //                        Furthermore this size is incorrect if the element is further transformed by its parents\n      // computedStyle: Only returns meaningful values if css was used with px. We dont go this route here!\n      // getBBox: returns the bounding box of its content - that doesn't help!\n      let { width, height } = this.attr(['width', 'height'])\n\n      // Width and height is a string when a number with a unit is present which we can't use\n      // So we try clientXYZ\n      if (\n        (!width && !height) ||\n        typeof width === 'string' ||\n        typeof height === 'string'\n      ) {\n        width = this.node.clientWidth\n        height = this.node.clientHeight\n      }\n\n      // Giving up...\n      if (!width || !height) {\n        throw new Error(\n          'Impossible to get absolute width and height. Please provide an absolute width and height attribute on the zooming element'\n        )\n      }\n\n      const v = this.viewbox()\n\n      const zoomX = width / v.width\n      const zoomY = height / v.height\n      const zoom = Math.min(zoomX, zoomY)\n\n      if (level == null) {\n        return zoom\n      }\n\n      let zoomAmount = zoom / level\n\n      // Set the zoomAmount to the highest value which is safe to process and recover from\n      // The * 100 is a bit of wiggle room for the matrix transformation\n      if (zoomAmount === Infinity) zoomAmount = Number.MAX_SAFE_INTEGER / 100\n\n      point =\n        point || new Point(width / 2 / zoomX + v.x, height / 2 / zoomY + v.y)\n\n      const box = new Box(v).transform(\n        new Matrix({ scale: zoomAmount, origin: point })\n      )\n\n      return this.viewbox(box)\n    }\n  }\n})\n\nregister(Box, 'Box')\n", "import { extend } from '../utils/adopter.js'\n// import { subClassArray } from './ArrayPolyfill.js'\n\nclass List extends Array {\n  constructor(arr = [], ...args) {\n    super(arr, ...args)\n    if (typeof arr === 'number') return this\n    this.length = 0\n    this.push(...arr)\n  }\n}\n\n/* = subClassArray('List', Array, function (arr = []) {\n  // This catches the case, that native map tries to create an array with new Array(1)\n  if (typeof arr === 'number') return this\n  this.length = 0\n  this.push(...arr)\n}) */\n\nexport default List\n\nextend([List], {\n  each(fnOrMethodName, ...args) {\n    if (typeof fnOrMethodName === 'function') {\n      return this.map((el, i, arr) => {\n        return fnOrMethodName.call(el, el, i, arr)\n      })\n    } else {\n      return this.map((el) => {\n        return el[fnOrMethodName](...args)\n      })\n    }\n  },\n\n  toArray() {\n    return Array.prototype.concat.apply([], this)\n  }\n})\n\nconst reserved = ['toArray', 'constructor', 'each']\n\nList.extend = function (methods) {\n  methods = methods.reduce((obj, name) => {\n    // Don't overwrite own methods\n    if (reserved.includes(name)) return obj\n\n    // Don't add private methods\n    if (name[0] === '_') return obj\n\n    // Allow access to original Array methods through a prefix\n    if (name in Array.prototype) {\n      obj['$' + name] = Array.prototype[name]\n    }\n\n    // Relay every call to each()\n    obj[name] = function (...attrs) {\n      return this.each(name, ...attrs)\n    }\n    return obj\n  }, {})\n\n  extend([List], methods)\n}\n", "import { adopt } from '../../utils/adopter.js'\nimport { globals } from '../../utils/window.js'\nimport { map } from '../../utils/utils.js'\nimport List from '../../types/List.js'\n\nexport default function baseFind(query, parent) {\n  return new List(\n    map((parent || globals.document).querySelectorAll(query), function (node) {\n      return adopt(node)\n    })\n  )\n}\n\n// Scoped find method\nexport function find(query) {\n  return baseFind(query, this.node)\n}\n\nexport function findOne(query) {\n  return adopt(this.node.querySelector(query))\n}\n", "import { delimiter } from './regex.js'\nimport { makeInstance } from '../../utils/adopter.js'\nimport { globals } from '../../utils/window.js'\n\nlet listenerId = 0\nexport const windowEvents = {}\n\nexport function getEvents(instance) {\n  let n = instance.getEventHolder()\n\n  // We dont want to save events in global space\n  if (n === globals.window) n = windowEvents\n  if (!n.events) n.events = {}\n  return n.events\n}\n\nexport function getEventTarget(instance) {\n  return instance.getEventTarget()\n}\n\nexport function clearEvents(instance) {\n  let n = instance.getEventHolder()\n  if (n === globals.window) n = windowEvents\n  if (n.events) n.events = {}\n}\n\n// Add event binder in the SVG namespace\nexport function on(node, events, listener, binding, options) {\n  const l = listener.bind(binding || node)\n  const instance = makeInstance(node)\n  const bag = getEvents(instance)\n  const n = getEventTarget(instance)\n\n  // events can be an array of events or a string of events\n  events = Array.isArray(events) ? events : events.split(delimiter)\n\n  // add id to listener\n  if (!listener._svgjsListenerId) {\n    listener._svgjsListenerId = ++listenerId\n  }\n\n  events.forEach(function (event) {\n    const ev = event.split('.')[0]\n    const ns = event.split('.')[1] || '*'\n\n    // ensure valid object\n    bag[ev] = bag[ev] || {}\n    bag[ev][ns] = bag[ev][ns] || {}\n\n    // reference listener\n    bag[ev][ns][listener._svgjsListenerId] = l\n\n    // add listener\n    n.addEventListener(ev, l, options || false)\n  })\n}\n\n// Add event unbinder in the SVG namespace\nexport function off(node, events, listener, options) {\n  const instance = makeInstance(node)\n  const bag = getEvents(instance)\n  const n = getEventTarget(instance)\n\n  // listener can be a function or a number\n  if (typeof listener === 'function') {\n    listener = listener._svgjsListenerId\n    if (!listener) return\n  }\n\n  // events can be an array of events or a string or undefined\n  events = Array.isArray(events) ? events : (events || '').split(delimiter)\n\n  events.forEach(function (event) {\n    const ev = event && event.split('.')[0]\n    const ns = event && event.split('.')[1]\n    let namespace, l\n\n    if (listener) {\n      // remove listener reference\n      if (bag[ev] && bag[ev][ns || '*']) {\n        // removeListener\n        n.removeEventListener(\n          ev,\n          bag[ev][ns || '*'][listener],\n          options || false\n        )\n\n        delete bag[ev][ns || '*'][listener]\n      }\n    } else if (ev && ns) {\n      // remove all listeners for a namespaced event\n      if (bag[ev] && bag[ev][ns]) {\n        for (l in bag[ev][ns]) {\n          off(n, [ev, ns].join('.'), l)\n        }\n\n        delete bag[ev][ns]\n      }\n    } else if (ns) {\n      // remove all listeners for a specific namespace\n      for (event in bag) {\n        for (namespace in bag[event]) {\n          if (ns === namespace) {\n            off(n, [event, ns].join('.'))\n          }\n        }\n      }\n    } else if (ev) {\n      // remove all listeners for the event\n      if (bag[ev]) {\n        for (namespace in bag[ev]) {\n          off(n, [ev, namespace].join('.'))\n        }\n\n        delete bag[ev]\n      }\n    } else {\n      // remove all listeners on a given node\n      for (event in bag) {\n        off(n, event)\n      }\n\n      clearEvents(instance)\n    }\n  })\n}\n\nexport function dispatch(node, event, data, options) {\n  const n = getEventTarget(node)\n\n  // Dispatch event\n  if (event instanceof globals.window.Event) {\n    n.dispatchEvent(event)\n  } else {\n    event = new globals.window.CustomEvent(event, {\n      detail: data,\n      cancelable: true,\n      ...options\n    })\n    n.dispatchEvent(event)\n  }\n  return event\n}\n", "import { dispatch, off, on } from '../modules/core/event.js'\nimport { register } from '../utils/adopter.js'\nimport Base from './Base.js'\n\nexport default class EventTarget extends Base {\n  addEventListener() {}\n\n  dispatch(event, data, options) {\n    return dispatch(this, event, data, options)\n  }\n\n  dispatchEvent(event) {\n    const bag = this.getEventHolder().events\n    if (!bag) return true\n\n    const events = bag[event.type]\n\n    for (const i in events) {\n      for (const j in events[i]) {\n        events[i][j](event)\n      }\n    }\n\n    return !event.defaultPrevented\n  }\n\n  // Fire given event\n  fire(event, data, options) {\n    this.dispatch(event, data, options)\n    return this\n  }\n\n  getEventHolder() {\n    return this\n  }\n\n  getEventTarget() {\n    return this\n  }\n\n  // Unbind event from listener\n  off(event, listener, options) {\n    off(this, event, listener, options)\n    return this\n  }\n\n  // Bind given event to listener\n  on(event, listener, binding, options) {\n    on(this, event, listener, binding, options)\n    return this\n  }\n\n  removeEventListener() {}\n}\n\nregister(EventTarget, 'EventTarget')\n", "export function noop() {}\n\n// Default animation values\nexport const timeline = {\n  duration: 400,\n  ease: '>',\n  delay: 0\n}\n\n// Default attribute values\nexport const attrs = {\n  // fill and stroke\n  'fill-opacity': 1,\n  'stroke-opacity': 1,\n  'stroke-width': 0,\n  'stroke-linejoin': 'miter',\n  'stroke-linecap': 'butt',\n  fill: '#000000',\n  stroke: '#000000',\n  opacity: 1,\n\n  // position\n  x: 0,\n  y: 0,\n  cx: 0,\n  cy: 0,\n\n  // size\n  width: 0,\n  height: 0,\n\n  // radius\n  r: 0,\n  rx: 0,\n  ry: 0,\n\n  // gradient\n  offset: 0,\n  'stop-opacity': 1,\n  'stop-color': '#000000',\n\n  // text\n  'text-anchor': 'start'\n}\n", "import { delimiter } from '../modules/core/regex.js'\n\nexport default class SVGArray extends Array {\n  constructor(...args) {\n    super(...args)\n    this.init(...args)\n  }\n\n  clone() {\n    return new this.constructor(this)\n  }\n\n  init(arr) {\n    // This catches the case, that native map tries to create an array with new Array(1)\n    if (typeof arr === 'number') return this\n    this.length = 0\n    this.push(...this.parse(arr))\n    return this\n  }\n\n  // Parse whitespace separated string\n  parse(array = []) {\n    // If already is an array, no need to parse it\n    if (array instanceof Array) return array\n\n    return array.trim().split(delimiter).map(parseFloat)\n  }\n\n  toArray() {\n    return Array.prototype.concat.apply([], this)\n  }\n\n  toSet() {\n    return new Set(this)\n  }\n\n  toString() {\n    return this.join(' ')\n  }\n\n  // Flattens the array if needed\n  valueOf() {\n    const ret = []\n    ret.push(...this)\n    return ret\n  }\n}\n", "import { numberAndUnit } from '../modules/core/regex.js'\n\n// Module for unit conversions\nexport default class SVGNumber {\n  // Initialize\n  constructor(...args) {\n    this.init(...args)\n  }\n\n  convert(unit) {\n    return new SVGNumber(this.value, unit)\n  }\n\n  // Divide number\n  divide(number) {\n    number = new SVGNumber(number)\n    return new SVGNumber(this / number, this.unit || number.unit)\n  }\n\n  init(value, unit) {\n    unit = Array.isArray(value) ? value[1] : unit\n    value = Array.isArray(value) ? value[0] : value\n\n    // initialize defaults\n    this.value = 0\n    this.unit = unit || ''\n\n    // parse value\n    if (typeof value === 'number') {\n      // ensure a valid numeric value\n      this.value = isNaN(value)\n        ? 0\n        : !isFinite(value)\n          ? value < 0\n            ? -3.4e38\n            : +3.4e38\n          : value\n    } else if (typeof value === 'string') {\n      unit = value.match(numberAndUnit)\n\n      if (unit) {\n        // make value numeric\n        this.value = parseFloat(unit[1])\n\n        // normalize\n        if (unit[5] === '%') {\n          this.value /= 100\n        } else if (unit[5] === 's') {\n          this.value *= 1000\n        }\n\n        // store unit\n        this.unit = unit[5]\n      }\n    } else {\n      if (value instanceof SVGNumber) {\n        this.value = value.valueOf()\n        this.unit = value.unit\n      }\n    }\n\n    return this\n  }\n\n  // Subtract number\n  minus(number) {\n    number = new SVGNumber(number)\n    return new SVGNumber(this - number, this.unit || number.unit)\n  }\n\n  // Add number\n  plus(number) {\n    number = new SVGNumber(number)\n    return new SVGNumber(this + number, this.unit || number.unit)\n  }\n\n  // Multiply number\n  times(number) {\n    number = new SVGNumber(number)\n    return new SVGNumber(this * number, this.unit || number.unit)\n  }\n\n  toArray() {\n    return [this.value, this.unit]\n  }\n\n  toJSON() {\n    return this.toString()\n  }\n\n  toString() {\n    return (\n      (this.unit === '%'\n        ? ~~(this.value * 1e8) / 1e6\n        : this.unit === 's'\n          ? this.value / 1e3\n          : this.value) + this.unit\n    )\n  }\n\n  valueOf() {\n    return this.value\n  }\n}\n", "import { attrs as defaults } from './defaults.js'\nimport { isNumber } from './regex.js'\nimport Color from '../../types/Color.js'\nimport SVGArray from '../../types/SVGArray.js'\nimport SVGNumber from '../../types/SVGNumber.js'\n\nconst colorAttributes = new Set([\n  'fill',\n  'stroke',\n  'color',\n  'bgcolor',\n  'stop-color',\n  'flood-color',\n  'lighting-color'\n])\n\nconst hooks = []\nexport function registerAttrHook(fn) {\n  hooks.push(fn)\n}\n\n// Set svg element attribute\nexport default function attr(attr, val, ns) {\n  // act as full getter\n  if (attr == null) {\n    // get an object of attributes\n    attr = {}\n    val = this.node.attributes\n\n    for (const node of val) {\n      attr[node.nodeName] = isNumber.test(node.nodeValue)\n        ? parseFloat(node.nodeValue)\n        : node.nodeValue\n    }\n\n    return attr\n  } else if (attr instanceof Array) {\n    // loop through array and get all values\n    return attr.reduce((last, curr) => {\n      last[curr] = this.attr(curr)\n      return last\n    }, {})\n  } else if (typeof attr === 'object' && attr.constructor === Object) {\n    // apply every attribute individually if an object is passed\n    for (val in attr) this.attr(val, attr[val])\n  } else if (val === null) {\n    // remove value\n    this.node.removeAttribute(attr)\n  } else if (val == null) {\n    // act as a getter if the first and only argument is not an object\n    val = this.node.getAttribute(attr)\n    return val == null\n      ? defaults[attr]\n      : isNumber.test(val)\n        ? parseFloat(val)\n        : val\n  } else {\n    // Loop through hooks and execute them to convert value\n    val = hooks.reduce((_val, hook) => {\n      return hook(attr, _val, this)\n    }, val)\n\n    // ensure correct numeric values (also accepts NaN and Infinity)\n    if (typeof val === 'number') {\n      val = new SVGNumber(val)\n    } else if (colorAttributes.has(attr) && Color.isColor(val)) {\n      // ensure full hex color\n      val = new Color(val)\n    } else if (val.constructor === Array) {\n      // Check for plain arrays and parse array values\n      val = new SVGArray(val)\n    }\n\n    // if the passed attribute is leading...\n    if (attr === 'leading') {\n      // ... call the leading method instead\n      if (this.leading) {\n        this.leading(val)\n      }\n    } else {\n      // set given attribute on node\n      typeof ns === 'string'\n        ? this.node.setAttributeNS(ns, attr, val.toString())\n        : this.node.setAttribute(attr, val.toString())\n    }\n\n    // rebuild if required\n    if (this.rebuild && (attr === 'font-size' || attr === 'x')) {\n      this.rebuild()\n    }\n  }\n\n  return this\n}\n", "import {\n  adopt,\n  assignNewId,\n  eid,\n  extend,\n  makeInstance,\n  create,\n  register\n} from '../utils/adopter.js'\nimport { find, findOne } from '../modules/core/selector.js'\nimport { globals } from '../utils/window.js'\nimport { map } from '../utils/utils.js'\nimport { svg, html } from '../modules/core/namespaces.js'\nimport EventTarget from '../types/EventTarget.js'\nimport List from '../types/List.js'\nimport attr from '../modules/core/attr.js'\n\nexport default class Dom extends EventTarget {\n  constructor(node, attrs) {\n    super()\n    this.node = node\n    this.type = node.nodeName\n\n    if (attrs && node !== attrs) {\n      this.attr(attrs)\n    }\n  }\n\n  // Add given element at a position\n  add(element, i) {\n    element = makeInstance(element)\n\n    // If non-root svg nodes are added we have to remove their namespaces\n    if (\n      element.removeNamespace &&\n      this.node instanceof globals.window.SVGElement\n    ) {\n      element.removeNamespace()\n    }\n\n    if (i == null) {\n      this.node.appendChild(element.node)\n    } else if (element.node !== this.node.childNodes[i]) {\n      this.node.insertBefore(element.node, this.node.childNodes[i])\n    }\n\n    return this\n  }\n\n  // Add element to given container and return self\n  addTo(parent, i) {\n    return makeInstance(parent).put(this, i)\n  }\n\n  // Returns all child elements\n  children() {\n    return new List(\n      map(this.node.children, function (node) {\n        return adopt(node)\n      })\n    )\n  }\n\n  // Remove all elements in this container\n  clear() {\n    // remove children\n    while (this.node.hasChildNodes()) {\n      this.node.removeChild(this.node.lastChild)\n    }\n\n    return this\n  }\n\n  // Clone element\n  clone(deep = true, assignNewIds = true) {\n    // write dom data to the dom so the clone can pickup the data\n    this.writeDataToDom()\n\n    // clone element\n    let nodeClone = this.node.cloneNode(deep)\n    if (assignNewIds) {\n      // assign new id\n      nodeClone = assignNewId(nodeClone)\n    }\n    return new this.constructor(nodeClone)\n  }\n\n  // Iterates over all children and invokes a given block\n  each(block, deep) {\n    const children = this.children()\n    let i, il\n\n    for (i = 0, il = children.length; i < il; i++) {\n      block.apply(children[i], [i, children])\n\n      if (deep) {\n        children[i].each(block, deep)\n      }\n    }\n\n    return this\n  }\n\n  element(nodeName, attrs) {\n    return this.put(new Dom(create(nodeName), attrs))\n  }\n\n  // Get first child\n  first() {\n    return adopt(this.node.firstChild)\n  }\n\n  // Get a element at the given index\n  get(i) {\n    return adopt(this.node.childNodes[i])\n  }\n\n  getEventHolder() {\n    return this.node\n  }\n\n  getEventTarget() {\n    return this.node\n  }\n\n  // Checks if the given element is a child\n  has(element) {\n    return this.index(element) >= 0\n  }\n\n  html(htmlOrFn, outerHTML) {\n    return this.xml(htmlOrFn, outerHTML, html)\n  }\n\n  // Get / set id\n  id(id) {\n    // generate new id if no id set\n    if (typeof id === 'undefined' && !this.node.id) {\n      this.node.id = eid(this.type)\n    }\n\n    // don't set directly with this.node.id to make `null` work correctly\n    return this.attr('id', id)\n  }\n\n  // Gets index of given element\n  index(element) {\n    return [].slice.call(this.node.childNodes).indexOf(element.node)\n  }\n\n  // Get the last child\n  last() {\n    return adopt(this.node.lastChild)\n  }\n\n  // matches the element vs a css selector\n  matches(selector) {\n    const el = this.node\n    const matcher =\n      el.matches ||\n      el.matchesSelector ||\n      el.msMatchesSelector ||\n      el.mozMatchesSelector ||\n      el.webkitMatchesSelector ||\n      el.oMatchesSelector ||\n      null\n    return matcher && matcher.call(el, selector)\n  }\n\n  // Returns the parent element instance\n  parent(type) {\n    let parent = this\n\n    // check for parent\n    if (!parent.node.parentNode) return null\n\n    // get parent element\n    parent = adopt(parent.node.parentNode)\n\n    if (!type) return parent\n\n    // loop through ancestors if type is given\n    do {\n      if (\n        typeof type === 'string' ? parent.matches(type) : parent instanceof type\n      )\n        return parent\n    } while ((parent = adopt(parent.node.parentNode)))\n\n    return parent\n  }\n\n  // Basically does the same as `add()` but returns the added element instead\n  put(element, i) {\n    element = makeInstance(element)\n    this.add(element, i)\n    return element\n  }\n\n  // Add element to given container and return container\n  putIn(parent, i) {\n    return makeInstance(parent).add(this, i)\n  }\n\n  // Remove element\n  remove() {\n    if (this.parent()) {\n      this.parent().removeElement(this)\n    }\n\n    return this\n  }\n\n  // Remove a given child\n  removeElement(element) {\n    this.node.removeChild(element.node)\n\n    return this\n  }\n\n  // Replace this with element\n  replace(element) {\n    element = makeInstance(element)\n\n    if (this.node.parentNode) {\n      this.node.parentNode.replaceChild(element.node, this.node)\n    }\n\n    return element\n  }\n\n  round(precision = 2, map = null) {\n    const factor = 10 ** precision\n    const attrs = this.attr(map)\n\n    for (const i in attrs) {\n      if (typeof attrs[i] === 'number') {\n        attrs[i] = Math.round(attrs[i] * factor) / factor\n      }\n    }\n\n    this.attr(attrs)\n    return this\n  }\n\n  // Import / Export raw svg\n  svg(svgOrFn, outerSVG) {\n    return this.xml(svgOrFn, outerSVG, svg)\n  }\n\n  // Return id on string conversion\n  toString() {\n    return this.id()\n  }\n\n  words(text) {\n    // This is faster than removing all children and adding a new one\n    this.node.textContent = text\n    return this\n  }\n\n  wrap(node) {\n    const parent = this.parent()\n\n    if (!parent) {\n      return this.addTo(node)\n    }\n\n    const position = parent.index(this)\n    return parent.put(node, position).put(this)\n  }\n\n  // write svgjs data to the dom\n  writeDataToDom() {\n    // dump variables recursively\n    this.each(function () {\n      this.writeDataToDom()\n    })\n\n    return this\n  }\n\n  // Import / Export raw svg\n  xml(xmlOrFn, outerXML, ns) {\n    if (typeof xmlOrFn === 'boolean') {\n      ns = outerXML\n      outerXML = xmlOrFn\n      xmlOrFn = null\n    }\n\n    // act as getter if no svg string is given\n    if (xmlOrFn == null || typeof xmlOrFn === 'function') {\n      // The default for exports is, that the outerNode is included\n      outerXML = outerXML == null ? true : outerXML\n\n      // write svgjs data to the dom\n      this.writeDataToDom()\n      let current = this\n\n      // An export modifier was passed\n      if (xmlOrFn != null) {\n        current = adopt(current.node.cloneNode(true))\n\n        // If the user wants outerHTML we need to process this node, too\n        if (outerXML) {\n          const result = xmlOrFn(current)\n          current = result || current\n\n          // The user does not want this node? Well, then he gets nothing\n          if (result === false) return ''\n        }\n\n        // Deep loop through all children and apply modifier\n        current.each(function () {\n          const result = xmlOrFn(this)\n          const _this = result || this\n\n          // If modifier returns false, discard node\n          if (result === false) {\n            this.remove()\n\n            // If modifier returns new node, use it\n          } else if (result && this !== _this) {\n            this.replace(_this)\n          }\n        }, true)\n      }\n\n      // Return outer or inner content\n      return outerXML ? current.node.outerHTML : current.node.innerHTML\n    }\n\n    // Act as setter if we got a string\n\n    // The default for import is, that the current node is not replaced\n    outerXML = outerXML == null ? false : outerXML\n\n    // Create temporary holder\n    const well = create('wrapper', ns)\n    const fragment = globals.document.createDocumentFragment()\n\n    // Dump raw svg\n    well.innerHTML = xmlOrFn\n\n    // Transplant nodes into the fragment\n    for (let len = well.children.length; len--; ) {\n      fragment.appendChild(well.firstElementChild)\n    }\n\n    const parent = this.parent()\n\n    // Add the whole fragment at once\n    return outerXML ? this.replace(fragment) && parent : this.add(fragment)\n  }\n}\n\nextend(Dom, { attr, find, findOne })\nregister(Dom, 'Dom')\n", "import { bbox, rbox, inside } from '../types/Box.js'\nimport { ctm, screenCTM } from '../types/Matrix.js'\nimport {\n  extend,\n  getClass,\n  makeInstance,\n  register,\n  root\n} from '../utils/adopter.js'\nimport { globals } from '../utils/window.js'\nimport { point } from '../types/Point.js'\nimport { proportionalSize, writeDataToDom } from '../utils/utils.js'\nimport { reference } from '../modules/core/regex.js'\nimport Dom from './Dom.js'\nimport List from '../types/List.js'\nimport SVGNumber from '../types/SVGNumber.js'\n\nexport default class Element extends Dom {\n  constructor(node, attrs) {\n    super(node, attrs)\n\n    // initialize data object\n    this.dom = {}\n\n    // create circular reference\n    this.node.instance = this\n\n    if (node.hasAttribute('data-svgjs') || node.hasAttribute('svgjs:data')) {\n      // pull svgjs data from the dom (getAttributeNS doesn't work in html5)\n      this.setData(\n        JSON.parse(node.getAttribute('data-svgjs')) ??\n          JSON.parse(node.getAttribute('svgjs:data')) ??\n          {}\n      )\n    }\n  }\n\n  // Move element by its center\n  center(x, y) {\n    return this.cx(x).cy(y)\n  }\n\n  // Move by center over x-axis\n  cx(x) {\n    return x == null\n      ? this.x() + this.width() / 2\n      : this.x(x - this.width() / 2)\n  }\n\n  // Move by center over y-axis\n  cy(y) {\n    return y == null\n      ? this.y() + this.height() / 2\n      : this.y(y - this.height() / 2)\n  }\n\n  // Get defs\n  defs() {\n    const root = this.root()\n    return root && root.defs()\n  }\n\n  // Relative move over x and y axes\n  dmove(x, y) {\n    return this.dx(x).dy(y)\n  }\n\n  // Relative move over x axis\n  dx(x = 0) {\n    return this.x(new SVGNumber(x).plus(this.x()))\n  }\n\n  // Relative move over y axis\n  dy(y = 0) {\n    return this.y(new SVGNumber(y).plus(this.y()))\n  }\n\n  getEventHolder() {\n    return this\n  }\n\n  // Set height of element\n  height(height) {\n    return this.attr('height', height)\n  }\n\n  // Move element to given x and y values\n  move(x, y) {\n    return this.x(x).y(y)\n  }\n\n  // return array of all ancestors of given type up to the root svg\n  parents(until = this.root()) {\n    const isSelector = typeof until === 'string'\n    if (!isSelector) {\n      until = makeInstance(until)\n    }\n    const parents = new List()\n    let parent = this\n\n    while (\n      (parent = parent.parent()) &&\n      parent.node !== globals.document &&\n      parent.nodeName !== '#document-fragment'\n    ) {\n      parents.push(parent)\n\n      if (!isSelector && parent.node === until.node) {\n        break\n      }\n      if (isSelector && parent.matches(until)) {\n        break\n      }\n      if (parent.node === this.root().node) {\n        // We worked our way to the root and didn't match `until`\n        return null\n      }\n    }\n\n    return parents\n  }\n\n  // Get referenced element form attribute value\n  reference(attr) {\n    attr = this.attr(attr)\n    if (!attr) return null\n\n    const m = (attr + '').match(reference)\n    return m ? makeInstance(m[1]) : null\n  }\n\n  // Get parent document\n  root() {\n    const p = this.parent(getClass(root))\n    return p && p.root()\n  }\n\n  // set given data to the elements data property\n  setData(o) {\n    this.dom = o\n    return this\n  }\n\n  // Set element size to given width and height\n  size(width, height) {\n    const p = proportionalSize(this, width, height)\n\n    return this.width(new SVGNumber(p.width)).height(new SVGNumber(p.height))\n  }\n\n  // Set width of element\n  width(width) {\n    return this.attr('width', width)\n  }\n\n  // write svgjs data to the dom\n  writeDataToDom() {\n    writeDataToDom(this, this.dom)\n    return super.writeDataToDom()\n  }\n\n  // Move over x-axis\n  x(x) {\n    return this.attr('x', x)\n  }\n\n  // Move over y-axis\n  y(y) {\n    return this.attr('y', y)\n  }\n}\n\nextend(Element, {\n  bbox,\n  rbox,\n  inside,\n  point,\n  ctm,\n  screenCTM\n})\n\nregister(Element, 'Element')\n", "import { registerMethods } from '../../utils/methods.js'\nimport Color from '../../types/Color.js'\nimport Element from '../../elements/Element.js'\nimport Matrix from '../../types/Matrix.js'\nimport Point from '../../types/Point.js'\nimport SVGNumber from '../../types/SVGNumber.js'\n\n// Define list of available attributes for stroke and fill\nconst sugar = {\n  stroke: [\n    'color',\n    'width',\n    'opacity',\n    'linecap',\n    'linejoin',\n    'miterlimit',\n    'dasharray',\n    'dashoffset'\n  ],\n  fill: ['color', 'opacity', 'rule'],\n  prefix: function (t, a) {\n    return a === 'color' ? t : t + '-' + a\n  }\n}\n\n// Add sugar for fill and stroke\n;['fill', 'stroke'].forEach(function (m) {\n  const extension = {}\n  let i\n\n  extension[m] = function (o) {\n    if (typeof o === 'undefined') {\n      return this.attr(m)\n    }\n    if (\n      typeof o === 'string' ||\n      o instanceof Color ||\n      Color.isRgb(o) ||\n      o instanceof Element\n    ) {\n      this.attr(m, o)\n    } else {\n      // set all attributes from sugar.fill and sugar.stroke list\n      for (i = sugar[m].length - 1; i >= 0; i--) {\n        if (o[sugar[m][i]] != null) {\n          this.attr(sugar.prefix(m, sugar[m][i]), o[sugar[m][i]])\n        }\n      }\n    }\n\n    return this\n  }\n\n  registerMethods(['Element', 'Runner'], extension)\n})\n\nregisterMethods(['Element', 'Runner'], {\n  // Let the user set the matrix directly\n  matrix: function (mat, b, c, d, e, f) {\n    // Act as a getter\n    if (mat == null) {\n      return new Matrix(this)\n    }\n\n    // Act as a setter, the user can pass a matrix or a set of numbers\n    return this.attr('transform', new Matrix(mat, b, c, d, e, f))\n  },\n\n  // Map rotation to transform\n  rotate: function (angle, cx, cy) {\n    return this.transform({ rotate: angle, ox: cx, oy: cy }, true)\n  },\n\n  // Map skew to transform\n  skew: function (x, y, cx, cy) {\n    return arguments.length === 1 || arguments.length === 3\n      ? this.transform({ skew: x, ox: y, oy: cx }, true)\n      : this.transform({ skew: [x, y], ox: cx, oy: cy }, true)\n  },\n\n  shear: function (lam, cx, cy) {\n    return this.transform({ shear: lam, ox: cx, oy: cy }, true)\n  },\n\n  // Map scale to transform\n  scale: function (x, y, cx, cy) {\n    return arguments.length === 1 || arguments.length === 3\n      ? this.transform({ scale: x, ox: y, oy: cx }, true)\n      : this.transform({ scale: [x, y], ox: cx, oy: cy }, true)\n  },\n\n  // Map translate to transform\n  translate: function (x, y) {\n    return this.transform({ translate: [x, y] }, true)\n  },\n\n  // Map relative translations to transform\n  relative: function (x, y) {\n    return this.transform({ relative: [x, y] }, true)\n  },\n\n  // Map flip to transform\n  flip: function (direction = 'both', origin = 'center') {\n    if ('xybothtrue'.indexOf(direction) === -1) {\n      origin = direction\n      direction = 'both'\n    }\n\n    return this.transform({ flip: direction, origin: origin }, true)\n  },\n\n  // Opacity\n  opacity: function (value) {\n    return this.attr('opacity', value)\n  }\n})\n\nregisterMethods('radius', {\n  // Add x and y radius\n  radius: function (x, y = x) {\n    const type = (this._element || this).type\n    return type === 'radialGradient'\n      ? this.attr('r', new SVGNumber(x))\n      : this.rx(x).ry(y)\n  }\n})\n\nregisterMethods('Path', {\n  // Get path length\n  length: function () {\n    return this.node.getTotalLength()\n  },\n  // Get point at length\n  pointAt: function (length) {\n    return new Point(this.node.getPointAtLength(length))\n  }\n})\n\nregisterMethods(['Element', 'Runner'], {\n  // Set font\n  font: function (a, v) {\n    if (typeof a === 'object') {\n      for (v in a) this.font(v, a[v])\n      return this\n    }\n\n    return a === 'leading'\n      ? this.leading(v)\n      : a === 'anchor'\n        ? this.attr('text-anchor', v)\n        : a === 'size' ||\n            a === 'family' ||\n            a === 'weight' ||\n            a === 'stretch' ||\n            a === 'variant' ||\n            a === 'style'\n          ? this.attr('font-' + a, v)\n          : this.attr(a, v)\n  }\n})\n\n// Add events to elements\nconst methods = [\n  'click',\n  'dblclick',\n  'mousedown',\n  'mouseup',\n  'mouseover',\n  'mouseout',\n  'mousemove',\n  'mouseenter',\n  'mouseleave',\n  'touchstart',\n  'touchmove',\n  'touchleave',\n  'touchend',\n  'touchcancel',\n  'contextmenu',\n  'wheel',\n  'pointerdown',\n  'pointermove',\n  'pointerup',\n  'pointerleave',\n  'pointercancel'\n].reduce(function (last, event) {\n  // add event to Element\n  const fn = function (f) {\n    if (f === null) {\n      this.off(event)\n    } else {\n      this.on(event, f)\n    }\n    return this\n  }\n\n  last[event] = fn\n  return last\n}, {})\n\nregisterMethods('Element', methods)\n", "import { getOrigin, isDescriptive } from '../../utils/utils.js'\nimport { delimiter, transforms } from '../core/regex.js'\nimport { registerMethods } from '../../utils/methods.js'\nimport Matrix from '../../types/Matrix.js'\n\n// Reset all transformations\nexport function untransform() {\n  return this.attr('transform', null)\n}\n\n// merge the whole transformation chain into one matrix and returns it\nexport function matrixify() {\n  const matrix = (this.attr('transform') || '')\n    // split transformations\n    .split(transforms)\n    .slice(0, -1)\n    .map(function (str) {\n      // generate key => value pairs\n      const kv = str.trim().split('(')\n      return [\n        kv[0],\n        kv[1].split(delimiter).map(function (str) {\n          return parseFloat(str)\n        })\n      ]\n    })\n    .reverse()\n    // merge every transformation into one matrix\n    .reduce(function (matrix, transform) {\n      if (transform[0] === 'matrix') {\n        return matrix.lmultiply(Matrix.fromArray(transform[1]))\n      }\n      return matrix[transform[0]].apply(matrix, transform[1])\n    }, new Matrix())\n\n  return matrix\n}\n\n// add an element to another parent without changing the visual representation on the screen\nexport function toParent(parent, i) {\n  if (this === parent) return this\n\n  if (isDescriptive(this.node)) return this.addTo(parent, i)\n\n  const ctm = this.screenCTM()\n  const pCtm = parent.screenCTM().inverse()\n\n  this.addTo(parent, i).untransform().transform(pCtm.multiply(ctm))\n\n  return this\n}\n\n// same as above with parent equals root-svg\nexport function toRoot(i) {\n  return this.toParent(this.root(), i)\n}\n\n// Add transformations\nexport function transform(o, relative) {\n  // Act as a getter if no object was passed\n  if (o == null || typeof o === 'string') {\n    const decomposed = new Matrix(this).decompose()\n    return o == null ? decomposed : decomposed[o]\n  }\n\n  if (!Matrix.isMatrixLike(o)) {\n    // Set the origin according to the defined transform\n    o = { ...o, origin: getOrigin(o, this) }\n  }\n\n  // The user can pass a boolean, an Element or an Matrix or nothing\n  const cleanRelative = relative === true ? this : relative || false\n  const result = new Matrix(cleanRelative).transform(o)\n  return this.attr('transform', result)\n}\n\nregisterMethods('Element', {\n  untransform,\n  matrixify,\n  toParent,\n  toRoot,\n  transform\n})\n", "import { register } from '../utils/adopter.js'\nimport Element from './Element.js'\n\nexport default class Container extends Element {\n  flatten() {\n    this.each(function () {\n      if (this instanceof Container) {\n        return this.flatten().ungroup()\n      }\n    })\n\n    return this\n  }\n\n  ungroup(parent = this.parent(), index = parent.index(this)) {\n    // when parent != this, we want append all elements to the end\n    index = index === -1 ? parent.children().length : index\n\n    this.each(function (i, children) {\n      // reverse each\n      return children[children.length - i - 1].toParent(parent, index)\n    })\n\n    return this.remove()\n  }\n}\n\nregister(Container, 'Container')\n", "import { nodeOrNew, register } from '../utils/adopter.js'\nimport Container from './Container.js'\n\nexport default class Defs extends Container {\n  constructor(node, attrs = node) {\n    super(nodeOrNew('defs', node), attrs)\n  }\n\n  flatten() {\n    return this\n  }\n\n  ungroup() {\n    return this\n  }\n}\n\nregister(Defs, 'Defs')\n", "import { register } from '../utils/adopter.js'\nimport Element from './Element.js'\n\nexport default class Shape extends Element {}\n\nregister(Shape, 'Shape')\n", "import SVGNumber from '../../types/SVGNumber.js'\n\n// Radius x value\nexport function rx(rx) {\n  return this.attr('rx', rx)\n}\n\n// Radius y value\nexport function ry(ry) {\n  return this.attr('ry', ry)\n}\n\n// Move over x-axis\nexport function x(x) {\n  return x == null ? this.cx() - this.rx() : this.cx(x + this.rx())\n}\n\n// Move over y-axis\nexport function y(y) {\n  return y == null ? this.cy() - this.ry() : this.cy(y + this.ry())\n}\n\n// Move by center over x-axis\nexport function cx(x) {\n  return this.attr('cx', x)\n}\n\n// Move by center over y-axis\nexport function cy(y) {\n  return this.attr('cy', y)\n}\n\n// Set width of element\nexport function width(width) {\n  return width == null ? this.rx() * 2 : this.rx(new SVGNumber(width).divide(2))\n}\n\n// Set height of element\nexport function height(height) {\n  return height == null\n    ? this.ry() * 2\n    : this.ry(new SVGNumber(height).divide(2))\n}\n", "import {\n  extend,\n  nodeOrNew,\n  register,\n  wrapWithAttrCheck\n} from '../utils/adopter.js'\nimport { proportionalSize } from '../utils/utils.js'\nimport { registerMethods } from '../utils/methods.js'\nimport SVGNumber from '../types/SVGNumber.js'\nimport Shape from './Shape.js'\nimport * as circled from '../modules/core/circled.js'\n\nexport default class Ellipse extends Shape {\n  constructor(node, attrs = node) {\n    super(nodeOrNew('ellipse', node), attrs)\n  }\n\n  size(width, height) {\n    const p = proportionalSize(this, width, height)\n\n    return this.rx(new SVGNumber(p.width).divide(2)).ry(\n      new SVGNumber(p.height).divide(2)\n    )\n  }\n}\n\nextend(Ellipse, circled)\n\nregisterMethods('Container', {\n  // Create an ellipse\n  ellipse: wrapWithAttrCheck(function (width = 0, height = width) {\n    return this.put(new Ellipse()).size(width, height).move(0, 0)\n  })\n})\n\nregister(Ellipse, 'Ellipse')\n", "import Dom from './Dom.js'\nimport { globals } from '../utils/window.js'\nimport { register, create } from '../utils/adopter.js'\n\nclass Fragment extends Dom {\n  constructor(node = globals.document.createDocumentFragment()) {\n    super(node)\n  }\n\n  // Import / Export raw xml\n  xml(xmlOrFn, outerXML, ns) {\n    if (typeof xmlOrFn === 'boolean') {\n      ns = outerXML\n      outerXML = xmlOrFn\n      xmlOrFn = null\n    }\n\n    // because this is a fragment we have to put all elements into a wrapper first\n    // before we can get the innerXML from it\n    if (xmlOrFn == null || typeof xmlOrFn === 'function') {\n      const wrapper = new Dom(create('wrapper', ns))\n      wrapper.add(this.node.cloneNode(true))\n\n      return wrapper.xml(false, ns)\n    }\n\n    // Act as setter if we got a string\n    return super.xml(xmlOrFn, false, ns)\n  }\n}\n\nregister(Fragment, 'Fragment')\n\nexport default Fragment\n", "import SVGNumber from '../../types/SVGNumber.js'\n\nexport function from(x, y) {\n  return (this._element || this).type === 'radialGradient'\n    ? this.attr({ fx: new SVGNumber(x), fy: new SVGNumber(y) })\n    : this.attr({ x1: new SVGNumber(x), y1: new SVGNumber(y) })\n}\n\nexport function to(x, y) {\n  return (this._element || this).type === 'radialGradient'\n    ? this.attr({ cx: new SVGNumber(x), cy: new SVGNumber(y) })\n    : this.attr({ x2: new SVGNumber(x), y2: new SVGNumber(y) })\n}\n", "import {\n  extend,\n  nodeOrNew,\n  register,\n  wrapWithAttrCheck\n} from '../utils/adopter.js'\nimport { registerMethods } from '../utils/methods.js'\nimport Box from '../types/Box.js'\nimport Container from './Container.js'\nimport baseFind from '../modules/core/selector.js'\nimport * as gradiented from '../modules/core/gradiented.js'\n\nexport default class Gradient extends Container {\n  constructor(type, attrs) {\n    super(\n      nodeOrNew(type + 'Gradient', typeof type === 'string' ? null : type),\n      attrs\n    )\n  }\n\n  // custom attr to handle transform\n  attr(a, b, c) {\n    if (a === 'transform') a = 'gradientTransform'\n    return super.attr(a, b, c)\n  }\n\n  bbox() {\n    return new Box()\n  }\n\n  targets() {\n    return baseFind('svg [fill*=' + this.id() + ']')\n  }\n\n  // Alias string conversion to fill\n  toString() {\n    return this.url()\n  }\n\n  // Update gradient\n  update(block) {\n    // remove all stops\n    this.clear()\n\n    // invoke passed block\n    if (typeof block === 'function') {\n      block.call(this, this)\n    }\n\n    return this\n  }\n\n  // Return the fill id\n  url() {\n    return 'url(#' + this.id() + ')'\n  }\n}\n\nextend(Gradient, gradiented)\n\nregisterMethods({\n  Container: {\n    // Create gradient element in defs\n    gradient(...args) {\n      return this.defs().gradient(...args)\n    }\n  },\n  // define gradient\n  Defs: {\n    gradient: wrapWithAttrCheck(function (type, block) {\n      return this.put(new Gradient(type)).update(block)\n    })\n  }\n})\n\nregister(Gradient, 'Gradient')\n", "import { nodeOrNew, register, wrapWithAttrCheck } from '../utils/adopter.js'\nimport { registerMethods } from '../utils/methods.js'\nimport Box from '../types/Box.js'\nimport Container from './Container.js'\nimport baseFind from '../modules/core/selector.js'\n\nexport default class Pattern extends Container {\n  // Initialize node\n  constructor(node, attrs = node) {\n    super(nodeOrNew('pattern', node), attrs)\n  }\n\n  // custom attr to handle transform\n  attr(a, b, c) {\n    if (a === 'transform') a = 'patternTransform'\n    return super.attr(a, b, c)\n  }\n\n  bbox() {\n    return new Box()\n  }\n\n  targets() {\n    return baseFind('svg [fill*=' + this.id() + ']')\n  }\n\n  // Alias string conversion to fill\n  toString() {\n    return this.url()\n  }\n\n  // Update pattern by rebuilding\n  update(block) {\n    // remove content\n    this.clear()\n\n    // invoke passed block\n    if (typeof block === 'function') {\n      block.call(this, this)\n    }\n\n    return this\n  }\n\n  // Return the fill id\n  url() {\n    return 'url(#' + this.id() + ')'\n  }\n}\n\nregisterMethods({\n  Container: {\n    // Create pattern element in defs\n    pattern(...args) {\n      return this.defs().pattern(...args)\n    }\n  },\n  Defs: {\n    pattern: wrapWithAttrCheck(function (width, height, block) {\n      return this.put(new Pattern()).update(block).attr({\n        x: 0,\n        y: 0,\n        width: width,\n        height: height,\n        patternUnits: 'userSpaceOnUse'\n      })\n    })\n  }\n})\n\nregister(Pattern, 'Pattern')\n", "import { isImage } from '../modules/core/regex.js'\nimport { nodeOrNew, register, wrapWithAttrCheck } from '../utils/adopter.js'\nimport { off, on } from '../modules/core/event.js'\nimport { registerAttrHook } from '../modules/core/attr.js'\nimport { registerMethods } from '../utils/methods.js'\nimport { xlink } from '../modules/core/namespaces.js'\nimport Pattern from './Pattern.js'\nimport Shape from './Shape.js'\nimport { globals } from '../utils/window.js'\n\nexport default class Image extends Shape {\n  constructor(node, attrs = node) {\n    super(nodeOrNew('image', node), attrs)\n  }\n\n  // (re)load image\n  load(url, callback) {\n    if (!url) return this\n\n    const img = new globals.window.Image()\n\n    on(\n      img,\n      'load',\n      function (e) {\n        const p = this.parent(Pattern)\n\n        // ensure image size\n        if (this.width() === 0 && this.height() === 0) {\n          this.size(img.width, img.height)\n        }\n\n        if (p instanceof Pattern) {\n          // ensure pattern size if not set\n          if (p.width() === 0 && p.height() === 0) {\n            p.size(this.width(), this.height())\n          }\n        }\n\n        if (typeof callback === 'function') {\n          callback.call(this, e)\n        }\n      },\n      this\n    )\n\n    on(img, 'load error', function () {\n      // dont forget to unbind memory leaking events\n      off(img)\n    })\n\n    return this.attr('href', (img.src = url), xlink)\n  }\n}\n\nregisterAttrHook(function (attr, val, _this) {\n  // convert image fill and stroke to patterns\n  if (attr === 'fill' || attr === 'stroke') {\n    if (isImage.test(val)) {\n      val = _this.root().defs().image(val)\n    }\n  }\n\n  if (val instanceof Image) {\n    val = _this\n      .root()\n      .defs()\n      .pattern(0, 0, (pattern) => {\n        pattern.add(val)\n      })\n  }\n\n  return val\n})\n\nregisterMethods({\n  Container: {\n    // create image element, load image and set its size\n    image: wrapWithAttrCheck(function (source, callback) {\n      return this.put(new Image()).size(0, 0).load(source, callback)\n    })\n  }\n})\n\nregister(Image, 'Image')\n", "import { delimiter } from '../modules/core/regex.js'\nimport SVGArray from './SVGArray.js'\nimport Box from './Box.js'\nimport Matrix from './Matrix.js'\n\nexport default class PointArray extends SVGArray {\n  // Get bounding box of points\n  bbox() {\n    let maxX = -Infinity\n    let maxY = -Infinity\n    let minX = Infinity\n    let minY = Infinity\n    this.forEach(function (el) {\n      maxX = Math.max(el[0], maxX)\n      maxY = Math.max(el[1], maxY)\n      minX = Math.min(el[0], minX)\n      minY = Math.min(el[1], minY)\n    })\n    return new Box(minX, minY, maxX - minX, maxY - minY)\n  }\n\n  // Move point string\n  move(x, y) {\n    const box = this.bbox()\n\n    // get relative offset\n    x -= box.x\n    y -= box.y\n\n    // move every point\n    if (!isNaN(x) && !isNaN(y)) {\n      for (let i = this.length - 1; i >= 0; i--) {\n        this[i] = [this[i][0] + x, this[i][1] + y]\n      }\n    }\n\n    return this\n  }\n\n  // Parse point string and flat array\n  parse(array = [0, 0]) {\n    const points = []\n\n    // if it is an array, we flatten it and therefore clone it to 1 depths\n    if (array instanceof Array) {\n      array = Array.prototype.concat.apply([], array)\n    } else {\n      // Else, it is considered as a string\n      // parse points\n      array = array.trim().split(delimiter).map(parseFloat)\n    }\n\n    // validate points - https://svgwg.org/svg2-draft/shapes.html#DataTypePoints\n    // Odd number of coordinates is an error. In such cases, drop the last odd coordinate.\n    if (array.length % 2 !== 0) array.pop()\n\n    // wrap points in two-tuples\n    for (let i = 0, len = array.length; i < len; i = i + 2) {\n      points.push([array[i], array[i + 1]])\n    }\n\n    return points\n  }\n\n  // Resize poly string\n  size(width, height) {\n    let i\n    const box = this.bbox()\n\n    // recalculate position of all points according to new size\n    for (i = this.length - 1; i >= 0; i--) {\n      if (box.width)\n        this[i][0] = ((this[i][0] - box.x) * width) / box.width + box.x\n      if (box.height)\n        this[i][1] = ((this[i][1] - box.y) * height) / box.height + box.y\n    }\n\n    return this\n  }\n\n  // Convert array to line object\n  toLine() {\n    return {\n      x1: this[0][0],\n      y1: this[0][1],\n      x2: this[1][0],\n      y2: this[1][1]\n    }\n  }\n\n  // Convert array to string\n  toString() {\n    const array = []\n    // convert to a poly point string\n    for (let i = 0, il = this.length; i < il; i++) {\n      array.push(this[i].join(','))\n    }\n\n    return array.join(' ')\n  }\n\n  transform(m) {\n    return this.clone().transformO(m)\n  }\n\n  // transform points with matrix (similar to Point.transform)\n  transformO(m) {\n    if (!Matrix.isMatrixLike(m)) {\n      m = new Matrix(m)\n    }\n\n    for (let i = this.length; i--; ) {\n      // Perform the matrix multiplication\n      const [x, y] = this[i]\n      this[i][0] = m.a * x + m.c * y + m.e\n      this[i][1] = m.b * x + m.d * y + m.f\n    }\n\n    return this\n  }\n}\n", "import PointArray from '../../types/PointArray.js'\n\nexport const MorphArray = PointArray\n\n// Move by left top corner over x-axis\nexport function x(x) {\n  return x == null ? this.bbox().x : this.move(x, this.bbox().y)\n}\n\n// Move by left top corner over y-axis\nexport function y(y) {\n  return y == null ? this.bbox().y : this.move(this.bbox().x, y)\n}\n\n// Set width of element\nexport function width(width) {\n  const b = this.bbox()\n  return width == null ? b.width : this.size(width, b.height)\n}\n\n// Set height of element\nexport function height(height) {\n  const b = this.bbox()\n  return height == null ? b.height : this.size(b.width, height)\n}\n", "import {\n  extend,\n  nodeOrNew,\n  register,\n  wrapWithAttrCheck\n} from '../utils/adopter.js'\nimport { proportionalSize } from '../utils/utils.js'\nimport { registerMethods } from '../utils/methods.js'\nimport PointArray from '../types/PointArray.js'\nimport Shape from './Shape.js'\nimport * as pointed from '../modules/core/pointed.js'\n\nexport default class Line extends Shape {\n  // Initialize node\n  constructor(node, attrs = node) {\n    super(nodeOrNew('line', node), attrs)\n  }\n\n  // Get array\n  array() {\n    return new PointArray([\n      [this.attr('x1'), this.attr('y1')],\n      [this.attr('x2'), this.attr('y2')]\n    ])\n  }\n\n  // Move by left top corner\n  move(x, y) {\n    return this.attr(this.array().move(x, y).toLine())\n  }\n\n  // Overwrite native plot() method\n  plot(x1, y1, x2, y2) {\n    if (x1 == null) {\n      return this.array()\n    } else if (typeof y1 !== 'undefined') {\n      x1 = { x1, y1, x2, y2 }\n    } else {\n      x1 = new PointArray(x1).toLine()\n    }\n\n    return this.attr(x1)\n  }\n\n  // Set element size to given width and height\n  size(width, height) {\n    const p = proportionalSize(this, width, height)\n    return this.attr(this.array().size(p.width, p.height).toLine())\n  }\n}\n\nextend(Line, pointed)\n\nregisterMethods({\n  Container: {\n    // Create a line element\n    line: wrapWithAttrCheck(function (...args) {\n      // make sure plot is called as a setter\n      // x1 is not necessarily a number, it can also be an array, a string and a PointArray\n      return Line.prototype.plot.apply(\n        this.put(new Line()),\n        args[0] != null ? args : [0, 0, 0, 0]\n      )\n    })\n  }\n})\n\nregister(Line, 'Line')\n", "import { nodeOrNew, register, wrapWithAttrCheck } from '../utils/adopter.js'\nimport { registerMethods } from '../utils/methods.js'\nimport Container from './Container.js'\n\nexport default class Marker extends Container {\n  // Initialize node\n  constructor(node, attrs = node) {\n    super(nodeOrNew('marker', node), attrs)\n  }\n\n  // Set height of element\n  height(height) {\n    return this.attr('markerHeight', height)\n  }\n\n  orient(orient) {\n    return this.attr('orient', orient)\n  }\n\n  // Set marker refX and refY\n  ref(x, y) {\n    return this.attr('refX', x).attr('refY', y)\n  }\n\n  // Return the fill id\n  toString() {\n    return 'url(#' + this.id() + ')'\n  }\n\n  // Update marker\n  update(block) {\n    // remove all content\n    this.clear()\n\n    // invoke passed block\n    if (typeof block === 'function') {\n      block.call(this, this)\n    }\n\n    return this\n  }\n\n  // Set width of element\n  width(width) {\n    return this.attr('markerWidth', width)\n  }\n}\n\nregisterMethods({\n  Container: {\n    marker(...args) {\n      // Create marker element in defs\n      return this.defs().marker(...args)\n    }\n  },\n  Defs: {\n    // Create marker\n    marker: wrapWithAttrCheck(function (width, height, block) {\n      // Set default viewbox to match the width and height, set ref to cx and cy and set orient to auto\n      return this.put(new Marker())\n        .size(width, height)\n        .ref(width / 2, height / 2)\n        .viewbox(0, 0, width, height)\n        .attr('orient', 'auto')\n        .update(block)\n    })\n  },\n  marker: {\n    // Create and attach markers\n    marker(marker, width, height, block) {\n      let attr = ['marker']\n\n      // Build attribute name\n      if (marker !== 'all') attr.push(marker)\n      attr = attr.join('-')\n\n      // Set marker attribute\n      marker =\n        arguments[1] instanceof Marker\n          ? arguments[1]\n          : this.defs().marker(width, height, block)\n\n      return this.attr(attr, marker)\n    }\n  }\n})\n\nregister(Marker, 'Marker')\n", "import { timeline } from '../modules/core/defaults.js'\nimport { extend } from '../utils/adopter.js'\n\n/***\nBase Class\n==========\nThe base stepper class that will be\n***/\n\nfunction makeSetterGetter(k, f) {\n  return function (v) {\n    if (v == null) return this[k]\n    this[k] = v\n    if (f) f.call(this)\n    return this\n  }\n}\n\nexport const easing = {\n  '-': function (pos) {\n    return pos\n  },\n  '<>': function (pos) {\n    return -Math.cos(pos * Math.PI) / 2 + 0.5\n  },\n  '>': function (pos) {\n    return Math.sin((pos * Math.PI) / 2)\n  },\n  '<': function (pos) {\n    return -Math.cos((pos * Math.PI) / 2) + 1\n  },\n  bezier: function (x1, y1, x2, y2) {\n    // see https://www.w3.org/TR/css-easing-1/#cubic-bezier-algo\n    return function (t) {\n      if (t < 0) {\n        if (x1 > 0) {\n          return (y1 / x1) * t\n        } else if (x2 > 0) {\n          return (y2 / x2) * t\n        } else {\n          return 0\n        }\n      } else if (t > 1) {\n        if (x2 < 1) {\n          return ((1 - y2) / (1 - x2)) * t + (y2 - x2) / (1 - x2)\n        } else if (x1 < 1) {\n          return ((1 - y1) / (1 - x1)) * t + (y1 - x1) / (1 - x1)\n        } else {\n          return 1\n        }\n      } else {\n        return 3 * t * (1 - t) ** 2 * y1 + 3 * t ** 2 * (1 - t) * y2 + t ** 3\n      }\n    }\n  },\n  // see https://www.w3.org/TR/css-easing-1/#step-timing-function-algo\n  steps: function (steps, stepPosition = 'end') {\n    // deal with \"jump-\" prefix\n    stepPosition = stepPosition.split('-').reverse()[0]\n\n    let jumps = steps\n    if (stepPosition === 'none') {\n      --jumps\n    } else if (stepPosition === 'both') {\n      ++jumps\n    }\n\n    // The beforeFlag is essentially useless\n    return (t, beforeFlag = false) => {\n      // Step is called currentStep in referenced url\n      let step = Math.floor(t * steps)\n      const jumping = (t * step) % 1 === 0\n\n      if (stepPosition === 'start' || stepPosition === 'both') {\n        ++step\n      }\n\n      if (beforeFlag && jumping) {\n        --step\n      }\n\n      if (t >= 0 && step < 0) {\n        step = 0\n      }\n\n      if (t <= 1 && step > jumps) {\n        step = jumps\n      }\n\n      return step / jumps\n    }\n  }\n}\n\nexport class Stepper {\n  done() {\n    return false\n  }\n}\n\n/***\nEasing Functions\n================\n***/\n\nexport class Ease extends Stepper {\n  constructor(fn = timeline.ease) {\n    super()\n    this.ease = easing[fn] || fn\n  }\n\n  step(from, to, pos) {\n    if (typeof from !== 'number') {\n      return pos < 1 ? from : to\n    }\n    return from + (to - from) * this.ease(pos)\n  }\n}\n\n/***\nController Types\n================\n***/\n\nexport class Controller extends Stepper {\n  constructor(fn) {\n    super()\n    this.stepper = fn\n  }\n\n  done(c) {\n    return c.done\n  }\n\n  step(current, target, dt, c) {\n    return this.stepper(current, target, dt, c)\n  }\n}\n\nfunction recalculate() {\n  // Apply the default parameters\n  const duration = (this._duration || 500) / 1000\n  const overshoot = this._overshoot || 0\n\n  // Calculate the PID natural response\n  const eps = 1e-10\n  const pi = Math.PI\n  const os = Math.log(overshoot / 100 + eps)\n  const zeta = -os / Math.sqrt(pi * pi + os * os)\n  const wn = 3.9 / (zeta * duration)\n\n  // Calculate the Spring values\n  this.d = 2 * zeta * wn\n  this.k = wn * wn\n}\n\nexport class Spring extends Controller {\n  constructor(duration = 500, overshoot = 0) {\n    super()\n    this.duration(duration).overshoot(overshoot)\n  }\n\n  step(current, target, dt, c) {\n    if (typeof current === 'string') return current\n    c.done = dt === Infinity\n    if (dt === Infinity) return target\n    if (dt === 0) return current\n\n    if (dt > 100) dt = 16\n\n    dt /= 1000\n\n    // Get the previous velocity\n    const velocity = c.velocity || 0\n\n    // Apply the control to get the new position and store it\n    const acceleration = -this.d * velocity - this.k * (current - target)\n    const newPosition = current + velocity * dt + (acceleration * dt * dt) / 2\n\n    // Store the velocity\n    c.velocity = velocity + acceleration * dt\n\n    // Figure out if we have converged, and if so, pass the value\n    c.done = Math.abs(target - newPosition) + Math.abs(velocity) < 0.002\n    return c.done ? target : newPosition\n  }\n}\n\nextend(Spring, {\n  duration: makeSetterGetter('_duration', recalculate),\n  overshoot: makeSetterGetter('_overshoot', recalculate)\n})\n\nexport class PID extends Controller {\n  constructor(p = 0.1, i = 0.01, d = 0, windup = 1000) {\n    super()\n    this.p(p).i(i).d(d).windup(windup)\n  }\n\n  step(current, target, dt, c) {\n    if (typeof current === 'string') return current\n    c.done = dt === Infinity\n\n    if (dt === Infinity) return target\n    if (dt === 0) return current\n\n    const p = target - current\n    let i = (c.integral || 0) + p * dt\n    const d = (p - (c.error || 0)) / dt\n    const windup = this._windup\n\n    // antiwindup\n    if (windup !== false) {\n      i = Math.max(-windup, Math.min(i, windup))\n    }\n\n    c.error = p\n    c.integral = i\n\n    c.done = Math.abs(p) < 0.001\n\n    return c.done ? target : current + (this.P * p + this.I * i + this.D * d)\n  }\n}\n\nextend(PID, {\n  windup: makeSetterGetter('_windup'),\n  p: makeSetterGetter('P'),\n  i: makeSetterGetter('I'),\n  d: makeSetterGetter('D')\n})\n", "import { isPathLetter } from '../modules/core/regex.js'\nimport Point from '../types/Point.js'\n\nconst segmentParameters = {\n  M: 2,\n  L: 2,\n  H: 1,\n  V: 1,\n  C: 6,\n  S: 4,\n  Q: 4,\n  T: 2,\n  A: 7,\n  Z: 0\n}\n\nconst pathHandlers = {\n  M: function (c, p, p0) {\n    p.x = p0.x = c[0]\n    p.y = p0.y = c[1]\n\n    return ['M', p.x, p.y]\n  },\n  L: function (c, p) {\n    p.x = c[0]\n    p.y = c[1]\n    return ['L', c[0], c[1]]\n  },\n  H: function (c, p) {\n    p.x = c[0]\n    return ['H', c[0]]\n  },\n  V: function (c, p) {\n    p.y = c[0]\n    return ['V', c[0]]\n  },\n  C: function (c, p) {\n    p.x = c[4]\n    p.y = c[5]\n    return ['C', c[0], c[1], c[2], c[3], c[4], c[5]]\n  },\n  S: function (c, p) {\n    p.x = c[2]\n    p.y = c[3]\n    return ['S', c[0], c[1], c[2], c[3]]\n  },\n  Q: function (c, p) {\n    p.x = c[2]\n    p.y = c[3]\n    return ['Q', c[0], c[1], c[2], c[3]]\n  },\n  T: function (c, p) {\n    p.x = c[0]\n    p.y = c[1]\n    return ['T', c[0], c[1]]\n  },\n  Z: function (c, p, p0) {\n    p.x = p0.x\n    p.y = p0.y\n    return ['Z']\n  },\n  A: function (c, p) {\n    p.x = c[5]\n    p.y = c[6]\n    return ['A', c[0], c[1], c[2], c[3], c[4], c[5], c[6]]\n  }\n}\n\nconst mlhvqtcsaz = 'mlhvqtcsaz'.split('')\n\nfor (let i = 0, il = mlhvqtcsaz.length; i < il; ++i) {\n  pathHandlers[mlhvqtcsaz[i]] = (function (i) {\n    return function (c, p, p0) {\n      if (i === 'H') c[0] = c[0] + p.x\n      else if (i === 'V') c[0] = c[0] + p.y\n      else if (i === 'A') {\n        c[5] = c[5] + p.x\n        c[6] = c[6] + p.y\n      } else {\n        for (let j = 0, jl = c.length; j < jl; ++j) {\n          c[j] = c[j] + (j % 2 ? p.y : p.x)\n        }\n      }\n\n      return pathHandlers[i](c, p, p0)\n    }\n  })(mlhvqtcsaz[i].toUpperCase())\n}\n\nfunction makeAbsolut(parser) {\n  const command = parser.segment[0]\n  return pathHandlers[command](parser.segment.slice(1), parser.p, parser.p0)\n}\n\nfunction segmentComplete(parser) {\n  return (\n    parser.segment.length &&\n    parser.segment.length - 1 ===\n      segmentParameters[parser.segment[0].toUpperCase()]\n  )\n}\n\nfunction startNewSegment(parser, token) {\n  parser.inNumber && finalizeNumber(parser, false)\n  const pathLetter = isPathLetter.test(token)\n\n  if (pathLetter) {\n    parser.segment = [token]\n  } else {\n    const lastCommand = parser.lastCommand\n    const small = lastCommand.toLowerCase()\n    const isSmall = lastCommand === small\n    parser.segment = [small === 'm' ? (isSmall ? 'l' : 'L') : lastCommand]\n  }\n\n  parser.inSegment = true\n  parser.lastCommand = parser.segment[0]\n\n  return pathLetter\n}\n\nfunction finalizeNumber(parser, inNumber) {\n  if (!parser.inNumber) throw new Error('Parser Error')\n  parser.number && parser.segment.push(parseFloat(parser.number))\n  parser.inNumber = inNumber\n  parser.number = ''\n  parser.pointSeen = false\n  parser.hasExponent = false\n\n  if (segmentComplete(parser)) {\n    finalizeSegment(parser)\n  }\n}\n\nfunction finalizeSegment(parser) {\n  parser.inSegment = false\n  if (parser.absolute) {\n    parser.segment = makeAbsolut(parser)\n  }\n  parser.segments.push(parser.segment)\n}\n\nfunction isArcFlag(parser) {\n  if (!parser.segment.length) return false\n  const isArc = parser.segment[0].toUpperCase() === 'A'\n  const length = parser.segment.length\n\n  return isArc && (length === 4 || length === 5)\n}\n\nfunction isExponential(parser) {\n  return parser.lastToken.toUpperCase() === 'E'\n}\n\nconst pathDelimiters = new Set([' ', ',', '\\t', '\\n', '\\r', '\\f'])\nexport function pathParser(d, toAbsolute = true) {\n  let index = 0\n  let token = ''\n  const parser = {\n    segment: [],\n    inNumber: false,\n    number: '',\n    lastToken: '',\n    inSegment: false,\n    segments: [],\n    pointSeen: false,\n    hasExponent: false,\n    absolute: toAbsolute,\n    p0: new Point(),\n    p: new Point()\n  }\n\n  while (((parser.lastToken = token), (token = d.charAt(index++)))) {\n    if (!parser.inSegment) {\n      if (startNewSegment(parser, token)) {\n        continue\n      }\n    }\n\n    if (token === '.') {\n      if (parser.pointSeen || parser.hasExponent) {\n        finalizeNumber(parser, false)\n        --index\n        continue\n      }\n      parser.inNumber = true\n      parser.pointSeen = true\n      parser.number += token\n      continue\n    }\n\n    if (!isNaN(parseInt(token))) {\n      if (parser.number === '0' || isArcFlag(parser)) {\n        parser.inNumber = true\n        parser.number = token\n        finalizeNumber(parser, true)\n        continue\n      }\n\n      parser.inNumber = true\n      parser.number += token\n      continue\n    }\n\n    if (pathDelimiters.has(token)) {\n      if (parser.inNumber) {\n        finalizeNumber(parser, false)\n      }\n      continue\n    }\n\n    if (token === '-' || token === '+') {\n      if (parser.inNumber && !isExponential(parser)) {\n        finalizeNumber(parser, false)\n        --index\n        continue\n      }\n      parser.number += token\n      parser.inNumber = true\n      continue\n    }\n\n    if (token.toUpperCase() === 'E') {\n      parser.number += token\n      parser.hasExponent = true\n      continue\n    }\n\n    if (isPathLetter.test(token)) {\n      if (parser.inNumber) {\n        finalizeNumber(parser, false)\n      } else if (!segmentComplete(parser)) {\n        throw new Error('parser Error')\n      } else {\n        finalizeSegment(parser)\n      }\n      --index\n    }\n  }\n\n  if (parser.inNumber) {\n    finalizeNumber(parser, false)\n  }\n\n  if (parser.inSegment && segmentComplete(parser)) {\n    finalizeSegment(parser)\n  }\n\n  return parser.segments\n}\n", "import SVGArray from './SVGArray.js'\nimport parser from '../modules/core/parser.js'\nimport Box from './Box.js'\nimport { pathParser } from '../utils/pathParser.js'\n\nfunction arrayToString(a) {\n  let s = ''\n  for (let i = 0, il = a.length; i < il; i++) {\n    s += a[i][0]\n\n    if (a[i][1] != null) {\n      s += a[i][1]\n\n      if (a[i][2] != null) {\n        s += ' '\n        s += a[i][2]\n\n        if (a[i][3] != null) {\n          s += ' '\n          s += a[i][3]\n          s += ' '\n          s += a[i][4]\n\n          if (a[i][5] != null) {\n            s += ' '\n            s += a[i][5]\n            s += ' '\n            s += a[i][6]\n\n            if (a[i][7] != null) {\n              s += ' '\n              s += a[i][7]\n            }\n          }\n        }\n      }\n    }\n  }\n\n  return s + ' '\n}\n\nexport default class PathArray extends SVGArray {\n  // Get bounding box of path\n  bbox() {\n    parser().path.setAttribute('d', this.toString())\n    return new Box(parser.nodes.path.getBBox())\n  }\n\n  // Move path string\n  move(x, y) {\n    // get bounding box of current situation\n    const box = this.bbox()\n\n    // get relative offset\n    x -= box.x\n    y -= box.y\n\n    if (!isNaN(x) && !isNaN(y)) {\n      // move every point\n      for (let l, i = this.length - 1; i >= 0; i--) {\n        l = this[i][0]\n\n        if (l === 'M' || l === 'L' || l === 'T') {\n          this[i][1] += x\n          this[i][2] += y\n        } else if (l === 'H') {\n          this[i][1] += x\n        } else if (l === 'V') {\n          this[i][1] += y\n        } else if (l === 'C' || l === 'S' || l === 'Q') {\n          this[i][1] += x\n          this[i][2] += y\n          this[i][3] += x\n          this[i][4] += y\n\n          if (l === 'C') {\n            this[i][5] += x\n            this[i][6] += y\n          }\n        } else if (l === 'A') {\n          this[i][6] += x\n          this[i][7] += y\n        }\n      }\n    }\n\n    return this\n  }\n\n  // Absolutize and parse path to array\n  parse(d = 'M0 0') {\n    if (Array.isArray(d)) {\n      d = Array.prototype.concat.apply([], d).toString()\n    }\n\n    return pathParser(d)\n  }\n\n  // Resize path string\n  size(width, height) {\n    // get bounding box of current situation\n    const box = this.bbox()\n    let i, l\n\n    // If the box width or height is 0 then we ignore\n    // transformations on the respective axis\n    box.width = box.width === 0 ? 1 : box.width\n    box.height = box.height === 0 ? 1 : box.height\n\n    // recalculate position of all points according to new size\n    for (i = this.length - 1; i >= 0; i--) {\n      l = this[i][0]\n\n      if (l === 'M' || l === 'L' || l === 'T') {\n        this[i][1] = ((this[i][1] - box.x) * width) / box.width + box.x\n        this[i][2] = ((this[i][2] - box.y) * height) / box.height + box.y\n      } else if (l === 'H') {\n        this[i][1] = ((this[i][1] - box.x) * width) / box.width + box.x\n      } else if (l === 'V') {\n        this[i][1] = ((this[i][1] - box.y) * height) / box.height + box.y\n      } else if (l === 'C' || l === 'S' || l === 'Q') {\n        this[i][1] = ((this[i][1] - box.x) * width) / box.width + box.x\n        this[i][2] = ((this[i][2] - box.y) * height) / box.height + box.y\n        this[i][3] = ((this[i][3] - box.x) * width) / box.width + box.x\n        this[i][4] = ((this[i][4] - box.y) * height) / box.height + box.y\n\n        if (l === 'C') {\n          this[i][5] = ((this[i][5] - box.x) * width) / box.width + box.x\n          this[i][6] = ((this[i][6] - box.y) * height) / box.height + box.y\n        }\n      } else if (l === 'A') {\n        // resize radii\n        this[i][1] = (this[i][1] * width) / box.width\n        this[i][2] = (this[i][2] * height) / box.height\n\n        // move position values\n        this[i][6] = ((this[i][6] - box.x) * width) / box.width + box.x\n        this[i][7] = ((this[i][7] - box.y) * height) / box.height + box.y\n      }\n    }\n\n    return this\n  }\n\n  // Convert array to string\n  toString() {\n    return arrayToString(this)\n  }\n}\n", "import { Ease } from './Controller.js'\nimport {\n  delimiter,\n  numberAndUnit,\n  isPathLetter\n} from '../modules/core/regex.js'\nimport { extend } from '../utils/adopter.js'\nimport Color from '../types/Color.js'\nimport PathArray from '../types/PathArray.js'\nimport SVGArray from '../types/SVGArray.js'\nimport SVGNumber from '../types/SVGNumber.js'\n\nconst getClassForType = (value) => {\n  const type = typeof value\n\n  if (type === 'number') {\n    return SVGNumber\n  } else if (type === 'string') {\n    if (Color.isColor(value)) {\n      return Color\n    } else if (delimiter.test(value)) {\n      return isPathLetter.test(value) ? PathArray : SVGArray\n    } else if (numberAndUnit.test(value)) {\n      return SVGNumber\n    } else {\n      return NonMorphable\n    }\n  } else if (morphableTypes.indexOf(value.constructor) > -1) {\n    return value.constructor\n  } else if (Array.isArray(value)) {\n    return SVGArray\n  } else if (type === 'object') {\n    return ObjectBag\n  } else {\n    return NonMorphable\n  }\n}\n\nexport default class Morphable {\n  constructor(stepper) {\n    this._stepper = stepper || new Ease('-')\n\n    this._from = null\n    this._to = null\n    this._type = null\n    this._context = null\n    this._morphObj = null\n  }\n\n  at(pos) {\n    return this._morphObj.morph(\n      this._from,\n      this._to,\n      pos,\n      this._stepper,\n      this._context\n    )\n  }\n\n  done() {\n    const complete = this._context.map(this._stepper.done).reduce(function (\n      last,\n      curr\n    ) {\n      return last && curr\n    }, true)\n    return complete\n  }\n\n  from(val) {\n    if (val == null) {\n      return this._from\n    }\n\n    this._from = this._set(val)\n    return this\n  }\n\n  stepper(stepper) {\n    if (stepper == null) return this._stepper\n    this._stepper = stepper\n    return this\n  }\n\n  to(val) {\n    if (val == null) {\n      return this._to\n    }\n\n    this._to = this._set(val)\n    return this\n  }\n\n  type(type) {\n    // getter\n    if (type == null) {\n      return this._type\n    }\n\n    // setter\n    this._type = type\n    return this\n  }\n\n  _set(value) {\n    if (!this._type) {\n      this.type(getClassForType(value))\n    }\n\n    let result = new this._type(value)\n    if (this._type === Color) {\n      result = this._to\n        ? result[this._to[4]]()\n        : this._from\n          ? result[this._from[4]]()\n          : result\n    }\n\n    if (this._type === ObjectBag) {\n      result = this._to\n        ? result.align(this._to)\n        : this._from\n          ? result.align(this._from)\n          : result\n    }\n\n    result = result.toConsumable()\n\n    this._morphObj = this._morphObj || new this._type()\n    this._context =\n      this._context ||\n      Array.apply(null, Array(result.length))\n        .map(Object)\n        .map(function (o) {\n          o.done = true\n          return o\n        })\n    return result\n  }\n}\n\nexport class NonMorphable {\n  constructor(...args) {\n    this.init(...args)\n  }\n\n  init(val) {\n    val = Array.isArray(val) ? val[0] : val\n    this.value = val\n    return this\n  }\n\n  toArray() {\n    return [this.value]\n  }\n\n  valueOf() {\n    return this.value\n  }\n}\n\nexport class TransformBag {\n  constructor(...args) {\n    this.init(...args)\n  }\n\n  init(obj) {\n    if (Array.isArray(obj)) {\n      obj = {\n        scaleX: obj[0],\n        scaleY: obj[1],\n        shear: obj[2],\n        rotate: obj[3],\n        translateX: obj[4],\n        translateY: obj[5],\n        originX: obj[6],\n        originY: obj[7]\n      }\n    }\n\n    Object.assign(this, TransformBag.defaults, obj)\n    return this\n  }\n\n  toArray() {\n    const v = this\n\n    return [\n      v.scaleX,\n      v.scaleY,\n      v.shear,\n      v.rotate,\n      v.translateX,\n      v.translateY,\n      v.originX,\n      v.originY\n    ]\n  }\n}\n\nTransformBag.defaults = {\n  scaleX: 1,\n  scaleY: 1,\n  shear: 0,\n  rotate: 0,\n  translateX: 0,\n  translateY: 0,\n  originX: 0,\n  originY: 0\n}\n\nconst sortByKey = (a, b) => {\n  return a[0] < b[0] ? -1 : a[0] > b[0] ? 1 : 0\n}\n\nexport class ObjectBag {\n  constructor(...args) {\n    this.init(...args)\n  }\n\n  align(other) {\n    const values = this.values\n    for (let i = 0, il = values.length; i < il; ++i) {\n      // If the type is the same we only need to check if the color is in the correct format\n      if (values[i + 1] === other[i + 1]) {\n        if (values[i + 1] === Color && other[i + 7] !== values[i + 7]) {\n          const space = other[i + 7]\n          const color = new Color(this.values.splice(i + 3, 5))\n            [space]()\n            .toArray()\n          this.values.splice(i + 3, 0, ...color)\n        }\n\n        i += values[i + 2] + 2\n        continue\n      }\n\n      if (!other[i + 1]) {\n        return this\n      }\n\n      // The types differ, so we overwrite the new type with the old one\n      // And initialize it with the types default (e.g. black for color or 0 for number)\n      const defaultObject = new other[i + 1]().toArray()\n\n      // Than we fix the values array\n      const toDelete = values[i + 2] + 3\n\n      values.splice(\n        i,\n        toDelete,\n        other[i],\n        other[i + 1],\n        other[i + 2],\n        ...defaultObject\n      )\n\n      i += values[i + 2] + 2\n    }\n    return this\n  }\n\n  init(objOrArr) {\n    this.values = []\n\n    if (Array.isArray(objOrArr)) {\n      this.values = objOrArr.slice()\n      return\n    }\n\n    objOrArr = objOrArr || {}\n    const entries = []\n\n    for (const i in objOrArr) {\n      const Type = getClassForType(objOrArr[i])\n      const val = new Type(objOrArr[i]).toArray()\n      entries.push([i, Type, val.length, ...val])\n    }\n\n    entries.sort(sortByKey)\n\n    this.values = entries.reduce((last, curr) => last.concat(curr), [])\n    return this\n  }\n\n  toArray() {\n    return this.values\n  }\n\n  valueOf() {\n    const obj = {}\n    const arr = this.values\n\n    // for (var i = 0, len = arr.length; i < len; i += 2) {\n    while (arr.length) {\n      const key = arr.shift()\n      const Type = arr.shift()\n      const num = arr.shift()\n      const values = arr.splice(0, num)\n      obj[key] = new Type(values) // .valueOf()\n    }\n\n    return obj\n  }\n}\n\nconst morphableTypes = [NonMorphable, TransformBag, ObjectBag]\n\nexport function registerMorphableType(type = []) {\n  morphableTypes.push(...[].concat(type))\n}\n\nexport function makeMorphable() {\n  extend(morphableTypes, {\n    to(val) {\n      return new Morphable()\n        .type(this.constructor)\n        .from(this.toArray()) // this.valueOf())\n        .to(val)\n    },\n    fromArray(arr) {\n      this.init(arr)\n      return this\n    },\n    toConsumable() {\n      return this.toArray()\n    },\n    morph(from, to, pos, stepper, context) {\n      const mapper = function (i, index) {\n        return stepper.step(i, to[index], pos, context[index], context)\n      }\n\n      return this.fromArray(from.map(mapper))\n    }\n  })\n}\n", "import { nodeOrNew, register, wrapWithAttrCheck } from '../utils/adopter.js'\nimport { proportionalSize } from '../utils/utils.js'\nimport { registerMethods } from '../utils/methods.js'\nimport PathArray from '../types/PathArray.js'\nimport Shape from './Shape.js'\n\nexport default class Path extends Shape {\n  // Initialize node\n  constructor(node, attrs = node) {\n    super(nodeOrNew('path', node), attrs)\n  }\n\n  // Get array\n  array() {\n    return this._array || (this._array = new PathArray(this.attr('d')))\n  }\n\n  // Clear array cache\n  clear() {\n    delete this._array\n    return this\n  }\n\n  // Set height of element\n  height(height) {\n    return height == null\n      ? this.bbox().height\n      : this.size(this.bbox().width, height)\n  }\n\n  // Move by left top corner\n  move(x, y) {\n    return this.attr('d', this.array().move(x, y))\n  }\n\n  // Plot new path\n  plot(d) {\n    return d == null\n      ? this.array()\n      : this.clear().attr(\n          'd',\n          typeof d === 'string' ? d : (this._array = new PathArray(d))\n        )\n  }\n\n  // Set element size to given width and height\n  size(width, height) {\n    const p = proportionalSize(this, width, height)\n    return this.attr('d', this.array().size(p.width, p.height))\n  }\n\n  // Set width of element\n  width(width) {\n    return width == null\n      ? this.bbox().width\n      : this.size(width, this.bbox().height)\n  }\n\n  // Move by left top corner over x-axis\n  x(x) {\n    return x == null ? this.bbox().x : this.move(x, this.bbox().y)\n  }\n\n  // Move by left top corner over y-axis\n  y(y) {\n    return y == null ? this.bbox().y : this.move(this.bbox().x, y)\n  }\n}\n\n// Define morphable array\nPath.prototype.MorphArray = PathArray\n\n// Add parent method\nregisterMethods({\n  Container: {\n    // Create a wrapped path element\n    path: wrapWithAttrCheck(function (d) {\n      // make sure plot is called as a setter\n      return this.put(new Path()).plot(d || new PathArray())\n    })\n  }\n})\n\nregister(Path, 'Path')\n", "import { proportionalSize } from '../../utils/utils.js'\nimport PointArray from '../../types/PointArray.js'\n\n// Get array\nexport function array() {\n  return this._array || (this._array = new PointArray(this.attr('points')))\n}\n\n// Clear array cache\nexport function clear() {\n  delete this._array\n  return this\n}\n\n// Move by left top corner\nexport function move(x, y) {\n  return this.attr('points', this.array().move(x, y))\n}\n\n// Plot new path\nexport function plot(p) {\n  return p == null\n    ? this.array()\n    : this.clear().attr(\n        'points',\n        typeof p === 'string' ? p : (this._array = new PointArray(p))\n      )\n}\n\n// Set element size to given width and height\nexport function size(width, height) {\n  const p = proportionalSize(this, width, height)\n  return this.attr('points', this.array().size(p.width, p.height))\n}\n", "import {\n  extend,\n  nodeOrNew,\n  register,\n  wrapWithAttrCheck\n} from '../utils/adopter.js'\nimport { registerMethods } from '../utils/methods.js'\nimport PointArray from '../types/PointArray.js'\nimport Shape from './Shape.js'\nimport * as pointed from '../modules/core/pointed.js'\nimport * as poly from '../modules/core/poly.js'\n\nexport default class Polygon extends Shape {\n  // Initialize node\n  constructor(node, attrs = node) {\n    super(nodeOrNew('polygon', node), attrs)\n  }\n}\n\nregisterMethods({\n  Container: {\n    // Create a wrapped polygon element\n    polygon: wrapWithAttrCheck(function (p) {\n      // make sure plot is called as a setter\n      return this.put(new Polygon()).plot(p || new PointArray())\n    })\n  }\n})\n\nextend(Polygon, pointed)\nextend(Polygon, poly)\nregister(Polygon, 'Polygon')\n", "import {\n  extend,\n  nodeOrNew,\n  register,\n  wrapWithAttrCheck\n} from '../utils/adopter.js'\nimport { registerMethods } from '../utils/methods.js'\nimport PointArray from '../types/PointArray.js'\nimport Shape from './Shape.js'\nimport * as pointed from '../modules/core/pointed.js'\nimport * as poly from '../modules/core/poly.js'\n\nexport default class Polyline extends Shape {\n  // Initialize node\n  constructor(node, attrs = node) {\n    super(nodeOrNew('polyline', node), attrs)\n  }\n}\n\nregisterMethods({\n  Container: {\n    // Create a wrapped polygon element\n    polyline: wrapWithAttrCheck(function (p) {\n      // make sure plot is called as a setter\n      return this.put(new Polyline()).plot(p || new PointArray())\n    })\n  }\n})\n\nextend(Polyline, pointed)\nextend(Polyline, poly)\nregister(Polyline, 'Polyline')\n", "import {\n  extend,\n  nodeOrNew,\n  register,\n  wrapWithAttrCheck\n} from '../utils/adopter.js'\nimport { registerMethods } from '../utils/methods.js'\nimport { rx, ry } from '../modules/core/circled.js'\nimport Shape from './Shape.js'\n\nexport default class Rect extends Shape {\n  // Initialize node\n  constructor(node, attrs = node) {\n    super(nodeOrNew('rect', node), attrs)\n  }\n}\n\nextend(Rect, { rx, ry })\n\nregisterMethods({\n  Container: {\n    // Create a rect element\n    rect: wrapWithAttrCheck(function (width, height) {\n      return this.put(new Rect()).size(width, height)\n    })\n  }\n})\n\nregister(Rect, 'Rect')\n", "export default class Queue {\n  constructor() {\n    this._first = null\n    this._last = null\n  }\n\n  // Shows us the first item in the list\n  first() {\n    return this._first && this._first.value\n  }\n\n  // Shows us the last item in the list\n  last() {\n    return this._last && this._last.value\n  }\n\n  push(value) {\n    // An item stores an id and the provided value\n    const item =\n      typeof value.next !== 'undefined'\n        ? value\n        : { value: value, next: null, prev: null }\n\n    // Deal with the queue being empty or populated\n    if (this._last) {\n      item.prev = this._last\n      this._last.next = item\n      this._last = item\n    } else {\n      this._last = item\n      this._first = item\n    }\n\n    // Return the current item\n    return item\n  }\n\n  // Removes the item that was returned from the push\n  remove(item) {\n    // Relink the previous item\n    if (item.prev) item.prev.next = item.next\n    if (item.next) item.next.prev = item.prev\n    if (item === this._last) this._last = item.prev\n    if (item === this._first) this._first = item.next\n\n    // Invalidate item\n    item.prev = null\n    item.next = null\n  }\n\n  shift() {\n    // Check if we have a value\n    const remove = this._first\n    if (!remove) return null\n\n    // If we do, remove it and relink things\n    this._first = remove.next\n    if (this._first) this._first.prev = null\n    this._last = this._first ? this._last : null\n    return remove.value\n  }\n}\n", "import { globals } from '../utils/window.js'\nimport Queue from './Queue.js'\n\nconst Animator = {\n  nextDraw: null,\n  frames: new Queue(),\n  timeouts: new Queue(),\n  immediates: new Queue(),\n  timer: () => globals.window.performance || globals.window.Date,\n  transforms: [],\n\n  frame(fn) {\n    // Store the node\n    const node = Animator.frames.push({ run: fn })\n\n    // Request an animation frame if we don't have one\n    if (Animator.nextDraw === null) {\n      Animator.nextDraw = globals.window.requestAnimationFrame(Animator._draw)\n    }\n\n    // Return the node so we can remove it easily\n    return node\n  },\n\n  timeout(fn, delay) {\n    delay = delay || 0\n\n    // Work out when the event should fire\n    const time = Animator.timer().now() + delay\n\n    // Add the timeout to the end of the queue\n    const node = Animator.timeouts.push({ run: fn, time: time })\n\n    // Request another animation frame if we need one\n    if (Animator.nextDraw === null) {\n      Animator.nextDraw = globals.window.requestAnimationFrame(Animator._draw)\n    }\n\n    return node\n  },\n\n  immediate(fn) {\n    // Add the immediate fn to the end of the queue\n    const node = Animator.immediates.push(fn)\n    // Request another animation frame if we need one\n    if (Animator.nextDraw === null) {\n      Animator.nextDraw = globals.window.requestAnimationFrame(Animator._draw)\n    }\n\n    return node\n  },\n\n  cancelFrame(node) {\n    node != null && Animator.frames.remove(node)\n  },\n\n  clearTimeout(node) {\n    node != null && Animator.timeouts.remove(node)\n  },\n\n  cancelImmediate(node) {\n    node != null && Animator.immediates.remove(node)\n  },\n\n  _draw(now) {\n    // Run all the timeouts we can run, if they are not ready yet, add them\n    // to the end of the queue immediately! (bad timeouts!!! [sarcasm])\n    let nextTimeout = null\n    const lastTimeout = Animator.timeouts.last()\n    while ((nextTimeout = Animator.timeouts.shift())) {\n      // Run the timeout if its time, or push it to the end\n      if (now >= nextTimeout.time) {\n        nextTimeout.run()\n      } else {\n        Animator.timeouts.push(nextTimeout)\n      }\n\n      // If we hit the last item, we should stop shifting out more items\n      if (nextTimeout === lastTimeout) break\n    }\n\n    // Run all of the animation frames\n    let nextFrame = null\n    const lastFrame = Animator.frames.last()\n    while (nextFrame !== lastFrame && (nextFrame = Animator.frames.shift())) {\n      nextFrame.run(now)\n    }\n\n    let nextImmediate = null\n    while ((nextImmediate = Animator.immediates.shift())) {\n      nextImmediate()\n    }\n\n    // If we have remaining timeouts or frames, draw until we don't anymore\n    Animator.nextDraw =\n      Animator.timeouts.first() || Animator.frames.first()\n        ? globals.window.requestAnimationFrame(Animator._draw)\n        : null\n  }\n}\n\nexport default Animator\n", "import { globals } from '../utils/window.js'\nimport { registerMethods } from '../utils/methods.js'\nimport Animator from './Animator.js'\nimport EventTarget from '../types/EventTarget.js'\n\nconst makeSchedule = function (runnerInfo) {\n  const start = runnerInfo.start\n  const duration = runnerInfo.runner.duration()\n  const end = start + duration\n  return {\n    start: start,\n    duration: duration,\n    end: end,\n    runner: runnerInfo.runner\n  }\n}\n\nconst defaultSource = function () {\n  const w = globals.window\n  return (w.performance || w.Date).now()\n}\n\nexport default class Timeline extends EventTarget {\n  // Construct a new timeline on the given element\n  constructor(timeSource = defaultSource) {\n    super()\n\n    this._timeSource = timeSource\n\n    // terminate resets all variables to their initial state\n    this.terminate()\n  }\n\n  active() {\n    return !!this._nextFrame\n  }\n\n  finish() {\n    // Go to end and pause\n    this.time(this.getEndTimeOfTimeline() + 1)\n    return this.pause()\n  }\n\n  // Calculates the end of the timeline\n  getEndTime() {\n    const lastRunnerInfo = this.getLastRunnerInfo()\n    const lastDuration = lastRunnerInfo ? lastRunnerInfo.runner.duration() : 0\n    const lastStartTime = lastRunnerInfo ? lastRunnerInfo.start : this._time\n    return lastStartTime + lastDuration\n  }\n\n  getEndTimeOfTimeline() {\n    const endTimes = this._runners.map((i) => i.start + i.runner.duration())\n    return Math.max(0, ...endTimes)\n  }\n\n  getLastRunnerInfo() {\n    return this.getRunnerInfoById(this._lastRunnerId)\n  }\n\n  getRunnerInfoById(id) {\n    return this._runners[this._runnerIds.indexOf(id)] || null\n  }\n\n  pause() {\n    this._paused = true\n    return this._continue()\n  }\n\n  persist(dtOrForever) {\n    if (dtOrForever == null) return this._persist\n    this._persist = dtOrForever\n    return this\n  }\n\n  play() {\n    // Now make sure we are not paused and continue the animation\n    this._paused = false\n    return this.updateTime()._continue()\n  }\n\n  reverse(yes) {\n    const currentSpeed = this.speed()\n    if (yes == null) return this.speed(-currentSpeed)\n\n    const positive = Math.abs(currentSpeed)\n    return this.speed(yes ? -positive : positive)\n  }\n\n  // schedules a runner on the timeline\n  schedule(runner, delay, when) {\n    if (runner == null) {\n      return this._runners.map(makeSchedule)\n    }\n\n    // The start time for the next animation can either be given explicitly,\n    // derived from the current timeline time or it can be relative to the\n    // last start time to chain animations directly\n\n    let absoluteStartTime = 0\n    const endTime = this.getEndTime()\n    delay = delay || 0\n\n    // Work out when to start the animation\n    if (when == null || when === 'last' || when === 'after') {\n      // Take the last time and increment\n      absoluteStartTime = endTime\n    } else if (when === 'absolute' || when === 'start') {\n      absoluteStartTime = delay\n      delay = 0\n    } else if (when === 'now') {\n      absoluteStartTime = this._time\n    } else if (when === 'relative') {\n      const runnerInfo = this.getRunnerInfoById(runner.id)\n      if (runnerInfo) {\n        absoluteStartTime = runnerInfo.start + delay\n        delay = 0\n      }\n    } else if (when === 'with-last') {\n      const lastRunnerInfo = this.getLastRunnerInfo()\n      const lastStartTime = lastRunnerInfo ? lastRunnerInfo.start : this._time\n      absoluteStartTime = lastStartTime\n    } else {\n      throw new Error('Invalid value for the \"when\" parameter')\n    }\n\n    // Manage runner\n    runner.unschedule()\n    runner.timeline(this)\n\n    const persist = runner.persist()\n    const runnerInfo = {\n      persist: persist === null ? this._persist : persist,\n      start: absoluteStartTime + delay,\n      runner\n    }\n\n    this._lastRunnerId = runner.id\n\n    this._runners.push(runnerInfo)\n    this._runners.sort((a, b) => a.start - b.start)\n    this._runnerIds = this._runners.map((info) => info.runner.id)\n\n    this.updateTime()._continue()\n    return this\n  }\n\n  seek(dt) {\n    return this.time(this._time + dt)\n  }\n\n  source(fn) {\n    if (fn == null) return this._timeSource\n    this._timeSource = fn\n    return this\n  }\n\n  speed(speed) {\n    if (speed == null) return this._speed\n    this._speed = speed\n    return this\n  }\n\n  stop() {\n    // Go to start and pause\n    this.time(0)\n    return this.pause()\n  }\n\n  time(time) {\n    if (time == null) return this._time\n    this._time = time\n    return this._continue(true)\n  }\n\n  // Remove the runner from this timeline\n  unschedule(runner) {\n    const index = this._runnerIds.indexOf(runner.id)\n    if (index < 0) return this\n\n    this._runners.splice(index, 1)\n    this._runnerIds.splice(index, 1)\n\n    runner.timeline(null)\n    return this\n  }\n\n  // Makes sure, that after pausing the time doesn't jump\n  updateTime() {\n    if (!this.active()) {\n      this._lastSourceTime = this._timeSource()\n    }\n    return this\n  }\n\n  // Checks if we are running and continues the animation\n  _continue(immediateStep = false) {\n    Animator.cancelFrame(this._nextFrame)\n    this._nextFrame = null\n\n    if (immediateStep) return this._stepImmediate()\n    if (this._paused) return this\n\n    this._nextFrame = Animator.frame(this._step)\n    return this\n  }\n\n  _stepFn(immediateStep = false) {\n    // Get the time delta from the last time and update the time\n    const time = this._timeSource()\n    let dtSource = time - this._lastSourceTime\n\n    if (immediateStep) dtSource = 0\n\n    const dtTime = this._speed * dtSource + (this._time - this._lastStepTime)\n    this._lastSourceTime = time\n\n    // Only update the time if we use the timeSource.\n    // Otherwise use the current time\n    if (!immediateStep) {\n      // Update the time\n      this._time += dtTime\n      this._time = this._time < 0 ? 0 : this._time\n    }\n    this._lastStepTime = this._time\n    this.fire('time', this._time)\n\n    // This is for the case that the timeline was seeked so that the time\n    // is now before the startTime of the runner. That is why we need to set\n    // the runner to position 0\n\n    // FIXME:\n    // However, resetting in insertion order leads to bugs. Considering the case,\n    // where 2 runners change the same attribute but in different times,\n    // resetting both of them will lead to the case where the later defined\n    // runner always wins the reset even if the other runner started earlier\n    // and therefore should win the attribute battle\n    // this can be solved by resetting them backwards\n    for (let k = this._runners.length; k--; ) {\n      // Get and run the current runner and ignore it if its inactive\n      const runnerInfo = this._runners[k]\n      const runner = runnerInfo.runner\n\n      // Make sure that we give the actual difference\n      // between runner start time and now\n      const dtToStart = this._time - runnerInfo.start\n\n      // Dont run runner if not started yet\n      // and try to reset it\n      if (dtToStart <= 0) {\n        runner.reset()\n      }\n    }\n\n    // Run all of the runners directly\n    let runnersLeft = false\n    for (let i = 0, len = this._runners.length; i < len; i++) {\n      // Get and run the current runner and ignore it if its inactive\n      const runnerInfo = this._runners[i]\n      const runner = runnerInfo.runner\n      let dt = dtTime\n\n      // Make sure that we give the actual difference\n      // between runner start time and now\n      const dtToStart = this._time - runnerInfo.start\n\n      // Dont run runner if not started yet\n      if (dtToStart <= 0) {\n        runnersLeft = true\n        continue\n      } else if (dtToStart < dt) {\n        // Adjust dt to make sure that animation is on point\n        dt = dtToStart\n      }\n\n      if (!runner.active()) continue\n\n      // If this runner is still going, signal that we need another animation\n      // frame, otherwise, remove the completed runner\n      const finished = runner.step(dt).done\n      if (!finished) {\n        runnersLeft = true\n        // continue\n      } else if (runnerInfo.persist !== true) {\n        // runner is finished. And runner might get removed\n        const endTime = runner.duration() - runner.time() + this._time\n\n        if (endTime + runnerInfo.persist < this._time) {\n          // Delete runner and correct index\n          runner.unschedule()\n          --i\n          --len\n        }\n      }\n    }\n\n    // Basically: we continue when there are runners right from us in time\n    // when -->, and when runners are left from us when <--\n    if (\n      (runnersLeft && !(this._speed < 0 && this._time === 0)) ||\n      (this._runnerIds.length && this._speed < 0 && this._time > 0)\n    ) {\n      this._continue()\n    } else {\n      this.pause()\n      this.fire('finished')\n    }\n\n    return this\n  }\n\n  terminate() {\n    // cleanup memory\n\n    // Store the timing variables\n    this._startTime = 0\n    this._speed = 1.0\n\n    // Determines how long a runner is hold in memory. Can be a dt or true/false\n    this._persist = 0\n\n    // Keep track of the running animations and their starting parameters\n    this._nextFrame = null\n    this._paused = true\n    this._runners = []\n    this._runnerIds = []\n    this._lastRunnerId = -1\n    this._time = 0\n    this._lastSourceTime = 0\n    this._lastStepTime = 0\n\n    // Make sure that step is always called in class context\n    this._step = this._stepFn.bind(this, false)\n    this._stepImmediate = this._stepFn.bind(this, true)\n  }\n}\n\nregisterMethods({\n  Element: {\n    timeline: function (timeline) {\n      if (timeline == null) {\n        this._timeline = this._timeline || new Timeline()\n        return this._timeline\n      } else {\n        this._timeline = timeline\n        return this\n      }\n    }\n  }\n})\n", "import { <PERSON>, Ease, Stepper } from './Controller.js'\nimport { extend, register } from '../utils/adopter.js'\nimport { from, to } from '../modules/core/gradiented.js'\nimport { getOrigin } from '../utils/utils.js'\nimport { noop, timeline } from '../modules/core/defaults.js'\nimport { registerMethods } from '../utils/methods.js'\nimport { rx, ry } from '../modules/core/circled.js'\nimport Animator from './Animator.js'\nimport Box from '../types/Box.js'\nimport EventTarget from '../types/EventTarget.js'\nimport Matrix from '../types/Matrix.js'\nimport Morphable, { TransformBag, ObjectBag } from './Morphable.js'\nimport Point from '../types/Point.js'\nimport SVGNumber from '../types/SVGNumber.js'\nimport Timeline from './Timeline.js'\n\nexport default class Runner extends EventTarget {\n  constructor(options) {\n    super()\n\n    // Store a unique id on the runner, so that we can identify it later\n    this.id = Runner.id++\n\n    // Ensure a default value\n    options = options == null ? timeline.duration : options\n\n    // Ensure that we get a controller\n    options = typeof options === 'function' ? new Controller(options) : options\n\n    // Declare all of the variables\n    this._element = null\n    this._timeline = null\n    this.done = false\n    this._queue = []\n\n    // Work out the stepper and the duration\n    this._duration = typeof options === 'number' && options\n    this._isDeclarative = options instanceof Controller\n    this._stepper = this._isDeclarative ? options : new Ease()\n\n    // We copy the current values from the timeline because they can change\n    this._history = {}\n\n    // Store the state of the runner\n    this.enabled = true\n    this._time = 0\n    this._lastTime = 0\n\n    // At creation, the runner is in reset state\n    this._reseted = true\n\n    // Save transforms applied to this runner\n    this.transforms = new Matrix()\n    this.transformId = 1\n\n    // Looping variables\n    this._haveReversed = false\n    this._reverse = false\n    this._loopsDone = 0\n    this._swing = false\n    this._wait = 0\n    this._times = 1\n\n    this._frameId = null\n\n    // Stores how long a runner is stored after being done\n    this._persist = this._isDeclarative ? true : null\n  }\n\n  static sanitise(duration, delay, when) {\n    // Initialise the default parameters\n    let times = 1\n    let swing = false\n    let wait = 0\n    duration = duration ?? timeline.duration\n    delay = delay ?? timeline.delay\n    when = when || 'last'\n\n    // If we have an object, unpack the values\n    if (typeof duration === 'object' && !(duration instanceof Stepper)) {\n      delay = duration.delay ?? delay\n      when = duration.when ?? when\n      swing = duration.swing || swing\n      times = duration.times ?? times\n      wait = duration.wait ?? wait\n      duration = duration.duration ?? timeline.duration\n    }\n\n    return {\n      duration: duration,\n      delay: delay,\n      swing: swing,\n      times: times,\n      wait: wait,\n      when: when\n    }\n  }\n\n  active(enabled) {\n    if (enabled == null) return this.enabled\n    this.enabled = enabled\n    return this\n  }\n\n  /*\n  Private Methods\n  ===============\n  Methods that shouldn't be used externally\n  */\n  addTransform(transform) {\n    this.transforms.lmultiplyO(transform)\n    return this\n  }\n\n  after(fn) {\n    return this.on('finished', fn)\n  }\n\n  animate(duration, delay, when) {\n    const o = Runner.sanitise(duration, delay, when)\n    const runner = new Runner(o.duration)\n    if (this._timeline) runner.timeline(this._timeline)\n    if (this._element) runner.element(this._element)\n    return runner.loop(o).schedule(o.delay, o.when)\n  }\n\n  clearTransform() {\n    this.transforms = new Matrix()\n    return this\n  }\n\n  // TODO: Keep track of all transformations so that deletion is faster\n  clearTransformsFromQueue() {\n    if (\n      !this.done ||\n      !this._timeline ||\n      !this._timeline._runnerIds.includes(this.id)\n    ) {\n      this._queue = this._queue.filter((item) => {\n        return !item.isTransform\n      })\n    }\n  }\n\n  delay(delay) {\n    return this.animate(0, delay)\n  }\n\n  duration() {\n    return this._times * (this._wait + this._duration) - this._wait\n  }\n\n  during(fn) {\n    return this.queue(null, fn)\n  }\n\n  ease(fn) {\n    this._stepper = new Ease(fn)\n    return this\n  }\n  /*\n  Runner Definitions\n  ==================\n  These methods help us define the runtime behaviour of the Runner or they\n  help us make new runners from the current runner\n  */\n\n  element(element) {\n    if (element == null) return this._element\n    this._element = element\n    element._prepareRunner()\n    return this\n  }\n\n  finish() {\n    return this.step(Infinity)\n  }\n\n  loop(times, swing, wait) {\n    // Deal with the user passing in an object\n    if (typeof times === 'object') {\n      swing = times.swing\n      wait = times.wait\n      times = times.times\n    }\n\n    // Sanitise the values and store them\n    this._times = times || Infinity\n    this._swing = swing || false\n    this._wait = wait || 0\n\n    // Allow true to be passed\n    if (this._times === true) {\n      this._times = Infinity\n    }\n\n    return this\n  }\n\n  loops(p) {\n    const loopDuration = this._duration + this._wait\n    if (p == null) {\n      const loopsDone = Math.floor(this._time / loopDuration)\n      const relativeTime = this._time - loopsDone * loopDuration\n      const position = relativeTime / this._duration\n      return Math.min(loopsDone + position, this._times)\n    }\n    const whole = Math.floor(p)\n    const partial = p % 1\n    const time = loopDuration * whole + this._duration * partial\n    return this.time(time)\n  }\n\n  persist(dtOrForever) {\n    if (dtOrForever == null) return this._persist\n    this._persist = dtOrForever\n    return this\n  }\n\n  position(p) {\n    // Get all of the variables we need\n    const x = this._time\n    const d = this._duration\n    const w = this._wait\n    const t = this._times\n    const s = this._swing\n    const r = this._reverse\n    let position\n\n    if (p == null) {\n      /*\n      This function converts a time to a position in the range [0, 1]\n      The full explanation can be found in this desmos demonstration\n        https://www.desmos.com/calculator/u4fbavgche\n      The logic is slightly simplified here because we can use booleans\n      */\n\n      // Figure out the value without thinking about the start or end time\n      const f = function (x) {\n        const swinging = s * Math.floor((x % (2 * (w + d))) / (w + d))\n        const backwards = (swinging && !r) || (!swinging && r)\n        const uncliped =\n          (Math.pow(-1, backwards) * (x % (w + d))) / d + backwards\n        const clipped = Math.max(Math.min(uncliped, 1), 0)\n        return clipped\n      }\n\n      // Figure out the value by incorporating the start time\n      const endTime = t * (w + d) - w\n      position =\n        x <= 0\n          ? Math.round(f(1e-5))\n          : x < endTime\n            ? f(x)\n            : Math.round(f(endTime - 1e-5))\n      return position\n    }\n\n    // Work out the loops done and add the position to the loops done\n    const loopsDone = Math.floor(this.loops())\n    const swingForward = s && loopsDone % 2 === 0\n    const forwards = (swingForward && !r) || (r && swingForward)\n    position = loopsDone + (forwards ? p : 1 - p)\n    return this.loops(position)\n  }\n\n  progress(p) {\n    if (p == null) {\n      return Math.min(1, this._time / this.duration())\n    }\n    return this.time(p * this.duration())\n  }\n\n  /*\n  Basic Functionality\n  ===================\n  These methods allow us to attach basic functions to the runner directly\n  */\n  queue(initFn, runFn, retargetFn, isTransform) {\n    this._queue.push({\n      initialiser: initFn || noop,\n      runner: runFn || noop,\n      retarget: retargetFn,\n      isTransform: isTransform,\n      initialised: false,\n      finished: false\n    })\n    const timeline = this.timeline()\n    timeline && this.timeline()._continue()\n    return this\n  }\n\n  reset() {\n    if (this._reseted) return this\n    this.time(0)\n    this._reseted = true\n    return this\n  }\n\n  reverse(reverse) {\n    this._reverse = reverse == null ? !this._reverse : reverse\n    return this\n  }\n\n  schedule(timeline, delay, when) {\n    // The user doesn't need to pass a timeline if we already have one\n    if (!(timeline instanceof Timeline)) {\n      when = delay\n      delay = timeline\n      timeline = this.timeline()\n    }\n\n    // If there is no timeline, yell at the user...\n    if (!timeline) {\n      throw Error('Runner cannot be scheduled without timeline')\n    }\n\n    // Schedule the runner on the timeline provided\n    timeline.schedule(this, delay, when)\n    return this\n  }\n\n  step(dt) {\n    // If we are inactive, this stepper just gets skipped\n    if (!this.enabled) return this\n\n    // Update the time and get the new position\n    dt = dt == null ? 16 : dt\n    this._time += dt\n    const position = this.position()\n\n    // Figure out if we need to run the stepper in this frame\n    const running = this._lastPosition !== position && this._time >= 0\n    this._lastPosition = position\n\n    // Figure out if we just started\n    const duration = this.duration()\n    const justStarted = this._lastTime <= 0 && this._time > 0\n    const justFinished = this._lastTime < duration && this._time >= duration\n\n    this._lastTime = this._time\n    if (justStarted) {\n      this.fire('start', this)\n    }\n\n    // Work out if the runner is finished set the done flag here so animations\n    // know, that they are running in the last step (this is good for\n    // transformations which can be merged)\n    const declarative = this._isDeclarative\n    this.done = !declarative && !justFinished && this._time >= duration\n\n    // Runner is running. So its not in reset state anymore\n    this._reseted = false\n\n    let converged = false\n    // Call initialise and the run function\n    if (running || declarative) {\n      this._initialise(running)\n\n      // clear the transforms on this runner so they dont get added again and again\n      this.transforms = new Matrix()\n      converged = this._run(declarative ? dt : position)\n\n      this.fire('step', this)\n    }\n    // correct the done flag here\n    // declarative animations itself know when they converged\n    this.done = this.done || (converged && declarative)\n    if (justFinished) {\n      this.fire('finished', this)\n    }\n    return this\n  }\n\n  /*\n  Runner animation methods\n  ========================\n  Control how the animation plays\n  */\n  time(time) {\n    if (time == null) {\n      return this._time\n    }\n    const dt = time - this._time\n    this.step(dt)\n    return this\n  }\n\n  timeline(timeline) {\n    // check explicitly for undefined so we can set the timeline to null\n    if (typeof timeline === 'undefined') return this._timeline\n    this._timeline = timeline\n    return this\n  }\n\n  unschedule() {\n    const timeline = this.timeline()\n    timeline && timeline.unschedule(this)\n    return this\n  }\n\n  // Run each initialise function in the runner if required\n  _initialise(running) {\n    // If we aren't running, we shouldn't initialise when not declarative\n    if (!running && !this._isDeclarative) return\n\n    // Loop through all of the initialisers\n    for (let i = 0, len = this._queue.length; i < len; ++i) {\n      // Get the current initialiser\n      const current = this._queue[i]\n\n      // Determine whether we need to initialise\n      const needsIt = this._isDeclarative || (!current.initialised && running)\n      running = !current.finished\n\n      // Call the initialiser if we need to\n      if (needsIt && running) {\n        current.initialiser.call(this)\n        current.initialised = true\n      }\n    }\n  }\n\n  // Save a morpher to the morpher list so that we can retarget it later\n  _rememberMorpher(method, morpher) {\n    this._history[method] = {\n      morpher: morpher,\n      caller: this._queue[this._queue.length - 1]\n    }\n\n    // We have to resume the timeline in case a controller\n    // is already done without being ever run\n    // This can happen when e.g. this is done:\n    //    anim = el.animate(new SVG.Spring)\n    // and later\n    //    anim.move(...)\n    if (this._isDeclarative) {\n      const timeline = this.timeline()\n      timeline && timeline.play()\n    }\n  }\n\n  // Try to set the target for a morpher if the morpher exists, otherwise\n  // Run each run function for the position or dt given\n  _run(positionOrDt) {\n    // Run all of the _queue directly\n    let allfinished = true\n    for (let i = 0, len = this._queue.length; i < len; ++i) {\n      // Get the current function to run\n      const current = this._queue[i]\n\n      // Run the function if its not finished, we keep track of the finished\n      // flag for the sake of declarative _queue\n      const converged = current.runner.call(this, positionOrDt)\n      current.finished = current.finished || converged === true\n      allfinished = allfinished && current.finished\n    }\n\n    // We report when all of the constructors are finished\n    return allfinished\n  }\n\n  // do nothing and return false\n  _tryRetarget(method, target, extra) {\n    if (this._history[method]) {\n      // if the last method wasn't even initialised, throw it away\n      if (!this._history[method].caller.initialised) {\n        const index = this._queue.indexOf(this._history[method].caller)\n        this._queue.splice(index, 1)\n        return false\n      }\n\n      // for the case of transformations, we use the special retarget function\n      // which has access to the outer scope\n      if (this._history[method].caller.retarget) {\n        this._history[method].caller.retarget.call(this, target, extra)\n        // for everything else a simple morpher change is sufficient\n      } else {\n        this._history[method].morpher.to(target)\n      }\n\n      this._history[method].caller.finished = false\n      const timeline = this.timeline()\n      timeline && timeline.play()\n      return true\n    }\n    return false\n  }\n}\n\nRunner.id = 0\n\nexport class FakeRunner {\n  constructor(transforms = new Matrix(), id = -1, done = true) {\n    this.transforms = transforms\n    this.id = id\n    this.done = done\n  }\n\n  clearTransformsFromQueue() {}\n}\n\nextend([Runner, FakeRunner], {\n  mergeWith(runner) {\n    return new FakeRunner(\n      runner.transforms.lmultiply(this.transforms),\n      runner.id\n    )\n  }\n})\n\n// FakeRunner.emptyRunner = new FakeRunner()\n\nconst lmultiply = (last, curr) => last.lmultiplyO(curr)\nconst getRunnerTransform = (runner) => runner.transforms\n\nfunction mergeTransforms() {\n  // Find the matrix to apply to the element and apply it\n  const runners = this._transformationRunners.runners\n  const netTransform = runners\n    .map(getRunnerTransform)\n    .reduce(lmultiply, new Matrix())\n\n  this.transform(netTransform)\n\n  this._transformationRunners.merge()\n\n  if (this._transformationRunners.length() === 1) {\n    this._frameId = null\n  }\n}\n\nexport class RunnerArray {\n  constructor() {\n    this.runners = []\n    this.ids = []\n  }\n\n  add(runner) {\n    if (this.runners.includes(runner)) return\n    const id = runner.id + 1\n\n    this.runners.push(runner)\n    this.ids.push(id)\n\n    return this\n  }\n\n  clearBefore(id) {\n    const deleteCnt = this.ids.indexOf(id + 1) || 1\n    this.ids.splice(0, deleteCnt, 0)\n    this.runners\n      .splice(0, deleteCnt, new FakeRunner())\n      .forEach((r) => r.clearTransformsFromQueue())\n    return this\n  }\n\n  edit(id, newRunner) {\n    const index = this.ids.indexOf(id + 1)\n    this.ids.splice(index, 1, id + 1)\n    this.runners.splice(index, 1, newRunner)\n    return this\n  }\n\n  getByID(id) {\n    return this.runners[this.ids.indexOf(id + 1)]\n  }\n\n  length() {\n    return this.ids.length\n  }\n\n  merge() {\n    let lastRunner = null\n    for (let i = 0; i < this.runners.length; ++i) {\n      const runner = this.runners[i]\n\n      const condition =\n        lastRunner &&\n        runner.done &&\n        lastRunner.done &&\n        // don't merge runner when persisted on timeline\n        (!runner._timeline ||\n          !runner._timeline._runnerIds.includes(runner.id)) &&\n        (!lastRunner._timeline ||\n          !lastRunner._timeline._runnerIds.includes(lastRunner.id))\n\n      if (condition) {\n        // the +1 happens in the function\n        this.remove(runner.id)\n        const newRunner = runner.mergeWith(lastRunner)\n        this.edit(lastRunner.id, newRunner)\n        lastRunner = newRunner\n        --i\n      } else {\n        lastRunner = runner\n      }\n    }\n\n    return this\n  }\n\n  remove(id) {\n    const index = this.ids.indexOf(id + 1)\n    this.ids.splice(index, 1)\n    this.runners.splice(index, 1)\n    return this\n  }\n}\n\nregisterMethods({\n  Element: {\n    animate(duration, delay, when) {\n      const o = Runner.sanitise(duration, delay, when)\n      const timeline = this.timeline()\n      return new Runner(o.duration)\n        .loop(o)\n        .element(this)\n        .timeline(timeline.play())\n        .schedule(o.delay, o.when)\n    },\n\n    delay(by, when) {\n      return this.animate(0, by, when)\n    },\n\n    // this function searches for all runners on the element and deletes the ones\n    // which run before the current one. This is because absolute transformations\n    // overwrite anything anyway so there is no need to waste time computing\n    // other runners\n    _clearTransformRunnersBefore(currentRunner) {\n      this._transformationRunners.clearBefore(currentRunner.id)\n    },\n\n    _currentTransform(current) {\n      return (\n        this._transformationRunners.runners\n          // we need the equal sign here to make sure, that also transformations\n          // on the same runner which execute before the current transformation are\n          // taken into account\n          .filter((runner) => runner.id <= current.id)\n          .map(getRunnerTransform)\n          .reduce(lmultiply, new Matrix())\n      )\n    },\n\n    _addRunner(runner) {\n      this._transformationRunners.add(runner)\n\n      // Make sure that the runner merge is executed at the very end of\n      // all Animator functions. That is why we use immediate here to execute\n      // the merge right after all frames are run\n      Animator.cancelImmediate(this._frameId)\n      this._frameId = Animator.immediate(mergeTransforms.bind(this))\n    },\n\n    _prepareRunner() {\n      if (this._frameId == null) {\n        this._transformationRunners = new RunnerArray().add(\n          new FakeRunner(new Matrix(this))\n        )\n      }\n    }\n  }\n})\n\n// Will output the elements from array A that are not in the array B\nconst difference = (a, b) => a.filter((x) => !b.includes(x))\n\nextend(Runner, {\n  attr(a, v) {\n    return this.styleAttr('attr', a, v)\n  },\n\n  // Add animatable styles\n  css(s, v) {\n    return this.styleAttr('css', s, v)\n  },\n\n  styleAttr(type, nameOrAttrs, val) {\n    if (typeof nameOrAttrs === 'string') {\n      return this.styleAttr(type, { [nameOrAttrs]: val })\n    }\n\n    let attrs = nameOrAttrs\n    if (this._tryRetarget(type, attrs)) return this\n\n    let morpher = new Morphable(this._stepper).to(attrs)\n    let keys = Object.keys(attrs)\n\n    this.queue(\n      function () {\n        morpher = morpher.from(this.element()[type](keys))\n      },\n      function (pos) {\n        this.element()[type](morpher.at(pos).valueOf())\n        return morpher.done()\n      },\n      function (newToAttrs) {\n        // Check if any new keys were added\n        const newKeys = Object.keys(newToAttrs)\n        const differences = difference(newKeys, keys)\n\n        // If their are new keys, initialize them and add them to morpher\n        if (differences.length) {\n          // Get the values\n          const addedFromAttrs = this.element()[type](differences)\n\n          // Get the already initialized values\n          const oldFromAttrs = new ObjectBag(morpher.from()).valueOf()\n\n          // Merge old and new\n          Object.assign(oldFromAttrs, addedFromAttrs)\n          morpher.from(oldFromAttrs)\n        }\n\n        // Get the object from the morpher\n        const oldToAttrs = new ObjectBag(morpher.to()).valueOf()\n\n        // Merge in new attributes\n        Object.assign(oldToAttrs, newToAttrs)\n\n        // Change morpher target\n        morpher.to(oldToAttrs)\n\n        // Make sure that we save the work we did so we don't need it to do again\n        keys = newKeys\n        attrs = newToAttrs\n      }\n    )\n\n    this._rememberMorpher(type, morpher)\n    return this\n  },\n\n  zoom(level, point) {\n    if (this._tryRetarget('zoom', level, point)) return this\n\n    let morpher = new Morphable(this._stepper).to(new SVGNumber(level))\n\n    this.queue(\n      function () {\n        morpher = morpher.from(this.element().zoom())\n      },\n      function (pos) {\n        this.element().zoom(morpher.at(pos), point)\n        return morpher.done()\n      },\n      function (newLevel, newPoint) {\n        point = newPoint\n        morpher.to(newLevel)\n      }\n    )\n\n    this._rememberMorpher('zoom', morpher)\n    return this\n  },\n\n  /**\n   ** absolute transformations\n   **/\n\n  //\n  // M v -----|-----(D M v = F v)------|----->  T v\n  //\n  // 1. define the final state (T) and decompose it (once)\n  //    t = [tx, ty, the, lam, sy, sx]\n  // 2. on every frame: pull the current state of all previous transforms\n  //    (M - m can change)\n  //   and then write this as m = [tx0, ty0, the0, lam0, sy0, sx0]\n  // 3. Find the interpolated matrix F(pos) = m + pos * (t - m)\n  //   - Note F(0) = M\n  //   - Note F(1) = T\n  // 4. Now you get the delta matrix as a result: D = F * inv(M)\n\n  transform(transforms, relative, affine) {\n    // If we have a declarative function, we should retarget it if possible\n    relative = transforms.relative || relative\n    if (\n      this._isDeclarative &&\n      !relative &&\n      this._tryRetarget('transform', transforms)\n    ) {\n      return this\n    }\n\n    // Parse the parameters\n    const isMatrix = Matrix.isMatrixLike(transforms)\n    affine =\n      transforms.affine != null\n        ? transforms.affine\n        : affine != null\n          ? affine\n          : !isMatrix\n\n    // Create a morpher and set its type\n    const morpher = new Morphable(this._stepper).type(\n      affine ? TransformBag : Matrix\n    )\n\n    let origin\n    let element\n    let current\n    let currentAngle\n    let startTransform\n\n    function setup() {\n      // make sure element and origin is defined\n      element = element || this.element()\n      origin = origin || getOrigin(transforms, element)\n\n      startTransform = new Matrix(relative ? undefined : element)\n\n      // add the runner to the element so it can merge transformations\n      element._addRunner(this)\n\n      // Deactivate all transforms that have run so far if we are absolute\n      if (!relative) {\n        element._clearTransformRunnersBefore(this)\n      }\n    }\n\n    function run(pos) {\n      // clear all other transforms before this in case something is saved\n      // on this runner. We are absolute. We dont need these!\n      if (!relative) this.clearTransform()\n\n      const { x, y } = new Point(origin).transform(\n        element._currentTransform(this)\n      )\n\n      let target = new Matrix({ ...transforms, origin: [x, y] })\n      let start = this._isDeclarative && current ? current : startTransform\n\n      if (affine) {\n        target = target.decompose(x, y)\n        start = start.decompose(x, y)\n\n        // Get the current and target angle as it was set\n        const rTarget = target.rotate\n        const rCurrent = start.rotate\n\n        // Figure out the shortest path to rotate directly\n        const possibilities = [rTarget - 360, rTarget, rTarget + 360]\n        const distances = possibilities.map((a) => Math.abs(a - rCurrent))\n        const shortest = Math.min(...distances)\n        const index = distances.indexOf(shortest)\n        target.rotate = possibilities[index]\n      }\n\n      if (relative) {\n        // we have to be careful here not to overwrite the rotation\n        // with the rotate method of Matrix\n        if (!isMatrix) {\n          target.rotate = transforms.rotate || 0\n        }\n        if (this._isDeclarative && currentAngle) {\n          start.rotate = currentAngle\n        }\n      }\n\n      morpher.from(start)\n      morpher.to(target)\n\n      const affineParameters = morpher.at(pos)\n      currentAngle = affineParameters.rotate\n      current = new Matrix(affineParameters)\n\n      this.addTransform(current)\n      element._addRunner(this)\n      return morpher.done()\n    }\n\n    function retarget(newTransforms) {\n      // only get a new origin if it changed since the last call\n      if (\n        (newTransforms.origin || 'center').toString() !==\n        (transforms.origin || 'center').toString()\n      ) {\n        origin = getOrigin(newTransforms, element)\n      }\n\n      // overwrite the old transformations with the new ones\n      transforms = { ...newTransforms, origin }\n    }\n\n    this.queue(setup, run, retarget, true)\n    this._isDeclarative && this._rememberMorpher('transform', morpher)\n    return this\n  },\n\n  // Animatable x-axis\n  x(x) {\n    return this._queueNumber('x', x)\n  },\n\n  // Animatable y-axis\n  y(y) {\n    return this._queueNumber('y', y)\n  },\n\n  ax(x) {\n    return this._queueNumber('ax', x)\n  },\n\n  ay(y) {\n    return this._queueNumber('ay', y)\n  },\n\n  dx(x = 0) {\n    return this._queueNumberDelta('x', x)\n  },\n\n  dy(y = 0) {\n    return this._queueNumberDelta('y', y)\n  },\n\n  dmove(x, y) {\n    return this.dx(x).dy(y)\n  },\n\n  _queueNumberDelta(method, to) {\n    to = new SVGNumber(to)\n\n    // Try to change the target if we have this method already registered\n    if (this._tryRetarget(method, to)) return this\n\n    // Make a morpher and queue the animation\n    const morpher = new Morphable(this._stepper).to(to)\n    let from = null\n    this.queue(\n      function () {\n        from = this.element()[method]()\n        morpher.from(from)\n        morpher.to(from + to)\n      },\n      function (pos) {\n        this.element()[method](morpher.at(pos))\n        return morpher.done()\n      },\n      function (newTo) {\n        morpher.to(from + new SVGNumber(newTo))\n      }\n    )\n\n    // Register the morpher so that if it is changed again, we can retarget it\n    this._rememberMorpher(method, morpher)\n    return this\n  },\n\n  _queueObject(method, to) {\n    // Try to change the target if we have this method already registered\n    if (this._tryRetarget(method, to)) return this\n\n    // Make a morpher and queue the animation\n    const morpher = new Morphable(this._stepper).to(to)\n    this.queue(\n      function () {\n        morpher.from(this.element()[method]())\n      },\n      function (pos) {\n        this.element()[method](morpher.at(pos))\n        return morpher.done()\n      }\n    )\n\n    // Register the morpher so that if it is changed again, we can retarget it\n    this._rememberMorpher(method, morpher)\n    return this\n  },\n\n  _queueNumber(method, value) {\n    return this._queueObject(method, new SVGNumber(value))\n  },\n\n  // Animatable center x-axis\n  cx(x) {\n    return this._queueNumber('cx', x)\n  },\n\n  // Animatable center y-axis\n  cy(y) {\n    return this._queueNumber('cy', y)\n  },\n\n  // Add animatable move\n  move(x, y) {\n    return this.x(x).y(y)\n  },\n\n  amove(x, y) {\n    return this.ax(x).ay(y)\n  },\n\n  // Add animatable center\n  center(x, y) {\n    return this.cx(x).cy(y)\n  },\n\n  // Add animatable size\n  size(width, height) {\n    // animate bbox based size for all other elements\n    let box\n\n    if (!width || !height) {\n      box = this._element.bbox()\n    }\n\n    if (!width) {\n      width = (box.width / box.height) * height\n    }\n\n    if (!height) {\n      height = (box.height / box.width) * width\n    }\n\n    return this.width(width).height(height)\n  },\n\n  // Add animatable width\n  width(width) {\n    return this._queueNumber('width', width)\n  },\n\n  // Add animatable height\n  height(height) {\n    return this._queueNumber('height', height)\n  },\n\n  // Add animatable plot\n  plot(a, b, c, d) {\n    // Lines can be plotted with 4 arguments\n    if (arguments.length === 4) {\n      return this.plot([a, b, c, d])\n    }\n\n    if (this._tryRetarget('plot', a)) return this\n\n    const morpher = new Morphable(this._stepper)\n      .type(this._element.MorphArray)\n      .to(a)\n\n    this.queue(\n      function () {\n        morpher.from(this._element.array())\n      },\n      function (pos) {\n        this._element.plot(morpher.at(pos))\n        return morpher.done()\n      }\n    )\n\n    this._rememberMorpher('plot', morpher)\n    return this\n  },\n\n  // Add leading method\n  leading(value) {\n    return this._queueNumber('leading', value)\n  },\n\n  // Add animatable viewbox\n  viewbox(x, y, width, height) {\n    return this._queueObject('viewbox', new Box(x, y, width, height))\n  },\n\n  update(o) {\n    if (typeof o !== 'object') {\n      return this.update({\n        offset: arguments[0],\n        color: arguments[1],\n        opacity: arguments[2]\n      })\n    }\n\n    if (o.opacity != null) this.attr('stop-opacity', o.opacity)\n    if (o.color != null) this.attr('stop-color', o.color)\n    if (o.offset != null) this.attr('offset', o.offset)\n\n    return this\n  }\n})\n\nextend(Runner, { rx, ry, from, to })\nregister(Runner, 'Runner')\n", "import {\n  adopt,\n  nodeOrNew,\n  register,\n  wrapWithAttrCheck\n} from '../utils/adopter.js'\nimport { svg, xlink, xmlns } from '../modules/core/namespaces.js'\nimport { registerMethods } from '../utils/methods.js'\nimport Container from './Container.js'\nimport Defs from './Defs.js'\nimport { globals } from '../utils/window.js'\n\nexport default class Svg extends Container {\n  constructor(node, attrs = node) {\n    super(nodeOrNew('svg', node), attrs)\n    this.namespace()\n  }\n\n  // Creates and returns defs element\n  defs() {\n    if (!this.isRoot()) return this.root().defs()\n\n    return adopt(this.node.querySelector('defs')) || this.put(new Defs())\n  }\n\n  isRoot() {\n    return (\n      !this.node.parentNode ||\n      (!(this.node.parentNode instanceof globals.window.SVGElement) &&\n        this.node.parentNode.nodeName !== '#document-fragment')\n    )\n  }\n\n  // Add namespaces\n  namespace() {\n    if (!this.isRoot()) return this.root().namespace()\n    return this.attr({ xmlns: svg, version: '1.1' }).attr(\n      'xmlns:xlink',\n      xlink,\n      xmlns\n    )\n  }\n\n  removeNamespace() {\n    return this.attr({ xmlns: null, version: null })\n      .attr('xmlns:xlink', null, xmlns)\n      .attr('xmlns:svgjs', null, xmlns)\n  }\n\n  // Check if this is a root svg\n  // If not, call root() from this element\n  root() {\n    if (this.isRoot()) return this\n    return super.root()\n  }\n}\n\nregisterMethods({\n  Container: {\n    // Create nested svg document\n    nested: wrapWithAttrCheck(function () {\n      return this.put(new Svg())\n    })\n  }\n})\n\nregister(Svg, 'Svg', true)\n", "import { nodeOrNew, register, wrapWithAttrCheck } from '../utils/adopter.js'\nimport { registerMethods } from '../utils/methods.js'\nimport Container from './Container.js'\n\nexport default class Symbol extends Container {\n  // Initialize node\n  constructor(node, attrs = node) {\n    super(nodeOrNew('symbol', node), attrs)\n  }\n}\n\nregisterMethods({\n  Container: {\n    symbol: wrapWithAttrCheck(function () {\n      return this.put(new Symbol())\n    })\n  }\n})\n\nregister(Symbol, 'Symbol')\n", "import { globals } from '../../utils/window.js'\n\n// Create plain text node\nexport function plain(text) {\n  // clear if build mode is disabled\n  if (this._build === false) {\n    this.clear()\n  }\n\n  // create text node\n  this.node.appendChild(globals.document.createTextNode(text))\n\n  return this\n}\n\n// Get length of text element\nexport function length() {\n  return this.node.getComputedTextLength()\n}\n\n// Move over x-axis\n// Text is moved by its bounding box\n// text-anchor does NOT matter\nexport function x(x, box = this.bbox()) {\n  if (x == null) {\n    return box.x\n  }\n\n  return this.attr('x', this.attr('x') + x - box.x)\n}\n\n// Move over y-axis\nexport function y(y, box = this.bbox()) {\n  if (y == null) {\n    return box.y\n  }\n\n  return this.attr('y', this.attr('y') + y - box.y)\n}\n\nexport function move(x, y, box = this.bbox()) {\n  return this.x(x, box).y(y, box)\n}\n\n// Move center over x-axis\nexport function cx(x, box = this.bbox()) {\n  if (x == null) {\n    return box.cx\n  }\n\n  return this.attr('x', this.attr('x') + x - box.cx)\n}\n\n// Move center over y-axis\nexport function cy(y, box = this.bbox()) {\n  if (y == null) {\n    return box.cy\n  }\n\n  return this.attr('y', this.attr('y') + y - box.cy)\n}\n\nexport function center(x, y, box = this.bbox()) {\n  return this.cx(x, box).cy(y, box)\n}\n\nexport function ax(x) {\n  return this.attr('x', x)\n}\n\nexport function ay(y) {\n  return this.attr('y', y)\n}\n\nexport function amove(x, y) {\n  return this.ax(x).ay(y)\n}\n\n// Enable / disable build mode\nexport function build(build) {\n  this._build = !!build\n  return this\n}\n", "import {\n  adopt,\n  extend,\n  nodeOrNew,\n  register,\n  wrapWithAttrCheck\n} from '../utils/adopter.js'\nimport { registerMethods } from '../utils/methods.js'\nimport SVGNumber from '../types/SVGNumber.js'\nimport Shape from './Shape.js'\nimport { globals } from '../utils/window.js'\nimport * as textable from '../modules/core/textable.js'\nimport { isDescriptive, writeDataToDom } from '../utils/utils.js'\n\nexport default class Text extends Shape {\n  // Initialize node\n  constructor(node, attrs = node) {\n    super(nodeOrNew('text', node), attrs)\n\n    this.dom.leading = this.dom.leading ?? new SVGNumber(1.3) // store leading value for rebuilding\n    this._rebuild = true // enable automatic updating of dy values\n    this._build = false // disable build mode for adding multiple lines\n  }\n\n  // Set / get leading\n  leading(value) {\n    // act as getter\n    if (value == null) {\n      return this.dom.leading\n    }\n\n    // act as setter\n    this.dom.leading = new SVGNumber(value)\n\n    return this.rebuild()\n  }\n\n  // Rebuild appearance type\n  rebuild(rebuild) {\n    // store new rebuild flag if given\n    if (typeof rebuild === 'boolean') {\n      this._rebuild = rebuild\n    }\n\n    // define position of all lines\n    if (this._rebuild) {\n      const self = this\n      let blankLineOffset = 0\n      const leading = this.dom.leading\n\n      this.each(function (i) {\n        if (isDescriptive(this.node)) return\n\n        const fontSize = globals.window\n          .getComputedStyle(this.node)\n          .getPropertyValue('font-size')\n\n        const dy = leading * new SVGNumber(fontSize)\n\n        if (this.dom.newLined) {\n          this.attr('x', self.attr('x'))\n\n          if (this.text() === '\\n') {\n            blankLineOffset += dy\n          } else {\n            this.attr('dy', i ? dy + blankLineOffset : 0)\n            blankLineOffset = 0\n          }\n        }\n      })\n\n      this.fire('rebuild')\n    }\n\n    return this\n  }\n\n  // overwrite method from parent to set data properly\n  setData(o) {\n    this.dom = o\n    this.dom.leading = new SVGNumber(o.leading || 1.3)\n    return this\n  }\n\n  writeDataToDom() {\n    writeDataToDom(this, this.dom, { leading: 1.3 })\n    return this\n  }\n\n  // Set the text content\n  text(text) {\n    // act as getter\n    if (text === undefined) {\n      const children = this.node.childNodes\n      let firstLine = 0\n      text = ''\n\n      for (let i = 0, len = children.length; i < len; ++i) {\n        // skip textPaths - they are no lines\n        if (children[i].nodeName === 'textPath' || isDescriptive(children[i])) {\n          if (i === 0) firstLine = i + 1\n          continue\n        }\n\n        // add newline if its not the first child and newLined is set to true\n        if (\n          i !== firstLine &&\n          children[i].nodeType !== 3 &&\n          adopt(children[i]).dom.newLined === true\n        ) {\n          text += '\\n'\n        }\n\n        // add content of this node\n        text += children[i].textContent\n      }\n\n      return text\n    }\n\n    // remove existing content\n    this.clear().build(true)\n\n    if (typeof text === 'function') {\n      // call block\n      text.call(this, this)\n    } else {\n      // store text and make sure text is not blank\n      text = (text + '').split('\\n')\n\n      // build new lines\n      for (let j = 0, jl = text.length; j < jl; j++) {\n        this.newLine(text[j])\n      }\n    }\n\n    // disable build mode and rebuild lines\n    return this.build(false).rebuild()\n  }\n}\n\nextend(Text, textable)\n\nregisterMethods({\n  Container: {\n    // Create text element\n    text: wrapWithAttrCheck(function (text = '') {\n      return this.put(new Text()).text(text)\n    }),\n\n    // Create plain text element\n    plain: wrapWithAttrCheck(function (text = '') {\n      return this.put(new Text()).plain(text)\n    })\n  }\n})\n\nregister(Text, 'Text')\n", "import {\n  extend,\n  nodeOrNew,\n  register,\n  wrapWithAttrCheck\n} from '../utils/adopter.js'\nimport { globals } from '../utils/window.js'\nimport { registerMethods } from '../utils/methods.js'\nimport SVGNumber from '../types/SVGNumber.js'\nimport Shape from './Shape.js'\nimport Text from './Text.js'\nimport * as textable from '../modules/core/textable.js'\n\nexport default class Tspan extends Shape {\n  // Initialize node\n  constructor(node, attrs = node) {\n    super(nodeOrNew('tspan', node), attrs)\n    this._build = false // disable build mode for adding multiple lines\n  }\n\n  // Shortcut dx\n  dx(dx) {\n    return this.attr('dx', dx)\n  }\n\n  // Shortcut dy\n  dy(dy) {\n    return this.attr('dy', dy)\n  }\n\n  // Create new line\n  newLine() {\n    // mark new line\n    this.dom.newLined = true\n\n    // fetch parent\n    const text = this.parent()\n\n    // early return in case we are not in a text element\n    if (!(text instanceof Text)) {\n      return this\n    }\n\n    const i = text.index(this)\n\n    const fontSize = globals.window\n      .getComputedStyle(this.node)\n      .getPropertyValue('font-size')\n    const dy = text.dom.leading * new SVGNumber(fontSize)\n\n    // apply new position\n    return this.dy(i ? dy : 0).attr('x', text.x())\n  }\n\n  // Set text content\n  text(text) {\n    if (text == null)\n      return this.node.textContent + (this.dom.newLined ? '\\n' : '')\n\n    if (typeof text === 'function') {\n      this.clear().build(true)\n      text.call(this, this)\n      this.build(false)\n    } else {\n      this.plain(text)\n    }\n\n    return this\n  }\n}\n\nextend(Tspan, textable)\n\nregisterMethods({\n  Tspan: {\n    tspan: wrapWithAttrCheck(function (text = '') {\n      const tspan = new Tspan()\n\n      // clear if build mode is disabled\n      if (!this._build) {\n        this.clear()\n      }\n\n      // add new tspan\n      return this.put(tspan).text(text)\n    })\n  },\n  Text: {\n    newLine: function (text = '') {\n      return this.tspan(text).newLine()\n    }\n  }\n})\n\nregister(Tspan, 'Tspan')\n", "import { cx, cy, height, width, x, y } from '../modules/core/circled.js'\nimport {\n  extend,\n  nodeOrNew,\n  register,\n  wrapWithAttrCheck\n} from '../utils/adopter.js'\nimport { registerMethods } from '../utils/methods.js'\nimport SVGNumber from '../types/SVGNumber.js'\nimport Shape from './Shape.js'\n\nexport default class Circle extends Shape {\n  constructor(node, attrs = node) {\n    super(nodeOrNew('circle', node), attrs)\n  }\n\n  radius(r) {\n    return this.attr('r', r)\n  }\n\n  // Radius x value\n  rx(rx) {\n    return this.attr('r', rx)\n  }\n\n  // Alias radius x value\n  ry(ry) {\n    return this.rx(ry)\n  }\n\n  size(size) {\n    return this.radius(new SVGNumber(size).divide(2))\n  }\n}\n\nextend(Circle, { x, y, cx, cy, width, height })\n\nregisterMethods({\n  Container: {\n    // Create circle element\n    circle: wrapWithAttrCheck(function (size = 0) {\n      return this.put(new Circle()).size(size).move(0, 0)\n    })\n  }\n})\n\nregister(Circle, 'Circle')\n", "import { nodeOrNew, register, wrapWithAttrCheck } from '../utils/adopter.js'\nimport { registerMethods } from '../utils/methods.js'\nimport Container from './Container.js'\nimport baseFind from '../modules/core/selector.js'\n\nexport default class ClipPath extends Container {\n  constructor(node, attrs = node) {\n    super(nodeOrNew('clipPath', node), attrs)\n  }\n\n  // Unclip all clipped elements and remove itself\n  remove() {\n    // unclip all targets\n    this.targets().forEach(function (el) {\n      el.unclip()\n    })\n\n    // remove clipPath from parent\n    return super.remove()\n  }\n\n  targets() {\n    return baseFind('svg [clip-path*=' + this.id() + ']')\n  }\n}\n\nregisterMethods({\n  Container: {\n    // Create clipping element\n    clip: wrapWithAttrCheck(function () {\n      return this.defs().put(new ClipPath())\n    })\n  },\n  Element: {\n    // Distribute clipPath to svg element\n    clipper() {\n      return this.reference('clip-path')\n    },\n\n    clipWith(element) {\n      // use given clip or create a new one\n      const clipper =\n        element instanceof ClipPath\n          ? element\n          : this.parent().clip().add(element)\n\n      // apply mask\n      return this.attr('clip-path', 'url(#' + clipper.id() + ')')\n    },\n\n    // Unclip element\n    unclip() {\n      return this.attr('clip-path', null)\n    }\n  }\n})\n\nregister(ClipPath, 'ClipPath')\n", "import { nodeOrNew, register, wrapWithAttrCheck } from '../utils/adopter.js'\nimport { registerMethods } from '../utils/methods.js'\nimport Element from './Element.js'\n\nexport default class ForeignObject extends Element {\n  constructor(node, attrs = node) {\n    super(nodeOrNew('foreignObject', node), attrs)\n  }\n}\n\nregisterMethods({\n  Container: {\n    foreignObject: wrapWithAttrCheck(function (width, height) {\n      return this.put(new ForeignObject()).size(width, height)\n    })\n  }\n})\n\nregister(ForeignObject, 'ForeignObject')\n", "import Matrix from '../../types/Matrix.js'\nimport Point from '../../types/Point.js'\nimport Box from '../../types/Box.js'\nimport { proportionalSize } from '../../utils/utils.js'\nimport { getWindow } from '../../utils/window.js'\n\nexport function dmove(dx, dy) {\n  this.children().forEach((child) => {\n    let bbox\n\n    // We have to wrap this for elements that dont have a bbox\n    // e.g. title and other descriptive elements\n    try {\n      // Get the childs bbox\n      // Bug: https://bugzilla.mozilla.org/show_bug.cgi?id=1905039\n      // Because bbox for nested svgs returns the contents bbox in the coordinate space of the svg itself (weird!), we cant use bbox for svgs\n      // Therefore we have to use getBoundingClientRect. But THAT is broken (as explained in the bug).\n      // Funnily enough the broken behavior would work for us but that breaks it in chrome\n      // So we have to replicate the broken behavior of FF by just reading the attributes of the svg itself\n      bbox =\n        child.node instanceof getWindow().SVGSVGElement\n          ? new Box(child.attr(['x', 'y', 'width', 'height']))\n          : child.bbox()\n    } catch (e) {\n      return\n    }\n\n    // Get childs matrix\n    const m = new Matrix(child)\n    // Translate childs matrix by amount and\n    // transform it back into parents space\n    const matrix = m.translate(dx, dy).transform(m.inverse())\n    // Calculate new x and y from old box\n    const p = new Point(bbox.x, bbox.y).transform(matrix)\n    // Move element\n    child.move(p.x, p.y)\n  })\n\n  return this\n}\n\nexport function dx(dx) {\n  return this.dmove(dx, 0)\n}\n\nexport function dy(dy) {\n  return this.dmove(0, dy)\n}\n\nexport function height(height, box = this.bbox()) {\n  if (height == null) return box.height\n  return this.size(box.width, height, box)\n}\n\nexport function move(x = 0, y = 0, box = this.bbox()) {\n  const dx = x - box.x\n  const dy = y - box.y\n\n  return this.dmove(dx, dy)\n}\n\nexport function size(width, height, box = this.bbox()) {\n  const p = proportionalSize(this, width, height, box)\n  const scaleX = p.width / box.width\n  const scaleY = p.height / box.height\n\n  this.children().forEach((child) => {\n    const o = new Point(box).transform(new Matrix(child).inverse())\n    child.scale(scaleX, scaleY, o.x, o.y)\n  })\n\n  return this\n}\n\nexport function width(width, box = this.bbox()) {\n  if (width == null) return box.width\n  return this.size(width, box.height, box)\n}\n\nexport function x(x, box = this.bbox()) {\n  if (x == null) return box.x\n  return this.move(x, box.y, box)\n}\n\nexport function y(y, box = this.bbox()) {\n  if (y == null) return box.y\n  return this.move(box.x, y, box)\n}\n", "import {\n  nodeOrNew,\n  register,\n  wrapWithAttrCheck,\n  extend\n} from '../utils/adopter.js'\nimport { registerMethods } from '../utils/methods.js'\nimport Container from './Container.js'\nimport * as containerGeometry from '../modules/core/containerGeometry.js'\n\nexport default class G extends Container {\n  constructor(node, attrs = node) {\n    super(nodeOrNew('g', node), attrs)\n  }\n}\n\nextend(G, containerGeometry)\n\nregisterMethods({\n  Container: {\n    // Create a group element\n    group: wrapWithAttrCheck(function () {\n      return this.put(new G())\n    })\n  }\n})\n\nregister(G, 'G')\n", "import {\n  nodeOr<PERSON><PERSON>,\n  register,\n  wrapWithAttrCheck,\n  extend\n} from '../utils/adopter.js'\nimport { registerMethods } from '../utils/methods.js'\nimport { xlink } from '../modules/core/namespaces.js'\nimport Container from './Container.js'\nimport * as containerGeometry from '../modules/core/containerGeometry.js'\n\nexport default class A extends Container {\n  constructor(node, attrs = node) {\n    super(nodeOrNew('a', node), attrs)\n  }\n\n  // Link target attribute\n  target(target) {\n    return this.attr('target', target)\n  }\n\n  // Link url\n  to(url) {\n    return this.attr('href', url, xlink)\n  }\n}\n\nextend(A, containerGeometry)\n\nregisterMethods({\n  Container: {\n    // Create a hyperlink element\n    link: wrapWithAttrCheck(function (url) {\n      return this.put(new A()).to(url)\n    })\n  },\n  Element: {\n    unlink() {\n      const link = this.linker()\n\n      if (!link) return this\n\n      const parent = link.parent()\n\n      if (!parent) {\n        return this.remove()\n      }\n\n      const index = parent.index(link)\n      parent.add(this, index)\n\n      link.remove()\n      return this\n    },\n    linkTo(url) {\n      // reuse old link if possible\n      let link = this.linker()\n\n      if (!link) {\n        link = new A()\n        this.wrap(link)\n      }\n\n      if (typeof url === 'function') {\n        url.call(link, link)\n      } else {\n        link.to(url)\n      }\n\n      return this\n    },\n    linker() {\n      const link = this.parent()\n      if (link && link.node.nodeName.toLowerCase() === 'a') {\n        return link\n      }\n\n      return null\n    }\n  }\n})\n\nregister(A, 'A')\n", "import { nodeOrNew, register, wrapWithAttrCheck } from '../utils/adopter.js'\nimport { registerMethods } from '../utils/methods.js'\nimport Container from './Container.js'\nimport baseFind from '../modules/core/selector.js'\n\nexport default class Mask extends Container {\n  // Initialize node\n  constructor(node, attrs = node) {\n    super(nodeOrNew('mask', node), attrs)\n  }\n\n  // Unmask all masked elements and remove itself\n  remove() {\n    // unmask all targets\n    this.targets().forEach(function (el) {\n      el.unmask()\n    })\n\n    // remove mask from parent\n    return super.remove()\n  }\n\n  targets() {\n    return baseFind('svg [mask*=' + this.id() + ']')\n  }\n}\n\nregisterMethods({\n  Container: {\n    mask: wrapWithAttrCheck(function () {\n      return this.defs().put(new Mask())\n    })\n  },\n  Element: {\n    // Distribute mask to svg element\n    masker() {\n      return this.reference('mask')\n    },\n\n    maskWith(element) {\n      // use given mask or create a new one\n      const masker =\n        element instanceof Mask ? element : this.parent().mask().add(element)\n\n      // apply mask\n      return this.attr('mask', 'url(#' + masker.id() + ')')\n    },\n\n    // Unmask element\n    unmask() {\n      return this.attr('mask', null)\n    }\n  }\n})\n\nregister(Mask, 'Mask')\n", "import { nodeOrNew, register } from '../utils/adopter.js'\nimport Element from './Element.js'\nimport SVGNumber from '../types/SVGNumber.js'\nimport { registerMethods } from '../utils/methods.js'\n\nexport default class Stop extends Element {\n  constructor(node, attrs = node) {\n    super(nodeOrNew('stop', node), attrs)\n  }\n\n  // add color stops\n  update(o) {\n    if (typeof o === 'number' || o instanceof SVGNumber) {\n      o = {\n        offset: arguments[0],\n        color: arguments[1],\n        opacity: arguments[2]\n      }\n    }\n\n    // set attributes\n    if (o.opacity != null) this.attr('stop-opacity', o.opacity)\n    if (o.color != null) this.attr('stop-color', o.color)\n    if (o.offset != null) this.attr('offset', new SVGNumber(o.offset))\n\n    return this\n  }\n}\n\nregisterMethods({\n  Gradient: {\n    // Add a color stop\n    stop: function (offset, color, opacity) {\n      return this.put(new Stop()).update(offset, color, opacity)\n    }\n  }\n})\n\nregister(Stop, 'Stop')\n", "import { nodeOrNew, register } from '../utils/adopter.js'\nimport { registerMethods } from '../utils/methods.js'\nimport { unCamelCase } from '../utils/utils.js'\nimport Element from './Element.js'\n\nfunction cssRule(selector, rule) {\n  if (!selector) return ''\n  if (!rule) return selector\n\n  let ret = selector + '{'\n\n  for (const i in rule) {\n    ret += unCamelCase(i) + ':' + rule[i] + ';'\n  }\n\n  ret += '}'\n\n  return ret\n}\n\nexport default class Style extends Element {\n  constructor(node, attrs = node) {\n    super(nodeOrNew('style', node), attrs)\n  }\n\n  addText(w = '') {\n    this.node.textContent += w\n    return this\n  }\n\n  font(name, src, params = {}) {\n    return this.rule('@font-face', {\n      fontFamily: name,\n      src: src,\n      ...params\n    })\n  }\n\n  rule(selector, obj) {\n    return this.addText(cssRule(selector, obj))\n  }\n}\n\nregisterMethods('Dom', {\n  style(selector, obj) {\n    return this.put(new Style()).rule(selector, obj)\n  },\n  fontface(name, src, params) {\n    return this.put(new Style()).font(name, src, params)\n  }\n})\n\nregister(Style, 'Style')\n", "import { nodeOrNew, register, wrapWithAttrCheck } from '../utils/adopter.js'\nimport { registerMethods } from '../utils/methods.js'\nimport { xlink } from '../modules/core/namespaces.js'\nimport Path from './Path.js'\nimport PathArray from '../types/PathArray.js'\nimport Text from './Text.js'\nimport baseFind from '../modules/core/selector.js'\n\nexport default class TextPath extends Text {\n  // Initialize node\n  constructor(node, attrs = node) {\n    super(nodeOrNew('textPath', node), attrs)\n  }\n\n  // return the array of the path track element\n  array() {\n    const track = this.track()\n\n    return track ? track.array() : null\n  }\n\n  // Plot path if any\n  plot(d) {\n    const track = this.track()\n    let pathArray = null\n\n    if (track) {\n      pathArray = track.plot(d)\n    }\n\n    return d == null ? pathArray : this\n  }\n\n  // Get the path element\n  track() {\n    return this.reference('href')\n  }\n}\n\nregisterMethods({\n  Container: {\n    textPath: wrapWithAttrCheck(function (text, path) {\n      // Convert text to instance if needed\n      if (!(text instanceof Text)) {\n        text = this.text(text)\n      }\n\n      return text.path(path)\n    })\n  },\n  Text: {\n    // Create path for text to run on\n    path: wrapWithAttrCheck(function (track, importNodes = true) {\n      const textPath = new TextPath()\n\n      // if track is a path, reuse it\n      if (!(track instanceof Path)) {\n        // create path element\n        track = this.defs().path(track)\n      }\n\n      // link textPath to path and add content\n      textPath.attr('href', '#' + track, xlink)\n\n      // Transplant all nodes from text to textPath\n      let node\n      if (importNodes) {\n        while ((node = this.node.firstChild)) {\n          textPath.node.appendChild(node)\n        }\n      }\n\n      // add textPath element as child node and return textPath\n      return this.put(textPath)\n    }),\n\n    // Get the textPath children\n    textPath() {\n      return this.findOne('textPath')\n    }\n  },\n  Path: {\n    // creates a textPath from this path\n    text: wrapWithAttrCheck(function (text) {\n      // Convert text to instance if needed\n      if (!(text instanceof Text)) {\n        text = new Text().addTo(this.parent()).text(text)\n      }\n\n      // Create textPath from text and path and return\n      return text.path(this)\n    }),\n\n    targets() {\n      return baseFind('svg textPath').filter((node) => {\n        return (node.attr('href') || '').includes(this.id())\n      })\n\n      // Does not work in IE11. Use when IE support is dropped\n      // return baseFind('svg textPath[*|href*=' + this.id() + ']')\n    }\n  }\n})\n\nTextPath.prototype.MorphArray = PathArray\nregister(TextPath, 'TextPath')\n", "import { nodeOrNew, register, wrapWithAttrCheck } from '../utils/adopter.js'\nimport { registerMethods } from '../utils/methods.js'\nimport { xlink } from '../modules/core/namespaces.js'\nimport Shape from './Shape.js'\n\nexport default class Use extends Shape {\n  constructor(node, attrs = node) {\n    super(nodeOrNew('use', node), attrs)\n  }\n\n  // Use element as a reference\n  use(element, file) {\n    // Set lined element\n    return this.attr('href', (file || '') + '#' + element, xlink)\n  }\n}\n\nregisterMethods({\n  Container: {\n    // Create a use element\n    use: wrapWithAttrCheck(function (element, file) {\n      return this.put(new Use()).use(element, file)\n    })\n  }\n})\n\nregister(Use, 'Use')\n", "/* Optional Modules */\nimport './modules/optional/arrange.js'\nimport './modules/optional/class.js'\nimport './modules/optional/css.js'\nimport './modules/optional/data.js'\nimport './modules/optional/memory.js'\nimport './modules/optional/sugar.js'\nimport './modules/optional/transform.js'\n\nimport { extend, makeInstance } from './utils/adopter.js'\nimport { getMethodNames, getMethodsFor } from './utils/methods.js'\nimport Box from './types/Box.js'\nimport Color from './types/Color.js'\nimport Container from './elements/Container.js'\nimport Defs from './elements/Defs.js'\nimport Dom from './elements/Dom.js'\nimport Element from './elements/Element.js'\nimport Ellipse from './elements/Ellipse.js'\nimport EventTarget from './types/EventTarget.js'\nimport Fragment from './elements/Fragment.js'\nimport Gradient from './elements/Gradient.js'\nimport Image from './elements/Image.js'\nimport Line from './elements/Line.js'\nimport List from './types/List.js'\nimport Marker from './elements/Marker.js'\nimport Matrix from './types/Matrix.js'\nimport Morphable, {\n  NonMorphable,\n  ObjectBag,\n  TransformBag,\n  makeMorphable,\n  registerMorphableType\n} from './animation/Morphable.js'\nimport Path from './elements/Path.js'\nimport PathArray from './types/PathArray.js'\nimport Pattern from './elements/Pattern.js'\nimport PointArray from './types/PointArray.js'\nimport Point from './types/Point.js'\nimport Polygon from './elements/Polygon.js'\nimport Polyline from './elements/Polyline.js'\nimport Rect from './elements/Rect.js'\nimport Runner from './animation/Runner.js'\nimport SVGArray from './types/SVGArray.js'\nimport SVGNumber from './types/SVGNumber.js'\nimport Shape from './elements/Shape.js'\nimport Svg from './elements/Svg.js'\nimport Symbol from './elements/Symbol.js'\nimport Text from './elements/Text.js'\nimport Tspan from './elements/Tspan.js'\nimport * as defaults from './modules/core/defaults.js'\nimport * as utils from './utils/utils.js'\nimport * as namespaces from './modules/core/namespaces.js'\nimport * as regex from './modules/core/regex.js'\n\nexport {\n  Morphable,\n  registerMorphableType,\n  makeMorphable,\n  TransformBag,\n  ObjectBag,\n  NonMorphable\n}\n\nexport { defaults, utils, namespaces, regex }\nexport const SVG = makeInstance\nexport { default as parser } from './modules/core/parser.js'\nexport { default as find } from './modules/core/selector.js'\nexport * from './modules/core/event.js'\nexport * from './utils/adopter.js'\nexport {\n  getWindow,\n  registerWindow,\n  restoreWindow,\n  saveWindow,\n  withWindow\n} from './utils/window.js'\n\n/* Animation Modules */\nexport { default as Animator } from './animation/Animator.js'\nexport {\n  Controller,\n  Ease,\n  PID,\n  Spring,\n  easing\n} from './animation/Controller.js'\nexport { default as Queue } from './animation/Queue.js'\nexport { default as Runner } from './animation/Runner.js'\nexport { default as Timeline } from './animation/Timeline.js'\n\n/* Types */\nexport { default as Array } from './types/SVGArray.js'\nexport { default as Box } from './types/Box.js'\nexport { default as Color } from './types/Color.js'\nexport { default as EventTarget } from './types/EventTarget.js'\nexport { default as Matrix } from './types/Matrix.js'\nexport { default as Number } from './types/SVGNumber.js'\nexport { default as PathArray } from './types/PathArray.js'\nexport { default as Point } from './types/Point.js'\nexport { default as PointArray } from './types/PointArray.js'\nexport { default as List } from './types/List.js'\n\n/* Elements */\nexport { default as Circle } from './elements/Circle.js'\nexport { default as ClipPath } from './elements/ClipPath.js'\nexport { default as Container } from './elements/Container.js'\nexport { default as Defs } from './elements/Defs.js'\nexport { default as Dom } from './elements/Dom.js'\nexport { default as Element } from './elements/Element.js'\nexport { default as Ellipse } from './elements/Ellipse.js'\nexport { default as ForeignObject } from './elements/ForeignObject.js'\nexport { default as Fragment } from './elements/Fragment.js'\nexport { default as Gradient } from './elements/Gradient.js'\nexport { default as G } from './elements/G.js'\nexport { default as A } from './elements/A.js'\nexport { default as Image } from './elements/Image.js'\nexport { default as Line } from './elements/Line.js'\nexport { default as Marker } from './elements/Marker.js'\nexport { default as Mask } from './elements/Mask.js'\nexport { default as Path } from './elements/Path.js'\nexport { default as Pattern } from './elements/Pattern.js'\nexport { default as Polygon } from './elements/Polygon.js'\nexport { default as Polyline } from './elements/Polyline.js'\nexport { default as Rect } from './elements/Rect.js'\nexport { default as Shape } from './elements/Shape.js'\nexport { default as Stop } from './elements/Stop.js'\nexport { default as Style } from './elements/Style.js'\nexport { default as Svg } from './elements/Svg.js'\nexport { default as Symbol } from './elements/Symbol.js'\nexport { default as Text } from './elements/Text.js'\nexport { default as TextPath } from './elements/TextPath.js'\nexport { default as Tspan } from './elements/Tspan.js'\nexport { default as Use } from './elements/Use.js'\n\nextend([Svg, Symbol, Image, Pattern, Marker], getMethodsFor('viewbox'))\n\nextend([Line, Polyline, Polygon, Path], getMethodsFor('marker'))\n\nextend(Text, getMethodsFor('Text'))\nextend(Path, getMethodsFor('Path'))\n\nextend(Defs, getMethodsFor('Defs'))\n\nextend([Text, Tspan], getMethodsFor('Tspan'))\n\nextend([Rect, Ellipse, Gradient, Runner], getMethodsFor('radius'))\n\nextend(EventTarget, getMethodsFor('EventTarget'))\nextend(Dom, getMethodsFor('Dom'))\nextend(Element, getMethodsFor('Element'))\nextend(Shape, getMethodsFor('Shape'))\nextend([Container, Fragment], getMethodsFor('Container'))\nextend(Gradient, getMethodsFor('Gradient'))\n\nextend(Runner, getMethodsFor('Runner'))\n\nList.extend(getMethodNames())\n\nregisterMorphableType([\n  SVGNumber,\n  Color,\n  Box,\n  Matrix,\n  SVGArray,\n  PointArray,\n  PathArray,\n  Point\n])\n\nmakeMorphable()\n", "import * as svgMembers from './main.js'\nimport { makeInstance } from './utils/adopter.js'\n\n// The main wrapping element\nexport default function SVG(element, isHTML) {\n  return makeInstance(element, isHTML)\n}\n\nObject.assign(SVG, svgMembers)\n"], "names": ["methods", "names", "registerMethods", "name", "m", "Array", "isArray", "_name", "addMethodNames", "Object", "getOwnPropertyNames", "assign", "getMethodsFor", "_names", "push", "map", "array", "block", "i", "il", "length", "result", "filter", "radians", "d", "Math", "PI", "unCamelCase", "s", "replace", "g", "toLowerCase", "capitalize", "char<PERSON>t", "toUpperCase", "slice", "proportionalSize", "element", "width", "height", "box", "bbox", "<PERSON><PERSON><PERSON><PERSON>", "o", "origin", "ox", "originX", "oy", "originY", "x", "y", "condX", "condY", "includes", "descriptiveElements", "Set", "isDescriptive", "has", "nodeName", "writeDataToDom", "data", "defaults", "cloned", "key", "valueOf", "keys", "node", "setAttribute", "JSON", "stringify", "removeAttribute", "r", "svg", "html", "xmlns", "xlink", "globals", "window", "document", "registerWindow", "win", "doc", "save", "saveWindow", "restoreWindow", "getWindow", "Base", "elements", "root", "create", "ns", "createElementNS", "makeInstance", "isHTML", "adopter", "querySelector", "wrapper", "createElement", "innerHTML", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "nodeOrNew", "Node", "ownerDocument", "defaultView", "adopt", "instance", "Fragment", "className", "register", "asRoot", "prototype", "getClass", "did", "eid", "assignNewId", "children", "id", "extend", "modules", "wrapWithAttrCheck", "fn", "args", "constructor", "apply", "this", "attr", "siblings", "parent", "position", "index", "next", "prev", "forward", "add", "remove", "backward", "front", "back", "before", "after", "insertBefore", "insertAfter", "numberAndUnit", "hex", "rgb", "reference", "transforms", "whitespace", "isHex", "isRgb", "isBlank", "isNumber", "isImage", "delimiter", "isPathLetter", "componentHex", "component", "integer", "round", "max", "min", "toString", "is", "object", "space", "hueToRgb", "p", "q", "t", "classes", "trim", "split", "hasClass", "indexOf", "addClass", "join", "removeClass", "c", "toggleClass", "css", "style", "val", "ret", "arguments", "cssText", "el", "for<PERSON>ach", "cased", "getPropertyValue", "setProperty", "test", "show", "hide", "visible", "a", "v", "attributes", "parse", "e", "remember", "k", "memory", "forget", "_memory", "Color", "inputs", "init", "static", "color", "b", "mode", "random", "sin", "pi", "l", "h", "grey", "Error", "cmyk", "_a", "_b", "_c", "hsl", "<PERSON><PERSON><PERSON>", "delta", "_d", "values", "params", "z", "getParameters", "noWhitespace", "exec", "parseInt", "hexParse", "substring", "sixDigitHex", "components", "lab", "xyz", "lch", "sqrt", "atan2", "dToR", "cos", "yL", "xL", "zL", "ct", "mx", "nm", "rU", "gU", "bU", "pow", "bd", "toArray", "toHex", "_clamped", "toRgb", "rV", "gV", "bV", "r255", "g255", "b255", "rL", "gL", "bL", "xU", "yU", "zU", "Point", "clone", "base", "source", "transform", "transformO", "Matrix", "isMatrixLike", "f", "close<PERSON>nough", "threshold", "abs", "flipBoth", "flip", "flipX", "flipY", "skewX", "skew", "isFinite", "skewY", "scaleX", "scale", "scaleY", "shear", "theta", "rotate", "around", "px", "positionX", "NaN", "py", "positionY", "translate", "tx", "translateX", "ty", "translateY", "relative", "rx", "relativeX", "ry", "relativeY", "cx", "cy", "matrix", "aroundO", "dx", "dy", "translateO", "lmultiplyO", "decompose", "determinant", "ccw", "sx", "thetaRad", "st", "lam", "sy", "equals", "other", "comp", "axis", "flipO", "scaleO", "fromArray", "Element", "matrixify", "parseFloat", "call", "inverse", "inverseO", "det", "na", "nb", "nc", "nd", "ne", "nf", "l<PERSON>ltip<PERSON>", "matrixMultiply", "multiply", "multiplyO", "rotateO", "shearO", "lx", "skewO", "tan", "ly", "formatTransforms", "transformer", "parser", "nodes", "size", "path", "parentNode", "body", "documentElement", "addTo", "isNulledBox", "Box", "addOffset", "pageXOffset", "pageYOffset", "left", "top", "w", "x2", "y2", "isNulled", "merge", "xMin", "Infinity", "xMax", "yMin", "yMax", "getBox", "getBBoxFn", "retry", "contains", "viewbox", "zoom", "level", "point", "clientWidth", "clientHeight", "zoomX", "zoomY", "zoomAmount", "Number", "MAX_SAFE_INTEGER", "List", "arr", "super", "each", "fnOrMethodName", "concat", "reserved", "baseFind", "query", "querySelectorAll", "reduce", "obj", "attrs", "listenerId", "windowEvents", "getEvents", "n", "getEventHolder", "events", "getEventTarget", "clearEvents", "on", "listener", "binding", "options", "bind", "bag", "_svgjsListenerId", "event", "ev", "addEventListener", "off", "namespace", "removeEventListener", "dispatch", "Event", "CustomEvent", "detail", "cancelable", "dispatchEvent", "EventTarget", "type", "j", "defaultPrevented", "fire", "noop", "timeline", "duration", "ease", "delay", "fill", "stroke", "opacity", "offset", "SVGArray", "toSet", "SVGNumber", "convert", "unit", "value", "divide", "number", "isNaN", "match", "minus", "plus", "times", "toJSON", "colorAttributes", "hooks", "Dom", "removeNamespace", "SVGElement", "append<PERSON><PERSON><PERSON>", "childNodes", "put", "clear", "hasChildNodes", "<PERSON><PERSON><PERSON><PERSON>", "deep", "assignNewIds", "nodeClone", "cloneNode", "first", "get", "htmlOrFn", "outerHTML", "xml", "last", "matches", "selector", "matcher", "matchesSelector", "msMatchesSelector", "mozMatchesSelector", "webkitMatchesSelector", "oMatchesSelector", "putIn", "removeElement", "<PERSON><PERSON><PERSON><PERSON>", "precision", "factor", "svgOrFn", "outerSVG", "words", "text", "textContent", "wrap", "xmlOrFn", "outerXML", "current", "_this", "well", "fragment", "createDocumentFragment", "len", "first<PERSON><PERSON><PERSON><PERSON><PERSON>", "nodeValue", "curr", "getAttribute", "_val", "hook", "isColor", "leading", "setAttributeNS", "rebuild", "find", "findOne", "dom", "hasAttribute", "setData", "center", "defs", "dmove", "move", "parents", "until", "isSelector", "getBBox", "rbox", "getBoundingClientRect", "screenCTM", "inside", "ctm", "getCTM", "isRoot", "rect", "getScreenCTM", "console", "warn", "sugar", "prefix", "extension", "mat", "angle", "direction", "radius", "_element", "getTotalLength", "pointAt", "getPointAtLength", "font", "untransform", "str", "kv", "reverse", "to<PERSON>arent", "pCtm", "toRoot", "decomposed", "Container", "flatten", "ungroup", "Defs", "<PERSON><PERSON><PERSON>", "Ellipse", "circled", "ellipse", "from", "fx", "fy", "x1", "y1", "to", "Gradient", "targets", "url", "update", "gradiented", "gradient", "Pattern", "pattern", "patternUnits", "Image", "load", "callback", "img", "src", "image", "PointArray", "maxX", "maxY", "minX", "minY", "points", "pop", "toLine", "Line", "plot", "pointed", "line", "<PERSON><PERSON>", "orient", "ref", "makeSetterGetter", "marker", "easing", "pos", "bezier", "steps", "stepPosition", "jumps", "beforeFlag", "step", "floor", "jumping", "Stepper", "done", "Ease", "Controller", "stepper", "target", "dt", "recalculate", "_duration", "overshoot", "_overshoot", "os", "log", "zeta", "wn", "Spring", "velocity", "acceleration", "newPosition", "PID", "windup", "integral", "error", "_windup", "P", "I", "D", "segmentParameters", "M", "L", "H", "V", "C", "S", "Q", "T", "A", "Z", "pathHandlers", "p0", "mlhvqtcsaz", "jl", "segmentComplete", "segment", "startNewSegment", "token", "inNumber", "finalizeNumber", "pathLetter", "lastCommand", "small", "isSmall", "inSegment", "pointSeen", "hasExponent", "finalizeSegment", "absolute", "command", "makeAbsolut", "segments", "isArcFlag", "isArc", "isExponential", "lastToken", "pathDelimiters", "PathArray", "toAbsolute", "<PERSON><PERSON><PERSON><PERSON>", "arrayToString", "getClassForType", "NonMorphable", "morphableTypes", "ObjectBag", "<PERSON><PERSON><PERSON><PERSON>", "_stepper", "_from", "_to", "_type", "_context", "_morphObj", "at", "morph", "_set", "align", "toConsumable", "TransformBag", "sortByKey", "splice", "defaultObject", "toDelete", "obj<PERSON>r<PERSON><PERSON>", "entries", "Type", "sort", "shift", "num", "registerMorphableType", "makeMorphable", "context", "Path", "_array", "MorphArray", "Polygon", "polygon", "poly", "Polyline", "polyline", "Rect", "Queue", "_first", "_last", "item", "Animator", "nextDraw", "frames", "timeouts", "immediates", "timer", "performance", "Date", "frame", "run", "requestAnimationFrame", "_draw", "timeout", "time", "now", "immediate", "cancelFrame", "clearTimeout", "cancelImmediate", "nextTimeout", "lastTimeout", "next<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "nextImmediate", "makeSchedule", "runnerInfo", "start", "runner", "end", "defaultSource", "Timeline", "timeSource", "_timeSource", "terminate", "active", "_nextFrame", "finish", "getEndTimeOfTimeline", "pause", "getEndTime", "lastRunnerInfo", "getLastRunnerInfo", "lastDuration", "_time", "endTimes", "_runners", "getRunnerInfoById", "_lastRunnerId", "_runnerIds", "_paused", "_continue", "persist", "dt<PERSON>r<PERSON><PERSON><PERSON>", "_persist", "play", "updateTime", "yes", "currentSpeed", "speed", "positive", "schedule", "when", "absoluteStartTime", "endTime", "unschedule", "info", "seek", "_speed", "stop", "_lastSourceTime", "immediateStep", "_stepImmediate", "_step", "_stepFn", "dtSource", "dtTime", "_lastStepTime", "reset", "runnersLeft", "dtToStart", "_startTime", "_timeline", "Runner", "_queue", "_isDeclarative", "_history", "enabled", "_lastTime", "_reseted", "transformId", "_haveReversed", "_reverse", "_loopsDone", "_swing", "_wait", "_times", "_frameId", "swing", "wait", "addTransform", "animate", "sanitise", "loop", "clearTransform", "clearTransformsFromQueue", "isTransform", "during", "queue", "_prepareRunner", "loops", "loopDuration", "loopsDone", "partial", "swinging", "backwards", "uncliped", "swingForward", "progress", "initFn", "runFn", "retargetFn", "initialiser", "retarget", "initialised", "finished", "running", "_lastPosition", "justStarted", "justFinished", "declarative", "converged", "_initialise", "_run", "needsIt", "_rememberMorpher", "method", "morpher", "caller", "positionOrDt", "allfinished", "_tryRetarget", "extra", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mergeWith", "getRunnerTransform", "mergeTransforms", "netTransform", "_transformationRunners", "runners", "RunnerArray", "ids", "clearBefore", "deleteCnt", "edit", "<PERSON><PERSON><PERSON><PERSON>", "getByID", "lastRunner", "by", "_clearTransformRunnersBefore", "current<PERSON><PERSON>ner", "_currentTransform", "_addRunner", "styleAttr", "nameOrAttrs", "newToAttrs", "newKeys", "differences", "difference", "addedFromAttrs", "oldFromAttrs", "oldToAttrs", "newLevel", "newPoint", "affine", "isMatrix", "currentAngle", "startTransform", "undefined", "r<PERSON>arget", "r<PERSON><PERSON>rent", "possibilities", "distances", "shortest", "affineParameters", "newTransforms", "_queueNumber", "ax", "ay", "_queueNumberDelta", "newTo", "_queueObject", "amove", "Svg", "version", "nested", "Symbol", "symbol", "build", "_build", "getComputedTextLength", "createTextNode", "Text", "_rebuild", "self", "blankLineOffset", "fontSize", "getComputedStyle", "newLined", "firstLine", "nodeType", "newLine", "textable", "plain", "Tspan", "tspan", "Circle", "circle", "<PERSON><PERSON><PERSON><PERSON>", "unclip", "clip", "clipper", "clipWith", "ForeignObject", "foreignObject", "child", "SVGSVGElement", "G", "containerGeometry", "group", "link", "unlink", "linker", "linkTo", "Mask", "unmask", "mask", "masker", "mask<PERSON>ith", "Stop", "Style", "addText", "rule", "fontFamily", "cssRule", "fontface", "TextPath", "track", "pathArray", "textPath", "importNodes", "Use", "use", "file", "SVG", "mock", "svgMembers"], "mappings": ";;;;;;;;;;;gCAAA,MAAMA,EAAU,CAAA,EACVC,EAAQ,GAEP,SAASC,EAAgBC,EAAMC,GACpC,GAAIC,MAAMC,QAAQH,GAChB,IAAK,MAAMI,KAASJ,EAClBD,EAAgBK,EAAOH,QAK3B,GAAoB,iBAATD,EAOXK,EAAeC,OAAOC,oBAAoBN,IAC1CJ,EAAQG,GAAQM,OAAOE,OAAOX,EAAQG,IAAS,GAAIC,QAPjD,IAAK,MAAMG,KAASJ,EAClBD,EAAgBK,EAAOJ,EAAKI,GAOlC,CAEO,SAASK,EAAcT,GAC5B,OAAOH,EAAQG,IAAS,EAC1B,CAMO,SAASK,EAAeK,GAC7BZ,EAAMa,QAAQD,EAChB,CC/BO,SAASE,EAAIC,EAAOC,GACzB,IAAIC,EACJ,MAAMC,EAAKH,EAAMI,OACXC,EAAS,GAEf,IAAKH,EAAI,EAAGA,EAAIC,EAAID,IAClBG,EAAOP,KAAKG,EAAMD,EAAME,KAG1B,OAAOG,CACT,CAGO,SAASC,EAAON,EAAOC,GAC5B,IAAIC,EACJ,MAAMC,EAAKH,EAAMI,OACXC,EAAS,GAEf,IAAKH,EAAI,EAAGA,EAAIC,EAAID,IACdD,EAAMD,EAAME,KACdG,EAAOP,KAAKE,EAAME,IAItB,OAAOG,CACT,CAGO,SAASE,EAAQC,GACtB,OAASA,EAAI,IAAOC,KAAKC,GAAM,GACjC,CAQO,SAASC,EAAYC,GAC1B,OAAOA,EAAEC,QAAQ,YAAY,SAAUzB,EAAG0B,GACxC,MAAO,IAAMA,EAAEC,aACjB,GACF,CAGO,SAASC,EAAWJ,GACzB,OAAOA,EAAEK,OAAO,GAAGC,cAAgBN,EAAEO,MAAM,EAC7C,CAGO,SAASC,EAAiBC,EAASC,EAAOC,EAAQC,GAWvD,OAVa,MAATF,GAA2B,MAAVC,IACnBC,EAAMA,GAAOH,EAAQI,OAER,MAATH,EACFA,EAASE,EAAIF,MAAQE,EAAID,OAAUA,EAChB,MAAVA,IACTA,EAAUC,EAAID,OAASC,EAAIF,MAASA,IAIjC,CACLA,MAAOA,EACPC,OAAQA,EAEZ,CAOO,SAASG,EAAUC,EAAGN,GAC3B,MAAMO,EAASD,EAAEC,OAEjB,IAAIC,EAAa,MAARF,EAAEE,GAAaF,EAAEE,GAAkB,MAAbF,EAAEG,QAAkBH,EAAEG,QAAU,SAC3DC,EAAa,MAARJ,EAAEI,GAAaJ,EAAEI,GAAkB,MAAbJ,EAAEK,QAAkBL,EAAEK,QAAU,SAGjD,MAAVJ,KACAC,EAAIE,GAAM1C,MAAMC,QAAQsC,GACtBA,EACkB,iBAAXA,EACL,CAACA,EAAOK,EAAGL,EAAOM,GAClB,CAACN,EAAQA,IAIjB,MAAMO,EAAsB,iBAAPN,EACfO,EAAsB,iBAAPL,EACrB,GAAII,GAASC,EAAO,CAClB,MAAMb,OAAEA,EAAMD,MAAEA,EAAKW,EAAEA,EAACC,EAAEA,GAAMb,EAAQI,OAGpCU,IACFN,EAAKA,EAAGQ,SAAS,QACbJ,EACAJ,EAAGQ,SAAS,SACVJ,EAAIX,EACJW,EAAIX,EAAQ,GAGhBc,IACFL,EAAKA,EAAGM,SAAS,OACbH,EACAH,EAAGM,SAAS,UACVH,EAAIX,EACJW,EAAIX,EAAS,EAEvB,CAGA,MAAO,CAACM,EAAIE,EACd,CAEA,MAAMO,EAAsB,IAAIC,IAAI,CAAC,OAAQ,WAAY,UAC5CC,EAAiBnB,GAC5BiB,EAAoBG,IAAIpB,EAAQqB,UAErBC,EAAiBA,CAACtB,EAASuB,EAAMC,EAAW,CAAA,KACvD,MAAMC,EAAS,IAAKF,GAEpB,IAAK,MAAMG,KAAOD,EACZA,EAAOC,GAAKC,YAAcH,EAASE,WAC9BD,EAAOC,GAIdtD,OAAOwD,KAAKH,GAAQ1C,OACtBiB,EAAQ6B,KAAKC,aAAa,aAAcC,KAAKC,UAAUP,KAEvDzB,EAAQ6B,KAAKI,gBAAgB,cAC7BjC,EAAQ6B,KAAKI,gBAAgB,cAC/B,6CApGK,SAAiBC,GACtB,OAAa,IAAJA,EAAW9C,KAAKC,GAAM,GACjC,0GCnCO,MAAM8C,EAAM,6BACNC,EAAO,+BACPC,EAAQ,gCACRC,EAAQ,mFCJd,MAAMC,EAAU,CACrBC,OAA0B,oBAAXA,OAAyB,KAAOA,OAC/CC,SAA8B,oBAAbA,SAA2B,KAAOA,UAG9C,SAASC,EAAeC,EAAM,KAAMC,EAAM,MAC/CL,EAAQC,OAASG,EACjBJ,EAAQE,SAAWG,CACrB,CAEA,MAAMC,EAAO,CAAA,EAEN,SAASC,IACdD,EAAKL,OAASD,EAAQC,OACtBK,EAAKJ,SAAWF,EAAQE,QAC1B,CAEO,SAASM,IACdR,EAAQC,OAASK,EAAKL,OACtBD,EAAQE,SAAWI,EAAKJ,QAC1B,CASO,SAASO,IACd,OAAOT,EAAQC,MACjB,CC/Be,MAAMS,GCMrB,MAAMC,EAAW,CAAA,EACJC,EAAO,sBAGb,SAASC,EAAOtF,EAAMuF,EAAKlB,GAEhC,OAAOI,EAAQE,SAASa,gBAAgBD,EAAIvF,EAC9C,CAEO,SAASyF,EAAavD,EAASwD,GAAS,GAC7C,GAAIxD,aAAmBiD,EAAM,OAAOjD,EAEpC,GAAuB,iBAAZA,EACT,OAAOyD,EAAQzD,GAGjB,GAAe,MAAXA,EACF,OAAO,IAAIkD,EAASC,GAGtB,GAAuB,iBAAZnD,GAA8C,MAAtBA,EAAQJ,OAAO,GAChD,OAAO6D,EAAQlB,EAAQE,SAASiB,cAAc1D,IAIhD,MAAM2D,EAAUH,EAASjB,EAAQE,SAASmB,cAAc,OAASR,EAAO,OASxE,OARAO,EAAQE,UAAY7D,EAIpBA,EAAUyD,EAAQE,EAAQG,YAG1BH,EAAQI,YAAYJ,EAAQG,YACrB9D,CACT,CAEO,SAASgE,EAAUlG,EAAM+D,GAC9B,OAAOA,IACJA,aAAgBU,EAAQC,OAAOyB,MAC7BpC,EAAKqC,eACJrC,aAAgBA,EAAKqC,cAAcC,YAAYF,MACjDpC,EACAuB,EAAOtF,EACb,CAGO,SAASsG,EAAMvC,GAEpB,IAAKA,EAAM,OAAO,KAGlB,GAAIA,EAAKwC,oBAAoBpB,EAAM,OAAOpB,EAAKwC,SAE/C,GAAsB,uBAAlBxC,EAAKR,SACP,OAAO,IAAI6B,EAASoB,SAASzC,GAI/B,IAAI0C,EAAY5E,EAAWkC,EAAKR,UAAY,OAW5C,MARkB,mBAAdkD,GAAgD,mBAAdA,EACpCA,EAAY,WAGFrB,EAASqB,KACnBA,EAAY,OAGP,IAAIrB,EAASqB,GAAW1C,EACjC,CAEA,IAAI4B,EAAUW,EAMP,SAASI,EAASxE,EAASlC,EAAOkC,EAAQlC,KAAM2G,GAAS,GAM9D,OALAvB,EAASpF,GAAQkC,EACbyE,IAAQvB,EAASC,GAAQnD,GAE7B7B,EAAeC,OAAOC,oBAAoB2B,EAAQ0E,YAE3C1E,CACT,CAEO,SAAS2E,EAAS7G,GACvB,OAAOoF,EAASpF,EAClB,CAGA,IAAI8G,EAAM,IAGH,SAASC,EAAI/G,GAClB,MAAO,QAAU6B,EAAW7B,GAAQ8G,GACtC,CAGO,SAASE,EAAYjD,GAE1B,IAAK,IAAIhD,EAAIgD,EAAKkD,SAAShG,OAAS,EAAGF,GAAK,EAAGA,IAC7CiG,EAAYjD,EAAKkD,SAASlG,IAG5B,OAAIgD,EAAKmD,IACPnD,EAAKmD,GAAKH,EAAIhD,EAAKR,UACZQ,GAGFA,CACT,CAGO,SAASoD,EAAOC,EAASvH,GAC9B,IAAI+D,EAAK7C,EAIT,IAAKA,GAFLqG,EAAUlH,MAAMC,QAAQiH,GAAWA,EAAU,CAACA,IAE7BnG,OAAS,EAAGF,GAAK,EAAGA,IACnC,IAAK6C,KAAO/D,EACVuH,EAAQrG,GAAG6F,UAAUhD,GAAO/D,EAAQ+D,EAG1C,CAEO,SAASyD,EAAkBC,GAChC,OAAO,YAAaC,GAClB,MAAM/E,EAAI+E,EAAKA,EAAKtG,OAAS,GAE7B,OAAIuB,GAAKA,EAAEgF,cAAgBlH,QAAYkC,aAAatC,MAG3CoH,EAAGG,MAAMC,KAAMH,GAFfD,EAAGG,MAAMC,KAAMH,EAAKvF,MAAM,GAAI,IAAI2F,KAAKnF,GAKpD,CC5CAzC,EAAgB,MAAO,CACrB6H,SAjGK,WACL,OAAOF,KAAKG,SAASZ,UACvB,EAgGEa,SA7FK,WACL,OAAOJ,KAAKG,SAASE,MAAML,KAC7B,EA4FEM,KAzFK,WACL,OAAON,KAAKE,WAAWF,KAAKI,WAAa,EAC3C,EAwFEG,KArFK,WACL,OAAOP,KAAKE,WAAWF,KAAKI,WAAa,EAC3C,EAoFEI,QAjFK,WACL,MAAMnH,EAAI2G,KAAKI,WAMf,OALUJ,KAAKG,SAGbM,IAAIT,KAAKU,SAAUrH,EAAI,GAElB2G,IACT,EA0EEW,SAvEK,WACL,MAAMtH,EAAI2G,KAAKI,WAKf,OAJUJ,KAAKG,SAEbM,IAAIT,KAAKU,SAAUrH,EAAIA,EAAI,EAAI,GAE1B2G,IACT,EAiEEY,MA9DK,WAML,OALUZ,KAAKG,SAGbM,IAAIT,KAAKU,UAEJV,IACT,EAwDEa,KArDK,WAML,OALUb,KAAKG,SAGbM,IAAIT,KAAKU,SAAU,GAEdV,IACT,EA+CEc,OA5CK,SAAgBtG,IACrBA,EAAUuD,EAAavD,IACfkG,SAER,MAAMrH,EAAI2G,KAAKI,WAIf,OAFAJ,KAAKG,SAASM,IAAIjG,EAASnB,GAEpB2G,IACT,EAoCEe,MAjCK,SAAevG,IACpBA,EAAUuD,EAAavD,IACfkG,SAER,MAAMrH,EAAI2G,KAAKI,WAIf,OAFAJ,KAAKG,SAASM,IAAIjG,EAASnB,EAAI,GAExB2G,IACT,EAyBEgB,aAvBK,SAAsBxG,GAG3B,OAFAA,EAAUuD,EAAavD,IACfsG,OAAOd,MACRA,IACT,EAoBEiB,YAlBK,SAAqBzG,GAG1B,OAFAA,EAAUuD,EAAavD,IACfuG,MAAMf,MACPA,IACT,ICjGO,MAAMkB,EACX,qDAGWC,EAAM,4CAGNC,EAAM,2BAGNC,EAAY,yBAGZC,EAAa,aAGbC,EAAa,MAGbC,EAAQ,iCAGRC,EAAQ,SAGRC,EAAU,WAGVC,EAAW,0CAGXC,GAAU,wCAGVC,GAAY,SAGZC,GAAe,uLCtB5B,SAASC,GAAaC,GACpB,MAAMC,EAAUrI,KAAKsI,MAAMF,GAErBb,EADUvH,KAAKuI,IAAI,EAAGvI,KAAKwI,IAAI,IAAKH,IACtBI,SAAS,IAC7B,OAAsB,IAAflB,EAAI5H,OAAe,IAAM4H,EAAMA,CACxC,CAEA,SAASmB,GAAGC,EAAQC,GAClB,IAAK,IAAInJ,EAAImJ,EAAMjJ,OAAQF,KACzB,GAAwB,MAApBkJ,EAAOC,EAAMnJ,IACf,OAAO,EAGX,OAAO,CACT,CA6BA,SAASoJ,GAASC,EAAGC,EAAGC,GAGtB,OAFIA,EAAI,IAAGA,GAAK,GACZA,EAAI,IAAGA,GAAK,GACZA,EAAI,EAAI,EAAUF,EAAc,GAATC,EAAID,GAASE,EACpCA,EAAI,GAAcD,EAClBC,EAAI,EAAI,EAAUF,GAAKC,EAAID,IAAM,EAAI,EAAIE,GAAK,EAC3CF,CACT,CCpBArK,EAAgB,MAAO,CACrBwK,QA3CK,WACL,MAAM5C,EAAOD,KAAKC,KAAK,SACvB,OAAe,MAARA,EAAe,GAAKA,EAAK6C,OAAOC,MAAMlB,GAC/C,EAyCEmB,SAtCK,SAAkB1K,GACvB,OAAyC,IAAlC0H,KAAK6C,UAAUI,QAAQ3K,EAChC,EAqCE4K,SAlCK,SAAkB5K,GACvB,IAAK0H,KAAKgD,SAAS1K,GAAO,CACxB,MAAMa,EAAQ6G,KAAK6C,UACnB1J,EAAMF,KAAKX,GACX0H,KAAKC,KAAK,QAAS9G,EAAMgK,KAAK,KAChC,CAEA,OAAOnD,IACT,EA2BEoD,YAxBK,SAAqB9K,GAY1B,OAXI0H,KAAKgD,SAAS1K,IAChB0H,KAAKC,KACH,QACAD,KAAK6C,UACFpJ,QAAO,SAAU4J,GAChB,OAAOA,IAAM/K,CACf,IACC6K,KAAK,MAILnD,IACT,EAYEsD,YATK,SAAqBhL,GAC1B,OAAO0H,KAAKgD,SAAS1K,GAAQ0H,KAAKoD,YAAY9K,GAAQ0H,KAAKkD,SAAS5K,EACtE,IC6BAD,EAAgB,MAAO,CACrBkL,IAtEK,SAAaC,EAAOC,GACzB,MAAMC,EAAM,CAAA,EACZ,GAAyB,IAArBC,UAAUpK,OAWZ,OATAyG,KAAK3D,KAAKmH,MAAMI,QACbb,MAAM,WACNtJ,QAAO,SAAUoK,GAChB,QAASA,EAAGtK,MACd,IACCuK,SAAQ,SAAUD,GACjB,MAAMjB,EAAIiB,EAAGd,MAAM,WACnBW,EAAId,EAAE,IAAMA,EAAE,EAChB,IACKc,EAGT,GAAIC,UAAUpK,OAAS,EAAG,CAExB,GAAIf,MAAMC,QAAQ+K,GAAQ,CACxB,IAAK,MAAMlL,KAAQkL,EAAO,CACxB,MAAMO,EAAQzL,EACdoL,EAAIpL,GAAQ0H,KAAK3D,KAAKmH,MAAMQ,iBAAiBD,EAC/C,CACA,OAAOL,CACT,CAGA,GAAqB,iBAAVF,EACT,OAAOxD,KAAK3D,KAAKmH,MAAMQ,iBAAiBR,GAI1C,GAAqB,iBAAVA,EACT,IAAK,MAAMlL,KAAQkL,EAEjBxD,KAAK3D,KAAKmH,MAAMS,YACd3L,EACe,MAAfkL,EAAMlL,IAAiBoJ,EAAQwC,KAAKV,EAAMlL,IAAS,GAAKkL,EAAMlL,GAItE,CAUA,OAPyB,IAArBqL,UAAUpK,QACZyG,KAAK3D,KAAKmH,MAAMS,YACdT,EACO,MAAPC,GAAe/B,EAAQwC,KAAKT,GAAO,GAAKA,GAIrCzD,IACT,EAmBEmE,KAhBK,WACL,OAAOnE,KAAKuD,IAAI,UAAW,GAC7B,EAeEa,KAZK,WACL,OAAOpE,KAAKuD,IAAI,UAAW,OAC7B,EAWEc,QARK,WACL,MAA+B,SAAxBrE,KAAKuD,IAAI,UAClB,ICzBAlL,EAAgB,MAAO,CAAE0D,KA1ClB,SAAcuI,EAAGC,EAAG7H,GACzB,GAAS,MAAL4H,EAEF,OAAOtE,KAAKjE,KACV7C,EACEO,EACEuG,KAAK3D,KAAKmI,YACTX,GAAwC,IAAjCA,EAAGhI,SAASoH,QAAQ,YAE7BY,GAAOA,EAAGhI,SAASvB,MAAM,MAGzB,GAAIgK,aAAa9L,MAAO,CAC7B,MAAMuD,EAAO,CAAA,EACb,IAAK,MAAMG,KAAOoI,EAChBvI,EAAKG,GAAO8D,KAAKjE,KAAKG,GAExB,OAAOH,CACT,CAAO,GAAiB,iBAANuI,EAChB,IAAKC,KAAKD,EACRtE,KAAKjE,KAAKwI,EAAGD,EAAEC,SAEZ,GAAIZ,UAAUpK,OAAS,EAC5B,IACE,OAAOgD,KAAKkI,MAAMzE,KAAKC,KAAK,QAAUqE,GACvC,CAAC,MAAOI,GACP,OAAO1E,KAAKC,KAAK,QAAUqE,EAC7B,MAEAtE,KAAKC,KACH,QAAUqE,EACJ,OAANC,EACI,MACM,IAAN7H,GAA2B,iBAAN6H,GAA+B,iBAANA,EAC5CA,EACAhI,KAAKC,UAAU+H,IAIzB,OAAOvE,IACT,ICLA3H,EAAgB,MAAO,CAAEsM,SApClB,SAAkBC,EAAGL,GAE1B,GAA4B,iBAAjBZ,UAAU,GACnB,IAAK,MAAMzH,KAAO0I,EAChB5E,KAAK2E,SAASzI,EAAK0I,EAAE1I,QAElB,IAAyB,IAArByH,UAAUpK,OAEnB,OAAOyG,KAAK6E,SAASD,GAGrB5E,KAAK6E,SAASD,GAAKL,CACrB,CAEA,OAAOvE,IACT,EAqBmC8E,OAlB5B,WACL,GAAyB,IAArBnB,UAAUpK,OACZyG,KAAK+E,QAAU,QAEf,IAAK,IAAI1L,EAAIsK,UAAUpK,OAAS,EAAGF,GAAK,EAAGA,WAClC2G,KAAK6E,SAASlB,UAAUtK,IAGnC,OAAO2G,IACT,EAS2C6E,OAJpC,WACL,OAAQ7E,KAAK+E,QAAU/E,KAAK+E,SAAW,CAAA,CACzC,IJ+Be,MAAMC,GACnBlF,eAAemF,GACbjF,KAAKkF,QAAQD,EACf,CAGAE,eAAeC,GACb,OACEA,IAAUA,aAAiBJ,IAAShF,KAAKyB,MAAM2D,IAAUpF,KAAKkE,KAAKkB,GAEvE,CAGAD,aAAaC,GACX,OACEA,GACmB,iBAAZA,EAAM1I,GACM,iBAAZ0I,EAAMnL,GACM,iBAAZmL,EAAMC,CAEjB,CAKAF,cAAcG,EAAO,UAAW1C,GAE9B,MAAM2C,OAAEA,EAAMrD,MAAEA,EAAKsD,IAAEA,EAAK3L,GAAI4L,GAAO7L,KAGvC,GAAa,YAAT0L,EAAoB,CACtB,MAAMI,EAAI,GAAYH,IAAW,GAC3BlC,EAAI,GAAYkC,IAAW,GAC3BI,EAAI,IAAMJ,IAEhB,OADc,IAAIP,GAAMU,EAAGrC,EAAGsC,EAAG,MAEnC,CAAO,GAAa,SAATL,EAAiB,CAE1B,MAAM5I,EAAIwF,EAAM,GAAKsD,EAAK,EAAIC,GAD9B7C,EAAS,MAALA,EAAY2C,IAAW3C,GACa,GAAM,KAAQ,KAChD3I,EAAIiI,EAAM,GAAKsD,EAAK,EAAIC,EAAK7C,EAAK,GAAM,KAAO,KAC/CyC,EAAInD,EAAM,IAAMsD,EAAK,EAAIC,EAAK7C,EAAK,GAAM,KAAO,KAEtD,OADc,IAAIoC,GAAMtI,EAAGzC,EAAGoL,EAEhC,CAAO,GAAa,WAATC,EAAmB,CAC5B,MAAMI,EAAI,EAAYH,IAAW,GAC3BlC,EAAI,GAAWkC,IAAW,EAC1BI,EAAI,IAAMJ,IAEhB,OADc,IAAIP,GAAMU,EAAGrC,EAAGsC,EAAG,MAEnC,CAAO,GAAa,SAATL,EAAiB,CAC1B,MAAMI,EAAI,GAAK,GAAKH,IACdlC,EAAI,GAAakC,IAAW,GAC5BI,EAAI,IAAMJ,IAEhB,OADc,IAAIP,GAAMU,EAAGrC,EAAGsC,EAAG,MAEnC,CAAO,GAAa,QAATL,EAAgB,CACzB,MAAM5I,EAAI,IAAM6I,IACVtL,EAAI,IAAMsL,IACVF,EAAI,IAAME,IAEhB,OADc,IAAIP,GAAMtI,EAAGzC,EAAGoL,EAEhC,CAAO,GAAa,QAATC,EAAgB,CACzB,MAAMI,EAAI,IAAMH,IACVjB,EAAI,IAAMiB,IAAW,IACrBF,EAAI,IAAME,IAAW,IAE3B,OADc,IAAIP,GAAMU,EAAGpB,EAAGe,EAAG,MAEnC,CAAO,GAAa,SAATC,EAAiB,CAC1B,MAAMM,EAAO,IAAML,IAEnB,OADc,IAAIP,GAAMY,EAAMA,EAAMA,EAEtC,CACE,MAAM,IAAIC,MAAM,gCAEpB,CAGAV,YAAYC,GACV,MAAwB,iBAAVA,IAAuB5D,EAAM0C,KAAKkB,IAAU3D,EAAMyC,KAAKkB,GACvE,CAEAU,OAEE,MAAMC,GAAEA,EAAEC,GAAEA,EAAEC,GAAEA,GAAOjG,KAAKoB,OACrB1E,EAAGzC,EAAGoL,GAAK,CAACU,EAAIC,EAAIC,GAAI/M,KAAKqL,GAAMA,EAAI,MAGxCK,EAAIhL,KAAKwI,IAAI,EAAI1F,EAAG,EAAIzC,EAAG,EAAIoL,GAErC,GAAU,IAANT,EAEF,OAAO,IAAII,GAAM,EAAG,EAAG,EAAG,EAAG,QAS/B,OADc,IAAIA,IALP,EAAItI,EAAIkI,IAAM,EAAIA,IAClB,EAAI3K,EAAI2K,IAAM,EAAIA,IAClB,EAAIS,EAAIT,IAAM,EAAIA,GAGIA,EAAG,OAEtC,CAEAsB,MAEE,MAAMH,GAAEA,EAAEC,GAAEA,EAAEC,GAAEA,GAAOjG,KAAKoB,OACrB1E,EAAGzC,EAAGoL,GAAK,CAACU,EAAIC,EAAIC,GAAI/M,KAAKqL,GAAMA,EAAI,MAGxCpC,EAAMvI,KAAKuI,IAAIzF,EAAGzC,EAAGoL,GACrBjD,EAAMxI,KAAKwI,IAAI1F,EAAGzC,EAAGoL,GACrBK,GAAKvD,EAAMC,GAAO,EAGlB+D,EAAShE,IAAQC,EAGjBgE,EAAQjE,EAAMC,EAkBpB,OADc,IAAI4C,GAAM,KAXdmB,EACN,EACAhE,IAAQzF,IACJzC,EAAIoL,GAAKe,GAASnM,EAAIoL,EAAI,EAAI,IAAM,EACtClD,IAAQlI,IACJoL,EAAI3I,GAAK0J,EAAQ,GAAK,EACxBjE,IAAQkD,IACJ3I,EAAIzC,GAAKmM,EAAQ,GAAK,EACxB,GAGuB,KAhBvBD,EACN,EACAT,EAAI,GACFU,GAAS,EAAIjE,EAAMC,GACnBgE,GAASjE,EAAMC,IAYqB,IAAMsD,EAAG,MAErD,CAEAR,KAAKZ,EAAI,EAAGe,EAAI,EAAGhC,EAAI,EAAG1J,EAAI,EAAG6I,EAAQ,OAKvC,GAHA8B,EAAKA,GAAI,EAGLtE,KAAKwC,MACP,IAAK,MAAMR,KAAahC,KAAKwC,aACpBxC,KAAKA,KAAKwC,MAAMR,IAI3B,GAAiB,iBAANsC,EAET9B,EAAqB,iBAAN7I,EAAiBA,EAAI6I,EACpC7I,EAAiB,iBAANA,EAAiB,EAAIA,EAGhCf,OAAOE,OAAOkH,KAAM,CAAE+F,GAAIzB,EAAG0B,GAAIX,EAAGY,GAAI5C,EAAGgD,GAAI1M,EAAG6I,eAE7C,GAAI8B,aAAa9L,MACtBwH,KAAKwC,MAAQ6C,IAAsB,iBAATf,EAAE,GAAkBA,EAAE,GAAKA,EAAE,KAAO,MAC9D1L,OAAOE,OAAOkH,KAAM,CAAE+F,GAAIzB,EAAE,GAAI0B,GAAI1B,EAAE,GAAI2B,GAAI3B,EAAE,GAAI+B,GAAI/B,EAAE,IAAM,SAC3D,GAAIA,aAAa1L,OAAQ,CAE9B,MAAM0N,EAtMZ,SAAuBhC,EAAGe,GACxB,MAAMkB,EAASjE,GAAGgC,EAAG,OACjB,CAAEyB,GAAIzB,EAAE5H,EAAGsJ,GAAI1B,EAAErK,EAAGgM,GAAI3B,EAAEe,EAAGgB,GAAI,EAAG7D,MAAO,OAC3CF,GAAGgC,EAAG,OACJ,CAAEyB,GAAIzB,EAAElJ,EAAG4K,GAAI1B,EAAEjJ,EAAG4K,GAAI3B,EAAEkC,EAAGH,GAAI,EAAG7D,MAAO,OAC3CF,GAAGgC,EAAG,OACJ,CAAEyB,GAAIzB,EAAEqB,EAAGK,GAAI1B,EAAEvK,EAAGkM,GAAI3B,EAAEoB,EAAGW,GAAI,EAAG7D,MAAO,OAC3CF,GAAGgC,EAAG,OACJ,CAAEyB,GAAIzB,EAAEoB,EAAGM,GAAI1B,EAAEA,EAAG2B,GAAI3B,EAAEe,EAAGgB,GAAI,EAAG7D,MAAO,OAC3CF,GAAGgC,EAAG,OACJ,CAAEyB,GAAIzB,EAAEoB,EAAGM,GAAI1B,EAAEjB,EAAG4C,GAAI3B,EAAEqB,EAAGU,GAAI,EAAG7D,MAAO,OAC3CF,GAAGgC,EAAG,QACJ,CAAEyB,GAAIzB,EAAEjB,EAAG2C,GAAI1B,EAAE/L,EAAG0N,GAAI3B,EAAEjJ,EAAGgL,GAAI/B,EAAEM,EAAGpC,MAAO,QAC7C,CAAEuD,GAAI,EAAGC,GAAI,EAAGC,GAAI,EAAGzD,MAAO,OAG5C,OADA+D,EAAO/D,MAAQ6C,GAAKkB,EAAO/D,MACpB+D,CACT,CAqLqBE,CAAcnC,EAAGe,GAChCzM,OAAOE,OAAOkH,KAAMsG,EACtB,MAAO,GAAiB,iBAANhC,EAChB,GAAI7C,EAAMyC,KAAKI,GAAI,CACjB,MAAMoC,EAAepC,EAAEtK,QAAQuH,EAAY,KACpCwE,EAAIC,EAAIC,GAAM7E,EAClBuF,KAAKD,GACLpM,MAAM,EAAG,GACTpB,KAAKqL,GAAMqC,SAASrC,KACvB3L,OAAOE,OAAOkH,KAAM,CAAE+F,KAAIC,KAAIC,KAAII,GAAI,EAAG7D,MAAO,OACjD,KAAM,KAAIhB,EAAM0C,KAAKI,GAIf,MAAMuB,MAAM,oDAJO,CACxB,MAAMgB,EAAYtC,GAAMqC,SAASrC,EAAG,MAC3BwB,EAAIC,EAAIC,GAAM9E,EAAIwF,KAhPnC,SAAqBxF,GACnB,OAAsB,IAAfA,EAAI5H,OACP,CACE,IACA4H,EAAI2F,UAAU,EAAG,GACjB3F,EAAI2F,UAAU,EAAG,GACjB3F,EAAI2F,UAAU,EAAG,GACjB3F,EAAI2F,UAAU,EAAG,GACjB3F,EAAI2F,UAAU,EAAG,GACjB3F,EAAI2F,UAAU,EAAG,IACjB3D,KAAK,IACPhC,CACN,CAoOwC4F,CAAYzC,IAAIpL,IAAI2N,GACpDjO,OAAOE,OAAOkH,KAAM,CAAE+F,KAAIC,KAAIC,KAAII,GAAI,EAAG7D,MAAO,OAClD,CAAsE,CAIxE,MAAMuD,GAAEA,EAAEC,GAAEA,EAAEC,GAAEA,EAAEI,GAAEA,GAAOrG,KACrBgH,EACW,QAAfhH,KAAKwC,MACD,CAAE9F,EAAGqJ,EAAI9L,EAAG+L,EAAIX,EAAGY,GACJ,QAAfjG,KAAKwC,MACH,CAAEpH,EAAG2K,EAAI1K,EAAG2K,EAAIQ,EAAGP,GACJ,QAAfjG,KAAKwC,MACH,CAAEmD,EAAGI,EAAIhM,EAAGiM,EAAIN,EAAGO,GACJ,QAAfjG,KAAKwC,MACH,CAAEkD,EAAGK,EAAIzB,EAAG0B,EAAIX,EAAGY,GACJ,QAAfjG,KAAKwC,MACH,CAAEkD,EAAGK,EAAI1C,EAAG2C,EAAIL,EAAGM,GACJ,SAAfjG,KAAKwC,MACH,CAAEa,EAAG0C,EAAIxN,EAAGyN,EAAI3K,EAAG4K,EAAIrB,EAAGyB,GAC1B,GAChBzN,OAAOE,OAAOkH,KAAMgH,EACtB,CAEAC,MAEE,MAAM7L,EAAEA,EAACC,EAAEA,EAACmL,EAAEA,GAAMxG,KAAKkH,MASzB,OADc,IAAIlC,GALR,IAAM3J,EAAI,GACV,KAAOD,EAAIC,GACX,KAAOA,EAAImL,GAGY,MAEnC,CAEAW,MAEE,MAAMzB,EAAEA,EAACpB,EAAEA,EAACe,EAAEA,GAAMrF,KAAKiH,MAGnB5D,EAAIzJ,KAAKwN,KAAK9C,GAAK,EAAIe,GAAK,GAClC,IAAIM,EAAK,IAAM/L,KAAKyN,MAAMhC,EAAGf,GAAM1K,KAAKC,GACpC8L,EAAI,IACNA,IAAM,EACNA,EAAI,IAAMA,GAKZ,OADc,IAAIX,GAAMU,EAAGrC,EAAGsC,EAAG,MAEnC,CAKAvE,MACE,GAAmB,QAAfpB,KAAKwC,MACP,OAAOxC,KACF,GA3PK,SADEwC,EA4PMxC,KAAKwC,QA3PM,QAAVA,GAA6B,QAAVA,EA2PP,CAE/B,IAAIpH,EAAEA,EAACC,EAAEA,EAACmL,EAAEA,GAAMxG,KAClB,GAAmB,QAAfA,KAAKwC,OAAkC,QAAfxC,KAAKwC,MAAiB,CAEhD,IAAIkD,EAAEA,EAACpB,EAAEA,EAACe,EAAEA,GAAMrF,KAClB,GAAmB,QAAfA,KAAKwC,MAAiB,CACxB,MAAMa,EAAEA,EAACsC,EAAEA,GAAM3F,KACXsH,EAAO1N,KAAKC,GAAK,IACvByK,EAAIjB,EAAIzJ,KAAK2N,IAAID,EAAO3B,GACxBN,EAAIhC,EAAIzJ,KAAK4L,IAAI8B,EAAO3B,EAC1B,CAGA,MAAM6B,GAAM9B,EAAI,IAAM,IAChB+B,EAAKnD,EAAI,IAAMkD,EACfE,EAAKF,EAAKnC,EAAI,IAGdsC,EAAK,GAAK,IACVC,EAAK,QACLC,EAAK,MACXzM,EAAI,QAAWqM,GAAM,EAAIG,EAAKH,GAAM,GAAKA,EAAKE,GAAME,GACpDxM,EAAI,GAAOmM,GAAM,EAAII,EAAKJ,GAAM,GAAKA,EAAKG,GAAME,GAChDrB,EAAI,SAAWkB,GAAM,EAAIE,EAAKF,GAAM,GAAKA,EAAKC,GAAME,EACtD,CAGA,MAAMC,EAAS,OAAJ1M,GAAkB,OAALC,GAAmB,MAALmL,EAChCuB,GAAU,MAAL3M,EAAkB,OAAJC,EAAiB,MAAJmL,EAChCwB,EAAS,MAAJ5M,GAAkB,KAALC,EAAiB,MAAJmL,EAG/ByB,EAAMrO,KAAKqO,IACXC,EAAK,SACLxL,EAAIoL,EAAKI,EAAK,MAAQD,EAAIH,EAAI,EAAI,KAAO,KAAQ,MAAQA,EACzD7N,EAAI8N,EAAKG,EAAK,MAAQD,EAAIF,EAAI,EAAI,KAAO,KAAQ,MAAQA,EACzD1C,EAAI2C,EAAKE,EAAK,MAAQD,EAAID,EAAI,EAAI,KAAO,KAAQ,MAAQA,EAI/D,OADc,IAAIhD,GAAM,IAAMtI,EAAG,IAAMzC,EAAG,IAAMoL,EAElD,CAAO,GAAmB,QAAfrF,KAAKwC,MAAiB,CAG/B,IAAImD,EAAEA,EAAC5L,EAAEA,EAAC2L,EAAEA,GAAM1F,KAMlB,GALA2F,GAAK,IACL5L,GAAK,IACL2L,GAAK,IAGK,IAAN3L,EAAS,CACX2L,GAAK,IAEL,OADc,IAAIV,GAAMU,EAAGA,EAAGA,EAEhC,CAGA,MAAM/C,EAAI+C,EAAI,GAAMA,GAAK,EAAI3L,GAAK2L,EAAI3L,EAAI2L,EAAI3L,EACxC2I,EAAI,EAAIgD,EAAI/C,EAGZjG,EAAI,IAAM+F,GAASC,EAAGC,EAAGgD,EAAI,EAAI,GACjC1L,EAAI,IAAMwI,GAASC,EAAGC,EAAGgD,GACzBN,EAAI,IAAM5C,GAASC,EAAGC,EAAGgD,EAAI,EAAI,GAIvC,OADc,IAAIX,GAAMtI,EAAGzC,EAAGoL,EAEhC,CAAO,GAAmB,SAAfrF,KAAKwC,MAAkB,CAGhC,MAAMa,EAAEA,EAAC9K,EAAEA,EAAC8C,EAAEA,EAACuJ,EAAEA,GAAM5E,KAGjBtD,EAAI,KAAO,EAAI9C,KAAKwI,IAAI,EAAGiB,GAAK,EAAIuB,GAAKA,IACzC3K,EAAI,KAAO,EAAIL,KAAKwI,IAAI,EAAG7J,GAAK,EAAIqM,GAAKA,IACzCS,EAAI,KAAO,EAAIzL,KAAKwI,IAAI,EAAG/G,GAAK,EAAIuJ,GAAKA,IAI/C,OADc,IAAII,GAAMtI,EAAGzC,EAAGoL,EAEhC,CACE,OAAOrF,KA/Ub,IAAkBwC,CAiVhB,CAEA2F,UACE,MAAMpC,GAAEA,EAAEC,GAAEA,EAAEC,GAAEA,EAAEI,GAAEA,EAAE7D,MAAEA,GAAUxC,KAClC,MAAO,CAAC+F,EAAIC,EAAIC,EAAII,EAAI7D,EAC1B,CAEA4F,QACE,MAAO1L,EAAGzC,EAAGoL,GAAKrF,KAAKqI,WAAWnP,IAAI6I,IACtC,MAAO,IAAIrF,IAAIzC,IAAIoL,GACrB,CAEAiD,QACE,MAAOC,EAAIC,EAAIC,GAAMzI,KAAKqI,WAE1B,MADe,OAAOE,KAAMC,KAAMC,IAEpC,CAEApG,WACE,OAAOrC,KAAKoI,OACd,CAEAlB,MAEE,MAAQnB,GAAI2C,EAAM1C,GAAI2C,EAAM1C,GAAI2C,GAAS5I,KAAKoB,OACvC1E,EAAGzC,EAAGoL,GAAK,CAACqD,EAAMC,EAAMC,GAAM1P,KAAKqL,GAAMA,EAAI,MAG9CsE,EAAKnM,EAAI,OAAU9C,KAAKqO,KAAKvL,EAAI,MAAS,MAAO,KAAOA,EAAI,MAC5DoM,EAAK7O,EAAI,OAAUL,KAAKqO,KAAKhO,EAAI,MAAS,MAAO,KAAOA,EAAI,MAC5D8O,EAAK1D,EAAI,OAAUzL,KAAKqO,KAAK5C,EAAI,MAAS,MAAO,KAAOA,EAAI,MAG5D2D,GAAW,MAALH,EAAmB,MAALC,EAAmB,MAALC,GAAe,OACjDE,GAAW,MAALJ,EAAmB,MAALC,EAAmB,MAALC,GAAe,EACjDG,GAAW,MAALL,EAAmB,MAALC,EAAmB,MAALC,GAAe,QAGjD3N,EAAI4N,EAAK,QAAWpP,KAAKqO,IAAIe,EAAI,EAAI,GAAK,MAAQA,EAAK,GAAK,IAC5D3N,EAAI4N,EAAK,QAAWrP,KAAKqO,IAAIgB,EAAI,EAAI,GAAK,MAAQA,EAAK,GAAK,IAC5DzC,EAAI0C,EAAK,QAAWtP,KAAKqO,IAAIiB,EAAI,EAAI,GAAK,MAAQA,EAAK,GAAK,IAIlE,OADc,IAAIlE,GAAM5J,EAAGC,EAAGmL,EAAG,MAEnC,CAMA6B,WACE,MAAMtC,GAAEA,EAAEC,GAAEA,EAAEC,GAAEA,GAAOjG,KAAKoB,OACtBe,IAAEA,EAAGC,IAAEA,EAAGF,MAAEA,GAAUtI,KAE5B,MAAO,CAACmM,EAAIC,EAAIC,GAAI/M,KADJqL,GAAMpC,EAAI,EAAGC,EAAIF,EAAMqC,GAAI,OAE7C,EK1ba,MAAM4E,GAEnBrJ,eAAeD,GACbG,KAAKkF,QAAQrF,EACf,CAGAuJ,QACE,OAAO,IAAID,GAAMnJ,KACnB,CAEAkF,KAAK9J,EAAGC,GACN,MAAMgO,EAAY,EAAZA,EAAkB,EAGlBC,EAAS9Q,MAAMC,QAAQ2C,GACzB,CAAEA,EAAGA,EAAE,GAAIC,EAAGD,EAAE,IACH,iBAANA,EACL,CAAEA,EAAGA,EAAEA,EAAGC,EAAGD,EAAEC,GACf,CAAED,EAAGA,EAAGC,EAAGA,GAMjB,OAHA2E,KAAK5E,EAAgB,MAAZkO,EAAOlO,EAAYiO,EAASC,EAAOlO,EAC5C4E,KAAK3E,EAAgB,MAAZiO,EAAOjO,EAAYgO,EAASC,EAAOjO,EAErC2E,IACT,CAEAmI,UACE,MAAO,CAACnI,KAAK5E,EAAG4E,KAAK3E,EACvB,CAEAkO,UAAUhR,GACR,OAAOyH,KAAKoJ,QAAQI,WAAWjR,EACjC,CAGAiR,WAAWjR,GACJkR,GAAOC,aAAanR,KACvBA,EAAI,IAAIkR,GAAOlR,IAGjB,MAAM6C,EAAEA,EAACC,EAAEA,GAAM2E,KAMjB,OAHAA,KAAK5E,EAAI7C,EAAE+L,EAAIlJ,EAAI7C,EAAE8K,EAAIhI,EAAI9C,EAAEmM,EAC/B1E,KAAK3E,EAAI9C,EAAE8M,EAAIjK,EAAI7C,EAAEoB,EAAI0B,EAAI9C,EAAEoR,EAExB3J,IACT,EC7CF,SAAS4J,GAAYtF,EAAGe,EAAGwE,GACzB,OAAOjQ,KAAKkQ,IAAIzE,EAAIf,GAAE,IACxB,CAEe,MAAMmF,GACnB3J,eAAeD,GACbG,KAAKkF,QAAQrF,EACf,CAEAsF,wBAAwBrK,GAEtB,MAAMiP,EAAsB,SAAXjP,EAAEkP,OAA8B,IAAXlP,EAAEkP,KAClCC,EAAQnP,EAAEkP,OAASD,GAAuB,MAAXjP,EAAEkP,OAAiB,EAAI,EACtDE,EAAQpP,EAAEkP,OAASD,GAAuB,MAAXjP,EAAEkP,OAAiB,EAAI,EACtDG,EACJrP,EAAEsP,MAAQtP,EAAEsP,KAAK7Q,OACbuB,EAAEsP,KAAK,GACPC,SAASvP,EAAEsP,MACTtP,EAAEsP,KACFC,SAASvP,EAAEqP,OACTrP,EAAEqP,MACF,EACJG,EACJxP,EAAEsP,MAAQtP,EAAEsP,KAAK7Q,OACbuB,EAAEsP,KAAK,GACPC,SAASvP,EAAEsP,MACTtP,EAAEsP,KACFC,SAASvP,EAAEwP,OACTxP,EAAEwP,MACF,EACJC,EACJzP,EAAE0P,OAAS1P,EAAE0P,MAAMjR,OACfuB,EAAE0P,MAAM,GAAKP,EACbI,SAASvP,EAAE0P,OACT1P,EAAE0P,MAAQP,EACVI,SAASvP,EAAEyP,QACTzP,EAAEyP,OAASN,EACXA,EACJQ,EACJ3P,EAAE0P,OAAS1P,EAAE0P,MAAMjR,OACfuB,EAAE0P,MAAM,GAAKN,EACbG,SAASvP,EAAE0P,OACT1P,EAAE0P,MAAQN,EACVG,SAASvP,EAAE2P,QACT3P,EAAE2P,OAASP,EACXA,EACJQ,EAAQ5P,EAAE4P,OAAS,EACnBC,EAAQ7P,EAAE8P,QAAU9P,EAAE6P,OAAS,EAC/B5P,EAAS,IAAIoO,GACjBrO,EAAEC,QAAUD,EAAE+P,QAAU/P,EAAEE,IAAMF,EAAEG,QAClCH,EAAEI,IAAMJ,EAAEK,SAENH,EAAKD,EAAOK,EACZF,EAAKH,EAAOM,EAEZ+E,EAAW,IAAI+I,GACnBrO,EAAEsF,UAAYtF,EAAEgQ,IAAMhQ,EAAEiQ,WAAaC,IACrClQ,EAAEmQ,IAAMnQ,EAAEoQ,WAAaF,KAEnBF,EAAK1K,EAAShF,EACd6P,EAAK7K,EAAS/E,EACd8P,EAAY,IAAIhC,GACpBrO,EAAEqQ,WAAarQ,EAAEsQ,IAAMtQ,EAAEuQ,WACzBvQ,EAAEwQ,IAAMxQ,EAAEyQ,YAENH,EAAKD,EAAU/P,EACfkQ,EAAKH,EAAU9P,EACfmQ,EAAW,IAAIrC,GACnBrO,EAAE0Q,UAAY1Q,EAAE2Q,IAAM3Q,EAAE4Q,UACxB5Q,EAAE6Q,IAAM7Q,EAAE8Q,WAMZ,MAAO,CACLrB,SACAE,SACAN,QACAG,QACAI,QACAC,QACAc,GAXSD,EAASpQ,EAYlBuQ,GAXSH,EAASnQ,EAYlB+P,KACAE,KACAtQ,KACAE,KACA4P,KACAG,KAEJ,CAEA9F,iBAAiBb,GACf,MAAO,CAAEA,EAAGA,EAAE,GAAIe,EAAGf,EAAE,GAAIjB,EAAGiB,EAAE,GAAI3K,EAAG2K,EAAE,GAAII,EAAGJ,EAAE,GAAIqF,EAAGrF,EAAE,GAC7D,CAEAa,oBAAoBrK,GAClB,OACS,MAAPA,EAAEwJ,GACK,MAAPxJ,EAAEuK,GACK,MAAPvK,EAAEuI,GACK,MAAPvI,EAAEnB,GACK,MAAPmB,EAAE4J,GACK,MAAP5J,EAAE6O,CAEN,CAGAxE,sBAAsBO,EAAGhJ,EAAG5B,GAE1B,MAAMwJ,EAAIoB,EAAEpB,EAAI5H,EAAE4H,EAAIoB,EAAErC,EAAI3G,EAAE2I,EACxBA,EAAIK,EAAEL,EAAI3I,EAAE4H,EAAIoB,EAAE/L,EAAI+C,EAAE2I,EACxBhC,EAAIqC,EAAEpB,EAAI5H,EAAE2G,EAAIqC,EAAErC,EAAI3G,EAAE/C,EACxBA,EAAI+L,EAAEL,EAAI3I,EAAE2G,EAAIqC,EAAE/L,EAAI+C,EAAE/C,EACxB+K,EAAIgB,EAAEhB,EAAIgB,EAAEpB,EAAI5H,EAAEgI,EAAIgB,EAAErC,EAAI3G,EAAEiN,EAC9BA,EAAIjE,EAAEiE,EAAIjE,EAAEL,EAAI3I,EAAEgI,EAAIgB,EAAE/L,EAAI+C,EAAEiN,EAUpC,OAPA7O,EAAEwJ,EAAIA,EACNxJ,EAAEuK,EAAIA,EACNvK,EAAEuI,EAAIA,EACNvI,EAAEnB,EAAIA,EACNmB,EAAE4J,EAAIA,EACN5J,EAAE6O,EAAIA,EAEC7O,CACT,CAEA+P,OAAOgB,EAAIC,EAAIC,GACb,OAAO/L,KAAKoJ,QAAQ4C,QAAQH,EAAIC,EAAIC,EACtC,CAGAC,QAAQH,EAAIC,EAAIC,GACd,MAAME,EAAKJ,GAAM,EACXK,EAAKJ,GAAM,EACjB,OAAO9L,KAAKmM,YAAYF,GAAKC,GAAIE,WAAWL,GAAQI,WAAWF,EAAIC,EACrE,CAGA9C,QACE,OAAO,IAAIK,GAAOzJ,KACpB,CAGAqM,UAAUR,EAAK,EAAGC,EAAK,GAErB,MAAMxH,EAAItE,KAAKsE,EACTe,EAAIrF,KAAKqF,EACThC,EAAIrD,KAAKqD,EACT1J,EAAIqG,KAAKrG,EACT+K,EAAI1E,KAAK0E,EACTiF,EAAI3J,KAAK2J,EAGT2C,EAAchI,EAAI3K,EAAI0L,EAAIhC,EAC1BkJ,EAAMD,EAAc,EAAI,GAAK,EAI7BE,EAAKD,EAAM3S,KAAKwN,KAAK9C,EAAIA,EAAIe,EAAIA,GACjCoH,EAAW7S,KAAKyN,MAAMkF,EAAMlH,EAAGkH,EAAMjI,GACrCqG,EAAS,IAAM/Q,KAAKC,GAAM4S,EAC1B9E,EAAK/N,KAAK2N,IAAIkF,GACdC,EAAK9S,KAAK4L,IAAIiH,GAIdE,GAAOrI,EAAIjB,EAAIgC,EAAI1L,GAAK2S,EACxBM,EAAMvJ,EAAImJ,GAAOG,EAAMrI,EAAIe,IAAO1L,EAAI6S,GAAOG,EAAMtH,EAAIf,GAO7D,MAAO,CAELiG,OAAQiC,EACR/B,OAAQmC,EACRlC,MAAOiC,EACP/B,OAAQD,EACRU,WAVS3G,EAAImH,EAAKA,EAAKlE,EAAK6E,EAAKV,GAAMa,EAAMhF,EAAK6E,EAAKE,EAAKE,GAW5DrB,WAVS5B,EAAImC,EAAKD,EAAKa,EAAKF,EAAKV,GAAMa,EAAMD,EAAKF,EAAK7E,EAAKiF,GAW5D3R,QAAS4Q,EACT1Q,QAAS2Q,EAGTxH,EAAGtE,KAAKsE,EACRe,EAAGrF,KAAKqF,EACRhC,EAAGrD,KAAKqD,EACR1J,EAAGqG,KAAKrG,EACR+K,EAAG1E,KAAK0E,EACRiF,EAAG3J,KAAK2J,EAEZ,CAGAkD,OAAOC,GACL,GAAIA,IAAU9M,KAAM,OAAO,EAC3B,MAAM+M,EAAO,IAAItD,GAAOqD,GACxB,OACElD,GAAY5J,KAAKsE,EAAGyI,EAAKzI,IACzBsF,GAAY5J,KAAKqF,EAAG0H,EAAK1H,IACzBuE,GAAY5J,KAAKqD,EAAG0J,EAAK1J,IACzBuG,GAAY5J,KAAKrG,EAAGoT,EAAKpT,IACzBiQ,GAAY5J,KAAK0E,EAAGqI,EAAKrI,IACzBkF,GAAY5J,KAAK2J,EAAGoD,EAAKpD,EAE7B,CAGAK,KAAKgD,EAAMnC,GACT,OAAO7K,KAAKoJ,QAAQ6D,MAAMD,EAAMnC,EAClC,CAEAoC,MAAMD,EAAMnC,GACV,MAAgB,MAATmC,EACHhN,KAAKkN,QAAQ,EAAG,EAAGrC,EAAQ,GAClB,MAATmC,EACEhN,KAAKkN,OAAO,GAAI,EAAG,EAAGrC,GACtB7K,KAAKkN,QAAQ,GAAI,EAAGF,EAAMnC,GAAUmC,EAC5C,CAGA9H,KAAKoE,GACH,MAAMD,EAAOI,GAAO0D,UAAU,CAAC,EAAG,EAAG,EAAG,EAAG,EAAG,IA0B9C,OAvBA7D,EACEA,aAAkB8D,QACd9D,EAAO+D,YACW,iBAAX/D,EACLG,GAAO0D,UAAU7D,EAAOvG,MAAMlB,IAAW3I,IAAIoU,aAC7C9U,MAAMC,QAAQ6Q,GACZG,GAAO0D,UAAU7D,GACC,iBAAXA,GAAuBG,GAAOC,aAAaJ,GAChDA,EACkB,iBAAXA,GACL,IAAIG,IAASF,UAAUD,GACF,IAArB3F,UAAUpK,OACRkQ,GAAO0D,UAAU,GAAG7S,MAAMiT,KAAK5J,YAC/B0F,EAGhBrJ,KAAKsE,EAAgB,MAAZgF,EAAOhF,EAAYgF,EAAOhF,EAAI+E,EAAK/E,EAC5CtE,KAAKqF,EAAgB,MAAZiE,EAAOjE,EAAYiE,EAAOjE,EAAIgE,EAAKhE,EAC5CrF,KAAKqD,EAAgB,MAAZiG,EAAOjG,EAAYiG,EAAOjG,EAAIgG,EAAKhG,EAC5CrD,KAAKrG,EAAgB,MAAZ2P,EAAO3P,EAAY2P,EAAO3P,EAAI0P,EAAK1P,EAC5CqG,KAAK0E,EAAgB,MAAZ4E,EAAO5E,EAAY4E,EAAO5E,EAAI2E,EAAK3E,EAC5C1E,KAAK2J,EAAgB,MAAZL,EAAOK,EAAYL,EAAOK,EAAIN,EAAKM,EAErC3J,IACT,CAEAwN,UACE,OAAOxN,KAAKoJ,QAAQqE,UACtB,CAGAA,WAEE,MAAMnJ,EAAItE,KAAKsE,EACTe,EAAIrF,KAAKqF,EACThC,EAAIrD,KAAKqD,EACT1J,EAAIqG,KAAKrG,EACT+K,EAAI1E,KAAK0E,EACTiF,EAAI3J,KAAK2J,EAGT+D,EAAMpJ,EAAI3K,EAAI0L,EAAIhC,EACxB,IAAKqK,EAAK,MAAM,IAAI7H,MAAM,iBAAmB7F,MAG7C,MAAM2N,EAAKhU,EAAI+T,EACTE,GAAMvI,EAAIqI,EACVG,GAAMxK,EAAIqK,EACVI,EAAKxJ,EAAIoJ,EAGTK,IAAOJ,EAAKjJ,EAAImJ,EAAKlE,GACrBqE,IAAOJ,EAAKlJ,EAAIoJ,EAAKnE,GAU3B,OAPA3J,KAAKsE,EAAIqJ,EACT3N,KAAKqF,EAAIuI,EACT5N,KAAKqD,EAAIwK,EACT7N,KAAKrG,EAAImU,EACT9N,KAAK0E,EAAIqJ,EACT/N,KAAK2J,EAAIqE,EAEFhO,IACT,CAEAiO,UAAUlC,GACR,OAAO/L,KAAKoJ,QAAQgD,WAAWL,EACjC,CAEAK,WAAWL,GACT,MACMrG,EAAIqG,aAAkBtC,GAASsC,EAAS,IAAItC,GAAOsC,GAEzD,OAAOtC,GAAOyE,eAAexI,EAHnB1F,KAGyBA,KACrC,CAGAmO,SAASpC,GACP,OAAO/L,KAAKoJ,QAAQgF,UAAUrC,EAChC,CAEAqC,UAAUrC,GAER,MACMrP,EAAIqP,aAAkBtC,GAASsC,EAAS,IAAItC,GAAOsC,GAEzD,OAAOtC,GAAOyE,eAHJlO,KAGsBtD,EAAGsD,KACrC,CAGA4K,OAAOlO,EAAGmP,EAAIC,GACZ,OAAO9L,KAAKoJ,QAAQiF,QAAQ3R,EAAGmP,EAAIC,EACrC,CAEAuC,QAAQ3R,EAAGmP,EAAK,EAAGC,EAAK,GAEtBpP,EAAIhD,EAAQgD,GAEZ,MAAM6K,EAAM3N,KAAK2N,IAAI7K,GACf8I,EAAM5L,KAAK4L,IAAI9I,IAEf4H,EAAEA,EAACe,EAAEA,EAAChC,EAAEA,EAAC1J,EAAEA,EAAC+K,EAAEA,EAACiF,EAAEA,GAAM3J,KAS7B,OAPAA,KAAKsE,EAAIA,EAAIiD,EAAMlC,EAAIG,EACvBxF,KAAKqF,EAAIA,EAAIkC,EAAMjD,EAAIkB,EACvBxF,KAAKqD,EAAIA,EAAIkE,EAAM5N,EAAI6L,EACvBxF,KAAKrG,EAAIA,EAAI4N,EAAMlE,EAAImC,EACvBxF,KAAK0E,EAAIA,EAAI6C,EAAMoC,EAAInE,EAAMsG,EAAKtG,EAAMqG,EAAKtE,EAAMsE,EACnD7L,KAAK2J,EAAIA,EAAIpC,EAAM7C,EAAIc,EAAMqG,EAAKrG,EAAMsG,EAAKvE,EAAMuE,EAE5C9L,IACT,CAGAwK,QACE,OAAOxK,KAAKoJ,QAAQ8D,UAAUvJ,UAChC,CAEAuJ,OAAO9R,EAAGC,EAAID,EAAGyQ,EAAK,EAAGC,EAAK,GAEH,IAArBnI,UAAUpK,SACZuS,EAAKD,EACLA,EAAKxQ,EACLA,EAAID,GAGN,MAAMkJ,EAAEA,EAACe,EAAEA,EAAChC,EAAEA,EAAC1J,EAAEA,EAAC+K,EAAEA,EAACiF,EAAEA,GAAM3J,KAS7B,OAPAA,KAAKsE,EAAIA,EAAIlJ,EACb4E,KAAKqF,EAAIA,EAAIhK,EACb2E,KAAKqD,EAAIA,EAAIjI,EACb4E,KAAKrG,EAAIA,EAAI0B,EACb2E,KAAK0E,EAAIA,EAAItJ,EAAIyQ,EAAKzQ,EAAIyQ,EAC1B7L,KAAK2J,EAAIA,EAAItO,EAAIyQ,EAAKzQ,EAAIyQ,EAEnB9L,IACT,CAGA0K,MAAMpG,EAAGuH,EAAIC,GACX,OAAO9L,KAAKoJ,QAAQkF,OAAOhK,EAAGuH,EAAIC,EACpC,CAGAwC,OAAOC,EAAI1C,EAAK,EAAGC,EAAK,GACtB,MAAMxH,EAAEA,EAACe,EAAEA,EAAChC,EAAEA,EAAC1J,EAAEA,EAAC+K,EAAEA,EAACiF,EAAEA,GAAM3J,KAM7B,OAJAA,KAAKsE,EAAIA,EAAIe,EAAIkJ,EACjBvO,KAAKqD,EAAIA,EAAI1J,EAAI4U,EACjBvO,KAAK0E,EAAIA,EAAIiF,EAAI4E,EAAKzC,EAAKyC,EAEpBvO,IACT,CAGAoK,OACE,OAAOpK,KAAKoJ,QAAQoF,SAAS7K,UAC/B,CAEA6K,MAAMpT,EAAGC,EAAID,EAAGyQ,EAAK,EAAGC,EAAK,GAEF,IAArBnI,UAAUpK,SACZuS,EAAKD,EACLA,EAAKxQ,EACLA,EAAID,GAINA,EAAI1B,EAAQ0B,GACZC,EAAI3B,EAAQ2B,GAEZ,MAAMkT,EAAK3U,KAAK6U,IAAIrT,GACdsT,EAAK9U,KAAK6U,IAAIpT,IAEdiJ,EAAEA,EAACe,EAAEA,EAAChC,EAAEA,EAAC1J,EAAEA,EAAC+K,EAAEA,EAACiF,EAAEA,GAAM3J,KAS7B,OAPAA,KAAKsE,EAAIA,EAAIe,EAAIkJ,EACjBvO,KAAKqF,EAAIA,EAAIf,EAAIoK,EACjB1O,KAAKqD,EAAIA,EAAI1J,EAAI4U,EACjBvO,KAAKrG,EAAIA,EAAI0J,EAAIqL,EACjB1O,KAAK0E,EAAIA,EAAIiF,EAAI4E,EAAKzC,EAAKyC,EAC3BvO,KAAK2J,EAAIA,EAAIjF,EAAIgK,EAAK7C,EAAK6C,EAEpB1O,IACT,CAGAmK,MAAM/O,EAAGyQ,EAAIC,GACX,OAAO9L,KAAKoK,KAAKhP,EAAG,EAAGyQ,EAAIC,EAC7B,CAGAxB,MAAMjP,EAAGwQ,EAAIC,GACX,OAAO9L,KAAKoK,KAAK,EAAG/O,EAAGwQ,EAAIC,EAC7B,CAEA3D,UACE,MAAO,CAACnI,KAAKsE,EAAGtE,KAAKqF,EAAGrF,KAAKqD,EAAGrD,KAAKrG,EAAGqG,KAAK0E,EAAG1E,KAAK2J,EACvD,CAGAtH,WACE,MACE,UACArC,KAAKsE,EACL,IACAtE,KAAKqF,EACL,IACArF,KAAKqD,EACL,IACArD,KAAKrG,EACL,IACAqG,KAAK0E,EACL,IACA1E,KAAK2J,EACL,GAEJ,CAGAJ,UAAUzO,GAER,GAAI2O,GAAOC,aAAa5O,GAAI,CAE1B,OADe,IAAI2O,GAAO3O,GACZsT,UAAUpO,KAC1B,CAGA,MAAM4C,EAAI6G,GAAOkF,iBAAiB7T,IAE1BM,EAAGJ,EAAIK,EAAGH,GAAO,IAAIiO,GAAMvG,EAAE5H,GAAI4H,EAAE1H,IAAIqO,UAD/BvJ,MAIV4O,GAAc,IAAInF,IACrB0C,WAAWvJ,EAAE6I,GAAI7I,EAAE+I,IACnBS,WANapM,MAObmM,YAAYnR,GAAKE,GACjBgS,OAAOtK,EAAE2H,OAAQ3H,EAAE6H,QACnB+D,MAAM5L,EAAEuH,MAAOvH,EAAE0H,OACjBgE,OAAO1L,EAAE8H,OACT2D,QAAQzL,EAAE+H,OACVwB,WAAWnR,EAAIE,GAGlB,GAAImP,SAASzH,EAAEkI,KAAOT,SAASzH,EAAEqI,IAAK,CACpC,MAAMlQ,EAAS,IAAIoO,GAAMnO,EAAIE,GAAIqO,UAAUqF,GAGrC3C,EAAK5B,SAASzH,EAAEkI,IAAMlI,EAAEkI,GAAK/P,EAAOK,EAAI,EACxC8Q,EAAK7B,SAASzH,EAAEqI,IAAMrI,EAAEqI,GAAKlQ,EAAOM,EAAI,EAC9CuT,EAAYzC,WAAWF,EAAIC,EAC7B,CAIA,OADA0C,EAAYzC,WAAWvJ,EAAEwI,GAAIxI,EAAE0I,IACxBsD,CACT,CAGAzD,UAAU/P,EAAGC,GACX,OAAO2E,KAAKoJ,QAAQ+C,WAAW/Q,EAAGC,EACpC,CAEA8Q,WAAW/Q,EAAGC,GAGZ,OAFA2E,KAAK0E,GAAKtJ,GAAK,EACf4E,KAAK2J,GAAKtO,GAAK,EACR2E,IACT,CAEA7D,UACE,MAAO,CACLmI,EAAGtE,KAAKsE,EACRe,EAAGrF,KAAKqF,EACRhC,EAAGrD,KAAKqD,EACR1J,EAAGqG,KAAKrG,EACR+K,EAAG1E,KAAK0E,EACRiF,EAAG3J,KAAK2J,EAEZ,EC/fa,SAASkF,KAEtB,IAAKA,GAAOC,MAAO,CACjB,MAAMnS,EAAMoB,IAAegR,KAAK,EAAG,GACnCpS,EAAIN,KAAKmH,MAAMI,QAAU,CACvB,aACA,qBACA,cACA,aACA,oBACAT,KAAK,KAEPxG,EAAIsD,KAAK,YAAa,SACtBtD,EAAIsD,KAAK,cAAe,QAExB,MAAM+O,EAAOrS,EAAIqS,OAAO3S,KAExBwS,GAAOC,MAAQ,CAAEnS,MAAKqS,OACxB,CAEA,IAAKH,GAAOC,MAAMnS,IAAIN,KAAK4S,WAAY,CACrC,MAAM5J,EAAItI,EAAQE,SAASiS,MAAQnS,EAAQE,SAASkS,gBACpDN,GAAOC,MAAMnS,IAAIyS,MAAM/J,EACzB,CAEA,OAAOwJ,GAAOC,KAChB,CCrBO,SAASO,GAAY1U,GAC1B,QAAQA,EAAIF,OAAUE,EAAID,QAAWC,EAAIS,GAAMT,EAAIU,EACrD,CFohBA2D,EAASyK,GAAQ,UElgBF,MAAM6F,GACnBxP,eAAeD,GACbG,KAAKkF,QAAQrF,EACf,CAEA0P,YAIE,OAFAvP,KAAK5E,GAAK2B,EAAQC,OAAOwS,YACzBxP,KAAK3E,GAAK0B,EAAQC,OAAOyS,YAClB,IAAIH,GAAItP,KACjB,CAEAkF,KAAKoE,GA6BH,OA3BAA,EACoB,iBAAXA,EACHA,EAAOvG,MAAMlB,IAAW3I,IAAIoU,YAC5B9U,MAAMC,QAAQ6Q,GACZA,EACkB,iBAAXA,EACL,CACiB,MAAfA,EAAOoG,KAAepG,EAAOoG,KAAOpG,EAAOlO,EAC7B,MAAdkO,EAAOqG,IAAcrG,EAAOqG,IAAMrG,EAAOjO,EACzCiO,EAAO7O,MACP6O,EAAO5O,QAEY,IAArBiJ,UAAUpK,OACR,GAAGe,MAAMiT,KAAK5J,WAdb,CAAC,EAAG,EAAG,EAAG,GAiBvB3D,KAAK5E,EAAIkO,EAAO,IAAM,EACtBtJ,KAAK3E,EAAIiO,EAAO,IAAM,EACtBtJ,KAAKvF,MAAQuF,KAAK4P,EAAItG,EAAO,IAAM,EACnCtJ,KAAKtF,OAASsF,KAAK2F,EAAI2D,EAAO,IAAM,EAGpCtJ,KAAK6P,GAAK7P,KAAK5E,EAAI4E,KAAK4P,EACxB5P,KAAK8P,GAAK9P,KAAK3E,EAAI2E,KAAK2F,EACxB3F,KAAK6L,GAAK7L,KAAK5E,EAAI4E,KAAK4P,EAAI,EAC5B5P,KAAK8L,GAAK9L,KAAK3E,EAAI2E,KAAK2F,EAAI,EAErB3F,IACT,CAEA+P,WACE,OAAOV,GAAYrP,KACrB,CAGAgQ,MAAMrV,GACJ,MAAMS,EAAIxB,KAAKwI,IAAIpC,KAAK5E,EAAGT,EAAIS,GACzBC,EAAIzB,KAAKwI,IAAIpC,KAAK3E,EAAGV,EAAIU,GACzBZ,EAAQb,KAAKuI,IAAInC,KAAK5E,EAAI4E,KAAKvF,MAAOE,EAAIS,EAAIT,EAAIF,OAASW,EAC3DV,EAASd,KAAKuI,IAAInC,KAAK3E,EAAI2E,KAAKtF,OAAQC,EAAIU,EAAIV,EAAID,QAAUW,EAEpE,OAAO,IAAIiU,GAAIlU,EAAGC,EAAGZ,EAAOC,EAC9B,CAEAyN,UACE,MAAO,CAACnI,KAAK5E,EAAG4E,KAAK3E,EAAG2E,KAAKvF,MAAOuF,KAAKtF,OAC3C,CAEA2H,WACE,OAAOrC,KAAK5E,EAAI,IAAM4E,KAAK3E,EAAI,IAAM2E,KAAKvF,MAAQ,IAAMuF,KAAKtF,MAC/D,CAEA6O,UAAUhR,GACFA,aAAakR,KACjBlR,EAAI,IAAIkR,GAAOlR,IAGjB,IAAI0X,EAAOC,IACPC,GAAQD,IACRE,EAAOF,IACPG,GAAQH,IAiBZ,MAfY,CACV,IAAI/G,GAAMnJ,KAAK5E,EAAG4E,KAAK3E,GACvB,IAAI8N,GAAMnJ,KAAK6P,GAAI7P,KAAK3E,GACxB,IAAI8N,GAAMnJ,KAAK5E,EAAG4E,KAAK8P,IACvB,IAAI3G,GAAMnJ,KAAK6P,GAAI7P,KAAK8P,KAGtBhM,SAAQ,SAAUpB,GACpBA,EAAIA,EAAE6G,UAAUhR,GAChB0X,EAAOrW,KAAKwI,IAAI6N,EAAMvN,EAAEtH,GACxB+U,EAAOvW,KAAKuI,IAAIgO,EAAMzN,EAAEtH,GACxBgV,EAAOxW,KAAKwI,IAAIgO,EAAM1N,EAAErH,GACxBgV,EAAOzW,KAAKuI,IAAIkO,EAAM3N,EAAErH,EAC1B,IAEO,IAAIiU,GAAIW,EAAMG,EAAMD,EAAOF,EAAMI,EAAOD,EACjD,EAGF,SAASE,GAAOzM,EAAI0M,EAAWC,GAC7B,IAAI7V,EAEJ,IAME,GAJAA,EAAM4V,EAAU1M,EAAGxH,MAIfgT,GAAY1U,MAxHQ0B,EAwHawH,EAAGxH,QAtH/BU,EAAQE,YAEfF,EAAQE,SAASkS,gBAAgBsB,UACjC,SAAUpU,GAER,KAAOA,EAAK4S,YACV5S,EAAOA,EAAK4S,WAEd,OAAO5S,IAASU,EAAQE,QACzB,GACDsQ,KAAKxQ,EAAQE,SAASkS,gBAAiB9S,IA6GvC,MAAM,IAAIwJ,MAAM,yBAEnB,CAAC,MAAOnB,GAEP/J,EAAM6V,EAAM3M,EACd,CA9HK,IAAqBxH,EAgI1B,OAAO1B,CACT,CA8DAtC,EAAgB,CACdqY,QAAS,CACPA,QAAQtV,EAAGC,EAAGZ,EAAOC,GAEnB,OAAS,MAALU,EAAkB,IAAIkU,GAAItP,KAAKC,KAAK,YAGjCD,KAAKC,KAAK,UAAW,IAAIqP,GAAIlU,EAAGC,EAAGZ,EAAOC,GAClD,EAEDiW,KAAKC,EAAOC,GAQV,IAAIpW,MAAEA,EAAKC,OAAEA,GAAWsF,KAAKC,KAAK,CAAC,QAAS,WAc5C,IATIxF,GAAUC,IACK,iBAAVD,GACW,iBAAXC,IAEPD,EAAQuF,KAAK3D,KAAKyU,YAClBpW,EAASsF,KAAK3D,KAAK0U,eAIhBtW,IAAUC,EACb,MAAM,IAAImL,MACR,6HAIJ,MAAMtB,EAAIvE,KAAK0Q,UAETM,EAAQvW,EAAQ8J,EAAE9J,MAClBwW,EAAQvW,EAAS6J,EAAE7J,OACnBiW,EAAO/W,KAAKwI,IAAI4O,EAAOC,GAE7B,GAAa,MAATL,EACF,OAAOD,EAGT,IAAIO,EAAaP,EAAOC,EAIpBM,IAAehB,MAAUgB,EAAaC,OAAOC,iBAAmB,KAEpEP,EACEA,GAAS,IAAI1H,GAAM1O,EAAQ,EAAIuW,EAAQzM,EAAEnJ,EAAGV,EAAS,EAAIuW,EAAQ1M,EAAElJ,GAErE,MAAMV,EAAM,IAAI2U,GAAI/K,GAAGgF,UACrB,IAAIE,GAAO,CAAEe,MAAO0G,EAAYnW,OAAQ8V,KAG1C,OAAO7Q,KAAK0Q,QAAQ/V,EACtB,KAIJqE,EAASsQ,GAAK,OC1Qd,MAAM+B,WAAa7Y,MACjBsH,YAAYwR,EAAM,MAAOzR,GAEvB,GADA0R,MAAMD,KAAQzR,GACK,iBAARyR,EAAkB,OAAOtR,KACpCA,KAAKzG,OAAS,EACdyG,KAAK/G,QAAQqY,EACf,EAYF7R,EAAO,CAAC4R,IAAO,CACbG,KAAKC,KAAmB5R,GACtB,MAA8B,mBAAnB4R,EACFzR,KAAK9G,KAAI,CAAC2K,EAAIxK,EAAGiY,IACfG,EAAelE,KAAK1J,EAAIA,EAAIxK,EAAGiY,KAGjCtR,KAAK9G,KAAK2K,GACRA,EAAG4N,MAAmB5R,IAGlC,EAEDsI,UACE,OAAO3P,MAAM0G,UAAUwS,OAAO3R,MAAM,GAAIC,KAC1C,IAGF,MAAM2R,GAAW,CAAC,UAAW,cAAe,QClC7B,SAASC,GAASC,EAAO1R,GACtC,OAAO,IAAIkR,GACTnY,GAAKiH,GAAUpD,EAAQE,UAAU6U,iBAAiBD,IAAQ,SAAUxV,GAClE,OAAOuC,EAAMvC,EACd,IAEL,CD8BAgV,GAAK5R,OAAS,SAAUtH,GACtBA,EAAUA,EAAQ4Z,QAAO,CAACC,EAAK1Z,KAEzBqZ,GAASnW,SAASlD,IAGN,MAAZA,EAAK,KAGLA,KAAQE,MAAM0G,YAChB8S,EAAI,IAAM1Z,GAAQE,MAAM0G,UAAU5G,IAIpC0Z,EAAI1Z,GAAQ,YAAa2Z,GACvB,OAAOjS,KAAKwR,KAAKlZ,KAAS2Z,KAZQD,IAenC,CAAE,GAELvS,EAAO,CAAC4R,IAAOlZ,EACjB,EE1DA,IAAI+Z,GAAa,EACV,MAAMC,GAAe,CAAA,EAErB,SAASC,GAAUvT,GACxB,IAAIwT,EAAIxT,EAASyT,iBAKjB,OAFID,IAAMtV,EAAQC,SAAQqV,EAAIF,IACzBE,EAAEE,SAAQF,EAAEE,OAAS,CAAA,GACnBF,EAAEE,MACX,CAEO,SAASC,GAAe3T,GAC7B,OAAOA,EAAS2T,gBAClB,CAEO,SAASC,GAAY5T,GAC1B,IAAIwT,EAAIxT,EAASyT,iBACbD,IAAMtV,EAAQC,SAAQqV,EAAIF,IAC1BE,EAAEE,SAAQF,EAAEE,OAAS,CAAA,EAC3B,CAGO,SAASG,GAAGrW,EAAMkW,EAAQI,EAAUC,EAASC,GAClD,MAAMnN,EAAIiN,EAASG,KAAKF,GAAWvW,GAC7BwC,EAAWd,EAAa1B,GACxB0W,EAAMX,GAAUvT,GAChBwT,EAAIG,GAAe3T,GAGzB0T,EAAS/Z,MAAMC,QAAQ8Z,GAAUA,EAASA,EAAOxP,MAAMlB,IAGlD8Q,EAASK,mBACZL,EAASK,mBAAqBd,IAGhCK,EAAOzO,SAAQ,SAAUmP,GACvB,MAAMC,EAAKD,EAAMlQ,MAAM,KAAK,GACtBlF,EAAKoV,EAAMlQ,MAAM,KAAK,IAAM,IAGlCgQ,EAAIG,GAAMH,EAAIG,IAAO,CAAA,EACrBH,EAAIG,GAAIrV,GAAMkV,EAAIG,GAAIrV,IAAO,GAG7BkV,EAAIG,GAAIrV,GAAI8U,EAASK,kBAAoBtN,EAGzC2M,EAAEc,iBAAiBD,EAAIxN,EAAGmN,IAAW,EACvC,GACF,CAGO,SAASO,GAAI/W,EAAMkW,EAAQI,EAAUE,GAC1C,MAAMhU,EAAWd,EAAa1B,GACxB0W,EAAMX,GAAUvT,GAChBwT,EAAIG,GAAe3T,IAGD,mBAAb8T,IACTA,EAAWA,EAASK,qBAKtBT,EAAS/Z,MAAMC,QAAQ8Z,GAAUA,GAAUA,GAAU,IAAIxP,MAAMlB,KAExDiC,SAAQ,SAAUmP,GACvB,MAAMC,EAAKD,GAASA,EAAMlQ,MAAM,KAAK,GAC/BlF,EAAKoV,GAASA,EAAMlQ,MAAM,KAAK,GACrC,IAAIsQ,EAAW3N,EAEf,GAAIiN,EAEEI,EAAIG,IAAOH,EAAIG,GAAIrV,GAAM,OAE3BwU,EAAEiB,oBACAJ,EACAH,EAAIG,GAAIrV,GAAM,KAAK8U,GACnBE,IAAW,UAGNE,EAAIG,GAAIrV,GAAM,KAAK8U,SAEvB,GAAIO,GAAMrV,GAEf,GAAIkV,EAAIG,IAAOH,EAAIG,GAAIrV,GAAK,CAC1B,IAAK6H,KAAKqN,EAAIG,GAAIrV,GAChBuV,GAAIf,EAAG,CAACa,EAAIrV,GAAIsF,KAAK,KAAMuC,UAGtBqN,EAAIG,GAAIrV,EACjB,OACK,GAAIA,EAET,IAAKoV,KAASF,EACZ,IAAKM,KAAaN,EAAIE,GAChBpV,IAAOwV,GACTD,GAAIf,EAAG,CAACY,EAAOpV,GAAIsF,KAAK,WAIzB,GAAI+P,GAET,GAAIH,EAAIG,GAAK,CACX,IAAKG,KAAaN,EAAIG,GACpBE,GAAIf,EAAG,CAACa,EAAIG,GAAWlQ,KAAK,aAGvB4P,EAAIG,EACb,MACK,CAEL,IAAKD,KAASF,EACZK,GAAIf,EAAGY,GAGTR,GAAY5T,EACd,CACF,GACF,CAEO,SAAS0U,GAASlX,EAAM4W,EAAOlX,EAAM8W,GAC1C,MAAMR,EAAIG,GAAenW,GAazB,OAVI4W,aAAiBlW,EAAQC,OAAOwW,QAGlCP,EAAQ,IAAIlW,EAAQC,OAAOyW,YAAYR,EAAO,CAC5CS,OAAQ3X,EACR4X,YAAY,KACTd,KALLR,EAAEuB,cAAcX,GASXA,CACT,CC1Ie,MAAMY,WAAoBpW,EACvC0V,mBAAoB,CAEpBI,SAASN,EAAOlX,EAAM8W,GACpB,OAAOU,GAASvT,KAAMiT,EAAOlX,EAAM8W,EACrC,CAEAe,cAAcX,GACZ,MAAMF,EAAM/S,KAAKsS,iBAAiBC,OAClC,IAAKQ,EAAK,OAAO,EAEjB,MAAMR,EAASQ,EAAIE,EAAMa,MAEzB,IAAK,MAAMza,KAAKkZ,EACd,IAAK,MAAMwB,KAAKxB,EAAOlZ,GACrBkZ,EAAOlZ,GAAG0a,GAAGd,GAIjB,OAAQA,EAAMe,gBAChB,CAGAC,KAAKhB,EAAOlX,EAAM8W,GAEhB,OADA7S,KAAKuT,SAASN,EAAOlX,EAAM8W,GACpB7S,IACT,CAEAsS,iBACE,OAAOtS,IACT,CAEAwS,iBACE,OAAOxS,IACT,CAGAoT,IAAIH,EAAON,EAAUE,GAEnB,OADAO,GAAIpT,KAAMiT,EAAON,EAAUE,GACpB7S,IACT,CAGA0S,GAAGO,EAAON,EAAUC,EAASC,GAE3B,OADAH,GAAG1S,KAAMiT,EAAON,EAAUC,EAASC,GAC5B7S,IACT,CAEAsT,sBAAuB,ECpDlB,SAASY,KAAQ,CDuDxBlV,EAAS6U,GAAa,eCpDf,MAAMM,GAAW,CACtBC,SAAU,IACVC,KAAM,IACNC,MAAO,GAIIrC,GAAQ,CAEnB,eAAgB,EAChB,iBAAkB,EAClB,eAAgB,EAChB,kBAAmB,QACnB,iBAAkB,OAClBsC,KAAM,UACNC,OAAQ,UACRC,QAAS,EAGTrZ,EAAG,EACHC,EAAG,EACHwQ,GAAI,EACJC,GAAI,EAGJrR,MAAO,EACPC,OAAQ,EAGRgC,EAAG,EACH+O,GAAI,EACJE,GAAI,EAGJ+I,OAAQ,EACR,eAAgB,EAChB,aAAc,UAGd,cAAe,8DCxCF,MAAMC,WAAiBnc,MACpCsH,eAAeD,GACb0R,SAAS1R,GACTG,KAAKkF,QAAQrF,EACf,CAEAuJ,QACE,OAAO,IAAIpJ,KAAKF,YAAYE,KAC9B,CAEAkF,KAAKoM,GAEH,MAAmB,iBAARA,IACXtR,KAAKzG,OAAS,EACdyG,KAAK/G,QAAQ+G,KAAKyE,MAAM6M,KAFYtR,IAItC,CAGAyE,MAAMtL,EAAQ,IAEZ,OAAIA,aAAiBX,MAAcW,EAE5BA,EAAM2J,OAAOC,MAAMlB,IAAW3I,IAAIoU,WAC3C,CAEAnF,UACE,OAAO3P,MAAM0G,UAAUwS,OAAO3R,MAAM,GAAIC,KAC1C,CAEA4U,QACE,OAAO,IAAIlZ,IAAIsE,KACjB,CAEAqC,WACE,OAAOrC,KAAKmD,KAAK,IACnB,CAGAhH,UACE,MAAMuH,EAAM,GAEZ,OADAA,EAAIzK,QAAQ+G,MACL0D,CACT,EC1Ca,MAAMmR,GAEnB/U,eAAeD,GACbG,KAAKkF,QAAQrF,EACf,CAEAiV,QAAQC,GACN,OAAO,IAAIF,GAAU7U,KAAKgV,MAAOD,EACnC,CAGAE,OAAOC,GAEL,OADAA,EAAS,IAAIL,GAAUK,GAChB,IAAIL,GAAU7U,KAAOkV,EAAQlV,KAAK+U,MAAQG,EAAOH,KAC1D,CAEA7P,KAAK8P,EAAOD,GA0CV,OAzCAA,EAAOvc,MAAMC,QAAQuc,GAASA,EAAM,GAAKD,EACzCC,EAAQxc,MAAMC,QAAQuc,GAASA,EAAM,GAAKA,EAG1ChV,KAAKgV,MAAQ,EACbhV,KAAK+U,KAAOA,GAAQ,GAGC,iBAAVC,EAEThV,KAAKgV,MAAQG,MAAMH,GACf,EACC3K,SAAS2K,GAIRA,EAHAA,EAAQ,GACL,MACD,MAEkB,iBAAVA,GAChBD,EAAOC,EAAMI,MAAMlU,MAIjBlB,KAAKgV,MAAQ1H,WAAWyH,EAAK,IAGb,MAAZA,EAAK,GACP/U,KAAKgV,OAAS,IACO,MAAZD,EAAK,KACd/U,KAAKgV,OAAS,KAIhBhV,KAAK+U,KAAOA,EAAK,IAGfC,aAAiBH,KACnB7U,KAAKgV,MAAQA,EAAM7Y,UACnB6D,KAAK+U,KAAOC,EAAMD,MAIf/U,IACT,CAGAqV,MAAMH,GAEJ,OADAA,EAAS,IAAIL,GAAUK,GAChB,IAAIL,GAAU7U,KAAOkV,EAAQlV,KAAK+U,MAAQG,EAAOH,KAC1D,CAGAO,KAAKJ,GAEH,OADAA,EAAS,IAAIL,GAAUK,GAChB,IAAIL,GAAU7U,KAAOkV,EAAQlV,KAAK+U,MAAQG,EAAOH,KAC1D,CAGAQ,MAAML,GAEJ,OADAA,EAAS,IAAIL,GAAUK,GAChB,IAAIL,GAAU7U,KAAOkV,EAAQlV,KAAK+U,MAAQG,EAAOH,KAC1D,CAEA5M,UACE,MAAO,CAACnI,KAAKgV,MAAOhV,KAAK+U,KAC3B,CAEAS,SACE,OAAOxV,KAAKqC,UACd,CAEAA,WACE,OACiB,MAAdrC,KAAK+U,QACc,IAAb/U,KAAKgV,OAAe,IACT,MAAdhV,KAAK+U,KACH/U,KAAKgV,MAAQ,IACbhV,KAAKgV,OAAShV,KAAK+U,IAE7B,CAEA5Y,UACE,OAAO6D,KAAKgV,KACd,EChGF,MAAMS,GAAkB,IAAI/Z,IAAI,CAC9B,OACA,SACA,QACA,UACA,aACA,cACA,mBAGIga,GAAQ,GCCC,MAAMC,YAAY9B,GAC/B/T,YAAYzD,EAAM4V,GAChBV,QACAvR,KAAK3D,KAAOA,EACZ2D,KAAK8T,KAAOzX,EAAKR,SAEboW,GAAS5V,IAAS4V,GACpBjS,KAAKC,KAAKgS,EAEd,CAGAxR,IAAIjG,EAASnB,GAiBX,OAhBAmB,EAAUuD,EAAavD,IAIbob,iBACR5V,KAAK3D,gBAAgBU,EAAQC,OAAO6Y,YAEpCrb,EAAQob,kBAGD,MAALvc,EACF2G,KAAK3D,KAAKyZ,YAAYtb,EAAQ6B,MACrB7B,EAAQ6B,OAAS2D,KAAK3D,KAAK0Z,WAAW1c,IAC/C2G,KAAK3D,KAAK2E,aAAaxG,EAAQ6B,KAAM2D,KAAK3D,KAAK0Z,WAAW1c,IAGrD2G,IACT,CAGAoP,MAAMjP,EAAQ9G,GACZ,OAAO0E,EAAaoC,GAAQ6V,IAAIhW,KAAM3G,EACxC,CAGAkG,WACE,OAAO,IAAI8R,GACTnY,EAAI8G,KAAK3D,KAAKkD,UAAU,SAAUlD,GAChC,OAAOuC,EAAMvC,EACd,IAEL,CAGA4Z,QAEE,KAAOjW,KAAK3D,KAAK6Z,iBACflW,KAAK3D,KAAKkC,YAAYyB,KAAK3D,KAAK8Z,WAGlC,OAAOnW,IACT,CAGAoJ,MAAMgN,GAAO,EAAMC,GAAe,GAEhCrW,KAAKlE,iBAGL,IAAIwa,EAAYtW,KAAK3D,KAAKka,UAAUH,GAKpC,OAJIC,IAEFC,EAAYhX,EAAYgX,IAEnB,IAAItW,KAAKF,YAAYwW,EAC9B,CAGA9E,KAAKpY,EAAOgd,GACV,MAAM7W,EAAWS,KAAKT,WACtB,IAAIlG,EAAGC,EAEP,IAAKD,EAAI,EAAGC,EAAKiG,EAAShG,OAAQF,EAAIC,EAAID,IACxCD,EAAM2G,MAAMR,EAASlG,GAAI,CAACA,EAAGkG,IAEzB6W,GACF7W,EAASlG,GAAGmY,KAAKpY,EAAOgd,GAI5B,OAAOpW,IACT,CAEAxF,QAAQqB,EAAUoW,GAChB,OAAOjS,KAAKgW,IAAI,IAAIL,IAAI/X,EAAO/B,GAAWoW,GAC5C,CAGAuE,QACE,OAAO5X,EAAMoB,KAAK3D,KAAKiC,WACzB,CAGAmY,IAAIpd,GACF,OAAOuF,EAAMoB,KAAK3D,KAAK0Z,WAAW1c,GACpC,CAEAiZ,iBACE,OAAOtS,KAAK3D,IACd,CAEAmW,iBACE,OAAOxS,KAAK3D,IACd,CAGAT,IAAIpB,GACF,OAAOwF,KAAKK,MAAM7F,IAAY,CAChC,CAEAoC,KAAK8Z,EAAUC,GACb,OAAO3W,KAAK4W,IAAIF,EAAUC,EAAW/Z,EACvC,CAGA4C,GAAGA,GAOD,YALkB,IAAPA,GAAuBQ,KAAK3D,KAAKmD,KAC1CQ,KAAK3D,KAAKmD,GAAKH,EAAIW,KAAK8T,OAInB9T,KAAKC,KAAK,KAAMT,EACzB,CAGAa,MAAM7F,GACJ,MAAO,GAAGF,MAAMiT,KAAKvN,KAAK3D,KAAK0Z,YAAY9S,QAAQzI,EAAQ6B,KAC7D,CAGAwa,OACE,OAAOjY,EAAMoB,KAAK3D,KAAK8Z,UACzB,CAGAW,QAAQC,GACN,MAAMlT,EAAK7D,KAAK3D,KACV2a,EACJnT,EAAGiT,SACHjT,EAAGoT,iBACHpT,EAAGqT,mBACHrT,EAAGsT,oBACHtT,EAAGuT,uBACHvT,EAAGwT,kBACH,KACF,OAAOL,GAAWA,EAAQzJ,KAAK1J,EAAIkT,EACrC,CAGA5W,OAAO2T,GACL,IAAI3T,EAASH,KAGb,IAAKG,EAAO9D,KAAK4S,WAAY,OAAO,KAKpC,GAFA9O,EAASvB,EAAMuB,EAAO9D,KAAK4S,aAEtB6E,EAAM,OAAO3T,EAGlB,GACE,GACkB,iBAAT2T,EAAoB3T,EAAO2W,QAAQhD,GAAQ3T,aAAkB2T,EAEpE,OAAO3T,QACDA,EAASvB,EAAMuB,EAAO9D,KAAK4S,aAErC,OAAO9O,CACT,CAGA6V,IAAIxb,EAASnB,GAGX,OAFAmB,EAAUuD,EAAavD,GACvBwF,KAAKS,IAAIjG,EAASnB,GACXmB,CACT,CAGA8c,MAAMnX,EAAQ9G,GACZ,OAAO0E,EAAaoC,GAAQM,IAAIT,KAAM3G,EACxC,CAGAqH,SAKE,OAJIV,KAAKG,UACPH,KAAKG,SAASoX,cAAcvX,MAGvBA,IACT,CAGAuX,cAAc/c,GAGZ,OAFAwF,KAAK3D,KAAKkC,YAAY/D,EAAQ6B,MAEvB2D,IACT,CAGAhG,QAAQQ,GAON,OANAA,EAAUuD,EAAavD,GAEnBwF,KAAK3D,KAAK4S,YACZjP,KAAK3D,KAAK4S,WAAWuI,aAAahd,EAAQ6B,KAAM2D,KAAK3D,MAGhD7B,CACT,CAEA0H,MAAMuV,EAAY,EAAGve,EAAM,MACzB,MAAMwe,EAAS,IAAMD,EACfxF,EAAQjS,KAAKC,KAAK/G,GAExB,IAAK,MAAMG,KAAK4Y,EACU,iBAAbA,EAAM5Y,KACf4Y,EAAM5Y,GAAKO,KAAKsI,MAAM+P,EAAM5Y,GAAKqe,GAAUA,GAK/C,OADA1X,KAAKC,KAAKgS,GACHjS,IACT,CAGArD,IAAIgb,EAASC,GACX,OAAO5X,KAAK4W,IAAIe,EAASC,EAAUjb,EACrC,CAGA0F,WACE,OAAOrC,KAAKR,IACd,CAEAqY,MAAMC,GAGJ,OADA9X,KAAK3D,KAAK0b,YAAcD,EACjB9X,IACT,CAEAgY,KAAK3b,GACH,MAAM8D,EAASH,KAAKG,SAEpB,IAAKA,EACH,OAAOH,KAAKoP,MAAM/S,GAGpB,MAAM+D,EAAWD,EAAOE,MAAML,MAC9B,OAAOG,EAAO6V,IAAI3Z,EAAM+D,GAAU4V,IAAIhW,KACxC,CAGAlE,iBAME,OAJAkE,KAAKwR,MAAK,WACRxR,KAAKlE,gBACP,IAEOkE,IACT,CAGA4W,IAAIqB,EAASC,EAAUra,GAQrB,GAPuB,kBAAZoa,IACTpa,EAAKqa,EACLA,EAAWD,EACXA,EAAU,MAIG,MAAXA,GAAsC,mBAAZA,EAAwB,CAEpDC,EAAuB,MAAZA,GAA0BA,EAGrClY,KAAKlE,iBACL,IAAIqc,EAAUnY,KAGd,GAAe,MAAXiY,EAAiB,CAInB,GAHAE,EAAUvZ,EAAMuZ,EAAQ9b,KAAKka,WAAU,IAGnC2B,EAAU,CACZ,MAAM1e,EAASye,EAAQE,GAIvB,GAHAA,EAAU3e,GAAU2e,GAGL,IAAX3e,EAAkB,MAAO,EAC/B,CAGA2e,EAAQ3G,MAAK,WACX,MAAMhY,EAASye,EAAQjY,MACjBoY,EAAQ5e,GAAUwG,MAGT,IAAXxG,EACFwG,KAAKU,SAGIlH,GAAUwG,OAASoY,GAC5BpY,KAAKhG,QAAQoe,EAEhB,IAAE,EACL,CAGA,OAAOF,EAAWC,EAAQ9b,KAAKsa,UAAYwB,EAAQ9b,KAAKgC,SAC1D,CAKA6Z,EAAuB,MAAZA,GAA2BA,EAGtC,MAAMG,EAAOza,EAAO,UAAWC,GACzBya,EAAWvb,EAAQE,SAASsb,yBAGlCF,EAAKha,UAAY4Z,EAGjB,IAAK,IAAIO,EAAMH,EAAK9Y,SAAShG,OAAQif,KACnCF,EAASxC,YAAYuC,EAAKI,mBAG5B,MAAMtY,EAASH,KAAKG,SAGpB,OAAO+X,EAAWlY,KAAKhG,QAAQse,IAAanY,EAASH,KAAKS,IAAI6X,EAChE,EAGF7Y,EAAOkW,IAAK,CAAE1V,KD9UC,SAAcA,EAAMwD,EAAK5F,GAEtC,GAAY,MAARoC,EAAc,CAEhBA,EAAO,CAAA,EACPwD,EAAMzD,KAAK3D,KAAKmI,WAEhB,IAAK,MAAMnI,KAAQoH,EACjBxD,EAAK5D,EAAKR,UAAY8F,EAASuC,KAAK7H,EAAKqc,WACrCpL,WAAWjR,EAAKqc,WAChBrc,EAAKqc,UAGX,OAAOzY,CACT,CAAO,GAAIA,aAAgBzH,MAEzB,OAAOyH,EAAK8R,QAAO,CAAC8E,EAAM8B,KACxB9B,EAAK8B,GAAQ3Y,KAAKC,KAAK0Y,GAChB9B,IACN,CAAE,GACA,GAAoB,iBAAT5W,GAAqBA,EAAKH,cAAgBlH,OAE1D,IAAK6K,KAAOxD,EAAMD,KAAKC,KAAKwD,EAAKxD,EAAKwD,SACjC,GAAY,OAARA,EAETzD,KAAK3D,KAAKI,gBAAgBwD,OACrB,IAAW,MAAPwD,EAGT,OAAc,OADdA,EAAMzD,KAAK3D,KAAKuc,aAAa3Y,IAEzBjE,GAASiE,GACT0B,EAASuC,KAAKT,GACZ6J,WAAW7J,GACXA,EAQa,iBALnBA,EAAMiS,GAAM3D,QAAO,CAAC8G,EAAMC,IACjBA,EAAK7Y,EAAM4Y,EAAM7Y,OACvByD,IAIDA,EAAM,IAAIoR,GAAUpR,GACXgS,GAAgB7Z,IAAIqE,IAAS+E,GAAM+T,QAAQtV,GAEpDA,EAAM,IAAIuB,GAAMvB,GACPA,EAAI3D,cAAgBtH,QAE7BiL,EAAM,IAAIkR,GAASlR,IAIR,YAATxD,EAEED,KAAKgZ,SACPhZ,KAAKgZ,QAAQvV,GAID,iBAAP5F,EACHmC,KAAK3D,KAAK4c,eAAepb,EAAIoC,EAAMwD,EAAIpB,YACvCrC,KAAK3D,KAAKC,aAAa2D,EAAMwD,EAAIpB,aAInCrC,KAAKkZ,SAAqB,cAATjZ,GAAiC,MAATA,GAC3CD,KAAKkZ,SAET,CAEA,OAAOlZ,IACT,ECuQoBmZ,KPtVb,SAActH,GACnB,OAAOD,GAASC,EAAO7R,KAAK3D,KAC9B,EOoV0B+c,QPlVnB,SAAiBvH,GACtB,OAAOjT,EAAMoB,KAAK3D,KAAK6B,cAAc2T,GACvC,IOiVA7S,EAAS2W,IAAK,OCpVC,MAAMvI,gBAAgBuI,IACnC7V,YAAYzD,EAAM4V,GAChBV,MAAMlV,EAAM4V,GAGZjS,KAAKqZ,IAAM,GAGXrZ,KAAK3D,KAAKwC,SAAWmB,MAEjB3D,EAAKid,aAAa,eAAiBjd,EAAKid,aAAa,gBAEvDtZ,KAAKuZ,QACHhd,KAAKkI,MAAMpI,EAAKuc,aAAa,gBAC3Brc,KAAKkI,MAAMpI,EAAKuc,aAAa,gBAC7B,CAAA,EAGR,CAGAY,OAAOpe,EAAGC,GACR,OAAO2E,KAAK6L,GAAGzQ,GAAG0Q,GAAGzQ,EACvB,CAGAwQ,GAAGzQ,GACD,OAAY,MAALA,EACH4E,KAAK5E,IAAM4E,KAAKvF,QAAU,EAC1BuF,KAAK5E,EAAEA,EAAI4E,KAAKvF,QAAU,EAChC,CAGAqR,GAAGzQ,GACD,OAAY,MAALA,EACH2E,KAAK3E,IAAM2E,KAAKtF,SAAW,EAC3BsF,KAAK3E,EAAEA,EAAI2E,KAAKtF,SAAW,EACjC,CAGA+e,OACE,MAAM9b,EAAOqC,KAAKrC,OAClB,OAAOA,GAAQA,EAAK8b,MACtB,CAGAC,MAAMte,EAAGC,GACP,OAAO2E,KAAKiM,GAAG7Q,GAAG8Q,GAAG7Q,EACvB,CAGA4Q,GAAG7Q,EAAI,GACL,OAAO4E,KAAK5E,EAAE,IAAIyZ,GAAUzZ,GAAGka,KAAKtV,KAAK5E,KAC3C,CAGA8Q,GAAG7Q,EAAI,GACL,OAAO2E,KAAK3E,EAAE,IAAIwZ,GAAUxZ,GAAGia,KAAKtV,KAAK3E,KAC3C,CAEAiX,iBACE,OAAOtS,IACT,CAGAtF,OAAOA,GACL,OAAOsF,KAAKC,KAAK,SAAUvF,EAC7B,CAGAif,KAAKve,EAAGC,GACN,OAAO2E,KAAK5E,EAAEA,GAAGC,EAAEA,EACrB,CAGAue,QAAQC,EAAQ7Z,KAAKrC,QACnB,MAAMmc,EAA8B,iBAAVD,EACrBC,IACHD,EAAQ9b,EAAa8b,IAEvB,MAAMD,EAAU,IAAIvI,GACpB,IAAIlR,EAASH,KAEb,MACGG,EAASA,EAAOA,WACjBA,EAAO9D,OAASU,EAAQE,UACJ,uBAApBkD,EAAOtE,WAEP+d,EAAQ3gB,KAAKkH,GAER2Z,GAAc3Z,EAAO9D,OAASwd,EAAMxd,SAGrCyd,IAAc3Z,EAAO2W,QAAQ+C,KAGjC,GAAI1Z,EAAO9D,OAAS2D,KAAKrC,OAAOtB,KAE9B,OAAO,KAIX,OAAOud,CACT,CAGAvY,UAAUpB,GAER,KADAA,EAAOD,KAAKC,KAAKA,IACN,OAAO,KAElB,MAAM1H,GAAK0H,EAAO,IAAImV,MAAM/T,GAC5B,OAAO9I,EAAIwF,EAAaxF,EAAE,IAAM,IAClC,CAGAoF,OACE,MAAM+E,EAAI1C,KAAKG,OAAOhB,EAASxB,IAC/B,OAAO+E,GAAKA,EAAE/E,MAChB,CAGA4b,QAAQze,GAEN,OADAkF,KAAKqZ,IAAMve,EACJkF,IACT,CAGA+O,KAAKtU,EAAOC,GACV,MAAMgI,EAAInI,EAAiByF,KAAMvF,EAAOC,GAExC,OAAOsF,KAAKvF,MAAM,IAAIoa,GAAUnS,EAAEjI,QAAQC,OAAO,IAAIma,GAAUnS,EAAEhI,QACnE,CAGAD,MAAMA,GACJ,OAAOuF,KAAKC,KAAK,QAASxF,EAC5B,CAGAqB,iBAEE,OADAA,EAAekE,KAAMA,KAAKqZ,KACnB9H,MAAMzV,gBACf,CAGAV,EAAEA,GACA,OAAO4E,KAAKC,KAAK,IAAK7E,EACxB,CAGAC,EAAEA,GACA,OAAO2E,KAAKC,KAAK,IAAK5E,EACxB,EAGFoE,EAAO2N,QAAS,CACdxS,KV9BK,WAEL,MAoBMD,EAAM2V,GAAOtQ,MApBF3D,GAASA,EAAK0d,YAIhBlW,IACb,IACE,MAAMuF,EAAQvF,EAAGuF,QAAQgG,MAAMP,KAASlS,KAAKwH,OACvCxJ,EAAMyO,EAAM/M,KAAK0d,UAEvB,OADA3Q,EAAM1I,SACC/F,CACR,CAAC,MAAO+J,GAEP,MAAM,IAAImB,MACR,4BACEhC,EAAGxH,KAAKR,8BACY6I,EAAErC,aAE5B,KAMF,OAFa,IAAIiN,GAAI3U,EAGvB,EUKEqf,KVHK,SAAcnW,GACnB,MASMlJ,EAAM2V,GAAOtQ,MATF3D,GAASA,EAAK4d,0BAChBpW,IAGb,MAAM,IAAIgC,MACR,4BAA4BhC,EAAGxH,KAAKR,4BACrC,IAIGme,EAAO,IAAI1K,GAAI3U,GAGrB,OAAIkJ,EACKmW,EAAKzQ,UAAU1F,EAAGqW,YAAYzM,YAKhCuM,EAAKzK,WACd,EUjBE4K,OVoBK,SAAgB/e,EAAGC,GACxB,MAAMV,EAAMqF,KAAKpF,OAEjB,OACEQ,EAAIT,EAAIS,GAAKC,EAAIV,EAAIU,GAAKD,EAAIT,EAAIS,EAAIT,EAAIF,OAASY,EAAIV,EAAIU,EAAIV,EAAID,MAEvE,EUzBEmW,Mb1HK,SAAezV,EAAGC,GACvB,OAAO,IAAI8N,GAAM/N,EAAGC,GAAGmO,WAAWxJ,KAAKka,YAAYzM,WACrD,EayHE2M,IZoVK,WACL,OAAO,IAAI3Q,GAAOzJ,KAAK3D,KAAKge,SAC9B,EYrVEH,UZuVK,WACL,IAKE,GAA2B,mBAAhBla,KAAKsa,SAA0Bta,KAAKsa,SAAU,CACvD,MAAMC,EAAOva,KAAKua,KAAK,EAAG,GACpBhiB,EAAIgiB,EAAKle,KAAKme,eAEpB,OADAD,EAAK7Z,SACE,IAAI+I,GAAOlR,EACpB,CACA,OAAO,IAAIkR,GAAOzJ,KAAK3D,KAAKme,eAC7B,CAAC,MAAO9V,GAIP,OAHA+V,QAAQC,KACN,gCAAgC1a,KAAK3D,KAAKR,sCAErC,IAAI4N,EACb,CACF,IYvWAzK,EAASoO,QAAS,WC7KlB,MAAMuN,GAAQ,CACZnG,OAAQ,CACN,QACA,QACA,UACA,UACA,WACA,aACA,YACA,cAEFD,KAAM,CAAC,QAAS,UAAW,QAC3BqG,OAAQ,SAAUhY,EAAG0B,GACnB,MAAa,UAANA,EAAgB1B,EAAIA,EAAI,IAAM0B,CACvC,GAID,CAAC,OAAQ,UAAUR,SAAQ,SAAUvL,GACpC,MAAMsiB,EAAY,CAAA,EAClB,IAAIxhB,EAEJwhB,EAAUtiB,GAAK,SAAUuC,GACvB,QAAiB,IAANA,EACT,OAAOkF,KAAKC,KAAK1H,GAEnB,GACe,iBAANuC,GACPA,aAAakK,IACbA,GAAMvD,MAAM3G,IACZA,aAAasS,QAEbpN,KAAKC,KAAK1H,EAAGuC,QAGb,IAAKzB,EAAIshB,GAAMpiB,GAAGgB,OAAS,EAAGF,GAAK,EAAGA,IACd,MAAlByB,EAAE6f,GAAMpiB,GAAGc,KACb2G,KAAKC,KAAK0a,GAAMC,OAAOriB,EAAGoiB,GAAMpiB,GAAGc,IAAKyB,EAAE6f,GAAMpiB,GAAGc,KAKzD,OAAO2G,MAGT3H,EAAgB,CAAC,UAAW,UAAWwiB,EACzC,IAEAxiB,EAAgB,CAAC,UAAW,UAAW,CAErC0T,OAAQ,SAAU+O,EAAKzV,EAAGhC,EAAG1J,EAAG+K,EAAGiF,GAEjC,OAAW,MAAPmR,EACK,IAAIrR,GAAOzJ,MAIbA,KAAKC,KAAK,YAAa,IAAIwJ,GAAOqR,EAAKzV,EAAGhC,EAAG1J,EAAG+K,EAAGiF,GAC3D,EAGDiB,OAAQ,SAAUmQ,EAAOlP,EAAIC,GAC3B,OAAO9L,KAAKuJ,UAAU,CAAEqB,OAAQmQ,EAAO/f,GAAI6Q,EAAI3Q,GAAI4Q,IAAM,EAC1D,EAGD1B,KAAM,SAAUhP,EAAGC,EAAGwQ,EAAIC,GACxB,OAA4B,IAArBnI,UAAUpK,QAAqC,IAArBoK,UAAUpK,OACvCyG,KAAKuJ,UAAU,CAAEa,KAAMhP,EAAGJ,GAAIK,EAAGH,GAAI2Q,IAAM,GAC3C7L,KAAKuJ,UAAU,CAAEa,KAAM,CAAChP,EAAGC,GAAIL,GAAI6Q,EAAI3Q,GAAI4Q,IAAM,EACtD,EAEDpB,MAAO,SAAUiC,EAAKd,EAAIC,GACxB,OAAO9L,KAAKuJ,UAAU,CAAEmB,MAAOiC,EAAK3R,GAAI6Q,EAAI3Q,GAAI4Q,IAAM,EACvD,EAGDtB,MAAO,SAAUpP,EAAGC,EAAGwQ,EAAIC,GACzB,OAA4B,IAArBnI,UAAUpK,QAAqC,IAArBoK,UAAUpK,OACvCyG,KAAKuJ,UAAU,CAAEiB,MAAOpP,EAAGJ,GAAIK,EAAGH,GAAI2Q,IAAM,GAC5C7L,KAAKuJ,UAAU,CAAEiB,MAAO,CAACpP,EAAGC,GAAIL,GAAI6Q,EAAI3Q,GAAI4Q,IAAM,EACvD,EAGDX,UAAW,SAAU/P,EAAGC,GACtB,OAAO2E,KAAKuJ,UAAU,CAAE4B,UAAW,CAAC/P,EAAGC,KAAM,EAC9C,EAGDmQ,SAAU,SAAUpQ,EAAGC,GACrB,OAAO2E,KAAKuJ,UAAU,CAAEiC,SAAU,CAACpQ,EAAGC,KAAM,EAC7C,EAGD2O,KAAM,SAAUgR,EAAY,OAAQjgB,EAAS,UAM3C,OALyC,IAArC,aAAakI,QAAQ+X,KACvBjgB,EAASigB,EACTA,EAAY,QAGPhb,KAAKuJ,UAAU,CAAES,KAAMgR,EAAWjgB,OAAQA,IAAU,EAC5D,EAGD0Z,QAAS,SAAUO,GACjB,OAAOhV,KAAKC,KAAK,UAAW+U,EAC9B,IAGF3c,EAAgB,SAAU,CAExB4iB,OAAQ,SAAU7f,EAAGC,EAAID,GAEvB,MAAgB,oBADF4E,KAAKkb,UAAYlb,MAAM8T,KAEjC9T,KAAKC,KAAK,IAAK,IAAI4U,GAAUzZ,IAC7B4E,KAAKyL,GAAGrQ,GAAGuQ,GAAGtQ,EACpB,IAGFhD,EAAgB,OAAQ,CAEtBkB,OAAQ,WACN,OAAOyG,KAAK3D,KAAK8e,gBAClB,EAEDC,QAAS,SAAU7hB,GACjB,OAAO,IAAI4P,GAAMnJ,KAAK3D,KAAKgf,iBAAiB9hB,GAC9C,IAGFlB,EAAgB,CAAC,UAAW,UAAW,CAErCijB,KAAM,SAAUhX,EAAGC,GACjB,GAAiB,iBAAND,EAAgB,CACzB,IAAKC,KAAKD,EAAGtE,KAAKsb,KAAK/W,EAAGD,EAAEC,IAC5B,OAAOvE,IACT,CAEA,MAAa,YAANsE,EACHtE,KAAKgZ,QAAQzU,GACP,WAAND,EACEtE,KAAKC,KAAK,cAAesE,GACnB,SAAND,GACQ,WAANA,GACM,WAANA,GACM,YAANA,GACM,YAANA,GACM,UAANA,EACAtE,KAAKC,KAAK,QAAUqE,EAAGC,GACvBvE,KAAKC,KAAKqE,EAAGC,EACvB,IAyCFlM,EAAgB,UArCA,CACd,QACA,WACA,YACA,UACA,YACA,WACA,YACA,aACA,aACA,aACA,YACA,aACA,WACA,cACA,cACA,QACA,cACA,cACA,YACA,eACA,iBACA0Z,QAAO,SAAU8E,EAAM5D,GAYvB,OADA4D,EAAK5D,GATM,SAAUtJ,GAMnB,OALU,OAANA,EACF3J,KAAKoT,IAAIH,GAETjT,KAAK0S,GAAGO,EAAOtJ,GAEV3J,MAIF6W,CACT,GAAG,CAAE,ICzHLxe,EAAgB,UAAW,CACzBkjB,YAvEK,WACL,OAAOvb,KAAKC,KAAK,YAAa,KAChC,EAsEEoN,UAnEK,WACL,MAAMtB,GAAU/L,KAAKC,KAAK,cAAgB,IAEvC8C,MAAMzB,GACNhH,MAAM,GAAI,GACVpB,KAAI,SAAUsiB,GAEb,MAAMC,EAAKD,EAAI1Y,OAAOC,MAAM,KAC5B,MAAO,CACL0Y,EAAG,GACHA,EAAG,GAAG1Y,MAAMlB,IAAW3I,KAAI,SAAUsiB,GACnC,OAAOlO,WAAWkO,EACnB,IAEJ,IACAE,UAEA3J,QAAO,SAAUhG,EAAQxC,GACxB,MAAqB,WAAjBA,EAAU,GACLwC,EAAOkC,UAAUxE,GAAO0D,UAAU5D,EAAU,KAE9CwC,EAAOxC,EAAU,IAAIxJ,MAAMgM,EAAQxC,EAAU,GACtD,GAAG,IAAIE,IAET,OAAOsC,CACT,EA2CE4P,SAxCK,SAAkBxb,EAAQ9G,GAC/B,GAAI2G,OAASG,EAAQ,OAAOH,KAE5B,GAAIrE,EAAcqE,KAAK3D,MAAO,OAAO2D,KAAKoP,MAAMjP,EAAQ9G,GAExD,MAAM+gB,EAAMpa,KAAKka,YACX0B,EAAOzb,EAAO+Z,YAAY1M,UAIhC,OAFAxN,KAAKoP,MAAMjP,EAAQ9G,GAAGkiB,cAAchS,UAAUqS,EAAKzN,SAASiM,IAErDpa,IACT,EA8BE6b,OA3BK,SAAgBxiB,GACrB,OAAO2G,KAAK2b,SAAS3b,KAAKrC,OAAQtE,EACpC,EA0BEkQ,UAvBK,SAAmBzO,EAAG0Q,GAE3B,GAAS,MAAL1Q,GAA0B,iBAANA,EAAgB,CACtC,MAAMghB,EAAa,IAAIrS,GAAOzJ,MAAMqM,YACpC,OAAY,MAALvR,EAAYghB,EAAaA,EAAWhhB,EAC7C,CAEK2O,GAAOC,aAAa5O,KAEvBA,EAAI,IAAKA,EAAGC,OAAQF,EAAUC,EAAGkF,QAInC,MACMxG,EAAS,IAAIiQ,IADgB,IAAb+B,EAAoBxL,KAAOwL,IAAY,GACpBjC,UAAUzO,GACnD,OAAOkF,KAAKC,KAAK,YAAazG,EAChC,ICvEe,MAAMuiB,kBAAkB3O,QACrC4O,UAOE,OANAhc,KAAKwR,MAAK,WACR,GAAIxR,gBAAgB+b,UAClB,OAAO/b,KAAKgc,UAAUC,SAE1B,IAEOjc,IACT,CAEAic,QAAQ9b,EAASH,KAAKG,SAAUE,EAAQF,EAAOE,MAAML,OASnD,OAPAK,GAAmB,IAAXA,EAAeF,EAAOZ,WAAWhG,OAAS8G,EAElDL,KAAKwR,MAAK,SAAUnY,EAAGkG,GAErB,OAAOA,EAASA,EAAShG,OAASF,EAAI,GAAGsiB,SAASxb,EAAQE,EAC5D,IAEOL,KAAKU,QACd,EAGF1B,EAAS+c,UAAW,aCxBL,MAAMG,aAAaH,UAChCjc,YAAYzD,EAAM4V,EAAQ5V,GACxBkV,MAAM/S,EAAU,OAAQnC,GAAO4V,EACjC,CAEA+J,UACE,OAAOhc,IACT,CAEAic,UACE,OAAOjc,IACT,EAGFhB,EAASkd,KAAM,QCdA,MAAMC,cAAc/O,SCA5B,SAAS3B,GAAGA,GACjB,OAAOzL,KAAKC,KAAK,KAAMwL,EACzB,CAGO,SAASE,GAAGA,GACjB,OAAO3L,KAAKC,KAAK,KAAM0L,EACzB,CAGO,SAASvQ,GAAEA,GAChB,OAAY,MAALA,EAAY4E,KAAK6L,KAAO7L,KAAKyL,KAAOzL,KAAK6L,GAAGzQ,EAAI4E,KAAKyL,KAC9D,CAGO,SAASpQ,GAAEA,GAChB,OAAY,MAALA,EAAY2E,KAAK8L,KAAO9L,KAAK2L,KAAO3L,KAAK8L,GAAGzQ,EAAI2E,KAAK2L,KAC9D,CAGO,SAASE,GAAGzQ,GACjB,OAAO4E,KAAKC,KAAK,KAAM7E,EACzB,CAGO,SAAS0Q,GAAGzQ,GACjB,OAAO2E,KAAKC,KAAK,KAAM5E,EACzB,CAGO,SAASZ,GAAMA,GACpB,OAAgB,MAATA,EAA4B,EAAZuF,KAAKyL,KAAWzL,KAAKyL,GAAG,IAAIoJ,GAAUpa,GAAOwa,OAAO,GAC7E,CAGO,SAASva,GAAOA,GACrB,OAAiB,MAAVA,EACS,EAAZsF,KAAK2L,KACL3L,KAAK2L,GAAG,IAAIkJ,GAAUna,GAAQua,OAAO,GAC3C,CDrCAjW,EAASmd,MAAO,sFEOD,MAAMC,gBAAgBD,MACnCrc,YAAYzD,EAAM4V,EAAQ5V,GACxBkV,MAAM/S,EAAU,UAAWnC,GAAO4V,EACpC,CAEAlD,KAAKtU,EAAOC,GACV,MAAMgI,EAAInI,EAAiByF,KAAMvF,EAAOC,GAExC,OAAOsF,KAAKyL,GAAG,IAAIoJ,GAAUnS,EAAEjI,OAAOwa,OAAO,IAAItJ,GAC/C,IAAIkJ,GAAUnS,EAAEhI,QAAQua,OAAO,GAEnC,EAGFxV,EAAO2c,QAASC,IAEhBhkB,EAAgB,YAAa,CAE3BikB,QAAS3c,GAAkB,SAAUlF,EAAQ,EAAGC,EAASD,GACvD,OAAOuF,KAAKgW,IAAI,IAAIoG,SAAWrN,KAAKtU,EAAOC,GAAQif,KAAK,EAAG,QAI/D3a,EAASod,QAAS,WC/BlB,MAAMtd,WAAiB6W,IACrB7V,YAAYzD,EAAOU,EAAQE,SAASsb,0BAClChH,MAAMlV,EACR,CAGAua,IAAIqB,EAASC,EAAUra,GASrB,GARuB,kBAAZoa,IACTpa,EAAKqa,EACLA,EAAWD,EACXA,EAAU,MAKG,MAAXA,GAAsC,mBAAZA,EAAwB,CACpD,MAAM9Z,EAAU,IAAIwX,IAAI/X,EAAO,UAAWC,IAG1C,OAFAM,EAAQsC,IAAIT,KAAK3D,KAAKka,WAAU,IAEzBpY,EAAQyY,KAAI,EAAO/Y,EAC5B,CAGA,OAAO0T,MAAMqF,IAAIqB,GAAS,EAAOpa,EACnC,EC1BK,SAAS0e,GAAKnhB,EAAGC,GACtB,MAAwC,oBAAhC2E,KAAKkb,UAAYlb,MAAM8T,KAC3B9T,KAAKC,KAAK,CAAEuc,GAAI,IAAI3H,GAAUzZ,GAAIqhB,GAAI,IAAI5H,GAAUxZ,KACpD2E,KAAKC,KAAK,CAAEyc,GAAI,IAAI7H,GAAUzZ,GAAIuhB,GAAI,IAAI9H,GAAUxZ,IAC1D,CAEO,SAASuhB,GAAGxhB,EAAGC,GACpB,MAAwC,oBAAhC2E,KAAKkb,UAAYlb,MAAM8T,KAC3B9T,KAAKC,KAAK,CAAE4L,GAAI,IAAIgJ,GAAUzZ,GAAI0Q,GAAI,IAAI+I,GAAUxZ,KACpD2E,KAAKC,KAAK,CAAE4P,GAAI,IAAIgF,GAAUzZ,GAAI0U,GAAI,IAAI+E,GAAUxZ,IAC1D,CDmBA2D,EAASF,GAAU,gBVdcc,qCYLlB,MAAMid,iBAAiBd,UACpCjc,YAAYgU,EAAM7B,GAChBV,MACE/S,EAAUsV,EAAO,WAA4B,iBAATA,EAAoB,KAAOA,GAC/D7B,EAEJ,CAGAhS,KAAKqE,EAAGe,EAAGhC,GAET,MADU,cAANiB,IAAmBA,EAAI,qBACpBiN,MAAMtR,KAAKqE,EAAGe,EAAGhC,EAC1B,CAEAzI,OACE,OAAO,IAAI0U,EACb,CAEAwN,UACE,OAAOlL,GAAS,cAAgB5R,KAAKR,KAAO,IAC9C,CAGA6C,WACE,OAAOrC,KAAK+c,KACd,CAGAC,OAAO5jB,GASL,OAPA4G,KAAKiW,QAGgB,mBAAV7c,GACTA,EAAMmU,KAAKvN,KAAMA,MAGZA,IACT,CAGA+c,MACE,MAAO,QAAU/c,KAAKR,KAAO,GAC/B,EAGFC,EAAOod,SAAUI,IAEjB5kB,EAAgB,CACd0jB,UAAW,CAETmB,YAAYrd,GACV,OAAOG,KAAKyZ,OAAOyD,YAAYrd,EACjC,GAGFqc,KAAM,CACJgB,SAAUvd,GAAkB,SAAUmU,EAAM1a,GAC1C,OAAO4G,KAAKgW,IAAI,IAAI6G,SAAS/I,IAAOkJ,OAAO5jB,SAKjD4F,EAAS6d,SAAU,YCrEJ,MAAMM,gBAAgBpB,UAEnCjc,YAAYzD,EAAM4V,EAAQ5V,GACxBkV,MAAM/S,EAAU,UAAWnC,GAAO4V,EACpC,CAGAhS,KAAKqE,EAAGe,EAAGhC,GAET,MADU,cAANiB,IAAmBA,EAAI,oBACpBiN,MAAMtR,KAAKqE,EAAGe,EAAGhC,EAC1B,CAEAzI,OACE,OAAO,IAAI0U,EACb,CAEAwN,UACE,OAAOlL,GAAS,cAAgB5R,KAAKR,KAAO,IAC9C,CAGA6C,WACE,OAAOrC,KAAK+c,KACd,CAGAC,OAAO5jB,GASL,OAPA4G,KAAKiW,QAGgB,mBAAV7c,GACTA,EAAMmU,KAAKvN,KAAMA,MAGZA,IACT,CAGA+c,MACE,MAAO,QAAU/c,KAAKR,KAAO,GAC/B,EAGFnH,EAAgB,CACd0jB,UAAW,CAETqB,WAAWvd,GACT,OAAOG,KAAKyZ,OAAO2D,WAAWvd,EAChC,GAEFqc,KAAM,CACJkB,QAASzd,GAAkB,SAAUlF,EAAOC,EAAQtB,GAClD,OAAO4G,KAAKgW,IAAI,IAAImH,SAAWH,OAAO5jB,GAAO6G,KAAK,CAChD7E,EAAG,EACHC,EAAG,EACHZ,MAAOA,EACPC,OAAQA,EACR2iB,aAAc,yBAMtBre,EAASme,QAAS,WC5DH,MAAMG,cAAcnB,MACjCrc,YAAYzD,EAAM4V,EAAQ5V,GACxBkV,MAAM/S,EAAU,QAASnC,GAAO4V,EAClC,CAGAsL,KAAKR,EAAKS,GACR,IAAKT,EAAK,OAAO/c,KAEjB,MAAMyd,EAAM,IAAI1gB,EAAQC,OAAOsgB,MAgC/B,OA9BA5K,GACE+K,EACA,QACA,SAAU/Y,GACR,MAAMhC,EAAI1C,KAAKG,OAAOgd,SAGD,IAAjBnd,KAAKvF,SAAmC,IAAlBuF,KAAKtF,UAC7BsF,KAAK+O,KAAK0O,EAAIhjB,MAAOgjB,EAAI/iB,QAGvBgI,aAAaya,SAEG,IAAdza,EAAEjI,SAAgC,IAAfiI,EAAEhI,UACvBgI,EAAEqM,KAAK/O,KAAKvF,QAASuF,KAAKtF,UAIN,mBAAb8iB,GACTA,EAASjQ,KAAKvN,KAAM0E,EAEvB,GACD1E,MAGF0S,GAAG+K,EAAK,cAAc,WAEpBrK,GAAIqK,EACN,IAEOzd,KAAKC,KAAK,OAASwd,EAAIC,IAAMX,EAAMjgB,EAC5C,EdnC+B8C,GcsChB,SAAUK,EAAMwD,EAAK2U,GAiBpC,MAfa,SAATnY,GAA4B,WAATA,GACjB2B,GAAQsC,KAAKT,KACfA,EAAM2U,EAAMza,OAAO8b,OAAOkE,MAAMla,IAIhCA,aAAe6Z,QACjB7Z,EAAM2U,EACHza,OACA8b,OACA2D,QAAQ,EAAG,GAAIA,IACdA,EAAQ3c,IAAIgD,EAAI,KAIfA,CACT,EdvDEiS,GAAMzc,KAAK2G,IcyDbvH,EAAgB,CACd0jB,UAAW,CAET4B,MAAOhe,GAAkB,SAAU2J,EAAQkU,GACzC,OAAOxd,KAAKgW,IAAI,IAAIsH,OAASvO,KAAK,EAAG,GAAGwO,KAAKjU,EAAQkU,SAK3Dxe,EAASse,MAAO,SC/ED,MAAMM,WAAmBjJ,GAEtC/Z,OACE,IAAIijB,GAAQ3N,IACR4N,GAAQ5N,IACR6N,EAAO7N,IACP8N,EAAO9N,IAOX,OANAlQ,KAAK8D,SAAQ,SAAUD,GACrBga,EAAOjkB,KAAKuI,IAAI0B,EAAG,GAAIga,GACvBC,EAAOlkB,KAAKuI,IAAI0B,EAAG,GAAIia,GACvBC,EAAOnkB,KAAKwI,IAAIyB,EAAG,GAAIka,GACvBC,EAAOpkB,KAAKwI,IAAIyB,EAAG,GAAIma,EACzB,IACO,IAAI1O,GAAIyO,EAAMC,EAAMH,EAAOE,EAAMD,EAAOE,EACjD,CAGArE,KAAKve,EAAGC,GACN,MAAMV,EAAMqF,KAAKpF,OAOjB,GAJAQ,GAAKT,EAAIS,EACTC,GAAKV,EAAIU,GAGJ8Z,MAAM/Z,KAAO+Z,MAAM9Z,GACtB,IAAK,IAAIhC,EAAI2G,KAAKzG,OAAS,EAAGF,GAAK,EAAGA,IACpC2G,KAAK3G,GAAK,CAAC2G,KAAK3G,GAAG,GAAK+B,EAAG4E,KAAK3G,GAAG,GAAKgC,GAI5C,OAAO2E,IACT,CAGAyE,MAAMtL,EAAQ,CAAC,EAAG,IAChB,MAAM8kB,EAAS,IAIb9kB,EADEA,aAAiBX,MACXA,MAAM0G,UAAUwS,OAAO3R,MAAM,GAAI5G,GAIjCA,EAAM2J,OAAOC,MAAMlB,IAAW3I,IAAIoU,aAKlC/T,OAAS,GAAM,GAAGJ,EAAM+kB,MAGlC,IAAK,IAAI7kB,EAAI,EAAGmf,EAAMrf,EAAMI,OAAQF,EAAImf,EAAKnf,GAAQ,EACnD4kB,EAAOhlB,KAAK,CAACE,EAAME,GAAIF,EAAME,EAAI,KAGnC,OAAO4kB,CACT,CAGAlP,KAAKtU,EAAOC,GACV,IAAIrB,EACJ,MAAMsB,EAAMqF,KAAKpF,OAGjB,IAAKvB,EAAI2G,KAAKzG,OAAS,EAAGF,GAAK,EAAGA,IAC5BsB,EAAIF,QACNuF,KAAK3G,GAAG,IAAO2G,KAAK3G,GAAG,GAAKsB,EAAIS,GAAKX,EAASE,EAAIF,MAAQE,EAAIS,GAC5DT,EAAID,SACNsF,KAAK3G,GAAG,IAAO2G,KAAK3G,GAAG,GAAKsB,EAAIU,GAAKX,EAAUC,EAAID,OAASC,EAAIU,GAGpE,OAAO2E,IACT,CAGAme,SACE,MAAO,CACLzB,GAAI1c,KAAK,GAAG,GACZ2c,GAAI3c,KAAK,GAAG,GACZ6P,GAAI7P,KAAK,GAAG,GACZ8P,GAAI9P,KAAK,GAAG,GAEhB,CAGAqC,WACE,MAAMlJ,EAAQ,GAEd,IAAK,IAAIE,EAAI,EAAGC,EAAK0G,KAAKzG,OAAQF,EAAIC,EAAID,IACxCF,EAAMF,KAAK+G,KAAK3G,GAAG8J,KAAK,MAG1B,OAAOhK,EAAMgK,KAAK,IACpB,CAEAoG,UAAUhR,GACR,OAAOyH,KAAKoJ,QAAQI,WAAWjR,EACjC,CAGAiR,WAAWjR,GACJkR,GAAOC,aAAanR,KACvBA,EAAI,IAAIkR,GAAOlR,IAGjB,IAAK,IAAIc,EAAI2G,KAAKzG,OAAQF,KAAO,CAE/B,MAAO+B,EAAGC,GAAK2E,KAAK3G,GACpB2G,KAAK3G,GAAG,GAAKd,EAAE+L,EAAIlJ,EAAI7C,EAAE8K,EAAIhI,EAAI9C,EAAEmM,EACnC1E,KAAK3G,GAAG,GAAKd,EAAE8M,EAAIjK,EAAI7C,EAAEoB,EAAI0B,EAAI9C,EAAEoR,CACrC,CAEA,OAAO3J,IACT,oCCrHwB4d,UAmBnB,SAAgBljB,GACrB,MAAM2K,EAAIrF,KAAKpF,OACf,OAAiB,MAAVF,EAAiB2K,EAAE3K,OAASsF,KAAK+O,KAAK1J,EAAE5K,MAAOC,EACxD,QATO,SAAeD,GACpB,MAAM4K,EAAIrF,KAAKpF,OACf,OAAgB,MAATH,EAAgB4K,EAAE5K,MAAQuF,KAAK+O,KAAKtU,EAAO4K,EAAE3K,OACtD,IAbO,SAAWU,GAChB,OAAY,MAALA,EAAY4E,KAAKpF,OAAOQ,EAAI4E,KAAK2Z,KAAKve,EAAG4E,KAAKpF,OAAOS,EAC9D,IAGO,SAAWA,GAChB,OAAY,MAALA,EAAY2E,KAAKpF,OAAOS,EAAI2E,KAAK2Z,KAAK3Z,KAAKpF,OAAOQ,EAAGC,EAC9D,GCAe,MAAM+iB,aAAajC,MAEhCrc,YAAYzD,EAAM4V,EAAQ5V,GACxBkV,MAAM/S,EAAU,OAAQnC,GAAO4V,EACjC,CAGA9Y,QACE,OAAO,IAAIykB,GAAW,CACpB,CAAC5d,KAAKC,KAAK,MAAOD,KAAKC,KAAK,OAC5B,CAACD,KAAKC,KAAK,MAAOD,KAAKC,KAAK,QAEhC,CAGA0Z,KAAKve,EAAGC,GACN,OAAO2E,KAAKC,KAAKD,KAAK7G,QAAQwgB,KAAKve,EAAGC,GAAG8iB,SAC3C,CAGAE,KAAK3B,EAAIC,EAAI9M,EAAIC,GACf,OAAU,MAAN4M,EACK1c,KAAK7G,SAEZujB,OADuB,IAAPC,EACX,CAAED,KAAIC,KAAI9M,KAAIC,MAEd,IAAI8N,GAAWlB,GAAIyB,SAGnBne,KAAKC,KAAKyc,GACnB,CAGA3N,KAAKtU,EAAOC,GACV,MAAMgI,EAAInI,EAAiByF,KAAMvF,EAAOC,GACxC,OAAOsF,KAAKC,KAAKD,KAAK7G,QAAQ4V,KAAKrM,EAAEjI,MAAOiI,EAAEhI,QAAQyjB,SACxD,EAGF1e,EAAO2e,KAAME,IAEbjmB,EAAgB,CACd0jB,UAAW,CAETwC,KAAM5e,GAAkB,YAAaE,GAGnC,OAAOue,KAAKlf,UAAUmf,KAAKte,MACzBC,KAAKgW,IAAI,IAAIoI,MACF,MAAXve,EAAK,GAAaA,EAAO,CAAC,EAAG,EAAG,EAAG,UAM3Cb,EAASof,KAAM,QC/DA,MAAMI,eAAezC,UAElCjc,YAAYzD,EAAM4V,EAAQ5V,GACxBkV,MAAM/S,EAAU,SAAUnC,GAAO4V,EACnC,CAGAvX,OAAOA,GACL,OAAOsF,KAAKC,KAAK,eAAgBvF,EACnC,CAEA+jB,OAAOA,GACL,OAAOze,KAAKC,KAAK,SAAUwe,EAC7B,CAGAC,IAAItjB,EAAGC,GACL,OAAO2E,KAAKC,KAAK,OAAQ7E,GAAG6E,KAAK,OAAQ5E,EAC3C,CAGAgH,WACE,MAAO,QAAUrC,KAAKR,KAAO,GAC/B,CAGAwd,OAAO5jB,GASL,OAPA4G,KAAKiW,QAGgB,mBAAV7c,GACTA,EAAMmU,KAAKvN,KAAMA,MAGZA,IACT,CAGAvF,MAAMA,GACJ,OAAOuF,KAAKC,KAAK,cAAexF,EAClC,ECpCF,SAASkkB,GAAiB/Z,EAAG+E,GAC3B,OAAO,SAAUpF,GACf,OAAS,MAALA,EAAkBvE,KAAK4E,IAC3B5E,KAAK4E,GAAKL,EACNoF,GAAGA,EAAE4D,KAAKvN,MACPA,MAEX,CDgCA3H,EAAgB,CACd0jB,UAAW,CACT6C,UAAU/e,GAER,OAAOG,KAAKyZ,OAAOmF,UAAU/e,EAC/B,GAEFqc,KAAM,CAEJ0C,OAAQjf,GAAkB,SAAUlF,EAAOC,EAAQtB,GAEjD,OAAO4G,KAAKgW,IAAI,IAAIwI,QACjBzP,KAAKtU,EAAOC,GACZgkB,IAAIjkB,EAAQ,EAAGC,EAAS,GACxBgW,QAAQ,EAAG,EAAGjW,EAAOC,GACrBuF,KAAK,SAAU,QACf+c,OAAO5jB,OAGdwlB,OAAQ,CAENA,OAAOA,EAAQnkB,EAAOC,EAAQtB,GAC5B,IAAI6G,EAAO,CAAC,UAYZ,MATe,QAAX2e,GAAkB3e,EAAKhH,KAAK2lB,GAChC3e,EAAOA,EAAKkD,KAAK,KAGjByb,EACEjb,UAAU,aAAc6a,OACpB7a,UAAU,GACV3D,KAAKyZ,OAAOmF,OAAOnkB,EAAOC,EAAQtB,GAEjC4G,KAAKC,KAAKA,EAAM2e,EACzB,KAIJ5f,EAASwf,OAAQ,UCrEV,MAAMK,GAAS,CACpB,IAAK,SAAUC,GACb,OAAOA,CACR,EACD,KAAM,SAAUA,GACd,OAAQllB,KAAK2N,IAAIuX,EAAMllB,KAAKC,IAAM,EAAI,EACvC,EACD,IAAK,SAAUilB,GACb,OAAOllB,KAAK4L,IAAKsZ,EAAMllB,KAAKC,GAAM,EACnC,EACD,IAAK,SAAUilB,GACb,OAAwC,EAAhCllB,KAAK2N,IAAKuX,EAAMllB,KAAKC,GAAM,EACpC,EACDklB,OAAQ,SAAUrC,EAAIC,EAAI9M,EAAIC,GAE5B,OAAO,SAAUlN,GACf,OAAIA,EAAI,EACF8Z,EAAK,EACCC,EAAKD,EAAM9Z,EACViN,EAAK,EACNC,EAAKD,EAAMjN,EAEZ,EAEAA,EAAI,EACTiN,EAAK,GACE,EAAIC,IAAO,EAAID,GAAOjN,GAAKkN,EAAKD,IAAO,EAAIA,GAC3C6M,EAAK,GACL,EAAIC,IAAO,EAAID,GAAO9Z,GAAK+Z,EAAKD,IAAO,EAAIA,GAE7C,EAGF,EAAI9Z,GAAK,EAAIA,IAAM,EAAI+Z,EAAK,EAAI/Z,GAAK,GAAK,EAAIA,GAAKkN,EAAKlN,GAAK,EAGzE,EAEDoc,MAAO,SAAUA,EAAOC,EAAe,OAErCA,EAAeA,EAAalc,MAAM,KAAK2Y,UAAU,GAEjD,IAAIwD,EAAQF,EAQZ,MAPqB,SAAjBC,IACAC,EACwB,SAAjBD,KACPC,EAIG,CAACtc,EAAGuc,GAAa,KAEtB,IAAIC,EAAOxlB,KAAKylB,MAAMzc,EAAIoc,GAC1B,MAAMM,EAAW1c,EAAIwc,EAAQ,GAAM,EAkBnC,MAhBqB,UAAjBH,GAA6C,SAAjBA,KAC5BG,EAGAD,GAAcG,KACdF,EAGAxc,GAAK,GAAKwc,EAAO,IACnBA,EAAO,GAGLxc,GAAK,GAAKwc,EAAOF,IACnBE,EAAOF,GAGFE,EAAOF,CAAK,CAEvB,GAGK,MAAMK,GACXC,OACE,OAAO,CACT,EAQK,MAAMC,WAAaF,GACxBzf,YAAYF,EAAKuU,GAASE,MACxB9C,QACAvR,KAAKqU,KAAOwK,GAAOjf,IAAOA,CAC5B,CAEAwf,KAAK7C,EAAMK,EAAIkC,GACb,MAAoB,iBAATvC,EACFuC,EAAM,EAAIvC,EAAOK,EAEnBL,GAAQK,EAAKL,GAAQvc,KAAKqU,KAAKyK,EACxC,EAQK,MAAMY,WAAmBH,GAC9Bzf,YAAYF,GACV2R,QACAvR,KAAK2f,QAAU/f,CACjB,CAEA4f,KAAKnc,GACH,OAAOA,EAAEmc,IACX,CAEAJ,KAAKjH,EAASyH,EAAQC,EAAIxc,GACxB,OAAOrD,KAAK2f,QAAQxH,EAASyH,EAAQC,EAAIxc,EAC3C,EAGF,SAASyc,KAEP,MAAM1L,GAAYpU,KAAK+f,WAAa,KAAO,IACrCC,EAAYhgB,KAAKigB,YAAc,EAI/Bxa,EAAK7L,KAAKC,GACVqmB,EAAKtmB,KAAKumB,IAAIH,EAAY,IAFpB,OAGNI,GAAQF,EAAKtmB,KAAKwN,KAAK3B,EAAKA,EAAKya,EAAKA,GACtCG,EAAK,KAAOD,EAAOhM,GAGzBpU,KAAKrG,EAAI,EAAIymB,EAAOC,EACpBrgB,KAAK4E,EAAIyb,EAAKA,CAChB,CAEO,MAAMC,WAAeZ,GAC1B5f,YAAYsU,EAAW,IAAK4L,EAAY,GACtCzO,QACAvR,KAAKoU,SAASA,GAAU4L,UAAUA,EACpC,CAEAZ,KAAKjH,EAASyH,EAAQC,EAAIxc,GACxB,GAAuB,iBAAZ8U,EAAsB,OAAOA,EAExC,GADA9U,EAAEmc,KAAOK,IAAO3P,IACZ2P,IAAO3P,IAAU,OAAO0P,EAC5B,GAAW,IAAPC,EAAU,OAAO1H,EAEjB0H,EAAK,MAAKA,EAAK,IAEnBA,GAAM,IAGN,MAAMU,EAAWld,EAAEkd,UAAY,EAGzBC,GAAgBxgB,KAAKrG,EAAI4mB,EAAWvgB,KAAK4E,GAAKuT,EAAUyH,GACxDa,EAActI,EAAUoI,EAAWV,EAAMW,EAAeX,EAAKA,EAAM,EAOzE,OAJAxc,EAAEkd,SAAWA,EAAWC,EAAeX,EAGvCxc,EAAEmc,KAAO5lB,KAAKkQ,IAAI8V,EAASa,GAAe7mB,KAAKkQ,IAAIyW,GAAY,KACxDld,EAAEmc,KAAOI,EAASa,CAC3B,EAGFhhB,EAAO6gB,GAAQ,CACblM,SAAUuK,GAAiB,YAAamB,IACxCE,UAAWrB,GAAiB,aAAcmB,MAGrC,MAAMY,WAAYhB,GACvB5f,YAAY4C,EAAI,GAAKrJ,EAAI,IAAMM,EAAI,EAAGgnB,EAAS,KAC7CpP,QACAvR,KAAK0C,EAAEA,GAAGrJ,EAAEA,GAAGM,EAAEA,GAAGgnB,OAAOA,EAC7B,CAEAvB,KAAKjH,EAASyH,EAAQC,EAAIxc,GACxB,GAAuB,iBAAZ8U,EAAsB,OAAOA,EAGxC,GAFA9U,EAAEmc,KAAOK,IAAO3P,IAEZ2P,IAAO3P,IAAU,OAAO0P,EAC5B,GAAW,IAAPC,EAAU,OAAO1H,EAErB,MAAMzV,EAAIkd,EAASzH,EACnB,IAAI9e,GAAKgK,EAAEud,UAAY,GAAKle,EAAImd,EAChC,MAAMlmB,GAAK+I,GAAKW,EAAEwd,OAAS,IAAMhB,EAC3Bc,EAAS3gB,KAAK8gB,QAYpB,OATe,IAAXH,IACFtnB,EAAIO,KAAKuI,KAAKwe,EAAQ/mB,KAAKwI,IAAI/I,EAAGsnB,KAGpCtd,EAAEwd,MAAQne,EACVW,EAAEud,SAAWvnB,EAEbgK,EAAEmc,KAAO5lB,KAAKkQ,IAAIpH,GAAK,KAEhBW,EAAEmc,KAAOI,EAASzH,GAAWnY,KAAK+gB,EAAIre,EAAI1C,KAAKghB,EAAI3nB,EAAI2G,KAAKihB,EAAItnB,EACzE,EAGF8F,EAAOihB,GAAK,CACVC,OAAQhC,GAAiB,WACzBjc,EAAGic,GAAiB,KACpBtlB,EAAGslB,GAAiB,KACpBhlB,EAAGglB,GAAiB,OClOtB,MAAMuC,GAAoB,CACxBC,EAAG,EACHC,EAAG,EACHC,EAAG,EACHC,EAAG,EACHC,EAAG,EACHC,EAAG,EACHC,EAAG,EACHC,EAAG,EACHC,EAAG,EACHC,EAAG,GAGCC,GAAe,CACnBV,EAAG,SAAU9d,EAAGX,EAAGof,GAIjB,OAHApf,EAAEtH,EAAI0mB,EAAG1mB,EAAIiI,EAAE,GACfX,EAAErH,EAAIymB,EAAGzmB,EAAIgI,EAAE,GAER,CAAC,IAAKX,EAAEtH,EAAGsH,EAAErH,EACrB,EACD+lB,EAAG,SAAU/d,EAAGX,GAGd,OAFAA,EAAEtH,EAAIiI,EAAE,GACRX,EAAErH,EAAIgI,EAAE,GACD,CAAC,IAAKA,EAAE,GAAIA,EAAE,GACtB,EACDge,EAAG,SAAUhe,EAAGX,GAEd,OADAA,EAAEtH,EAAIiI,EAAE,GACD,CAAC,IAAKA,EAAE,GAChB,EACDie,EAAG,SAAUje,EAAGX,GAEd,OADAA,EAAErH,EAAIgI,EAAE,GACD,CAAC,IAAKA,EAAE,GAChB,EACDke,EAAG,SAAUle,EAAGX,GAGd,OAFAA,EAAEtH,EAAIiI,EAAE,GACRX,EAAErH,EAAIgI,EAAE,GACD,CAAC,IAAKA,EAAE,GAAIA,EAAE,GAAIA,EAAE,GAAIA,EAAE,GAAIA,EAAE,GAAIA,EAAE,GAC9C,EACDme,EAAG,SAAUne,EAAGX,GAGd,OAFAA,EAAEtH,EAAIiI,EAAE,GACRX,EAAErH,EAAIgI,EAAE,GACD,CAAC,IAAKA,EAAE,GAAIA,EAAE,GAAIA,EAAE,GAAIA,EAAE,GAClC,EACDoe,EAAG,SAAUpe,EAAGX,GAGd,OAFAA,EAAEtH,EAAIiI,EAAE,GACRX,EAAErH,EAAIgI,EAAE,GACD,CAAC,IAAKA,EAAE,GAAIA,EAAE,GAAIA,EAAE,GAAIA,EAAE,GAClC,EACDqe,EAAG,SAAUre,EAAGX,GAGd,OAFAA,EAAEtH,EAAIiI,EAAE,GACRX,EAAErH,EAAIgI,EAAE,GACD,CAAC,IAAKA,EAAE,GAAIA,EAAE,GACtB,EACDue,EAAG,SAAUve,EAAGX,EAAGof,GAGjB,OAFApf,EAAEtH,EAAI0mB,EAAG1mB,EACTsH,EAAErH,EAAIymB,EAAGzmB,EACF,CAAC,IACT,EACDsmB,EAAG,SAAUte,EAAGX,GAGd,OAFAA,EAAEtH,EAAIiI,EAAE,GACRX,EAAErH,EAAIgI,EAAE,GACD,CAAC,IAAKA,EAAE,GAAIA,EAAE,GAAIA,EAAE,GAAIA,EAAE,GAAIA,EAAE,GAAIA,EAAE,GAAIA,EAAE,GACrD,GAGI0e,GAAa,aAAahf,MAAM,IAEtC,IAAK,IAAI1J,EAAI,EAAGC,EAAKyoB,GAAWxoB,OAAQF,EAAIC,IAAMD,EAChDwoB,GAAaE,GAAW1oB,IAAO,SAAUA,GACvC,OAAO,SAAUgK,EAAGX,EAAGof,GACrB,GAAU,MAANzoB,EAAWgK,EAAE,GAAKA,EAAE,GAAKX,EAAEtH,OAC1B,GAAU,MAAN/B,EAAWgK,EAAE,GAAKA,EAAE,GAAKX,EAAErH,OAC/B,GAAU,MAANhC,EACPgK,EAAE,GAAKA,EAAE,GAAKX,EAAEtH,EAChBiI,EAAE,GAAKA,EAAE,GAAKX,EAAErH,OAEhB,IAAK,IAAI0Y,EAAI,EAAGiO,EAAK3e,EAAE9J,OAAQwa,EAAIiO,IAAMjO,EACvC1Q,EAAE0Q,GAAK1Q,EAAE0Q,IAAMA,EAAI,EAAIrR,EAAErH,EAAIqH,EAAEtH,GAInC,OAAOymB,GAAaxoB,GAAGgK,EAAGX,EAAGof,GAEhC,CAf8B,CAe5BC,GAAW1oB,GAAGgB,eAQnB,SAAS4nB,GAAgBpT,GACvB,OACEA,EAAOqT,QAAQ3oB,QACfsV,EAAOqT,QAAQ3oB,OAAS,IACtB2nB,GAAkBrS,EAAOqT,QAAQ,GAAG7nB,cAE1C,CAEA,SAAS8nB,GAAgBtT,EAAQuT,GAC/BvT,EAAOwT,UAAYC,GAAezT,GAAQ,GAC1C,MAAM0T,EAAazgB,GAAaoC,KAAKke,GAErC,GAAIG,EACF1T,EAAOqT,QAAU,CAACE,OACb,CACL,MAAMI,EAAc3T,EAAO2T,YACrBC,EAAQD,EAAYtoB,cACpBwoB,EAAUF,IAAgBC,EAChC5T,EAAOqT,QAAU,CAAW,MAAVO,EAAiBC,EAAU,IAAM,IAAOF,EAC5D,CAKA,OAHA3T,EAAO8T,WAAY,EACnB9T,EAAO2T,YAAc3T,EAAOqT,QAAQ,GAE7BK,CACT,CAEA,SAASD,GAAezT,EAAQwT,GAC9B,IAAKxT,EAAOwT,SAAU,MAAM,IAAIxc,MAAM,gBACtCgJ,EAAOqG,QAAUrG,EAAOqT,QAAQjpB,KAAKqU,WAAWuB,EAAOqG,SACvDrG,EAAOwT,SAAWA,EAClBxT,EAAOqG,OAAS,GAChBrG,EAAO+T,WAAY,EACnB/T,EAAOgU,aAAc,EAEjBZ,GAAgBpT,IAClBiU,GAAgBjU,EAEpB,CAEA,SAASiU,GAAgBjU,GACvBA,EAAO8T,WAAY,EACf9T,EAAOkU,WACTlU,EAAOqT,QAhDX,SAAqBrT,GACnB,MAAMmU,EAAUnU,EAAOqT,QAAQ,GAC/B,OAAOL,GAAamB,GAASnU,EAAOqT,QAAQ5nB,MAAM,GAAIuU,EAAOnM,EAAGmM,EAAOiT,GACzE,CA6CqBmB,CAAYpU,IAE/BA,EAAOqU,SAASjqB,KAAK4V,EAAOqT,QAC9B,CAEA,SAASiB,GAAUtU,GACjB,IAAKA,EAAOqT,QAAQ3oB,OAAQ,OAAO,EACnC,MAAM6pB,EAA4C,MAApCvU,EAAOqT,QAAQ,GAAG7nB,cAC1Bd,EAASsV,EAAOqT,QAAQ3oB,OAE9B,OAAO6pB,IAAqB,IAAX7pB,GAA2B,IAAXA,EACnC,CAEA,SAAS8pB,GAAcxU,GACrB,MAA0C,MAAnCA,EAAOyU,UAAUjpB,aAC1B,CAEA,MAAMkpB,GAAiB,IAAI7nB,IAAI,CAAC,IAAK,IAAK,KAAM,KAAM,KAAM,OChH7C,MAAM8nB,WAAkB7O,GAErC/Z,OAEE,OADAiU,KAASG,KAAK1S,aAAa,IAAK0D,KAAKqC,YAC9B,IAAIiN,GAAIT,GAAOC,MAAME,KAAK+K,UACnC,CAGAJ,KAAKve,EAAGC,GAEN,MAAMV,EAAMqF,KAAKpF,OAMjB,GAHAQ,GAAKT,EAAIS,EACTC,GAAKV,EAAIU,GAEJ8Z,MAAM/Z,KAAO+Z,MAAM9Z,GAEtB,IAAK,IAAIqK,EAAGrM,EAAI2G,KAAKzG,OAAS,EAAGF,GAAK,EAAGA,IACvCqM,EAAI1F,KAAK3G,GAAG,GAEF,MAANqM,GAAmB,MAANA,GAAmB,MAANA,GAC5B1F,KAAK3G,GAAG,IAAM+B,EACd4E,KAAK3G,GAAG,IAAMgC,GACC,MAANqK,EACT1F,KAAK3G,GAAG,IAAM+B,EACC,MAANsK,EACT1F,KAAK3G,GAAG,IAAMgC,EACC,MAANqK,GAAmB,MAANA,GAAmB,MAANA,GACnC1F,KAAK3G,GAAG,IAAM+B,EACd4E,KAAK3G,GAAG,IAAMgC,EACd2E,KAAK3G,GAAG,IAAM+B,EACd4E,KAAK3G,GAAG,IAAMgC,EAEJ,MAANqK,IACF1F,KAAK3G,GAAG,IAAM+B,EACd4E,KAAK3G,GAAG,IAAMgC,IAED,MAANqK,IACT1F,KAAK3G,GAAG,IAAM+B,EACd4E,KAAK3G,GAAG,IAAMgC,GAKpB,OAAO2E,IACT,CAGAyE,MAAM9K,EAAI,QAKR,OAJInB,MAAMC,QAAQkB,KAChBA,EAAInB,MAAM0G,UAAUwS,OAAO3R,MAAM,GAAIpG,GAAG0I,YD8DvC,SAAoB1I,EAAG8pB,GAAa,GACzC,IAAIpjB,EAAQ,EACR+hB,EAAQ,GACZ,MAAMvT,EAAS,CACbqT,QAAS,GACTG,UAAU,EACVnN,OAAQ,GACRoO,UAAW,GACXX,WAAW,EACXO,SAAU,GACVN,WAAW,EACXC,aAAa,EACbE,SAAUU,EACV3B,GAAI,IAAI3Y,GACRzG,EAAG,IAAIyG,IAGT,KAAS0F,EAAOyU,UAAYlB,EAASA,EAAQzoB,EAAES,OAAOiG,MACpD,GAAKwO,EAAO8T,YACNR,GAAgBtT,EAAQuT,GAK9B,GAAc,MAAVA,EAYJ,GAAKjN,MAAMvO,SAASwb,IAapB,GAAImB,GAAe3nB,IAAIwmB,GACjBvT,EAAOwT,UACTC,GAAezT,GAAQ,QAK3B,GAAc,MAAVuT,GAA2B,MAAVA,EAWrB,GAA4B,MAAxBA,EAAM/nB,eAMV,GAAIyH,GAAaoC,KAAKke,GAAQ,CAC5B,GAAIvT,EAAOwT,SACTC,GAAezT,GAAQ,OAClB,KAAKoT,GAAgBpT,GAC1B,MAAM,IAAIhJ,MAAM,gBAEhBid,GAAgBjU,EAClB,GACExO,CACJ,OAdEwO,EAAOqG,QAAUkN,EACjBvT,EAAOgU,aAAc,MAbvB,CACE,GAAIhU,EAAOwT,WAAagB,GAAcxU,GAAS,CAC7CyT,GAAezT,GAAQ,KACrBxO,EACF,QACF,CACAwO,EAAOqG,QAAUkN,EACjBvT,EAAOwT,UAAW,CAEpB,KA7BA,CACE,GAAsB,MAAlBxT,EAAOqG,QAAkBiO,GAAUtU,GAAS,CAC9CA,EAAOwT,UAAW,EAClBxT,EAAOqG,OAASkN,EAChBE,GAAezT,GAAQ,GACvB,QACF,CAEAA,EAAOwT,UAAW,EAClBxT,EAAOqG,QAAUkN,CAEnB,KAvBA,CACE,GAAIvT,EAAO+T,WAAa/T,EAAOgU,YAAa,CAC1CP,GAAezT,GAAQ,KACrBxO,EACF,QACF,CACAwO,EAAOwT,UAAW,EAClBxT,EAAO+T,WAAY,EACnB/T,EAAOqG,QAAUkN,CAEnB,CA2DF,OARIvT,EAAOwT,UACTC,GAAezT,GAAQ,GAGrBA,EAAO8T,WAAaV,GAAgBpT,IACtCiU,GAAgBjU,GAGXA,EAAOqU,QAChB,CCzJWQ,CAAW/pB,EACpB,CAGAoV,KAAKtU,EAAOC,GAEV,MAAMC,EAAMqF,KAAKpF,OACjB,IAAIvB,EAAGqM,EAQP,IAJA/K,EAAIF,MAAsB,IAAdE,EAAIF,MAAc,EAAIE,EAAIF,MACtCE,EAAID,OAAwB,IAAfC,EAAID,OAAe,EAAIC,EAAID,OAGnCrB,EAAI2G,KAAKzG,OAAS,EAAGF,GAAK,EAAGA,IAChCqM,EAAI1F,KAAK3G,GAAG,GAEF,MAANqM,GAAmB,MAANA,GAAmB,MAANA,GAC5B1F,KAAK3G,GAAG,IAAO2G,KAAK3G,GAAG,GAAKsB,EAAIS,GAAKX,EAASE,EAAIF,MAAQE,EAAIS,EAC9D4E,KAAK3G,GAAG,IAAO2G,KAAK3G,GAAG,GAAKsB,EAAIU,GAAKX,EAAUC,EAAID,OAASC,EAAIU,GACjD,MAANqK,EACT1F,KAAK3G,GAAG,IAAO2G,KAAK3G,GAAG,GAAKsB,EAAIS,GAAKX,EAASE,EAAIF,MAAQE,EAAIS,EAC/C,MAANsK,EACT1F,KAAK3G,GAAG,IAAO2G,KAAK3G,GAAG,GAAKsB,EAAIU,GAAKX,EAAUC,EAAID,OAASC,EAAIU,EACjD,MAANqK,GAAmB,MAANA,GAAmB,MAANA,GACnC1F,KAAK3G,GAAG,IAAO2G,KAAK3G,GAAG,GAAKsB,EAAIS,GAAKX,EAASE,EAAIF,MAAQE,EAAIS,EAC9D4E,KAAK3G,GAAG,IAAO2G,KAAK3G,GAAG,GAAKsB,EAAIU,GAAKX,EAAUC,EAAID,OAASC,EAAIU,EAChE2E,KAAK3G,GAAG,IAAO2G,KAAK3G,GAAG,GAAKsB,EAAIS,GAAKX,EAASE,EAAIF,MAAQE,EAAIS,EAC9D4E,KAAK3G,GAAG,IAAO2G,KAAK3G,GAAG,GAAKsB,EAAIU,GAAKX,EAAUC,EAAID,OAASC,EAAIU,EAEtD,MAANqK,IACF1F,KAAK3G,GAAG,IAAO2G,KAAK3G,GAAG,GAAKsB,EAAIS,GAAKX,EAASE,EAAIF,MAAQE,EAAIS,EAC9D4E,KAAK3G,GAAG,IAAO2G,KAAK3G,GAAG,GAAKsB,EAAIU,GAAKX,EAAUC,EAAID,OAASC,EAAIU,IAEnD,MAANqK,IAET1F,KAAK3G,GAAG,GAAM2G,KAAK3G,GAAG,GAAKoB,EAASE,EAAIF,MACxCuF,KAAK3G,GAAG,GAAM2G,KAAK3G,GAAG,GAAKqB,EAAUC,EAAID,OAGzCsF,KAAK3G,GAAG,IAAO2G,KAAK3G,GAAG,GAAKsB,EAAIS,GAAKX,EAASE,EAAIF,MAAQE,EAAIS,EAC9D4E,KAAK3G,GAAG,IAAO2G,KAAK3G,GAAG,GAAKsB,EAAIU,GAAKX,EAAUC,EAAID,OAASC,EAAIU,GAIpE,OAAO2E,IACT,CAGAqC,WACE,OA9IJ,SAAuBiC,GACrB,IAAIvK,EAAI,GACR,IAAK,IAAIV,EAAI,EAAGC,EAAKgL,EAAE/K,OAAQF,EAAIC,EAAID,IACrCU,GAAKuK,EAAEjL,GAAG,GAEK,MAAXiL,EAAEjL,GAAG,KACPU,GAAKuK,EAAEjL,GAAG,GAEK,MAAXiL,EAAEjL,GAAG,KACPU,GAAK,IACLA,GAAKuK,EAAEjL,GAAG,GAEK,MAAXiL,EAAEjL,GAAG,KACPU,GAAK,IACLA,GAAKuK,EAAEjL,GAAG,GACVU,GAAK,IACLA,GAAKuK,EAAEjL,GAAG,GAEK,MAAXiL,EAAEjL,GAAG,KACPU,GAAK,IACLA,GAAKuK,EAAEjL,GAAG,GACVU,GAAK,IACLA,GAAKuK,EAAEjL,GAAG,GAEK,MAAXiL,EAAEjL,GAAG,KACPU,GAAK,IACLA,GAAKuK,EAAEjL,GAAG,QAQtB,OAAOU,EAAI,GACb,CA2GW4pB,CAAc3jB,KACvB,ECxIF,MAAM4jB,GAAmB5O,IACvB,MAAMlB,SAAckB,EAEpB,MAAa,WAATlB,EACKe,GACW,WAATf,EACL9O,GAAM+T,QAAQ/D,GACThQ,GACEnD,GAAUqC,KAAK8Q,GACjBlT,GAAaoC,KAAK8Q,GAASwO,GAAY7O,GACrCzT,EAAcgD,KAAK8Q,GACrBH,GAEAgP,GAEAC,GAAe7gB,QAAQ+R,EAAMlV,cAAgB,EAC/CkV,EAAMlV,YACJtH,MAAMC,QAAQuc,GAChBL,GACW,WAATb,EACFiQ,GAEAF,EACT,EAGa,MAAMG,GACnBlkB,YAAY6f,GACV3f,KAAKikB,SAAWtE,GAAW,IAAIF,GAAK,KAEpCzf,KAAKkkB,MAAQ,KACblkB,KAAKmkB,IAAM,KACXnkB,KAAKokB,MAAQ,KACbpkB,KAAKqkB,SAAW,KAChBrkB,KAAKskB,UAAY,IACnB,CAEAC,GAAGzF,GACD,OAAO9e,KAAKskB,UAAUE,MACpBxkB,KAAKkkB,MACLlkB,KAAKmkB,IACLrF,EACA9e,KAAKikB,SACLjkB,KAAKqkB,SAET,CAEA7E,OAOE,OANiBxf,KAAKqkB,SAASnrB,IAAI8G,KAAKikB,SAASzE,MAAMzN,QAAO,SAC5D8E,EACA8B,GAEA,OAAO9B,GAAQ8B,CAChB,IAAE,EAEL,CAEA4D,KAAK9Y,GACH,OAAW,MAAPA,EACKzD,KAAKkkB,OAGdlkB,KAAKkkB,MAAQlkB,KAAKykB,KAAKhhB,GAChBzD,KACT,CAEA2f,QAAQA,GACN,OAAe,MAAXA,EAAwB3f,KAAKikB,UACjCjkB,KAAKikB,SAAWtE,EACT3f,KACT,CAEA4c,GAAGnZ,GACD,OAAW,MAAPA,EACKzD,KAAKmkB,KAGdnkB,KAAKmkB,IAAMnkB,KAAKykB,KAAKhhB,GACdzD,KACT,CAEA8T,KAAKA,GAEH,OAAY,MAARA,EACK9T,KAAKokB,OAIdpkB,KAAKokB,MAAQtQ,EACN9T,KACT,CAEAykB,KAAKzP,GACEhV,KAAKokB,OACRpkB,KAAK8T,KAAK8P,GAAgB5O,IAG5B,IAAIxb,EAAS,IAAIwG,KAAKokB,MAAMpP,GA4B5B,OA3BIhV,KAAKokB,QAAUpf,KACjBxL,EAASwG,KAAKmkB,IACV3qB,EAAOwG,KAAKmkB,IAAI,MAChBnkB,KAAKkkB,MACH1qB,EAAOwG,KAAKkkB,MAAM,MAClB1qB,GAGJwG,KAAKokB,QAAUL,KACjBvqB,EAASwG,KAAKmkB,IACV3qB,EAAOkrB,MAAM1kB,KAAKmkB,KAClBnkB,KAAKkkB,MACH1qB,EAAOkrB,MAAM1kB,KAAKkkB,OAClB1qB,GAGRA,EAASA,EAAOmrB,eAEhB3kB,KAAKskB,UAAYtkB,KAAKskB,WAAa,IAAItkB,KAAKokB,MAC5CpkB,KAAKqkB,SACHrkB,KAAKqkB,UACL7rB,MAAMuH,MAAM,KAAMvH,MAAMgB,EAAOD,SAC5BL,IAAIN,QACJM,KAAI,SAAU4B,GAEb,OADAA,EAAE0kB,MAAO,EACF1kB,CACT,IACGtB,CACT,EAGK,MAAMqqB,GACX/jB,eAAeD,GACbG,KAAKkF,QAAQrF,EACf,CAEAqF,KAAKzB,GAGH,OAFAA,EAAMjL,MAAMC,QAAQgL,GAAOA,EAAI,GAAKA,EACpCzD,KAAKgV,MAAQvR,EACNzD,IACT,CAEAmI,UACE,MAAO,CAACnI,KAAKgV,MACf,CAEA7Y,UACE,OAAO6D,KAAKgV,KACd,EAGK,MAAM4P,GACX9kB,eAAeD,GACbG,KAAKkF,QAAQrF,EACf,CAEAqF,KAAK8M,GAeH,OAdIxZ,MAAMC,QAAQuZ,KAChBA,EAAM,CACJzH,OAAQyH,EAAI,GACZvH,OAAQuH,EAAI,GACZtH,MAAOsH,EAAI,GACXpH,OAAQoH,EAAI,GACZ3G,WAAY2G,EAAI,GAChBzG,WAAYyG,EAAI,GAChB/W,QAAS+W,EAAI,GACb7W,QAAS6W,EAAI,KAIjBpZ,OAAOE,OAAOkH,KAAM4kB,GAAa5oB,SAAUgW,GACpChS,IACT,CAEAmI,UACE,MAAM5D,EAAIvE,KAEV,MAAO,CACLuE,EAAEgG,OACFhG,EAAEkG,OACFlG,EAAEmG,MACFnG,EAAEqG,OACFrG,EAAE8G,WACF9G,EAAEgH,WACFhH,EAAEtJ,QACFsJ,EAAEpJ,QAEN,EAGFypB,GAAa5oB,SAAW,CACtBuO,OAAQ,EACRE,OAAQ,EACRC,MAAO,EACPE,OAAQ,EACRS,WAAY,EACZE,WAAY,EACZtQ,QAAS,EACTE,QAAS,GAGX,MAAM0pB,GAAYA,CAACvgB,EAAGe,IACbf,EAAE,GAAKe,EAAE,IAAM,EAAIf,EAAE,GAAKe,EAAE,GAAK,EAAI,EAGvC,MAAM0e,GACXjkB,eAAeD,GACbG,KAAKkF,QAAQrF,EACf,CAEA6kB,MAAM5X,GACJ,MAAMxG,EAAStG,KAAKsG,OACpB,IAAK,IAAIjN,EAAI,EAAGC,EAAKgN,EAAO/M,OAAQF,EAAIC,IAAMD,EAAG,CAE/C,GAAIiN,EAAOjN,EAAI,KAAOyT,EAAMzT,EAAI,GAAI,CAClC,GAAIiN,EAAOjN,EAAI,KAAO2L,IAAS8H,EAAMzT,EAAI,KAAOiN,EAAOjN,EAAI,GAAI,CAC7D,MAAMmJ,EAAQsK,EAAMzT,EAAI,GAClB+L,EAAQ,IAAIJ,GAAMhF,KAAKsG,OAAOwe,OAAOzrB,EAAI,EAAG,IAC/CmJ,KACA2F,UACHnI,KAAKsG,OAAOwe,OAAOzrB,EAAI,EAAG,KAAM+L,EAClC,CAEA/L,GAAKiN,EAAOjN,EAAI,GAAK,EACrB,QACF,CAEA,IAAKyT,EAAMzT,EAAI,GACb,OAAO2G,KAKT,MAAM+kB,GAAgB,IAAIjY,EAAMzT,EAAI,IAAK8O,UAGnC6c,EAAW1e,EAAOjN,EAAI,GAAK,EAEjCiN,EAAOwe,OACLzrB,EACA2rB,EACAlY,EAAMzT,GACNyT,EAAMzT,EAAI,GACVyT,EAAMzT,EAAI,MACP0rB,GAGL1rB,GAAKiN,EAAOjN,EAAI,GAAK,CACvB,CACA,OAAO2G,IACT,CAEAkF,KAAK+f,GAGH,GAFAjlB,KAAKsG,OAAS,GAEV9N,MAAMC,QAAQwsB,GAEhB,YADAjlB,KAAKsG,OAAS2e,EAAS3qB,SAIzB2qB,EAAWA,GAAY,GACvB,MAAMC,EAAU,GAEhB,IAAK,MAAM7rB,KAAK4rB,EAAU,CACxB,MAAME,EAAOvB,GAAgBqB,EAAS5rB,IAChCoK,EAAM,IAAI0hB,EAAKF,EAAS5rB,IAAI8O,UAClC+c,EAAQjsB,KAAK,CAACI,EAAG8rB,EAAM1hB,EAAIlK,UAAWkK,GACxC,CAKA,OAHAyhB,EAAQE,KAAKP,IAEb7kB,KAAKsG,OAAS4e,EAAQnT,QAAO,CAAC8E,EAAM8B,IAAS9B,EAAKnF,OAAOiH,IAAO,IACzD3Y,IACT,CAEAmI,UACE,OAAOnI,KAAKsG,MACd,CAEAnK,UACE,MAAM6V,EAAM,CAAA,EACNV,EAAMtR,KAAKsG,OAGjB,KAAOgL,EAAI/X,QAAQ,CACjB,MAAM2C,EAAMoV,EAAI+T,QACVF,EAAO7T,EAAI+T,QACXC,EAAMhU,EAAI+T,QACV/e,EAASgL,EAAIwT,OAAO,EAAGQ,GAC7BtT,EAAI9V,GAAO,IAAIipB,EAAK7e,EACtB,CAEA,OAAO0L,CACT,EAGF,MAAM8R,GAAiB,CAACD,GAAce,GAAcb,IAE7C,SAASwB,GAAsBzR,EAAO,IAC3CgQ,GAAe7qB,QAAQ,GAAGyY,OAAOoC,GACnC,CAEO,SAAS0R,KACd/lB,EAAOqkB,GAAgB,CACrBlH,GAAGnZ,GACD,OAAO,IAAIugB,IACRlQ,KAAK9T,KAAKF,aACVyc,KAAKvc,KAAKmI,WACVyU,GAAGnZ,EACP,EACD0J,UAAUmE,GAER,OADAtR,KAAKkF,KAAKoM,GACHtR,IACR,EACD2kB,eACE,OAAO3kB,KAAKmI,SACb,EACDqc,MAAMjI,EAAMK,EAAIkC,EAAKa,EAAS8F,GAK5B,OAAOzlB,KAAKmN,UAAUoP,EAAKrjB,KAJZ,SAAUG,EAAGgH,GAC1B,OAAOsf,EAAQP,KAAK/lB,EAAGujB,EAAGvc,GAAQye,EAAK2G,EAAQplB,GAAQolB,MAI3D,GAEJ,CCzUe,MAAMC,aAAavJ,MAEhCrc,YAAYzD,EAAM4V,EAAQ5V,GACxBkV,MAAM/S,EAAU,OAAQnC,GAAO4V,EACjC,CAGA9Y,QACE,OAAO6G,KAAK2lB,SAAW3lB,KAAK2lB,OAAS,IAAInC,GAAUxjB,KAAKC,KAAK,MAC/D,CAGAgW,QAEE,cADOjW,KAAK2lB,OACL3lB,IACT,CAGAtF,OAAOA,GACL,OAAiB,MAAVA,EACHsF,KAAKpF,OAAOF,OACZsF,KAAK+O,KAAK/O,KAAKpF,OAAOH,MAAOC,EACnC,CAGAif,KAAKve,EAAGC,GACN,OAAO2E,KAAKC,KAAK,IAAKD,KAAK7G,QAAQwgB,KAAKve,EAAGC,GAC7C,CAGAgjB,KAAK1kB,GACH,OAAY,MAALA,EACHqG,KAAK7G,QACL6G,KAAKiW,QAAQhW,KACX,IACa,iBAANtG,EAAiBA,EAAKqG,KAAK2lB,OAAS,IAAInC,GAAU7pB,GAEjE,CAGAoV,KAAKtU,EAAOC,GACV,MAAMgI,EAAInI,EAAiByF,KAAMvF,EAAOC,GACxC,OAAOsF,KAAKC,KAAK,IAAKD,KAAK7G,QAAQ4V,KAAKrM,EAAEjI,MAAOiI,EAAEhI,QACrD,CAGAD,MAAMA,GACJ,OAAgB,MAATA,EACHuF,KAAKpF,OAAOH,MACZuF,KAAK+O,KAAKtU,EAAOuF,KAAKpF,OAAOF,OACnC,CAGAU,EAAEA,GACA,OAAY,MAALA,EAAY4E,KAAKpF,OAAOQ,EAAI4E,KAAK2Z,KAAKve,EAAG4E,KAAKpF,OAAOS,EAC9D,CAGAA,EAAEA,GACA,OAAY,MAALA,EAAY2E,KAAKpF,OAAOS,EAAI2E,KAAK2Z,KAAK3Z,KAAKpF,OAAOQ,EAAGC,EAC9D,EAIFqqB,KAAKxmB,UAAU0mB,WAAapC,GAG5BnrB,EAAgB,CACd0jB,UAAW,CAET/M,KAAMrP,GAAkB,SAAUhG,GAEhC,OAAOqG,KAAKgW,IAAI,IAAI0P,MAAQrH,KAAK1kB,GAAK,IAAI6pB,UAKhDxkB,EAAS0mB,KAAM,qCC/ER,WACL,OAAO1lB,KAAK2lB,SAAW3lB,KAAK2lB,OAAS,IAAI/H,GAAW5d,KAAKC,KAAK,WAChE,QAGO,WAEL,cADOD,KAAK2lB,OACL3lB,IACT,OAGO,SAAc5E,EAAGC,GACtB,OAAO2E,KAAKC,KAAK,SAAUD,KAAK7G,QAAQwgB,KAAKve,EAAGC,GAClD,OAGO,SAAcqH,GACnB,OAAY,MAALA,EACH1C,KAAK7G,QACL6G,KAAKiW,QAAQhW,KACX,SACa,iBAANyC,EAAiBA,EAAK1C,KAAK2lB,OAAS,IAAI/H,GAAWlb,GAElE,OAGO,SAAcjI,EAAOC,GAC1B,MAAMgI,EAAInI,EAAiByF,KAAMvF,EAAOC,GACxC,OAAOsF,KAAKC,KAAK,SAAUD,KAAK7G,QAAQ4V,KAAKrM,EAAEjI,MAAOiI,EAAEhI,QAC1D,GCrBe,MAAMmrB,gBAAgB1J,MAEnCrc,YAAYzD,EAAM4V,EAAQ5V,GACxBkV,MAAM/S,EAAU,UAAWnC,GAAO4V,EACpC,EAGF5Z,EAAgB,CACd0jB,UAAW,CAET+J,QAASnmB,GAAkB,SAAU+C,GAEnC,OAAO1C,KAAKgW,IAAI,IAAI6P,SAAWxH,KAAK3b,GAAK,IAAIkb,UAKnDne,EAAOomB,QAASvH,IAChB7e,EAAOomB,QAASE,IAChB/mB,EAAS6mB,QAAS,WCnBH,MAAMG,iBAAiB7J,MAEpCrc,YAAYzD,EAAM4V,EAAQ5V,GACxBkV,MAAM/S,EAAU,WAAYnC,GAAO4V,EACrC,EAGF5Z,EAAgB,CACd0jB,UAAW,CAETkK,SAAUtmB,GAAkB,SAAU+C,GAEpC,OAAO1C,KAAKgW,IAAI,IAAIgQ,UAAY3H,KAAK3b,GAAK,IAAIkb,UAKpDne,EAAOumB,SAAU1H,IACjB7e,EAAOumB,SAAUD,IACjB/mB,EAASgnB,SAAU,YCrBJ,MAAME,aAAa/J,MAEhCrc,YAAYzD,EAAM4V,EAAQ5V,GACxBkV,MAAM/S,EAAU,OAAQnC,GAAO4V,EACjC,EAGFxS,EAAOymB,KAAM,CAAEza,MAAIE,QAEnBtT,EAAgB,CACd0jB,UAAW,CAETxB,KAAM5a,GAAkB,SAAUlF,EAAOC,GACvC,OAAOsF,KAAKgW,IAAI,IAAIkQ,MAAQnX,KAAKtU,EAAOC,SAK9CsE,EAASknB,KAAM,QC5BA,MAAMC,GACnBrmB,cACEE,KAAKomB,OAAS,KACdpmB,KAAKqmB,MAAQ,IACf,CAGA7P,QACE,OAAOxW,KAAKomB,QAAUpmB,KAAKomB,OAAOpR,KACpC,CAGA6B,OACE,OAAO7W,KAAKqmB,OAASrmB,KAAKqmB,MAAMrR,KAClC,CAEA/b,KAAK+b,GAEH,MAAMsR,OACkB,IAAftR,EAAM1U,KACT0U,EACA,CAAEA,MAAOA,EAAO1U,KAAM,KAAMC,KAAM,MAaxC,OAVIP,KAAKqmB,OACPC,EAAK/lB,KAAOP,KAAKqmB,MACjBrmB,KAAKqmB,MAAM/lB,KAAOgmB,EAClBtmB,KAAKqmB,MAAQC,IAEbtmB,KAAKqmB,MAAQC,EACbtmB,KAAKomB,OAASE,GAITA,CACT,CAGA5lB,OAAO4lB,GAEDA,EAAK/lB,OAAM+lB,EAAK/lB,KAAKD,KAAOgmB,EAAKhmB,MACjCgmB,EAAKhmB,OAAMgmB,EAAKhmB,KAAKC,KAAO+lB,EAAK/lB,MACjC+lB,IAAStmB,KAAKqmB,QAAOrmB,KAAKqmB,MAAQC,EAAK/lB,MACvC+lB,IAAStmB,KAAKomB,SAAQpmB,KAAKomB,OAASE,EAAKhmB,MAG7CgmB,EAAK/lB,KAAO,KACZ+lB,EAAKhmB,KAAO,IACd,CAEA+kB,QAEE,MAAM3kB,EAASV,KAAKomB,OACpB,OAAK1lB,GAGLV,KAAKomB,OAAS1lB,EAAOJ,KACjBN,KAAKomB,SAAQpmB,KAAKomB,OAAO7lB,KAAO,MACpCP,KAAKqmB,MAAQrmB,KAAKomB,OAASpmB,KAAKqmB,MAAQ,KACjC3lB,EAAOsU,OANM,IAOtB,ECzDF,MAAMuR,GAAW,CACfC,SAAU,KACVC,OAAQ,IAAIN,GACZO,SAAU,IAAIP,GACdQ,WAAY,IAAIR,GAChBS,MAAOA,IAAM7pB,EAAQC,OAAO6pB,aAAe9pB,EAAQC,OAAO8pB,KAC1DxlB,WAAY,GAEZylB,MAAMnnB,GAEJ,MAAMvD,EAAOkqB,GAASE,OAAOxtB,KAAK,CAAE+tB,IAAKpnB,IAQzC,OAL0B,OAAtB2mB,GAASC,WACXD,GAASC,SAAWzpB,EAAQC,OAAOiqB,sBAAsBV,GAASW,QAI7D7qB,CACR,EAED8qB,QAAQvnB,EAAI0U,GACVA,EAAQA,GAAS,EAGjB,MAAM8S,EAAOb,GAASK,QAAQS,MAAQ/S,EAGhCjY,EAAOkqB,GAASG,SAASztB,KAAK,CAAE+tB,IAAKpnB,EAAIwnB,KAAMA,IAOrD,OAJ0B,OAAtBb,GAASC,WACXD,GAASC,SAAWzpB,EAAQC,OAAOiqB,sBAAsBV,GAASW,QAG7D7qB,CACR,EAEDirB,UAAU1nB,GAER,MAAMvD,EAAOkqB,GAASI,WAAW1tB,KAAK2G,GAMtC,OAJ0B,OAAtB2mB,GAASC,WACXD,GAASC,SAAWzpB,EAAQC,OAAOiqB,sBAAsBV,GAASW,QAG7D7qB,CACR,EAEDkrB,YAAYlrB,GACF,MAARA,GAAgBkqB,GAASE,OAAO/lB,OAAOrE,EACxC,EAEDmrB,aAAanrB,GACH,MAARA,GAAgBkqB,GAASG,SAAShmB,OAAOrE,EAC1C,EAEDorB,gBAAgBprB,GACN,MAARA,GAAgBkqB,GAASI,WAAWjmB,OAAOrE,EAC5C,EAED6qB,MAAMG,GAGJ,IAAIK,EAAc,KAClB,MAAMC,EAAcpB,GAASG,SAAS7P,OACtC,MAAQ6Q,EAAcnB,GAASG,SAASrB,WAElCgC,GAAOK,EAAYN,KACrBM,EAAYV,MAEZT,GAASG,SAASztB,KAAKyuB,GAIrBA,IAAgBC,KAItB,IAAIC,EAAY,KAChB,MAAMC,EAAYtB,GAASE,OAAO5P,OAClC,KAAO+Q,IAAcC,IAAcD,EAAYrB,GAASE,OAAOpB,UAC7DuC,EAAUZ,IAAIK,GAGhB,IAAIS,EAAgB,KACpB,KAAQA,EAAgBvB,GAASI,WAAWtB,SAC1CyC,IAIFvB,GAASC,SACPD,GAASG,SAASlQ,SAAW+P,GAASE,OAAOjQ,QACzCzZ,EAAQC,OAAOiqB,sBAAsBV,GAASW,OAC9C,IACR,GC7FIa,GAAe,SAAUC,GAC7B,MAAMC,EAAQD,EAAWC,MACnB7T,EAAW4T,EAAWE,OAAO9T,WAEnC,MAAO,CACL6T,MAAOA,EACP7T,SAAUA,EACV+T,IAJUF,EAAQ7T,EAKlB8T,OAAQF,EAAWE,OAEvB,EAEME,GAAgB,WACpB,MAAMxY,EAAI7S,EAAQC,OAClB,OAAQ4S,EAAEiX,aAAejX,EAAEkX,MAAMO,KACnC,EAEe,MAAMgB,WAAiBxU,GAEpC/T,YAAYwoB,EAAaF,IACvB7W,QAEAvR,KAAKuoB,YAAcD,EAGnBtoB,KAAKwoB,WACP,CAEAC,SACE,QAASzoB,KAAK0oB,UAChB,CAEAC,SAGE,OADA3oB,KAAKonB,KAAKpnB,KAAK4oB,uBAAyB,GACjC5oB,KAAK6oB,OACd,CAGAC,aACE,MAAMC,EAAiB/oB,KAAKgpB,oBACtBC,EAAeF,EAAiBA,EAAeb,OAAO9T,WAAa,EAEzE,OADsB2U,EAAiBA,EAAed,MAAQjoB,KAAKkpB,OAC5CD,CACzB,CAEAL,uBACE,MAAMO,EAAWnpB,KAAKopB,SAASlwB,KAAKG,GAAMA,EAAE4uB,MAAQ5uB,EAAE6uB,OAAO9T,aAC7D,OAAOxa,KAAKuI,IAAI,KAAMgnB,EACxB,CAEAH,oBACE,OAAOhpB,KAAKqpB,kBAAkBrpB,KAAKspB,cACrC,CAEAD,kBAAkB7pB,GAChB,OAAOQ,KAAKopB,SAASppB,KAAKupB,WAAWtmB,QAAQzD,KAAQ,IACvD,CAEAqpB,QAEE,OADA7oB,KAAKwpB,SAAU,EACRxpB,KAAKypB,WACd,CAEAC,QAAQC,GACN,OAAmB,MAAfA,EAA4B3pB,KAAK4pB,UACrC5pB,KAAK4pB,SAAWD,EACT3pB,KACT,CAEA6pB,OAGE,OADA7pB,KAAKwpB,SAAU,EACRxpB,KAAK8pB,aAAaL,WAC3B,CAEA/N,QAAQqO,GACN,MAAMC,EAAehqB,KAAKiqB,QAC1B,GAAW,MAAPF,EAAa,OAAO/pB,KAAKiqB,OAAOD,GAEpC,MAAME,EAAWtwB,KAAKkQ,IAAIkgB,GAC1B,OAAOhqB,KAAKiqB,MAAMF,GAAOG,EAAWA,EACtC,CAGAC,SAASjC,EAAQ5T,EAAO8V,GACtB,GAAc,MAAVlC,EACF,OAAOloB,KAAKopB,SAASlwB,IAAI6uB,IAO3B,IAAIsC,EAAoB,EACxB,MAAMC,EAAUtqB,KAAK8oB,aAIrB,GAHAxU,EAAQA,GAAS,EAGL,MAAR8V,GAAyB,SAATA,GAA4B,UAATA,EAErCC,EAAoBC,OACf,GAAa,aAATF,GAAgC,UAATA,EAChCC,EAAoB/V,EACpBA,EAAQ,OACH,GAAa,QAAT8V,EACTC,EAAoBrqB,KAAKkpB,WACpB,GAAa,aAATkB,EAAqB,CAC9B,MAAMpC,EAAahoB,KAAKqpB,kBAAkBnB,EAAO1oB,IAC7CwoB,IACFqC,EAAoBrC,EAAWC,MAAQ3T,EACvCA,EAAQ,EAEZ,KAAO,IAAa,cAAT8V,EAKT,MAAM,IAAIvkB,MAAM,0CALe,CAC/B,MAAMkjB,EAAiB/oB,KAAKgpB,oBAE5BqB,EADsBtB,EAAiBA,EAAed,MAAQjoB,KAAKkpB,KAErE,CAEA,CAGAhB,EAAOqC,aACPrC,EAAO/T,SAASnU,MAEhB,MAAM0pB,EAAUxB,EAAOwB,UACjB1B,EAAa,CACjB0B,QAAqB,OAAZA,EAAmB1pB,KAAK4pB,SAAWF,EAC5CzB,MAAOoC,EAAoB/V,EAC3B4T,UAUF,OAPAloB,KAAKspB,cAAgBpB,EAAO1oB,GAE5BQ,KAAKopB,SAASnwB,KAAK+uB,GACnBhoB,KAAKopB,SAAShE,MAAK,CAAC9gB,EAAGe,IAAMf,EAAE2jB,MAAQ5iB,EAAE4iB,QACzCjoB,KAAKupB,WAAavpB,KAAKopB,SAASlwB,KAAKsxB,GAASA,EAAKtC,OAAO1oB,KAE1DQ,KAAK8pB,aAAaL,YACXzpB,IACT,CAEAyqB,KAAK5K,GACH,OAAO7f,KAAKonB,KAAKpnB,KAAKkpB,MAAQrJ,EAChC,CAEAvW,OAAO1J,GACL,OAAU,MAANA,EAAmBI,KAAKuoB,aAC5BvoB,KAAKuoB,YAAc3oB,EACZI,KACT,CAEAiqB,MAAMA,GACJ,OAAa,MAATA,EAAsBjqB,KAAK0qB,QAC/B1qB,KAAK0qB,OAAST,EACPjqB,KACT,CAEA2qB,OAGE,OADA3qB,KAAKonB,KAAK,GACHpnB,KAAK6oB,OACd,CAEAzB,KAAKA,GACH,OAAY,MAARA,EAAqBpnB,KAAKkpB,OAC9BlpB,KAAKkpB,MAAQ9B,EACNpnB,KAAKypB,WAAU,GACxB,CAGAc,WAAWrC,GACT,MAAM7nB,EAAQL,KAAKupB,WAAWtmB,QAAQilB,EAAO1oB,IAC7C,OAAIa,EAAQ,IAEZL,KAAKopB,SAAStE,OAAOzkB,EAAO,GAC5BL,KAAKupB,WAAWzE,OAAOzkB,EAAO,GAE9B6nB,EAAO/T,SAAS,OALMnU,IAOxB,CAGA8pB,aAIE,OAHK9pB,KAAKyoB,WACRzoB,KAAK4qB,gBAAkB5qB,KAAKuoB,eAEvBvoB,IACT,CAGAypB,UAAUoB,GAAgB,GAIxB,OAHAtE,GAASgB,YAAYvnB,KAAK0oB,YAC1B1oB,KAAK0oB,WAAa,KAEdmC,EAAsB7qB,KAAK8qB,kBAC3B9qB,KAAKwpB,UAETxpB,KAAK0oB,WAAanC,GAASQ,MAAM/mB,KAAK+qB,QAFb/qB,KAI3B,CAEAgrB,QAAQH,GAAgB,GAEtB,MAAMzD,EAAOpnB,KAAKuoB,cAClB,IAAI0C,EAAW7D,EAAOpnB,KAAK4qB,gBAEvBC,IAAeI,EAAW,GAE9B,MAAMC,EAASlrB,KAAK0qB,OAASO,GAAYjrB,KAAKkpB,MAAQlpB,KAAKmrB,eAC3DnrB,KAAK4qB,gBAAkBxD,EAIlByD,IAEH7qB,KAAKkpB,OAASgC,EACdlrB,KAAKkpB,MAAQlpB,KAAKkpB,MAAQ,EAAI,EAAIlpB,KAAKkpB,OAEzClpB,KAAKmrB,cAAgBnrB,KAAKkpB,MAC1BlpB,KAAKiU,KAAK,OAAQjU,KAAKkpB,OAavB,IAAK,IAAItkB,EAAI5E,KAAKopB,SAAS7vB,OAAQqL,KAAO,CAExC,MAAMojB,EAAahoB,KAAKopB,SAASxkB,GAC3BsjB,EAASF,EAAWE,OAIRloB,KAAKkpB,MAAQlB,EAAWC,OAIzB,GACfC,EAAOkD,OAEX,CAGA,IAAIC,GAAc,EAClB,IAAK,IAAIhyB,EAAI,EAAGmf,EAAMxY,KAAKopB,SAAS7vB,OAAQF,EAAImf,EAAKnf,IAAK,CAExD,MAAM2uB,EAAahoB,KAAKopB,SAAS/vB,GAC3B6uB,EAASF,EAAWE,OAC1B,IAAIrI,EAAKqL,EAIT,MAAMI,EAAYtrB,KAAKkpB,MAAQlB,EAAWC,MAG1C,GAAIqD,GAAa,EAAG,CAClBD,GAAc,EACd,QACF,CAKA,GALWC,EAAYzL,IAErBA,EAAKyL,IAGFpD,EAAOO,SAAU,SAKtB,GADiBP,EAAO9I,KAAKS,GAAIL,MAI1B,IAA2B,IAAvBwI,EAAW0B,QAAkB,CAEtBxB,EAAO9T,WAAa8T,EAAOd,OAASpnB,KAAKkpB,MAE3ClB,EAAW0B,QAAU1pB,KAAKkpB,QAEtChB,EAAOqC,eACLlxB,IACAmf,EAEN,OAZE6S,GAAc,CAalB,CAcA,OATGA,KAAiBrrB,KAAK0qB,OAAS,GAAoB,IAAf1qB,KAAKkpB,QACzClpB,KAAKupB,WAAWhwB,QAAUyG,KAAK0qB,OAAS,GAAK1qB,KAAKkpB,MAAQ,EAE3DlpB,KAAKypB,aAELzpB,KAAK6oB,QACL7oB,KAAKiU,KAAK,aAGLjU,IACT,CAEAwoB,YAIExoB,KAAKurB,WAAa,EAClBvrB,KAAK0qB,OAAS,EAGd1qB,KAAK4pB,SAAW,EAGhB5pB,KAAK0oB,WAAa,KAClB1oB,KAAKwpB,SAAU,EACfxpB,KAAKopB,SAAW,GAChBppB,KAAKupB,WAAa,GAClBvpB,KAAKspB,eAAiB,EACtBtpB,KAAKkpB,MAAQ,EACblpB,KAAK4qB,gBAAkB,EACvB5qB,KAAKmrB,cAAgB,EAGrBnrB,KAAK+qB,MAAQ/qB,KAAKgrB,QAAQlY,KAAK9S,MAAM,GACrCA,KAAK8qB,eAAiB9qB,KAAKgrB,QAAQlY,KAAK9S,MAAM,EAChD,EAGF3H,EAAgB,CACd+U,QAAS,CACP+G,SAAU,SAAUA,GAClB,OAAgB,MAAZA,GACFnU,KAAKwrB,UAAYxrB,KAAKwrB,WAAa,IAAInD,GAChCroB,KAAKwrB,YAEZxrB,KAAKwrB,UAAYrX,EACVnU,KAEX,KC3UW,MAAMyrB,WAAe5X,GAClC/T,YAAY+S,GACVtB,QAGAvR,KAAKR,GAAKisB,GAAOjsB,KAMjBqT,EAA6B,mBAH7BA,EAAqB,MAAXA,EAAkBsB,GAASC,SAAWvB,GAGN,IAAI6M,GAAW7M,GAAWA,EAGpE7S,KAAKkb,SAAW,KAChBlb,KAAKwrB,UAAY,KACjBxrB,KAAKwf,MAAO,EACZxf,KAAK0rB,OAAS,GAGd1rB,KAAK+f,UAA+B,iBAAZlN,GAAwBA,EAChD7S,KAAK2rB,eAAiB9Y,aAAmB6M,GACzC1f,KAAKikB,SAAWjkB,KAAK2rB,eAAiB9Y,EAAU,IAAI4M,GAGpDzf,KAAK4rB,SAAW,GAGhB5rB,KAAK6rB,SAAU,EACf7rB,KAAKkpB,MAAQ,EACblpB,KAAK8rB,UAAY,EAGjB9rB,KAAK+rB,UAAW,EAGhB/rB,KAAKsB,WAAa,IAAImI,GACtBzJ,KAAKgsB,YAAc,EAGnBhsB,KAAKisB,eAAgB,EACrBjsB,KAAKksB,UAAW,EAChBlsB,KAAKmsB,WAAa,EAClBnsB,KAAKosB,QAAS,EACdpsB,KAAKqsB,MAAQ,EACbrsB,KAAKssB,OAAS,EAEdtsB,KAAKusB,SAAW,KAGhBvsB,KAAK4pB,WAAW5pB,KAAK2rB,gBAAwB,IAC/C,CAEAxmB,gBAAgBiP,EAAUE,EAAO8V,GAE/B,IAAI7U,EAAQ,EACRiX,GAAQ,EACRC,EAAO,EAeX,OAbAnY,EAAQA,GAASH,GAASG,MAC1B8V,EAAOA,GAAQ,OAGS,iBALxBhW,EAAWA,GAAYD,GAASC,WAKMA,aAAoBmL,KACxDjL,EAAQF,EAASE,OAASA,EAC1B8V,EAAOhW,EAASgW,MAAQA,EACxBoC,EAAQpY,EAASoY,OAASA,EAC1BjX,EAAQnB,EAASmB,OAASA,EAC1BkX,EAAOrY,EAASqY,MAAQA,EACxBrY,EAAWA,EAASA,UAAYD,GAASC,UAGpC,CACLA,SAAUA,EACVE,MAAOA,EACPkY,MAAOA,EACPjX,MAAOA,EACPkX,KAAMA,EACNrC,KAAMA,EAEV,CAEA3B,OAAOoD,GACL,OAAe,MAAXA,EAAwB7rB,KAAK6rB,SACjC7rB,KAAK6rB,QAAUA,EACR7rB,KACT,CAOA0sB,aAAanjB,GAEX,OADAvJ,KAAKsB,WAAW8K,WAAW7C,GACpBvJ,IACT,CAEAe,MAAMnB,GACJ,OAAOI,KAAK0S,GAAG,WAAY9S,EAC7B,CAEA+sB,QAAQvY,EAAUE,EAAO8V,GACvB,MAAMtvB,EAAI2wB,GAAOmB,SAASxY,EAAUE,EAAO8V,GACrClC,EAAS,IAAIuD,GAAO3wB,EAAEsZ,UAG5B,OAFIpU,KAAKwrB,WAAWtD,EAAO/T,SAASnU,KAAKwrB,WACrCxrB,KAAKkb,UAAUgN,EAAO1tB,QAAQwF,KAAKkb,UAChCgN,EAAO2E,KAAK/xB,GAAGqvB,SAASrvB,EAAEwZ,MAAOxZ,EAAEsvB,KAC5C,CAEA0C,iBAEE,OADA9sB,KAAKsB,WAAa,IAAImI,GACfzJ,IACT,CAGA+sB,2BAEK/sB,KAAKwf,MACLxf,KAAKwrB,WACLxrB,KAAKwrB,UAAUjC,WAAW/tB,SAASwE,KAAKR,MAEzCQ,KAAK0rB,OAAS1rB,KAAK0rB,OAAOjyB,QAAQ6sB,IACxBA,EAAK0G,cAGnB,CAEA1Y,MAAMA,GACJ,OAAOtU,KAAK2sB,QAAQ,EAAGrY,EACzB,CAEAF,WACE,OAAOpU,KAAKssB,QAAUtsB,KAAKqsB,MAAQrsB,KAAK+f,WAAa/f,KAAKqsB,KAC5D,CAEAY,OAAOrtB,GACL,OAAOI,KAAKktB,MAAM,KAAMttB,EAC1B,CAEAyU,KAAKzU,GAEH,OADAI,KAAKikB,SAAW,IAAIxE,GAAK7f,GAClBI,IACT,CAQAxF,QAAQA,GACN,OAAe,MAAXA,EAAwBwF,KAAKkb,UACjClb,KAAKkb,SAAW1gB,EAChBA,EAAQ2yB,iBACDntB,KACT,CAEA2oB,SACE,OAAO3oB,KAAKof,KAAKlP,IACnB,CAEA2c,KAAKtX,EAAOiX,EAAOC,GAkBjB,MAhBqB,iBAAVlX,IACTiX,EAAQjX,EAAMiX,MACdC,EAAOlX,EAAMkX,KACblX,EAAQA,EAAMA,OAIhBvV,KAAKssB,OAAS/W,GAASrF,IACvBlQ,KAAKosB,OAASI,IAAS,EACvBxsB,KAAKqsB,MAAQI,GAAQ,GAGD,IAAhBzsB,KAAKssB,SACPtsB,KAAKssB,OAASpc,KAGTlQ,IACT,CAEAotB,MAAM1qB,GACJ,MAAM2qB,EAAertB,KAAK+f,UAAY/f,KAAKqsB,MAC3C,GAAS,MAAL3pB,EAAW,CACb,MAAM4qB,EAAY1zB,KAAKylB,MAAMrf,KAAKkpB,MAAQmE,GAEpCjtB,GADeJ,KAAKkpB,MAAQoE,EAAYD,GACdrtB,KAAK+f,UACrC,OAAOnmB,KAAKwI,IAAIkrB,EAAYltB,EAAUJ,KAAKssB,OAC7C,CACA,MACMiB,EAAU7qB,EAAI,EACd0kB,EAAOiG,EAFCzzB,KAAKylB,MAAM3c,GAEW1C,KAAK+f,UAAYwN,EACrD,OAAOvtB,KAAKonB,KAAKA,EACnB,CAEAsC,QAAQC,GACN,OAAmB,MAAfA,EAA4B3pB,KAAK4pB,UACrC5pB,KAAK4pB,SAAWD,EACT3pB,KACT,CAEAI,SAASsC,GAEP,MAAMtH,EAAI4E,KAAKkpB,MACTvvB,EAAIqG,KAAK+f,UACTnQ,EAAI5P,KAAKqsB,MACTzpB,EAAI5C,KAAKssB,OACTvyB,EAAIiG,KAAKosB,OACT1vB,EAAIsD,KAAKksB,SACf,IAAI9rB,EAEJ,GAAS,MAALsC,EAAW,CASb,MAAMiH,EAAI,SAAUvO,GAClB,MAAMoyB,EAAWzzB,EAAIH,KAAKylB,MAAOjkB,GAAK,GAAKwU,EAAIjW,KAAQiW,EAAIjW,IACrD8zB,EAAaD,IAAa9wB,IAAQ8wB,GAAY9wB,EAC9CgxB,EACH9zB,KAAKqO,KAAK,EAAGwlB,IAAcryB,GAAKwU,EAAIjW,IAAOA,EAAI8zB,EAElD,OADgB7zB,KAAKuI,IAAIvI,KAAKwI,IAAIsrB,EAAU,GAAI,IAK5CpD,EAAU1nB,GAAKgN,EAAIjW,GAAKiW,EAO9B,OANAxP,EACEhF,GAAK,EACDxB,KAAKsI,MAAMyH,EAAE,OACbvO,EAAIkvB,EACF3gB,EAAEvO,GACFxB,KAAKsI,MAAMyH,EAAE2gB,EAAU,OACxBlqB,CACT,CAGA,MAAMktB,EAAY1zB,KAAKylB,MAAMrf,KAAKotB,SAC5BO,EAAe5zB,GAAKuzB,EAAY,GAAM,EAG5C,OADAltB,EAAWktB,GADOK,IAAiBjxB,GAAOA,GAAKixB,EACZjrB,EAAI,EAAIA,GACpC1C,KAAKotB,MAAMhtB,EACpB,CAEAwtB,SAASlrB,GACP,OAAS,MAALA,EACK9I,KAAKwI,IAAI,EAAGpC,KAAKkpB,MAAQlpB,KAAKoU,YAEhCpU,KAAKonB,KAAK1kB,EAAI1C,KAAKoU,WAC5B,CAOA8Y,MAAMW,EAAQC,EAAOC,EAAYf,GAC/BhtB,KAAK0rB,OAAOzyB,KAAK,CACf+0B,YAAaH,GAAU3Z,GACvBgU,OAAQ4F,GAAS5Z,GACjB+Z,SAAUF,EACVf,YAAaA,EACbkB,aAAa,EACbC,UAAU,IAIZ,OAFiBnuB,KAAKmU,YACVnU,KAAKmU,WAAWsV,YACrBzpB,IACT,CAEAorB,QACE,OAAIprB,KAAK+rB,WACT/rB,KAAKonB,KAAK,GACVpnB,KAAK+rB,UAAW,GAFU/rB,IAI5B,CAEA0b,QAAQA,GAEN,OADA1b,KAAKksB,SAAsB,MAAXxQ,GAAmB1b,KAAKksB,SAAWxQ,EAC5C1b,IACT,CAEAmqB,SAAShW,EAAUG,EAAO8V,GASxB,GAPMjW,aAAoBkU,KACxB+B,EAAO9V,EACPA,EAAQH,EACRA,EAAWnU,KAAKmU,aAIbA,EACH,MAAMtO,MAAM,+CAKd,OADAsO,EAASgW,SAASnqB,KAAMsU,EAAO8V,GACxBpqB,IACT,CAEAof,KAAKS,GAEH,IAAK7f,KAAK6rB,QAAS,OAAO7rB,KAG1B6f,EAAW,MAANA,EAAa,GAAKA,EACvB7f,KAAKkpB,OAASrJ,EACd,MAAMzf,EAAWJ,KAAKI,WAGhBguB,EAAUpuB,KAAKquB,gBAAkBjuB,GAAYJ,KAAKkpB,OAAS,EACjElpB,KAAKquB,cAAgBjuB,EAGrB,MAAMgU,EAAWpU,KAAKoU,WAChBka,EAActuB,KAAK8rB,WAAa,GAAK9rB,KAAKkpB,MAAQ,EAClDqF,EAAevuB,KAAK8rB,UAAY1X,GAAYpU,KAAKkpB,OAAS9U,EAEhEpU,KAAK8rB,UAAY9rB,KAAKkpB,MAClBoF,GACFtuB,KAAKiU,KAAK,QAASjU,MAMrB,MAAMwuB,EAAcxuB,KAAK2rB,eACzB3rB,KAAKwf,MAAQgP,IAAgBD,GAAgBvuB,KAAKkpB,OAAS9U,EAG3DpU,KAAK+rB,UAAW,EAEhB,IAAI0C,GAAY,EAiBhB,OAfIL,GAAWI,KACbxuB,KAAK0uB,YAAYN,GAGjBpuB,KAAKsB,WAAa,IAAImI,GACtBglB,EAAYzuB,KAAK2uB,KAAKH,EAAc3O,EAAKzf,GAEzCJ,KAAKiU,KAAK,OAAQjU,OAIpBA,KAAKwf,KAAOxf,KAAKwf,MAASiP,GAAaD,EACnCD,GACFvuB,KAAKiU,KAAK,WAAYjU,MAEjBA,IACT,CAOAonB,KAAKA,GACH,GAAY,MAARA,EACF,OAAOpnB,KAAKkpB,MAEd,MAAMrJ,EAAKuH,EAAOpnB,KAAKkpB,MAEvB,OADAlpB,KAAKof,KAAKS,GACH7f,IACT,CAEAmU,SAASA,GAEP,YAAwB,IAAbA,EAAiCnU,KAAKwrB,WACjDxrB,KAAKwrB,UAAYrX,EACVnU,KACT,CAEAuqB,aACE,MAAMpW,EAAWnU,KAAKmU,WAEtB,OADAA,GAAYA,EAASoW,WAAWvqB,MACzBA,IACT,CAGA0uB,YAAYN,GAEV,GAAKA,GAAYpuB,KAAK2rB,eAGtB,IAAK,IAAItyB,EAAI,EAAGmf,EAAMxY,KAAK0rB,OAAOnyB,OAAQF,EAAImf,IAAOnf,EAAG,CAEtD,MAAM8e,EAAUnY,KAAK0rB,OAAOryB,GAGtBu1B,EAAU5uB,KAAK2rB,iBAAoBxT,EAAQ+V,aAAeE,EAChEA,GAAWjW,EAAQgW,SAGfS,GAAWR,IACbjW,EAAQ6V,YAAYzgB,KAAKvN,MACzBmY,EAAQ+V,aAAc,EAE1B,CACF,CAGAW,iBAAiBC,EAAQC,GAYvB,GAXA/uB,KAAK4rB,SAASkD,GAAU,CACtBC,QAASA,EACTC,OAAQhvB,KAAK0rB,OAAO1rB,KAAK0rB,OAAOnyB,OAAS,IASvCyG,KAAK2rB,eAAgB,CACvB,MAAMxX,EAAWnU,KAAKmU,WACtBA,GAAYA,EAAS0V,MACvB,CACF,CAIA8E,KAAKM,GAEH,IAAIC,GAAc,EAClB,IAAK,IAAI71B,EAAI,EAAGmf,EAAMxY,KAAK0rB,OAAOnyB,OAAQF,EAAImf,IAAOnf,EAAG,CAEtD,MAAM8e,EAAUnY,KAAK0rB,OAAOryB,GAItBo1B,EAAYtW,EAAQ+P,OAAO3a,KAAKvN,KAAMivB,GAC5C9W,EAAQgW,SAAWhW,EAAQgW,WAA0B,IAAdM,EACvCS,EAAcA,GAAe/W,EAAQgW,QACvC,CAGA,OAAOe,CACT,CAGAC,aAAaL,EAAQlP,EAAQwP,GAC3B,GAAIpvB,KAAK4rB,SAASkD,GAAS,CAEzB,IAAK9uB,KAAK4rB,SAASkD,GAAQE,OAAOd,YAAa,CAC7C,MAAM7tB,EAAQL,KAAK0rB,OAAOzoB,QAAQjD,KAAK4rB,SAASkD,GAAQE,QAExD,OADAhvB,KAAK0rB,OAAO5G,OAAOzkB,EAAO,IACnB,CACT,CAIIL,KAAK4rB,SAASkD,GAAQE,OAAOf,SAC/BjuB,KAAK4rB,SAASkD,GAAQE,OAAOf,SAAS1gB,KAAKvN,KAAM4f,EAAQwP,GAGzDpvB,KAAK4rB,SAASkD,GAAQC,QAAQnS,GAAGgD,GAGnC5f,KAAK4rB,SAASkD,GAAQE,OAAOb,UAAW,EACxC,MAAMha,EAAWnU,KAAKmU,WAEtB,OADAA,GAAYA,EAAS0V,QACd,CACT,CACA,OAAO,CACT,EAGF4B,GAAOjsB,GAAK,EAEL,MAAM6vB,GACXvvB,YAAYwB,EAAa,IAAImI,GAAUjK,GAAK,EAAIggB,GAAO,GACrDxf,KAAKsB,WAAaA,EAClBtB,KAAKR,GAAKA,EACVQ,KAAKwf,KAAOA,CACd,CAEAuN,2BAA4B,EAG9BttB,EAAO,CAACgsB,GAAQ4D,IAAa,CAC3BC,UAAUpH,GACR,OAAO,IAAImH,GACTnH,EAAO5mB,WAAW2M,UAAUjO,KAAKsB,YACjC4mB,EAAO1oB,GAEX,IAKF,MAAMyO,GAAYA,CAAC4I,EAAM8B,IAAS9B,EAAKzK,WAAWuM,GAC5C4W,GAAsBrH,GAAWA,EAAO5mB,WAE9C,SAASkuB,KAEP,MACMC,EADUzvB,KAAK0vB,uBAAuBC,QAEzCz2B,IAAIq2B,IACJxd,OAAO9D,GAAW,IAAIxE,IAEzBzJ,KAAKuJ,UAAUkmB,GAEfzvB,KAAK0vB,uBAAuB1f,QAEiB,IAAzChQ,KAAK0vB,uBAAuBn2B,WAC9ByG,KAAKusB,SAAW,KAEpB,CAEO,MAAMqD,GACX9vB,cACEE,KAAK2vB,QAAU,GACf3vB,KAAK6vB,IAAM,EACb,CAEApvB,IAAIynB,GACF,GAAIloB,KAAK2vB,QAAQn0B,SAAS0sB,GAAS,OACnC,MAAM1oB,EAAK0oB,EAAO1oB,GAAK,EAKvB,OAHAQ,KAAK2vB,QAAQ12B,KAAKivB,GAClBloB,KAAK6vB,IAAI52B,KAAKuG,GAEPQ,IACT,CAEA8vB,YAAYtwB,GACV,MAAMuwB,EAAY/vB,KAAK6vB,IAAI5sB,QAAQzD,EAAK,IAAM,EAK9C,OAJAQ,KAAK6vB,IAAI/K,OAAO,EAAGiL,EAAW,GAC9B/vB,KAAK2vB,QACF7K,OAAO,EAAGiL,EAAW,IAAIV,IACzBvrB,SAASpH,GAAMA,EAAEqwB,6BACb/sB,IACT,CAEAgwB,KAAKxwB,EAAIywB,GACP,MAAM5vB,EAAQL,KAAK6vB,IAAI5sB,QAAQzD,EAAK,GAGpC,OAFAQ,KAAK6vB,IAAI/K,OAAOzkB,EAAO,EAAGb,EAAK,GAC/BQ,KAAK2vB,QAAQ7K,OAAOzkB,EAAO,EAAG4vB,GACvBjwB,IACT,CAEAkwB,QAAQ1wB,GACN,OAAOQ,KAAK2vB,QAAQ3vB,KAAK6vB,IAAI5sB,QAAQzD,EAAK,GAC5C,CAEAjG,SACE,OAAOyG,KAAK6vB,IAAIt2B,MAClB,CAEAyW,QACE,IAAImgB,EAAa,KACjB,IAAK,IAAI92B,EAAI,EAAGA,EAAI2G,KAAK2vB,QAAQp2B,SAAUF,EAAG,CAC5C,MAAM6uB,EAASloB,KAAK2vB,QAAQt2B,GAY5B,GATE82B,GACAjI,EAAO1I,MACP2Q,EAAW3Q,QAET0I,EAAOsD,YACNtD,EAAOsD,UAAUjC,WAAW/tB,SAAS0sB,EAAO1oB,QAC7C2wB,EAAW3E,YACV2E,EAAW3E,UAAUjC,WAAW/tB,SAAS20B,EAAW3wB,KAE1C,CAEbQ,KAAKU,OAAOwnB,EAAO1oB,IACnB,MAAMywB,EAAY/H,EAAOoH,UAAUa,GACnCnwB,KAAKgwB,KAAKG,EAAW3wB,GAAIywB,GACzBE,EAAaF,IACX52B,CACJ,MACE82B,EAAajI,CAEjB,CAEA,OAAOloB,IACT,CAEAU,OAAOlB,GACL,MAAMa,EAAQL,KAAK6vB,IAAI5sB,QAAQzD,EAAK,GAGpC,OAFAQ,KAAK6vB,IAAI/K,OAAOzkB,EAAO,GACvBL,KAAK2vB,QAAQ7K,OAAOzkB,EAAO,GACpBL,IACT,EAGF3H,EAAgB,CACd+U,QAAS,CACPuf,QAAQvY,EAAUE,EAAO8V,GACvB,MAAMtvB,EAAI2wB,GAAOmB,SAASxY,EAAUE,EAAO8V,GACrCjW,EAAWnU,KAAKmU,WACtB,OAAO,IAAIsX,GAAO3wB,EAAEsZ,UACjByY,KAAK/xB,GACLN,QAAQwF,MACRmU,SAASA,EAAS0V,QAClBM,SAASrvB,EAAEwZ,MAAOxZ,EAAEsvB,KACxB,EAED9V,MAAM8b,EAAIhG,GACR,OAAOpqB,KAAK2sB,QAAQ,EAAGyD,EAAIhG,EAC5B,EAMDiG,6BAA6BC,GAC3BtwB,KAAK0vB,uBAAuBI,YAAYQ,EAAc9wB,GACvD,EAED+wB,kBAAkBpY,GAChB,OACEnY,KAAK0vB,uBAAuBC,QAIzBl2B,QAAQyuB,GAAWA,EAAO1oB,IAAM2Y,EAAQ3Y,KACxCtG,IAAIq2B,IACJxd,OAAO9D,GAAW,IAAIxE,GAE5B,EAED+mB,WAAWtI,GACTloB,KAAK0vB,uBAAuBjvB,IAAIynB,GAKhC3B,GAASkB,gBAAgBznB,KAAKusB,UAC9BvsB,KAAKusB,SAAWhG,GAASe,UAAUkI,GAAgB1c,KAAK9S,MACzD,EAEDmtB,iBACuB,MAAjBntB,KAAKusB,WACPvsB,KAAK0vB,wBAAyB,IAAIE,IAAcnvB,IAC9C,IAAI4uB,GAAW,IAAI5lB,GAAOzJ,QAGhC,KAOJP,EAAOgsB,GAAQ,CACbxrB,KAAKqE,EAAGC,GACN,OAAOvE,KAAKywB,UAAU,OAAQnsB,EAAGC,EAClC,EAGDhB,IAAIxJ,EAAGwK,GACL,OAAOvE,KAAKywB,UAAU,MAAO12B,EAAGwK,EACjC,EAEDksB,UAAU3c,EAAM4c,EAAajtB,GAC3B,GAA2B,iBAAhBitB,EACT,OAAO1wB,KAAKywB,UAAU3c,EAAM,CAAE4c,CAACA,GAAcjtB,IAG/C,IAAIwO,EAAQye,EACZ,GAAI1wB,KAAKmvB,aAAarb,EAAM7B,GAAQ,OAAOjS,KAE3C,IAAI+uB,EAAU,IAAI/K,GAAUhkB,KAAKikB,UAAUrH,GAAG3K,GAC1C7V,EAAOxD,OAAOwD,KAAK6V,GA4CvB,OA1CAjS,KAAKktB,OACH,WACE6B,EAAUA,EAAQxS,KAAKvc,KAAKxF,UAAUsZ,GAAM1X,GAC7C,IACD,SAAU0iB,GAER,OADA9e,KAAKxF,UAAUsZ,GAAMib,EAAQxK,GAAGzF,GAAK3iB,WAC9B4yB,EAAQvP,MAChB,IACD,SAAUmR,GAER,MAAMC,EAAUh4B,OAAOwD,KAAKu0B,GACtBE,GAlCSxrB,EAkCyBjJ,EAATw0B,EAlCRn3B,QAAQ2B,IAAOiK,EAAE7J,SAASJ,MAAtC01B,IAAIzrB,EAqCf,GAAIwrB,EAAYt3B,OAAQ,CAEtB,MAAMw3B,EAAiB/wB,KAAKxF,UAAUsZ,GAAM+c,GAGtCG,EAAe,IAAIjN,GAAUgL,EAAQxS,QAAQpgB,UAGnDvD,OAAOE,OAAOk4B,EAAcD,GAC5BhC,EAAQxS,KAAKyU,EACf,CAGA,MAAMC,EAAa,IAAIlN,GAAUgL,EAAQnS,MAAMzgB,UAG/CvD,OAAOE,OAAOm4B,EAAYN,GAG1B5B,EAAQnS,GAAGqU,GAGX70B,EAAOw0B,EACP3e,EAAQ0e,CACV,IAGF3wB,KAAK6uB,iBAAiB/a,EAAMib,GACrB/uB,IACR,EAED2Q,KAAKC,EAAOC,GACV,GAAI7Q,KAAKmvB,aAAa,OAAQve,EAAOC,GAAQ,OAAO7Q,KAEpD,IAAI+uB,EAAU,IAAI/K,GAAUhkB,KAAKikB,UAAUrH,GAAG,IAAI/H,GAAUjE,IAiB5D,OAfA5Q,KAAKktB,OACH,WACE6B,EAAUA,EAAQxS,KAAKvc,KAAKxF,UAAUmW,OACvC,IACD,SAAUmO,GAER,OADA9e,KAAKxF,UAAUmW,KAAKoe,EAAQxK,GAAGzF,GAAMjO,GAC9Bke,EAAQvP,MACjB,IACA,SAAU0R,EAAUC,GAClBtgB,EAAQsgB,EACRpC,EAAQnS,GAAGsU,EACb,IAGFlxB,KAAK6uB,iBAAiB,OAAQE,GACvB/uB,IACR,EAmBDuJ,UAAUjI,EAAYkK,EAAU4lB,GAG9B,GADA5lB,EAAWlK,EAAWkK,UAAYA,EAEhCxL,KAAK2rB,iBACJngB,GACDxL,KAAKmvB,aAAa,YAAa7tB,GAE/B,OAAOtB,KAIT,MAAMqxB,EAAW5nB,GAAOC,aAAapI,GACrC8vB,EACuB,MAArB9vB,EAAW8vB,OACP9vB,EAAW8vB,OACD,MAAVA,EACEA,GACCC,EAGT,MAAMtC,EAAU,IAAI/K,GAAUhkB,KAAKikB,UAAUnQ,KAC3Csd,EAASxM,GAAenb,IAG1B,IAAI1O,EACAP,EACA2d,EACAmZ,EACAC,EAoFJ,OAFAvxB,KAAKktB,OAhFL,WAEE1yB,EAAUA,GAAWwF,KAAKxF,UAC1BO,EAASA,GAAUF,EAAUyG,EAAY9G,GAEzC+2B,EAAiB,IAAI9nB,GAAO+B,OAAWgmB,EAAYh3B,GAGnDA,EAAQg2B,WAAWxwB,MAGdwL,GACHhR,EAAQ61B,6BAA6BrwB,KAEzC,IAEA,SAAa8e,GAGNtT,GAAUxL,KAAK8sB,iBAEpB,MAAM1xB,EAAEA,EAACC,EAAEA,GAAM,IAAI8N,GAAMpO,GAAQwO,UACjC/O,EAAQ+1B,kBAAkBvwB,OAG5B,IAAI4f,EAAS,IAAInW,GAAO,IAAKnI,EAAYvG,OAAQ,CAACK,EAAGC,KACjD4sB,EAAQjoB,KAAK2rB,gBAAkBxT,EAAUA,EAAUoZ,EAEvD,GAAIH,EAAQ,CACVxR,EAASA,EAAOvT,UAAUjR,EAAGC,GAC7B4sB,EAAQA,EAAM5b,UAAUjR,EAAGC,GAG3B,MAAMo2B,EAAU7R,EAAOhV,OACjB8mB,EAAWzJ,EAAMrd,OAGjB+mB,EAAgB,CAACF,EAAU,IAAKA,EAASA,EAAU,KACnDG,EAAYD,EAAcz4B,KAAKoL,GAAM1K,KAAKkQ,IAAIxF,EAAIotB,KAClDG,EAAWj4B,KAAKwI,OAAOwvB,GACvBvxB,EAAQuxB,EAAU3uB,QAAQ4uB,GAChCjS,EAAOhV,OAAS+mB,EAActxB,EAChC,CAEImL,IAGG6lB,IACHzR,EAAOhV,OAAStJ,EAAWsJ,QAAU,GAEnC5K,KAAK2rB,gBAAkB2F,IACzBrJ,EAAMrd,OAAS0mB,IAInBvC,EAAQxS,KAAK0L,GACb8G,EAAQnS,GAAGgD,GAEX,MAAMkS,EAAmB/C,EAAQxK,GAAGzF,GAMpC,OALAwS,EAAeQ,EAAiBlnB,OAChCuN,EAAU,IAAI1O,GAAOqoB,GAErB9xB,KAAK0sB,aAAavU,GAClB3d,EAAQg2B,WAAWxwB,MACZ+uB,EAAQvP,MACjB,IAEA,SAAkBuS,IAGbA,EAAch3B,QAAU,UAAUsH,cAClCf,EAAWvG,QAAU,UAAUsH,aAEhCtH,EAASF,EAAUk3B,EAAev3B,IAIpC8G,EAAa,IAAKywB,EAAeh3B,SACnC,IAEiC,GACjCiF,KAAK2rB,gBAAkB3rB,KAAK6uB,iBAAiB,YAAaE,GACnD/uB,IACR,EAGD5E,EAAEA,GACA,OAAO4E,KAAKgyB,aAAa,IAAK52B,EAC/B,EAGDC,EAAEA,GACA,OAAO2E,KAAKgyB,aAAa,IAAK32B,EAC/B,EAED42B,GAAG72B,GACD,OAAO4E,KAAKgyB,aAAa,KAAM52B,EAChC,EAED82B,GAAG72B,GACD,OAAO2E,KAAKgyB,aAAa,KAAM32B,EAChC,EAED4Q,GAAG7Q,EAAI,GACL,OAAO4E,KAAKmyB,kBAAkB,IAAK/2B,EACpC,EAED8Q,GAAG7Q,EAAI,GACL,OAAO2E,KAAKmyB,kBAAkB,IAAK92B,EACpC,EAEDqe,MAAMte,EAAGC,GACP,OAAO2E,KAAKiM,GAAG7Q,GAAG8Q,GAAG7Q,EACtB,EAED82B,kBAAkBrD,EAAQlS,GAIxB,GAHAA,EAAK,IAAI/H,GAAU+H,GAGf5c,KAAKmvB,aAAaL,EAAQlS,GAAK,OAAO5c,KAG1C,MAAM+uB,EAAU,IAAI/K,GAAUhkB,KAAKikB,UAAUrH,GAAGA,GAChD,IAAIL,EAAO,KAkBX,OAjBAvc,KAAKktB,OACH,WACE3Q,EAAOvc,KAAKxF,UAAUs0B,KACtBC,EAAQxS,KAAKA,GACbwS,EAAQnS,GAAGL,EAAOK,EACnB,IACD,SAAUkC,GAER,OADA9e,KAAKxF,UAAUs0B,GAAQC,EAAQxK,GAAGzF,IAC3BiQ,EAAQvP,MAChB,IACD,SAAU4S,GACRrD,EAAQnS,GAAGL,EAAO,IAAI1H,GAAUud,GAClC,IAIFpyB,KAAK6uB,iBAAiBC,EAAQC,GACvB/uB,IACR,EAEDqyB,aAAavD,EAAQlS,GAEnB,GAAI5c,KAAKmvB,aAAaL,EAAQlS,GAAK,OAAO5c,KAG1C,MAAM+uB,EAAU,IAAI/K,GAAUhkB,KAAKikB,UAAUrH,GAAGA,GAahD,OAZA5c,KAAKktB,OACH,WACE6B,EAAQxS,KAAKvc,KAAKxF,UAAUs0B,KAC7B,IACD,SAAUhQ,GAER,OADA9e,KAAKxF,UAAUs0B,GAAQC,EAAQxK,GAAGzF,IAC3BiQ,EAAQvP,MACjB,IAIFxf,KAAK6uB,iBAAiBC,EAAQC,GACvB/uB,IACR,EAEDgyB,aAAalD,EAAQ9Z,GACnB,OAAOhV,KAAKqyB,aAAavD,EAAQ,IAAIja,GAAUG,GAChD,EAGDnJ,GAAGzQ,GACD,OAAO4E,KAAKgyB,aAAa,KAAM52B,EAChC,EAGD0Q,GAAGzQ,GACD,OAAO2E,KAAKgyB,aAAa,KAAM32B,EAChC,EAGDse,KAAKve,EAAGC,GACN,OAAO2E,KAAK5E,EAAEA,GAAGC,EAAEA,EACpB,EAEDi3B,MAAMl3B,EAAGC,GACP,OAAO2E,KAAKiyB,GAAG72B,GAAG82B,GAAG72B,EACtB,EAGDme,OAAOpe,EAAGC,GACR,OAAO2E,KAAK6L,GAAGzQ,GAAG0Q,GAAGzQ,EACtB,EAGD0T,KAAKtU,EAAOC,GAEV,IAAIC,EAcJ,OAZKF,GAAUC,IACbC,EAAMqF,KAAKkb,SAAStgB,QAGjBH,IACHA,EAASE,EAAIF,MAAQE,EAAID,OAAUA,GAGhCA,IACHA,EAAUC,EAAID,OAASC,EAAIF,MAASA,GAG/BuF,KAAKvF,MAAMA,GAAOC,OAAOA,EACjC,EAGDD,MAAMA,GACJ,OAAOuF,KAAKgyB,aAAa,QAASv3B,EACnC,EAGDC,OAAOA,GACL,OAAOsF,KAAKgyB,aAAa,SAAUt3B,EACpC,EAGD2jB,KAAK/Z,EAAGe,EAAGhC,EAAG1J,GAEZ,GAAyB,IAArBgK,UAAUpK,OACZ,OAAOyG,KAAKqe,KAAK,CAAC/Z,EAAGe,EAAGhC,EAAG1J,IAG7B,GAAIqG,KAAKmvB,aAAa,OAAQ7qB,GAAI,OAAOtE,KAEzC,MAAM+uB,EAAU,IAAI/K,GAAUhkB,KAAKikB,UAChCnQ,KAAK9T,KAAKkb,SAAS0K,YACnBhJ,GAAGtY,GAaN,OAXAtE,KAAKktB,OACH,WACE6B,EAAQxS,KAAKvc,KAAKkb,SAAS/hB,QAC5B,IACD,SAAU2lB,GAER,OADA9e,KAAKkb,SAASmD,KAAK0Q,EAAQxK,GAAGzF,IACvBiQ,EAAQvP,MACjB,IAGFxf,KAAK6uB,iBAAiB,OAAQE,GACvB/uB,IACR,EAGDgZ,QAAQhE,GACN,OAAOhV,KAAKgyB,aAAa,UAAWhd,EACrC,EAGDtE,QAAQtV,EAAGC,EAAGZ,EAAOC,GACnB,OAAOsF,KAAKqyB,aAAa,UAAW,IAAI/iB,GAAIlU,EAAGC,EAAGZ,EAAOC,GAC1D,EAEDsiB,OAAOliB,GACL,MAAiB,iBAANA,EACFkF,KAAKgd,OAAO,CACjBtI,OAAQ/Q,UAAU,GAClByB,MAAOzB,UAAU,GACjB8Q,QAAS9Q,UAAU,MAIN,MAAb7I,EAAE2Z,SAAiBzU,KAAKC,KAAK,eAAgBnF,EAAE2Z,SACpC,MAAX3Z,EAAEsK,OAAepF,KAAKC,KAAK,aAAcnF,EAAEsK,OAC/B,MAAZtK,EAAE4Z,QAAgB1U,KAAKC,KAAK,SAAUnF,EAAE4Z,QAErC1U,KACT,IAGFP,EAAOgsB,GAAQ,CAAEhgB,MAAIE,MAAI4Q,QAAMK,QAC/B5d,EAASysB,GAAQ,UChjCF,MAAM8G,YAAYxW,UAC/Bjc,YAAYzD,EAAM4V,EAAQ5V,GACxBkV,MAAM/S,EAAU,MAAOnC,GAAO4V,GAC9BjS,KAAKqT,WACP,CAGAoG,OACE,OAAKzZ,KAAKsa,SAEH1b,EAAMoB,KAAK3D,KAAK6B,cAAc,UAAY8B,KAAKgW,IAAI,IAAIkG,MAFnClc,KAAKrC,OAAO8b,MAGzC,CAEAa,SACE,OACGta,KAAK3D,KAAK4S,cACRjP,KAAK3D,KAAK4S,sBAAsBlS,EAAQC,OAAO6Y,aACd,uBAAlC7V,KAAK3D,KAAK4S,WAAWpT,QAE3B,CAGAwX,YACE,OAAKrT,KAAKsa,SACHta,KAAKC,KAAK,CAAEpD,MAAOF,EAAK61B,QAAS,QAASvyB,KAC/C,cACAnD,EACAD,GAJyBmD,KAAKrC,OAAO0V,WAMzC,CAEAuC,kBACE,OAAO5V,KAAKC,KAAK,CAAEpD,MAAO,KAAM21B,QAAS,OACtCvyB,KAAK,cAAe,KAAMpD,GAC1BoD,KAAK,cAAe,KAAMpD,EAC/B,CAIAc,OACE,OAAIqC,KAAKsa,SAAiBta,KACnBuR,MAAM5T,MACf,EAGFtF,EAAgB,CACd0jB,UAAW,CAET0W,OAAQ9yB,GAAkB,WACxB,OAAOK,KAAKgW,IAAI,IAAIuc,WAK1BvzB,EAASuzB,IAAK,OAAO,GC9DN,MAAMG,eAAe3W,UAElCjc,YAAYzD,EAAM4V,EAAQ5V,GACxBkV,MAAM/S,EAAU,SAAUnC,GAAO4V,EACnC,EAGF5Z,EAAgB,CACd0jB,UAAW,CACT4W,OAAQhzB,GAAkB,WACxB,OAAOK,KAAKgW,IAAI,IAAI0c,cAK1B1zB,EAAS0zB,OAAQ,uCCuDV,SAAet3B,EAAGC,GACvB,OAAO2E,KAAKiyB,GAAG72B,GAAG82B,GAAG72B,EACvB,KAVO,SAAYD,GACjB,OAAO4E,KAAKC,KAAK,IAAK7E,EACxB,KAEO,SAAYC,GACjB,OAAO2E,KAAKC,KAAK,IAAK5E,EACxB,QAOO,SAAeu3B,GAEpB,OADA5yB,KAAK6yB,SAAWD,EACT5yB,IACT,SApBO,SAAgB5E,EAAGC,EAAGV,EAAMqF,KAAKpF,QACtC,OAAOoF,KAAK6L,GAAGzQ,EAAGT,GAAKmR,GAAGzQ,EAAGV,EAC/B,KAnBO,SAAYS,EAAGT,EAAMqF,KAAKpF,QAC/B,OAAS,MAALQ,EACKT,EAAIkR,GAGN7L,KAAKC,KAAK,IAAKD,KAAKC,KAAK,KAAO7E,EAAIT,EAAIkR,GACjD,KAGO,SAAYxQ,EAAGV,EAAMqF,KAAKpF,QAC/B,OAAS,MAALS,EACKV,EAAImR,GAGN9L,KAAKC,KAAK,IAAKD,KAAKC,KAAK,KAAO5E,EAAIV,EAAImR,GACjD,SA5CO,WACL,OAAO9L,KAAK3D,KAAKy2B,uBACnB,OAsBO,SAAc13B,EAAGC,EAAGV,EAAMqF,KAAKpF,QACpC,OAAOoF,KAAK5E,EAAEA,EAAGT,GAAKU,EAAEA,EAAGV,EAC7B,QAvCO,SAAemd,GASpB,OAPoB,IAAhB9X,KAAK6yB,QACP7yB,KAAKiW,QAIPjW,KAAK3D,KAAKyZ,YAAY/Y,EAAQE,SAAS81B,eAAejb,IAE/C9X,IACT,IAUO,SAAW5E,EAAGT,EAAMqF,KAAKpF,QAC9B,OAAS,MAALQ,EACKT,EAAIS,EAGN4E,KAAKC,KAAK,IAAKD,KAAKC,KAAK,KAAO7E,EAAIT,EAAIS,EACjD,IAGO,SAAWC,EAAGV,EAAMqF,KAAKpF,QAC9B,OAAS,MAALS,EACKV,EAAIU,EAGN2E,KAAKC,KAAK,IAAKD,KAAKC,KAAK,KAAO5E,EAAIV,EAAIU,EACjD,GCxBe,MAAM23B,aAAa7W,MAEhCrc,YAAYzD,EAAM4V,EAAQ5V,GACxBkV,MAAM/S,EAAU,OAAQnC,GAAO4V,GAE/BjS,KAAKqZ,IAAIL,QAAUhZ,KAAKqZ,IAAIL,SAAW,IAAInE,GAAU,KACrD7U,KAAKizB,UAAW,EAChBjzB,KAAK6yB,QAAS,CAChB,CAGA7Z,QAAQhE,GAEN,OAAa,MAATA,EACKhV,KAAKqZ,IAAIL,SAIlBhZ,KAAKqZ,IAAIL,QAAU,IAAInE,GAAUG,GAE1BhV,KAAKkZ,UACd,CAGAA,QAAQA,GAON,GALuB,kBAAZA,IACTlZ,KAAKizB,SAAW/Z,GAIdlZ,KAAKizB,SAAU,CACjB,MAAMC,EAAOlzB,KACb,IAAImzB,EAAkB,EACtB,MAAMna,EAAUhZ,KAAKqZ,IAAIL,QAEzBhZ,KAAKwR,MAAK,SAAUnY,GAClB,GAAIsC,EAAcqE,KAAK3D,MAAO,OAE9B,MAAM+2B,EAAWr2B,EAAQC,OACtBq2B,iBAAiBrzB,KAAK3D,MACtB2H,iBAAiB,aAEdkI,EAAK8M,EAAU,IAAInE,GAAUue,GAE/BpzB,KAAKqZ,IAAIia,WACXtzB,KAAKC,KAAK,IAAKizB,EAAKjzB,KAAK,MAEL,OAAhBD,KAAK8X,OACPqb,GAAmBjnB,GAEnBlM,KAAKC,KAAK,KAAM5G,EAAI6S,EAAKinB,EAAkB,GAC3CA,EAAkB,GAGxB,IAEAnzB,KAAKiU,KAAK,UACZ,CAEA,OAAOjU,IACT,CAGAuZ,QAAQze,GAGN,OAFAkF,KAAKqZ,IAAMve,EACXkF,KAAKqZ,IAAIL,QAAU,IAAInE,GAAU/Z,EAAEke,SAAW,KACvChZ,IACT,CAEAlE,iBAEE,OADAA,EAAekE,KAAMA,KAAKqZ,IAAK,CAAEL,QAAS,MACnChZ,IACT,CAGA8X,KAAKA,GAEH,QAAa0Z,IAAT1Z,EAAoB,CACtB,MAAMvY,EAAWS,KAAK3D,KAAK0Z,WAC3B,IAAIwd,EAAY,EAChBzb,EAAO,GAEP,IAAK,IAAIze,EAAI,EAAGmf,EAAMjZ,EAAShG,OAAQF,EAAImf,IAAOnf,EAEnB,aAAzBkG,EAASlG,GAAGwC,UAA2BF,EAAc4D,EAASlG,IACtD,IAANA,IAASk6B,EAAYl6B,EAAI,IAM7BA,IAAMk6B,GACmB,IAAzBh0B,EAASlG,GAAGm6B,WACwB,IAApC50B,EAAMW,EAASlG,IAAIggB,IAAIia,WAEvBxb,GAAQ,MAIVA,GAAQvY,EAASlG,GAAG0e,aAGtB,OAAOD,CACT,CAKA,GAFA9X,KAAKiW,QAAQ2c,OAAM,GAEC,mBAAT9a,EAETA,EAAKvK,KAAKvN,KAAMA,WAMhB,IAAK,IAAI+T,EAAI,EAAGiO,GAHhBlK,GAAQA,EAAO,IAAI/U,MAAM,OAGCxJ,OAAQwa,EAAIiO,EAAIjO,IACxC/T,KAAKyzB,QAAQ3b,EAAK/D,IAKtB,OAAO/T,KAAK4yB,OAAM,GAAO1Z,SAC3B,EAGFzZ,EAAOuzB,KAAMU,IAEbr7B,EAAgB,CACd0jB,UAAW,CAETjE,KAAMnY,GAAkB,SAAUmY,EAAO,IACvC,OAAO9X,KAAKgW,IAAI,IAAIgd,MAAQlb,KAAKA,EACnC,IAGA6b,MAAOh0B,GAAkB,SAAUmY,EAAO,IACxC,OAAO9X,KAAKgW,IAAI,IAAIgd,MAAQW,MAAM7b,SAKxC9Y,EAASg0B,KAAM,QChJA,MAAMY,cAAczX,MAEjCrc,YAAYzD,EAAM4V,EAAQ5V,GACxBkV,MAAM/S,EAAU,QAASnC,GAAO4V,GAChCjS,KAAK6yB,QAAS,CAChB,CAGA5mB,GAAGA,GACD,OAAOjM,KAAKC,KAAK,KAAMgM,EACzB,CAGAC,GAAGA,GACD,OAAOlM,KAAKC,KAAK,KAAMiM,EACzB,CAGAunB,UAEEzzB,KAAKqZ,IAAIia,UAAW,EAGpB,MAAMxb,EAAO9X,KAAKG,SAGlB,KAAM2X,aAAgBkb,MACpB,OAAOhzB,KAGT,MAAM3G,EAAIye,EAAKzX,MAAML,MAEfozB,EAAWr2B,EAAQC,OACtBq2B,iBAAiBrzB,KAAK3D,MACtB2H,iBAAiB,aACdkI,EAAK4L,EAAKuB,IAAIL,QAAU,IAAInE,GAAUue,GAG5C,OAAOpzB,KAAKkM,GAAG7S,EAAI6S,EAAK,GAAGjM,KAAK,IAAK6X,EAAK1c,IAC5C,CAGA0c,KAAKA,GACH,OAAY,MAARA,EACK9X,KAAK3D,KAAK0b,aAAe/X,KAAKqZ,IAAIia,SAAW,KAAO,KAEzC,mBAATxb,GACT9X,KAAKiW,QAAQ2c,OAAM,GACnB9a,EAAKvK,KAAKvN,KAAMA,MAChBA,KAAK4yB,OAAM,IAEX5yB,KAAK2zB,MAAM7b,GAGN9X,KACT,EAGFP,EAAOm0B,MAAOF,IAEdr7B,EAAgB,CACdu7B,MAAO,CACLC,MAAOl0B,GAAkB,SAAUmY,EAAO,IACxC,MAAM+b,EAAQ,IAAID,MAQlB,OALK5zB,KAAK6yB,QACR7yB,KAAKiW,QAIAjW,KAAKgW,IAAI6d,GAAO/b,KAAKA,OAGhCkb,KAAM,CACJS,QAAS,SAAU3b,EAAO,IACxB,OAAO9X,KAAK6zB,MAAM/b,GAAM2b,SAC1B,KAIJz0B,EAAS40B,MAAO,SCnFD,MAAME,eAAe3X,MAClCrc,YAAYzD,EAAM4V,EAAQ5V,GACxBkV,MAAM/S,EAAU,SAAUnC,GAAO4V,EACnC,CAEAgJ,OAAOve,GACL,OAAOsD,KAAKC,KAAK,IAAKvD,EACxB,CAGA+O,GAAGA,GACD,OAAOzL,KAAKC,KAAK,IAAKwL,EACxB,CAGAE,GAAGA,GACD,OAAO3L,KAAKyL,GAAGE,EACjB,CAEAoD,KAAKA,GACH,OAAO/O,KAAKib,OAAO,IAAIpG,GAAU9F,GAAMkG,OAAO,GAChD,EAGFxV,EAAOq0B,OAAQ,GAAE14B,KAAGC,MAAGwQ,MAAIC,SAAIrR,GAAOC,OAAAA,KAEtCrC,EAAgB,CACd0jB,UAAW,CAETgY,OAAQp0B,GAAkB,SAAUoP,EAAO,GACzC,OAAO/O,KAAKgW,IAAI,IAAI8d,QAAU/kB,KAAKA,GAAM4K,KAAK,EAAG,SAKvD3a,EAAS80B,OAAQ,UCzCF,MAAME,iBAAiBjY,UACpCjc,YAAYzD,EAAM4V,EAAQ5V,GACxBkV,MAAM/S,EAAU,WAAYnC,GAAO4V,EACrC,CAGAvR,SAOE,OALAV,KAAK8c,UAAUhZ,SAAQ,SAAUD,GAC/BA,EAAGowB,QACL,IAGO1iB,MAAM7Q,QACf,CAEAoc,UACE,OAAOlL,GAAS,mBAAqB5R,KAAKR,KAAO,IACnD,EAGFnH,EAAgB,CACd0jB,UAAW,CAETmY,KAAMv0B,GAAkB,WACtB,OAAOK,KAAKyZ,OAAOzD,IAAI,IAAIge,cAG/B5mB,QAAS,CAEP+mB,UACE,OAAOn0B,KAAKqB,UAAU,YACvB,EAED+yB,SAAS55B,GAEP,MAAM25B,EACJ35B,aAAmBw5B,SACfx5B,EACAwF,KAAKG,SAAS+zB,OAAOzzB,IAAIjG,GAG/B,OAAOwF,KAAKC,KAAK,YAAa,QAAUk0B,EAAQ30B,KAAO,IACxD,EAGDy0B,SACE,OAAOj0B,KAAKC,KAAK,YAAa,KAChC,KAIJjB,EAASg1B,SAAU,YCrDJ,MAAMK,WAAsBjnB,QACzCtN,YAAYzD,EAAM4V,EAAQ5V,GACxBkV,MAAM/S,EAAU,gBAAiBnC,GAAO4V,EAC1C,EAGF5Z,EAAgB,CACd0jB,UAAW,CACTuY,cAAe30B,GAAkB,SAAUlF,EAAOC,GAChD,OAAOsF,KAAKgW,IAAI,IAAIqe,IAAiBtlB,KAAKtU,EAAOC,SAKvDsE,EAASq1B,GAAe,8CCZjB,SAAepoB,EAAIC,GAgCxB,OA/BAlM,KAAKT,WAAWuE,SAASywB,IACvB,IAAI35B,EAIJ,IAOEA,EACE25B,EAAMl4B,gBAAgBmB,IAAYg3B,cAC9B,IAAIllB,GAAIilB,EAAMt0B,KAAK,CAAC,IAAK,IAAK,QAAS,YACvCs0B,EAAM35B,MACb,CAAC,MAAO8J,GACP,MACF,CAGA,MAAMnM,EAAI,IAAIkR,GAAO8qB,GAGfxoB,EAASxT,EAAE4S,UAAUc,EAAIC,GAAI3C,UAAUhR,EAAEiV,WAEzC9K,EAAI,IAAIyG,GAAMvO,EAAKQ,EAAGR,EAAKS,GAAGkO,UAAUwC,GAE9CwoB,EAAM5a,KAAKjX,EAAEtH,EAAGsH,EAAErH,EAAE,IAGf2E,IACT,KAEO,SAAYiM,GACjB,OAAOjM,KAAK0Z,MAAMzN,EAAI,EACxB,KAEO,SAAYC,GACjB,OAAOlM,KAAK0Z,MAAM,EAAGxN,EACvB,SAEO,SAAgBxR,EAAQC,EAAMqF,KAAKpF,QACxC,OAAc,MAAVF,EAAuBC,EAAID,OACxBsF,KAAK+O,KAAKpU,EAAIF,MAAOC,EAAQC,EACtC,OAEO,SAAcS,EAAI,EAAGC,EAAI,EAAGV,EAAMqF,KAAKpF,QAC5C,MAAMqR,EAAK7Q,EAAIT,EAAIS,EACb8Q,EAAK7Q,EAAIV,EAAIU,EAEnB,OAAO2E,KAAK0Z,MAAMzN,EAAIC,EACxB,OAEO,SAAczR,EAAOC,EAAQC,EAAMqF,KAAKpF,QAC7C,MAAM8H,EAAInI,EAAiByF,KAAMvF,EAAOC,EAAQC,GAC1C4P,EAAS7H,EAAEjI,MAAQE,EAAIF,MACvBgQ,EAAS/H,EAAEhI,OAASC,EAAID,OAO9B,OALAsF,KAAKT,WAAWuE,SAASywB,IACvB,MAAMz5B,EAAI,IAAIqO,GAAMxO,GAAK4O,UAAU,IAAIE,GAAO8qB,GAAO/mB,WACrD+mB,EAAM/pB,MAAMD,EAAQE,EAAQ3P,EAAEM,EAAGN,EAAEO,EAAE,IAGhC2E,IACT,QAEO,SAAevF,EAAOE,EAAMqF,KAAKpF,QACtC,OAAa,MAATH,EAAsBE,EAAIF,MACvBuF,KAAK+O,KAAKtU,EAAOE,EAAID,OAAQC,EACtC,IAEO,SAAWS,EAAGT,EAAMqF,KAAKpF,QAC9B,OAAS,MAALQ,EAAkBT,EAAIS,EACnB4E,KAAK2Z,KAAKve,EAAGT,EAAIU,EAAGV,EAC7B,IAEO,SAAWU,EAAGV,EAAMqF,KAAKpF,QAC9B,OAAS,MAALS,EAAkBV,EAAIU,EACnB2E,KAAK2Z,KAAKhf,EAAIS,EAAGC,EAAGV,EAC7B,GC7Ee,MAAM85B,UAAU1Y,UAC7Bjc,YAAYzD,EAAM4V,EAAQ5V,GACxBkV,MAAM/S,EAAU,IAAKnC,GAAO4V,EAC9B,EAGFxS,EAAOg1B,EAAGC,IAEVr8B,EAAgB,CACd0jB,UAAW,CAET4Y,MAAOh1B,GAAkB,WACvB,OAAOK,KAAKgW,IAAI,IAAIye,SAK1Bz1B,EAASy1B,EAAG,KChBG,MAAM9S,UAAU5F,UAC7Bjc,YAAYzD,EAAM4V,EAAQ5V,GACxBkV,MAAM/S,EAAU,IAAKnC,GAAO4V,EAC9B,CAGA2N,OAAOA,GACL,OAAO5f,KAAKC,KAAK,SAAU2f,EAC7B,CAGAhD,GAAGG,GACD,OAAO/c,KAAKC,KAAK,OAAQ8c,EAAKjgB,EAChC,EAGF2C,EAAOkiB,EAAG+S,IAEVr8B,EAAgB,CACd0jB,UAAW,CAET6Y,KAAMj1B,GAAkB,SAAUod,GAChC,OAAO/c,KAAKgW,IAAI,IAAI2L,GAAK/E,GAAGG,OAGhC3P,QAAS,CACPynB,SACE,MAAMD,EAAO50B,KAAK80B,SAElB,IAAKF,EAAM,OAAO50B,KAElB,MAAMG,EAASy0B,EAAKz0B,SAEpB,IAAKA,EACH,OAAOH,KAAKU,SAGd,MAAML,EAAQF,EAAOE,MAAMu0B,GAI3B,OAHAz0B,EAAOM,IAAIT,KAAMK,GAEjBu0B,EAAKl0B,SACEV,IACR,EACD+0B,OAAOhY,GAEL,IAAI6X,EAAO50B,KAAK80B,SAahB,OAXKF,IACHA,EAAO,IAAIjT,EACX3hB,KAAKgY,KAAK4c,IAGO,mBAAR7X,EACTA,EAAIxP,KAAKqnB,EAAMA,GAEfA,EAAKhY,GAAGG,GAGH/c,IACR,EACD80B,SACE,MAAMF,EAAO50B,KAAKG,SAClB,OAAIy0B,GAA6C,MAArCA,EAAKv4B,KAAKR,SAAS3B,cACtB06B,EAGF,IACT,KAIJ51B,EAAS2iB,EAAG,KC7EG,MAAMqT,aAAajZ,UAEhCjc,YAAYzD,EAAM4V,EAAQ5V,GACxBkV,MAAM/S,EAAU,OAAQnC,GAAO4V,EACjC,CAGAvR,SAOE,OALAV,KAAK8c,UAAUhZ,SAAQ,SAAUD,GAC/BA,EAAGoxB,QACL,IAGO1jB,MAAM7Q,QACf,CAEAoc,UACE,OAAOlL,GAAS,cAAgB5R,KAAKR,KAAO,IAC9C,EAGFnH,EAAgB,CACd0jB,UAAW,CACTmZ,KAAMv1B,GAAkB,WACtB,OAAOK,KAAKyZ,OAAOzD,IAAI,IAAIgf,UAG/B5nB,QAAS,CAEP+nB,SACE,OAAOn1B,KAAKqB,UAAU,OACvB,EAED+zB,SAAS56B,GAEP,MAAM26B,EACJ36B,aAAmBw6B,KAAOx6B,EAAUwF,KAAKG,SAAS+0B,OAAOz0B,IAAIjG,GAG/D,OAAOwF,KAAKC,KAAK,OAAQ,QAAUk1B,EAAO31B,KAAO,IAClD,EAGDy1B,SACE,OAAOj1B,KAAKC,KAAK,OAAQ,KAC3B,KAIJjB,EAASg2B,KAAM,QClDA,MAAMK,aAAajoB,QAChCtN,YAAYzD,EAAM4V,EAAQ5V,GACxBkV,MAAM/S,EAAU,OAAQnC,GAAO4V,EACjC,CAGA+K,OAAOliB,GAcL,OAbiB,iBAANA,GAAkBA,aAAa+Z,MACxC/Z,EAAI,CACF4Z,OAAQ/Q,UAAU,GAClByB,MAAOzB,UAAU,GACjB8Q,QAAS9Q,UAAU,KAKN,MAAb7I,EAAE2Z,SAAiBzU,KAAKC,KAAK,eAAgBnF,EAAE2Z,SACpC,MAAX3Z,EAAEsK,OAAepF,KAAKC,KAAK,aAAcnF,EAAEsK,OAC/B,MAAZtK,EAAE4Z,QAAgB1U,KAAKC,KAAK,SAAU,IAAI4U,GAAU/Z,EAAE4Z,SAEnD1U,IACT,EAGF3H,EAAgB,CACdwkB,SAAU,CAER8N,KAAM,SAAUjW,EAAQtP,EAAOqP,GAC7B,OAAOzU,KAAKgW,IAAI,IAAIqf,MAAQrY,OAAOtI,EAAQtP,EAAOqP,EACpD,KAIJzV,EAASq2B,KAAM,QClBA,MAAMC,cAAcloB,QACjCtN,YAAYzD,EAAM4V,EAAQ5V,GACxBkV,MAAM/S,EAAU,QAASnC,GAAO4V,EAClC,CAEAsjB,QAAQ3lB,EAAI,IAEV,OADA5P,KAAK3D,KAAK0b,aAAenI,EAClB5P,IACT,CAEAsb,KAAKhjB,EAAMolB,EAAKnX,EAAS,CAAA,GACvB,OAAOvG,KAAKw1B,KAAK,aAAc,CAC7BC,WAAYn9B,EACZolB,IAAKA,KACFnX,GAEP,CAEAivB,KAAKze,EAAU/E,GACb,OAAOhS,KAAKu1B,QAlChB,SAAiBxe,EAAUye,GACzB,IAAKze,EAAU,MAAO,GACtB,IAAKye,EAAM,OAAOze,EAElB,IAAIrT,EAAMqT,EAAW,IAErB,IAAK,MAAM1d,KAAKm8B,EACd9xB,GAAO5J,EAAYT,GAAK,IAAMm8B,EAAKn8B,GAAK,IAK1C,OAFAqK,GAAO,IAEAA,CACT,CAqBwBgyB,CAAQ3e,EAAU/E,GACxC,EAGF3Z,EAAgB,MAAO,CACrBmL,MAAMuT,EAAU/E,GACd,OAAOhS,KAAKgW,IAAI,IAAIsf,OAASE,KAAKze,EAAU/E,EAC7C,EACD2jB,SAASr9B,EAAMolB,EAAKnX,GAClB,OAAOvG,KAAKgW,IAAI,IAAIsf,OAASha,KAAKhjB,EAAMolB,EAAKnX,EAC/C,IAGFvH,EAASs2B,MAAO,SC5CD,MAAMM,iBAAiB5C,KAEpClzB,YAAYzD,EAAM4V,EAAQ5V,GACxBkV,MAAM/S,EAAU,WAAYnC,GAAO4V,EACrC,CAGA9Y,QACE,MAAM08B,EAAQ71B,KAAK61B,QAEnB,OAAOA,EAAQA,EAAM18B,QAAU,IACjC,CAGAklB,KAAK1kB,GACH,MAAMk8B,EAAQ71B,KAAK61B,QACnB,IAAIC,EAAY,KAMhB,OAJID,IACFC,EAAYD,EAAMxX,KAAK1kB,IAGb,MAALA,EAAYm8B,EAAY91B,IACjC,CAGA61B,QACE,OAAO71B,KAAKqB,UAAU,OACxB,EAGFhJ,EAAgB,CACd0jB,UAAW,CACTga,SAAUp2B,GAAkB,SAAUmY,EAAM9I,GAM1C,OAJM8I,aAAgBkb,OACpBlb,EAAO9X,KAAK8X,KAAKA,IAGZA,EAAK9I,KAAKA,OAGrBgkB,KAAM,CAEJhkB,KAAMrP,GAAkB,SAAUk2B,EAAOG,GAAc,GACrD,MAAMD,EAAW,IAAIH,SAYrB,IAAIv5B,EACJ,GAVMw5B,aAAiBnQ,OAErBmQ,EAAQ71B,KAAKyZ,OAAOzK,KAAK6mB,IAI3BE,EAAS91B,KAAK,OAAQ,IAAM41B,EAAO/4B,GAI/Bk5B,EACF,KAAQ35B,EAAO2D,KAAK3D,KAAKiC,YACvBy3B,EAAS15B,KAAKyZ,YAAYzZ,GAK9B,OAAO2D,KAAKgW,IAAI+f,EAClB,IAGAA,WACE,OAAO/1B,KAAKoZ,QAAQ,WACtB,GAEFsM,KAAM,CAEJ5N,KAAMnY,GAAkB,SAAUmY,GAOhC,OALMA,aAAgBkb,OACpBlb,GAAO,IAAIkb,MAAO5jB,MAAMpP,KAAKG,UAAU2X,KAAKA,IAIvCA,EAAK9I,KAAKhP,KACnB,IAEA8c,UACE,OAAOlL,GAAS,gBAAgBnY,QAAQ4C,IAC9BA,EAAK4D,KAAK,SAAW,IAAIzE,SAASwE,KAAKR,OAKnD,KAIJo2B,SAAS12B,UAAU0mB,WAAapC,GAChCxkB,EAAS42B,SAAU,YCpGJ,MAAMK,YAAY9Z,MAC/Brc,YAAYzD,EAAM4V,EAAQ5V,GACxBkV,MAAM/S,EAAU,MAAOnC,GAAO4V,EAChC,CAGAikB,IAAI17B,EAAS27B,GAEX,OAAOn2B,KAAKC,KAAK,QAASk2B,GAAQ,IAAM,IAAM37B,EAASsC,EACzD,EAGFzE,EAAgB,CACd0jB,UAAW,CAETma,IAAKv2B,GAAkB,SAAUnF,EAAS27B,GACxC,OAAOn2B,KAAKgW,IAAI,IAAIigB,KAAOC,IAAI17B,EAAS27B,SAK9Cn3B,EAASi3B,IAAK,OCsCP,MAAMG,GAAMr4B,EAsEnB0B,EAAO,CAAC8yB,IAAKG,OAAQpV,MAAOH,QAASqB,QAASzlB,EAAc,YAE5D0G,EAAO,CAAC2e,KAAM4H,SAAUH,QAASH,MAAO3sB,EAAc,WAEtD0G,EAAOuzB,KAAMj6B,EAAc,SAC3B0G,EAAOimB,KAAM3sB,EAAc,SAE3B0G,EAAOyc,KAAMnjB,EAAc,SAE3B0G,EAAO,CAACuzB,KAAMY,OAAQ76B,EAAc,UAEpC0G,EAAO,CAACymB,KAAM9J,QAASS,SAAU4O,IAAS1yB,EAAc,WAExD0G,EAAOoU,GAAa9a,EAAc,gBAClC0G,EAAOkW,IAAK5c,EAAc,QAC1B0G,EAAO2N,QAASrU,EAAc,YAC9B0G,EAAO0c,MAAOpjB,EAAc,UAC5B0G,EAAO,CAACsc,UAAWjd,IAAW/F,EAAc,cAC5C0G,EAAOod,SAAU9jB,EAAc,aAE/B0G,EAAOgsB,GAAQ1yB,EAAc,WAE7BsY,GAAK5R,OxEjII,IAAI,IAAI/D,IAAItD,KwEmIrBmtB,GAAsB,CACpB1Q,GACA7P,GACAsK,GACA7F,GACAkL,GACAiJ,GACA4F,GACAra,KAGFqc,u1BnExFO,SAAmB6Q,EAAOz3B,GAC/BX,EAAUo4B,CACZ,uLF7DO,SAAoBl5B,EAAKyC,GAC9BtC,IACAJ,EAAeC,EAAKA,EAAIF,UACxB2C,EAAGzC,EAAKA,EAAIF,UACZM,GACF,uBsEvBe,SAAS64B,GAAI57B,EAASwD,GACnC,OAAOD,EAAavD,EAASwD,EAC/B,QAEApF,OAAOE,OAAOs9B,GAAKE"}
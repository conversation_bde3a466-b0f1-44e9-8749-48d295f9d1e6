{"name": "@svgdotjs/svg.js", "version": "3.2.4", "type": "module", "description": "A lightweight library for manipulating and animating SVG.", "url": "https://svgjs.dev/", "homepage": "https://svgjs.dev/", "keywords": ["svg", "vector", "graphics", "animation"], "author": "Wout <PERSON> <<EMAIL>>", "main": "dist/svg.node.cjs", "unpkg": "dist/svg.min.js", "jsdelivr": "dist/svg.min.js", "browser": "dist/svg.esm.js", "module": "src/main.js", "exports": {".": {"import": {"types": "./svg.js.d.ts", "default": "./src/main.js"}, "require": {"types": "./svg.js.d.ts", "default": "./dist/svg.node.cjs"}}}, "files": ["/dist", "/src", "/svg.js.d.ts", "/.config"], "maintainers": [{"name": "Wout <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "web": "http://www.ewerlof.name"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "web": "https://svgdotjs.github.io/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://keybase.io/dotnetcarpenter"}], "licenses": [{"type": "MIT", "url": "http://www.opensource.org/licenses/mit-license.php"}], "repository": {"type": "git", "url": "https://github.com/svgdotjs/svg.js.git"}, "github": "https://github.com/svgdotjs/svg.js", "license": "MIT", "funding": {"type": "github", "url": "https://github.com/sponsors/Fuzzyma"}, "typings": "./svg.js.d.ts", "scripts": {"build": "npm run format && npm run rollup", "build:polyfills": "npx rollup -c .config/rollup.polyfills.js", "build:tests": "npx rollup -c .config/rollup.tests.js", "fix": "npx eslint ./src --fix", "lint": "npx eslint ./src", "prettier": "npx prettier --write .", "format": "npm run fix && npm run prettier", "rollup": "npx rollup -c .config/rollup.config.js", "server": "npx http-server ./ -d", "test": "npx karma start .config/karma.conf.cjs || true", "test:ci": "karma start .config/karma.conf.saucelabs.cjs", "test:svgdom": "node ./spec/runSVGDomTest.js || true", "zip": "zip -j dist/svg.js.zip -- LICENSE.txt README.md CHANGELOG.md dist/svg.js dist/svg.js.map dist/svg.min.js dist/svg.min.js.map dist/polyfills.js dist/polyfillsIE.js", "prepublishOnly": "rm -rf ./dist && npm run build && npm run build:polyfills && npm test", "postpublish": "npm run zip", "checkTests": "node spec/checkForAllTests.js"}, "devDependencies": {"@babel/core": "^7.24.7", "@babel/eslint-parser": "^7.24.7", "@babel/plugin-transform-runtime": "^7.24.7", "@babel/preset-env": "^7.24.7", "@rollup/plugin-babel": "^6.0.4", "@rollup/plugin-commonjs": "^26.0.1", "@rollup/plugin-node-resolve": "^15.2.3", "@rollup/plugin-terser": "^0.4.4", "@target/custom-event-polyfill": "github:Adobe-Marketing-Cloud/custom-event-polyfill", "@types/jasmine": "^5.1.4", "babel-plugin-polyfill-corejs3": "^0.10.4", "core-js": "^3.37.1", "coveralls": "^3.1.1", "eslint": "^8.57.0", "eslint-config-prettier": "^9.1.0", "eslint-config-standard": "^17.1.0", "http-server": "^14.1.1", "jasmine": "^5.1.0", "jasmine-core": "^5.1.2", "karma": "^6.4.3", "karma-chrome-launcher": "^3.2.0", "karma-coverage": "^2.2.1", "karma-firefox-launcher": "^2.1.3", "karma-jasmine": "^5.1.0", "karma-sauce-launcher": "^4.3.6", "prettier": "^3.3.2", "rollup": "^4.18.0", "rollup-plugin-filesize": "^10.0.0", "svgdom": "^0.1.19", "typescript": "^5.4.5", "yargs": "^17.7.2"}, "browserslist": ">0.3%, last 2 version, not dead, not op_mini all"}
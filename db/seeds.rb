# This file should ensure the existence of records required to run the application in every environment (production,
# development, test). The code here should be idempotent so that it can be executed at any point in every environment.
# The data can then be loaded with the bin/rails db:seed command (or created alongside the database with db:setup).

puts "🌱 Seeding database..."

# Only create development/demo data in development environment
if Rails.env.development?
  puts "📈 Creating development data..."

  # Create subscription plans first
  subscription_plans = [
    {
      name: "Starter",
      stripe_product_id: "prod_starter",
      stripe_price_id: "price_starter_monthly",
      price_cents: 9900, # $99.00
      billing_cycle: "month",
      features: [
        "10 pipelines",
        "10,000 executions/month",
        "All connectors",
        "Email support",
        "Basic transformations"
      ].to_json,
      active: true
    },
    {
      name: "Professional",
      stripe_product_id: "prod_professional",
      stripe_price_id: "price_professional_monthly",
      price_cents: 29900, # $299.00
      billing_cycle: "month",
      features: [
        "50 pipelines",
        "100,000 executions/month",
        "All connectors",
        "Priority support",
        "Advanced transformations",
        "API access",
        "Custom webhooks"
      ].to_json,
      active: true
    },
    {
      name: "Enterprise",
      stripe_product_id: "prod_enterprise",
      stripe_price_id: "price_enterprise_monthly",
      price_cents: 59900, # $599.00
      billing_cycle: "month",
      features: [
        "Unlimited pipelines",
        "Unlimited executions",
        "All connectors",
        "Dedicated support",
        "Custom integrations",
        "SLA guarantee",
        "On-premise option",
        "White-label solution"
      ].to_json,
      active: true
    }
  ]

  subscription_plans.each do |plan_attrs|
    plan = SubscriptionPlan.find_or_initialize_by(name: plan_attrs[:name])
    plan.assign_attributes(plan_attrs)
    if plan.save
      puts "✅ Created/updated subscription plan: #{plan.name}"
    else
      puts "❌ Failed to create subscription plan: #{plan.name} - #{plan.errors.full_messages.join(', ')}"
    end
  end

  # Create demo accounts and users
  demo_accounts = [
    {
      name: "Acme Corp",
      subdomain: "acme",
      plan: "Professional",
      users: [
        { first_name: "John", last_name: "Smith", email: "<EMAIL>", role: "owner" },
        { first_name: "Sarah", last_name: "Johnson", email: "<EMAIL>", role: "admin" },
        { first_name: "Mike", last_name: "Davis", email: "<EMAIL>", role: "member" }
      ]
    },
    {
      name: "TechFlow Solutions",
      subdomain: "techflow",
      plan: "Enterprise",
      users: [
        { first_name: "Emily", last_name: "Chen", email: "<EMAIL>", role: "owner" },
        { first_name: "David", last_name: "Wilson", email: "<EMAIL>", role: "admin" }
      ]
    },
    {
      name: "DataViz Inc",
      subdomain: "dataviz",
      plan: "Starter",
      users: [
        { first_name: "Alex", last_name: "Martinez", email: "<EMAIL>", role: "owner" }
      ]
    }
  ]

  demo_accounts.each do |account_data|
    # Create or find account
    account = Account.find_or_initialize_by(subdomain: account_data[:subdomain])

    if account.new_record?
      account.assign_attributes(
        name: account_data[:name],
        status: :active,
        onboarding_completed_at: 1.week.ago
      )

      if account.save
        puts "✅ Created account: #{account.name} (#{account.subdomain})"

        # Create subscription if plan specified
        if account_data[:plan].present?
          plan = SubscriptionPlan.find_by(name: account_data[:plan])
          if plan
            subscription = account.build_subscription(
              subscription_plan: plan,
              status: :active,
              current_period_start: 1.month.ago,
              current_period_end: 1.month.from_now,
              stripe_customer_id: "cus_demo_#{account.subdomain}",
              stripe_subscription_id: "sub_demo_#{account.subdomain}"
            )
            subscription.save!
            puts "  📋 Added #{plan.name} subscription"
          end
        end

        # Create users for this account
        account_data[:users].each do |user_data|
          user = User.find_or_initialize_by(email: user_data[:email])
          if user.new_record?
            user.assign_attributes(
              first_name: user_data[:first_name],
              last_name: user_data[:last_name],
              password: "password123",
              password_confirmation: "password123",
              confirmed_at: Time.current,
              account: account,
              role: user_data[:role],
              time_zone: "America/New_York"
            )

            if user.save
              puts "  👤 Created user: #{user.full_name} (#{user.email})"
            else
              puts "  ❌ Failed to create user: #{user.email} - #{user.errors.full_messages.join(', ')}"
            end
          end
        end

        # Create sample data connectors for each account
        sample_connectors = [
          {
            name: "Sales Database",
            connector_type: "postgresql",
            description: "Main sales data from PostgreSQL",
            connection_config: {
              host: "localhost",
              port: "5432",
              database: "sales_db",
              username: "readonly_user"
            }
          },
          {
            name: "Customer CSV",
            connector_type: "csv_file",
            description: "Customer data export from legacy system",
            connection_config: {
              file_path: "/data/customers.csv",
              delimiter: ",",
              headers: true
            }
          },
          {
            name: "Analytics API",
            connector_type: "rest_api",
            description: "Real-time analytics from third-party API",
            connection_config: {
              base_url: "https://api.analytics.example.com",
              version: "v2",
              authentication: "api_key"
            }
          }
        ]

        owner = account.users.where(role: "owner").first

        sample_connectors.each do |connector_data|
          connector = account.data_connectors.build(connector_data.merge(created_by: owner))
          connector.status = :active
          connector.test_status = :test_passed
          connector.last_tested_at = rand(1.day.ago..Time.current)

          if connector.save
            puts "  🔌 Created connector: #{connector.name}"
          else
            puts "  ❌ Failed to create connector: #{connector.name} - #{connector.errors.full_messages.join(', ')}"
          end
        end

        # Create sample pipelines
        connectors = account.data_connectors.limit(2)
        if connectors.count >= 2
          source_connector = connectors.first
          destination_connector = connectors.last

          sample_pipelines = [
            {
              name: "Daily Sales Report",
              description: "Extract daily sales data and load into reporting system",
              schedule_type: :cron,
              schedule_config: {
                cron_expression: "0 6 * * *" # 6 AM daily
              },
              source_config: {
                connector_id: source_connector.id,
                query: "SELECT * FROM sales WHERE date >= CURRENT_DATE - INTERVAL '1 day'",
                format: "json"
              },
              destination_config: {
                connector_id: destination_connector.id,
                table: "daily_sales",
                mode: "append"
              }
            },
            {
              name: "Customer Data Sync",
              description: "Sync customer data between systems",
              schedule_type: :hourly,
              schedule_config: {
                minute: 0 # Every hour at minute 0
              },
              source_config: {
                connector_id: destination_connector.id,
                query: "customers",
                format: "csv"
              },
              destination_config: {
                connector_id: source_connector.id,
                table: "customers",
                mode: "upsert"
              }
            }
          ]

          sample_pipelines.each do |pipeline_data|
            pipeline = account.pipelines.build(pipeline_data.merge(created_by: owner))
            pipeline.status = :active
            pipeline.last_executed_at = rand(2.days.ago..6.hours.ago)

            if pipeline.save
              puts "  🔄 Created pipeline: #{pipeline.name}"

              # Create some execution history
              (1..5).each do |i|
                status_sample = [:success, :success, :success, :failed, :success].sample
                started_time = rand(7.days.ago..1.day.ago)
                execution_time_val = rand(10..300)
                records_processed = rand(100..10000)
                
                execution = pipeline.pipeline_executions.build(
                  status: status_sample,
                  started_at: started_time,
                  records_processed: records_processed,
                  records_success: status_sample == :success ? records_processed : rand(0..records_processed/2),
                  records_failed: status_sample == :failed ? rand(0..records_processed) : 0,
                  execution_time: execution_time_val,
                  execution_log: "Pipeline executed successfully. Processed #{records_processed} records."
                )
                execution.completed_at = execution.started_at + execution_time_val.seconds if [:success, :failed].include?(status_sample)
                execution.save!
              end
              puts "    📊 Created execution history"
            else
              puts "  ❌ Failed to create pipeline: #{pipeline.name} - #{pipeline.errors.full_messages.join(', ')}"
            end
          end
        end

        # Create sample notifications
        users = account.users.limit(2)
        users.each do |user|
          notifications = [
            {
              title: "Welcome to DataReflow!",
              message: "Your account has been set up successfully. Start by creating your first data connector.",
              notification_type: "system",
              priority: "low",
              read_at: rand > 0.5 ? rand(1.day.ago..Time.current) : nil
            },
            {
              title: "Pipeline execution completed",
              message: "Your 'Daily Sales Report' pipeline has completed successfully.",
              notification_type: "pipeline_success",
              priority: "medium",
              read_at: rand > 0.3 ? rand(1.day.ago..Time.current) : nil
            },
            {
              title: "Connection test failed",
              message: "The connection test for 'Analytics API' has failed. Please check your configuration.",
              notification_type: "connector_error",
              priority: "high",
              read_at: nil
            }
          ]

          notifications.each do |notification_data|
            notification = user.notifications.build(
              notification_data.merge(
                account: account,
                created_at: rand(3.days.ago..Time.current)
              )
            )
            if notification.save
              puts "  🔔 Created notification: #{notification.title}"
            end
          end
        end

        puts "✅ Completed setup for #{account.name}"
        puts ""
      else
        puts "❌ Failed to create account: #{account.name} - #{account.errors.full_messages.join(', ')}"
      end
    else
      puts "⏭️  Account already exists: #{account.name} (#{account.subdomain})"
    end
  end

  # Summary
  puts ""
  puts "📊 Development Data Summary:"
  puts "  Accounts: #{Account.count}"
  puts "  Users: #{User.count}"
  puts "  Subscription Plans: #{SubscriptionPlan.count}"
  puts "  Data Connectors: #{DataConnector.count}"
  puts "  Pipelines: #{Pipeline.count}"
  puts "  Pipeline Executions: #{PipelineExecution.count}"
  puts "  Notifications: #{Notification.count}"
  puts ""
  puts "🎉 You can now log in with any of the demo accounts:"
  User.joins(:account).limit(3).each do |user|
    puts "  • #{user.email} / password123 (#{user.account.name})"
  end

else
  # Production/other environments - only create essential data
  puts "🏭 Production mode - creating essential data only..."

  # Create subscription plans for production
  subscription_plans = [
    {
      name: "Starter",
      stripe_product_id: ENV['STRIPE_STARTER_PRODUCT_ID'],
      stripe_price_id: ENV['STRIPE_STARTER_PRICE_ID'],
      price_cents: 9900,
      billing_cycle: "month",
      features: [
        "10 pipelines",
        "10,000 executions/month",
        "All connectors",
        "Email support",
        "Basic transformations"
      ].to_json,
      active: true
    },
    {
      name: "Professional",
      stripe_product_id: ENV['STRIPE_PROFESSIONAL_PRODUCT_ID'],
      stripe_price_id: ENV['STRIPE_PROFESSIONAL_PRICE_ID'],
      price_cents: 29900,
      billing_cycle: "month",
      features: [
        "50 pipelines",
        "100,000 executions/month",
        "All connectors",
        "Priority support",
        "Advanced transformations",
        "API access",
        "Custom webhooks"
      ].to_json,
      active: true
    },
    {
      name: "Enterprise",
      stripe_product_id: ENV['STRIPE_ENTERPRISE_PRODUCT_ID'],
      stripe_price_id: ENV['STRIPE_ENTERPRISE_PRICE_ID'],
      price_cents: 59900,
      billing_cycle: "month",
      features: [
        "Unlimited pipelines",
        "Unlimited executions",
        "All connectors",
        "Dedicated support",
        "Custom integrations",
        "SLA guarantee",
        "On-premise option",
        "White-label solution"
      ].to_json,
      active: true
    }
  ]

  subscription_plans.each do |plan_attrs|
    plan = SubscriptionPlan.find_or_initialize_by(name: plan_attrs[:name])
    plan.assign_attributes(plan_attrs)
    if plan.save
      puts "✅ Created/updated subscription plan: #{plan.name}"
    else
      puts "❌ Failed to create subscription plan: #{plan.name} - #{plan.errors.full_messages.join(', ')}"
    end
  end
end

puts "🌱 Seeding completed!"

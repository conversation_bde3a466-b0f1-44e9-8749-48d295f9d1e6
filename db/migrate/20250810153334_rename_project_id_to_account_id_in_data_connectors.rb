class RenameProjectIdToAccountIdInDataConnectors < ActiveRecord::Migration[8.0]
  def up
    # Rename the column from project_id to account_id
    rename_column :data_connectors, :project_id, :account_id

    # Add the foreign key constraint if it doesn't exist
    unless foreign_key_exists?(:data_connectors, :accounts)
      add_foreign_key :data_connectors, :accounts, column: :account_id
    end

    # Add indexes that should exist for account_id
    unless index_exists?(:data_connectors, :account_id)
      add_index :data_connectors, :account_id
    end

    unless index_exists?(:data_connectors, [ :account_id, :connector_type ])
      add_index :data_connectors, [ :account_id, :connector_type ]
    end

    unless index_exists?(:data_connectors, [ :account_id, :status ])
      add_index :data_connectors, [ :account_id, :status ]
    end
  end

  def down
    # Remove indexes
    remove_index :data_connectors, [ :account_id, :status ] if index_exists?(:data_connectors, [ :account_id, :status ])
    remove_index :data_connectors, [ :account_id, :connector_type ] if index_exists?(:data_connectors, [ :account_id, :connector_type ])
    remove_index :data_connectors, :account_id if index_exists?(:data_connectors, :account_id)

    # Remove foreign key
    remove_foreign_key :data_connectors, :accounts if foreign_key_exists?(:data_connectors, :accounts)

    # Rename back to project_id
    rename_column :data_connectors, :account_id, :project_id
  end
end

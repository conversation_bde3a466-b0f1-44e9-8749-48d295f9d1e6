class CreateDashboards < ActiveRecord::Migration[8.0]
  def change
    create_table :dashboards do |t|
      t.references :account, null: false, foreign_key: true
      t.references :user, null: false, foreign_key: true
      t.string :name, null: false
      t.text :description
      t.integer :dashboard_type, default: 0, null: false # 0: custom, 1: system, 2: template
      t.jsonb :layout_config, default: {}, null: false # Grid layout configuration
      t.jsonb :filters, default: {}, null: false # Global dashboard filters
      t.jsonb :settings, default: {}, null: false # Dashboard settings
      t.boolean :is_default, default: false
      t.boolean :is_public, default: false
      t.string :public_slug # For shared dashboards
      t.integer :refresh_interval # In seconds, null means no auto-refresh
      t.datetime :last_viewed_at
      t.integer :view_count, default: 0
      t.integer :position # For ordering
      
      t.timestamps
    end
    
    add_index :dashboards, [:account_id, :user_id]
    add_index :dashboards, [:account_id, :is_default]
    add_index :dashboards, :public_slug, unique: true
    add_index :dashboards, [:account_id, :dashboard_type]
    add_index :dashboards, [:account_id, :position]
  end
end

class CreateSavedReports < ActiveRecord::Migration[8.0]
  def change
    create_table :saved_reports do |t|
      t.references :account, null: false, foreign_key: true
      t.references :user, null: false, foreign_key: true
      t.references :dashboard, foreign_key: true # Optional: report based on dashboard
      t.string :name, null: false
      t.text :description
      t.integer :report_type, default: 0 # 0: standard, 1: executive, 2: detailed
      t.jsonb :report_config, default: {}, null: false # Report configuration
      t.jsonb :data_sources, default: {}, null: false # Data queries/sources
      t.jsonb :sections, default: [], null: false # Report sections configuration
      t.integer :schedule_type, default: 0 # 0: once, 1: daily, 2: weekly, 3: monthly
      t.jsonb :schedule_config, default: {} # Cron expression or schedule details
      t.jsonb :recipients, default: [] # Email addresses or user IDs
      t.string :format, default: 'pdf' # pdf, excel, csv, html
      t.datetime :last_generated_at
      t.datetime :next_scheduled_at
      t.integer :generation_count, default: 0
      t.boolean :is_active, default: true
      
      t.timestamps
    end
    
    add_index :saved_reports, [:account_id, :user_id]
    add_index :saved_reports, [:account_id, :is_active]
    add_index :saved_reports, :next_scheduled_at
    add_index :saved_reports, :schedule_type
  end
end

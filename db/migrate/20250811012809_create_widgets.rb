class CreateWidgets < ActiveRecord::Migration[8.0]
  def change
    create_table :widgets do |t|
      t.references :dashboard, null: false, foreign_key: true
      t.string :name, null: false
      t.string :widget_type, null: false # line_chart, bar_chart, pie_chart, metric_card, data_table, etc.
      t.jsonb :position, default: {}, null: false # Grid position: {x, y, w, h}
      t.jsonb :config, default: {}, null: false # Widget-specific configuration
      t.jsonb :data_source, default: {}, null: false # Query or data source configuration
      t.jsonb :visualization_options, default: {}, null: false # Colors, labels, etc.
      t.jsonb :filters, default: {}, null: false # Widget-specific filters
      t.integer :refresh_interval # Override dashboard refresh interval
      t.boolean :is_visible, default: true
      t.integer :order_position # For mobile/list views
      t.jsonb :cached_data # Cached query results
      t.datetime :cached_at # When data was last cached
      
      t.timestamps
    end
    
    # dashboard_id index is already created by the foreign key reference
    add_index :widgets, [:dashboard_id, :order_position]
    add_index :widgets, :widget_type
    add_index :widgets, [:dashboard_id, :is_visible]
  end
end

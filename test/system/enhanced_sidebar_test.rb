require "application_system_test_case"

class EnhancedSidebarTest < ApplicationSystemTestCase
  setup do
    @user = users(:one)
    @account = accounts(:one)
    sign_in @user
  end

  test "sidebar displays all navigation sections" do
    visit subdomain_root_path(subdomain: @account.subdomain)
    
    # Check that all main sections are present
    assert_selector "[data-section-id='overview']"
    assert_selector "[data-section-id='data-management']"
    assert_selector "[data-section-id='analytics']"
    assert_selector "[data-section-id='agent']"
    assert_selector "[data-section-id='settings']"
  end

  test "sidebar sections can be collapsed and expanded" do
    visit subdomain_root_path(subdomain: @account.subdomain)
    
    # Find a collapsible section
    analytics_section = find("[data-section-id='analytics']")
    toggle_button = analytics_section.find("[data-sidebar-target='toggle']")
    content = analytics_section.find("[data-sidebar-target='content']")
    
    # Initially collapsed (based on default state)
    assert content[:style].include?("display: none")
    
    # Click to expand
    toggle_button.click
    
    # Should be expanded now
    sleep 0.5 # Wait for animation
    refute content[:style].include?("display: none")
  end

  test "navigation links have proper active states" do
    visit subdomain_root_path(subdomain: @account.subdomain)
    
    # Dashboard link should be active on root path
    dashboard_link = find("a[href='#{subdomain_root_path}']")
    assert dashboard_link[:style].include?("background-color: var(--color-primary-50)")
  end

  test "mobile navigation works correctly" do
    # Resize to mobile viewport
    page.driver.browser.manage.window.resize_to(375, 667)
    
    visit subdomain_root_path(subdomain: @account.subdomain)
    
    # Mobile nav button should be visible
    assert_selector "[data-navigation-target='mobileButton']"
    
    # Mobile overlay should be hidden initially
    overlay = find("[data-navigation-target='mobileOverlay']", visible: false)
    assert_equal "none", overlay[:style].match(/display:\s*([^;]+)/)[1]
    
    # Click mobile nav button
    find("[data-navigation-target='mobileButton']").click
    
    # Overlay should be visible
    sleep 0.2 # Wait for animation
    assert_selector "[data-navigation-target='mobileOverlay']", visible: true
  end

  test "navigation badges display correct counts" do
    # Create some test data
    create(:notification, account: @account, user: @user, read_at: nil)
    create(:pipeline, account: @account)
    create(:data_connector, account: @account)
    
    visit subdomain_root_path(subdomain: @account.subdomain)
    
    # Check for notification badge
    notifications_link = find("a[href='#{notifications_path}']")
    assert_selector ".badge", text: "1", within: notifications_link
    
    # Check for pipeline count badge
    pipelines_link = find("a[href='#{pipelines_path}']")
    assert_selector ".badge", within: pipelines_link
    
    # Check for connector count badge
    connectors_link = find("a[href='#{data_connectors_path}']")
    assert_selector ".badge", within: connectors_link
  end

  test "accessibility features work correctly" do
    visit subdomain_root_path(subdomain: @account.subdomain)
    
    # Check ARIA attributes
    toggle_buttons = all("[data-sidebar-target='toggle']")
    toggle_buttons.each do |button|
      assert button["aria-expanded"].present?
      assert button["aria-controls"].present?
    end
    
    # Check navigation landmarks
    assert_selector "nav[role='navigation']"
    assert_selector "nav[aria-label='Main navigation']"
    
    # Check screen reader content
    assert_selector ".sr-only", text: "Close menu"
  end

  test "keyboard navigation works" do
    visit subdomain_root_path(subdomain: @account.subdomain)
    
    # Focus first toggle button
    first_toggle = find("[data-sidebar-target='toggle']")
    first_toggle.send_keys(:tab)
    
    # Press Enter to toggle
    first_toggle.send_keys(:enter)
    
    # Should toggle the section
    sleep 0.3
    # Test passes if no JavaScript errors occur
  end

  private

  def sign_in(user)
    # Implement your sign-in logic here
    # This will depend on your authentication system
  end
end

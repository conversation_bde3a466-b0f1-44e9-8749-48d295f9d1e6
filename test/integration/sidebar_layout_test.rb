require "test_helper"

class SidebarLayoutTest < ActionDispatch::IntegrationTest
  setup do
    @user = users(:one)
    @account = accounts(:one)
    sign_in @user
  end

  test "notifications page includes enhanced sidebar" do
    get notifications_path
    
    assert_response :success
    
    # Check that the enhanced sidebar is present
    assert_select "[data-controller='sidebar']"
    assert_select "[data-section-id='overview']"
    assert_select "[data-section-id='data-management']"
    assert_select "[data-section-id='analytics']"
    assert_select "[data-section-id='agent']"
    assert_select "[data-section-id='settings']"
    
    # Check that notifications link is active
    assert_select "a[href='#{notifications_path}'][aria-current='page']"
  end

  test "dashboard page includes enhanced sidebar" do
    get subdomain_root_path
    
    assert_response :success
    assert_select "[data-controller='sidebar']"
    
    # Dashboard link should be active
    assert_select "a[href='#{subdomain_root_path}'][aria-current='page']"
  end

  test "pipelines page includes enhanced sidebar" do
    get pipelines_path
    
    assert_response :success
    assert_select "[data-controller='sidebar']"
    
    # Pipelines link should be active
    assert_select "a[href='#{pipelines_path}'][aria-current='page']"
  end

  test "analytics page includes enhanced sidebar" do
    get analytics_path
    
    assert_response :success
    assert_select "[data-controller='sidebar']"
    
    # Analytics link should be active
    assert_select "a[href='#{analytics_path}'][aria-current='page']"
  end

  test "team members page includes enhanced sidebar" do
    get team_members_path
    
    assert_response :success
    assert_select "[data-controller='sidebar']"
    
    # Team link should be active
    assert_select "a[href='#{team_members_path}'][aria-current='page']"
  end

  test "onboarding page includes enhanced sidebar" do
    # Make account incomplete to access onboarding
    @account.update!(onboarding_completed: false)
    
    get onboarding_index_path
    
    assert_response :success
    assert_select "[data-controller='sidebar']"
  end

  private

  def sign_in(user)
    # Implement your sign-in logic here
    # This will depend on your authentication system
    post user_session_path, params: {
      user: {
        email: user.email,
        password: 'password'
      }
    }
  end
end
